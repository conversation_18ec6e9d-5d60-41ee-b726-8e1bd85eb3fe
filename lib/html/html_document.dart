import 'dart:io';

import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';
import 'package:uuid/uuid.dart';

/// HTML文档模型
class HtmlDocument {
  /// 文档ID
  final String id;

  /// HTML内容
  String content;

  /// 文档标题
  String title;

  /// 创建时间
  final DateTime createdAt;

  /// 最后修改时间
  DateTime updatedAt;

  /// 构造函数
  HtmlDocument({
    String? id,
    required this.content,
    String? title,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) : id = id ?? const Uuid().v4(),
       title = title ?? 'Untitled HTML',
       createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();

  /// 从JSON创建
  factory HtmlDocument.fromJson(Map<String, dynamic> json) {
    return HtmlDocument(
      id: json['id'] as String,
      content: json['content'] as String,
      title: json['title'] as String,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'content': content,
      'title': title,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  /// 更新内容
  void updateContent(String newContent) {
    content = newContent;
    updatedAt = DateTime.now();
  }

  /// 更新标题
  void updateTitle(String newTitle) {
    title = newTitle;
    updatedAt = DateTime.now();
  }

  /// 从文件加载HTML
  static Future<HtmlDocument> fromFile(File file) async {
    final content = await file.readAsString();
    final fileName = file.path.split('/').last;
    final title = fileName.replaceAll('.html', '');

    return HtmlDocument(content: content, title: title);
  }

  /// 保存到文件
  Future<File> saveToFile() async {
    final directory = await getApplicationDocumentsDirectory();
    final fileName =
        '${title.replaceAll(' ', '_')}_${DateTime.now().millisecondsSinceEpoch}.html';
    final filePath = '${directory.path}/$fileName';

    final file = File(filePath);
    return await file.writeAsString(content);
  }

  /// 分享HTML
  Future<void> share() async {
    final file = await saveToFile();
    await SharePlus.instance.share(
      ShareParams(files: [XFile(file.path)], text: title),
    );
  }

  /// 提取HTML标题
  String? extractHtmlTitle() {
    final RegExp titleRegex = RegExp(r'<title>(.*?)</title>');
    final match = titleRegex.firstMatch(content);

    if (match != null && match.groupCount >= 1) {
      return match.group(1);
    }

    return null;
  }

  /// 复制HTML文档
  HtmlDocument copy() {
    return HtmlDocument(
      content: content,
      title: '$title - Copy',
      createdAt: DateTime.now(),
    );
  }

  /// 获取基本HTML模板
  static String getBasicTemplate() {
    return '';
  }
}
