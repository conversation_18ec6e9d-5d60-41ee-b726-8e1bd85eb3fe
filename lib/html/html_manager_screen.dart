import 'dart:io';
import 'dart:math' as math;
import 'dart:convert';
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';
import 'package:printing/printing.dart';
import 'package:pdf/pdf.dart' show PdfPageFormat;
import 'package:saver_gallery/saver_gallery.dart';
import 'package:permission_handler/permission_handler.dart';

import 'package:file_picker/file_picker.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:flutter/services.dart' show rootBundle;

import '../config/app_theme.dart';
import '../content/content_home_page.dart';
import '../content/content_save_button.dart';
import '../models/content_item.dart';
import '../services/content_service.dart';
import '../services/service_locator.dart';
import '../generated/l10n/app_localizations.dart';
import 'html_editor_screen.dart';
import 'html_document.dart';

/// HTML管理屏幕
class HtmlManagerScreen extends StatefulWidget {
  /// 可选：外部打开文件时传入的初始 HTML 内容
  final String? initialHtmlContent;

  /// 可选：外部打开文件时传入的标题
  final String? initialTitle;

  /// 构造函数
  const HtmlManagerScreen({
    super.key,
    this.initialHtmlContent,
    this.initialTitle,
  });

  @override
  State<HtmlManagerScreen> createState() => _HtmlManagerScreenState();
}

class _HtmlManagerScreenState extends State<HtmlManagerScreen> {
  /// HTML服务
  final _htmlService = ServiceLocator().htmlService;

  /// 内容服务
  final _contentService = ContentService();

  /// 是否正在导入
  bool _isImporting = false;

  /// 是否正在加载
  bool _isLoading = true;

  /// 是否正在导出图片
  bool _isExportingImage = false;

  /// 本地化
  late AppLocalizations _l10n;

  /// 当前选中的文档（为空时用内置模板）
  HtmlDocument? _selectedDocument;

  /// WebView 控制器
  WebViewController? _webViewController;
  bool _isWebViewInitialized = false;

  @override
  void initState() {
    super.initState();
    _contentService.initialize();
    _loadInitial();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _l10n = AppLocalizations.of(context);
  }

  @override
  void dispose() {
    super.dispose();
  }

  /// 创建新HTML
  void _createNewHtml() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const HtmlEditorScreen()),
    );
  }

  Future<void> _loadInitial() async {
    setState(() => _isLoading = true);
    try {
      await _ensureBuiltinHtmlLoaded();
      await _htmlService.initialize();
      final docs = _htmlService.documents;
      final settings = ServiceLocator().settingsService.settings;
      
      // 调试日志
      debugPrint('=== HTML Manager Screen 调试信息 ===');
      debugPrint('initialHtmlContent 是否为空: ${widget.initialHtmlContent == null}');
      if (widget.initialHtmlContent != null) {
        debugPrint('initialHtmlContent 长度: ${widget.initialHtmlContent!.length}');
        debugPrint('initialHtmlContent 前100字符: ${widget.initialHtmlContent!.substring(0, math.min(100, widget.initialHtmlContent!.length))}');
      }
      debugPrint('initialTitle: ${widget.initialTitle}');
      
      // 若存在外部打开的 HTML，优先展示该内容
      if (widget.initialHtmlContent != null &&
          widget.initialHtmlContent!.trim().isNotEmpty) {
        debugPrint('使用外部 HTML 内容');
        final title =
            (widget.initialTitle == null || widget.initialTitle!.trim().isEmpty)
                ? 'imported.html'
                : widget.initialTitle!.trim();
        _selectedDocument = HtmlDocument(
          title: title,
          content: widget.initialHtmlContent!,
        );
        debugPrint('已创建外部文档，标题: $title, 内容长度: ${widget.initialHtmlContent!.length}');
      } else {
        debugPrint('使用默认 HTML 内容');
        _selectedDocument =
            docs.isNotEmpty
                ? docs.first
                : HtmlDocument(
                  title: 'Starter',
                  content:
                      settings.useDefaultInitialTextHtml ? _builtinHtml : '',
                );
      }
      debugPrint('最终选中的文档标题: ${_selectedDocument?.title}');
      debugPrint('最终选中的文档内容长度: ${_selectedDocument?.content.length ?? 0}');
      
      _initWebView();
      _loadWebViewContent();
    } catch (_) {
      _initWebView();
      _loadWebViewContent();
    } finally {
      if (mounted) setState(() => _isLoading = false);
    }
  }

  void _initWebView() {
    if (_isWebViewInitialized) return;
    try {
      _webViewController =
          WebViewController()
            ..setJavaScriptMode(JavaScriptMode.unrestricted)
            ..setBackgroundColor(Colors.white);
      _isWebViewInitialized = true;
    } catch (_) {
      _isWebViewInitialized = false;
    }
  }

  void _loadWebViewContent() {
    final html = _selectedDocument?.content ?? _builtinHtml;
    if (!_isWebViewInitialized) _initWebView();
    _webViewController?.loadHtmlString(html);
  }

  void _editCurrent() {
    final doc = _selectedDocument;
    if (doc == null) return;
    final exists = _htmlService.documents.any((d) => d.id == doc.id);
    Navigator.push(
      context,
      MaterialPageRoute(
        builder:
            (context) => HtmlEditorScreen(
              documentId: exists ? doc.id : null,
              initialHtmlContent: doc.content,
              initialTitle: doc.title,
            ),
      ),
    ).then((_) => _loadInitial());
  }

  Future<void> _shareHtml() async {
    final doc = _selectedDocument;
    if (doc == null) return;
    try {
      // 生成临时 HTML 文件并通过系统分享
      final html = doc.content.isNotEmpty ? doc.content : _builtinHtml;
      final tempDir = await getTemporaryDirectory();
      var baseName = doc.title.trim().isEmpty ? 'document' : doc.title.trim();
      baseName = baseName.replaceAll(RegExp(r'[^\w\-]+'), '_');
      final fileName =
          baseName.toLowerCase().endsWith('.html')
              ? baseName
              : '$baseName.html';
      final file = File('${tempDir.path}/$fileName');
      await file.writeAsString(html, encoding: utf8);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(_l10n.htmlSavedToLibrary(doc.title))),
        );
      }
      await SharePlus.instance.share(
        ShareParams(
          files: [XFile(file.path, mimeType: 'text/html', name: fileName)],
          text: doc.title,
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(_l10n.htmlShareHtmlFailed(e.toString()))),
      );
    }
  }

  Future<void> _shareAsImage() async {
    final doc = _selectedDocument;
    if (doc == null) return;
    try {
      bool progressShown = false;
      if (mounted) {
        setState(() => _isExportingImage = true);
        showDialog(
          context: context,
          barrierDismissible: false,
          builder:
              (_) => AlertDialog(
                content: Row(
                  children: [
                    const CircularProgressIndicator(),
                    const SizedBox(width: 16),
                    Expanded(child: Text(_l10n.htmlProcessing)),
                  ],
                ),
              ),
        );
        progressShown = true;
      }
      // 优先：从当前 WebView 捕获图片，保证与预览一致（所见即所得）
      Uint8List? bytes = await _captureWebViewPng();
      // 回退：使用 printing 将 HTML 渲染为 PDF 后光栅化为 PNG（跨平台更稳）
      bytes ??= await _exportHtmlToPngViaPrinting();
      // 回退到服务端转换（基础占位）
      bytes ??= await _htmlService.convertToImage(doc);

      if (bytes != null) {
        // 关闭进度弹窗后展示预览对话框
        if (progressShown && mounted) {
          Navigator.of(context, rootNavigator: true).maybePop();
          progressShown = false;
        }
        if (mounted) setState(() => _isExportingImage = false);
        if (!mounted) return;
        await _showImagePreviewDialog(bytes, suggestName: doc.title);
        return;
      } else {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(_l10n.htmlExportImageFailed('no image data'))),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(_l10n.htmlShareImageFailed(e.toString()))),
      );
    } finally {
      if (mounted) {
        setState(() => _isExportingImage = false);
        // 若进度弹窗仍存在则关闭
        Navigator.of(context, rootNavigator: true).maybePop();
      }
    }
  }

  Future<void> _showImagePreviewDialog(
    Uint8List bytes, {
    String? suggestName,
  }) async {
    final name =
        (suggestName == null || suggestName.trim().isEmpty)
            ? 'html_preview'
            : suggestName.trim();
    await showDialog(
      context: context,
      barrierDismissible: true,
      builder: (context) {
        return AlertDialog(
          contentPadding: const EdgeInsets.all(8),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ConstrainedBox(
                constraints: const BoxConstraints(
                  maxWidth: 600,
                  maxHeight: 600,
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: Image.memory(bytes, fit: BoxFit.contain),
                ),
              ),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(_l10n.htmlCancel),
            ),
            FilledButton.icon(
              onPressed: () async {
                final ok = await _saveImageToGallery(bytes, name: name);
                if (!mounted) return;
                if (ok) {
                  Navigator.of(context).pop();
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text(_l10n.htmlSavedToLibrary(name))),
                  );
                } else {
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(_l10n.htmlExportImageFailed('save failed')),
                    ),
                  );
                }
              },
              icon: const Icon(Icons.save_alt),
              label: Text(_l10n.htmlSaveToGallery),
            ),
          ],
        );
      },
    );
  }

  Future<bool> _saveImageToGallery(
    Uint8List bytes, {
    required String name,
  }) async {
    try {
      // 权限处理：iOS 使用 Photos，Android 使用存储
      if (Platform.isIOS) {
        final status = await Permission.photosAddOnly.request();
        if (!status.isGranted) return false;
      } else if (Platform.isAndroid) {
        // 尝试请求存储权限（在新系统上可能不需要）
        final st = await Permission.storage.request();
        if (!st.isGranted && await Permission.manageExternalStorage.isDenied) {
          // 尽量不阻断，继续尝试保存
        }
      }

      final result = await SaverGallery.saveImage(
        bytes,
        fileName: '${name}_${DateTime.now().millisecondsSinceEpoch}.png',
        skipIfExists: false,
        quality: 100,
      );
      return result.isSuccess;
    } catch (_) {
      return false;
    }
  }

  Future<Uint8List?> _exportHtmlToPngViaPrinting() async {
    try {
      final html = _selectedDocument?.content ?? _builtinHtml;
      // 转为 PDF
      // TODO: Replace deprecated convertHtml with newer PDF generation method
      // Currently no direct alternative available in the printing package
      // ignore: deprecated_member_use
      final pdfBytes = await Printing.convertHtml(
        format: PdfPageFormat.a4,
        html: html,
      );
      // 光栅化第1页
      final images = await Printing.raster(pdfBytes, dpi: 200).toList();
      if (images.isNotEmpty) {
        final firstPage = images.first;
        final png = await firstPage.toPng();
        return png;
      }
      return null;
    } catch (_) {
      return null;
    }
  }

  Future<Uint8List?> _captureWebViewPng() async {
    if (!_isWebViewInitialized || _webViewController == null) return null;
    try {
      // 额外等待一帧，保证 WebView 完全渲染
      await Future.delayed(const Duration(milliseconds: 50));

      // 在执行截图前尝试滚动到顶部，避免被用户滚动打断
      const jsScrollTop = 'window.scrollTo(0, 0); "OK";';
      await _webViewController!.runJavaScriptReturningResult(jsScrollTop);

      // 计算兜底背景色（不修改页面，仅在截图时作为 backgroundColor 传入）
      final bool isDark = Theme.of(context).brightness == Brightness.dark;
      final String capBg = isDark ? '#0B1220' : '#FFFFFF';

      // 优先方案：注入 html2canvas 按“当前可见视口”截图，尽量与预览一致
      const jsHtml2CanvasTemplate = r"""
        (async () => {
          async function waitFonts() {
            try { if (document.fonts && document.fonts.ready) { await document.fonts.ready; } } catch(e) {}
            await new Promise(r => setTimeout(r, 60));
          }
          function loadFrom(url) {
            return new Promise((resolve, reject) => {
              const s = document.createElement('script');
              s.src = url;
              s.onload = () => resolve(true);
              s.onerror = () => reject(new Error('load failed'));
              document.head.appendChild(s);
            });
          }
          async function ensureHtml2Canvas() {
            if (window.html2canvas) return true;
            const cdns = [
              'https://cdn.jsdelivr.net/npm/html2canvas@1.4.1/dist/html2canvas.min.js',
              'https://unpkg.com/html2canvas@1.4.1/dist/html2canvas.min.js'
            ];
            for (const url of cdns) {
              try { await loadFrom(url); if (window.html2canvas) return true; } catch(e) {}
            }
            return !!window.html2canvas;
          }
          function resolveBg() {
            const target = document.body || document.documentElement;
            const bg = getComputedStyle(target).backgroundColor;
            if (!bg || bg === 'transparent' || bg === 'rgba(0, 0, 0, 0)') {
              return '__BG__';
            }
            return bg;
          }
          try {
            await waitFonts();
            await ensureHtml2Canvas();
            const node = document.body || document.documentElement;
            const scale = Math.min(2, window.devicePixelRatio || 1);
            const w = Math.floor(window.innerWidth);
            const h = Math.floor(window.innerHeight);
            const x = Math.floor(window.pageXOffset || 0);
            const y = Math.floor(window.pageYOffset || 0);
            const canvas = await window.html2canvas(node, {
              backgroundColor: resolveBg(),
              useCORS: true,
              allowTaint: true,
              scale: scale,
              width: w,
              height: h,
              x: x,
              y: y,
              windowWidth: Math.max(document.documentElement.scrollWidth, w),
              windowHeight: Math.max(document.documentElement.scrollHeight, h),
            });
            return canvas.toDataURL('image/png');
          } catch (e) {
            return 'ERROR:' + ('' + e);
          }
        })();
      """;
      final jsHtml2Canvas = jsHtml2CanvasTemplate.replaceAll('__BG__', capBg);
      final result1 = await _webViewController!.runJavaScriptReturningResult(
        jsHtml2Canvas,
      );
      if (result1 is String &&
          result1.isNotEmpty &&
          !result1.contains('ERROR:')) {
        final dataUrl = result1.replaceAll('"', '').replaceAll("\n", '').trim();
        final idx = dataUrl.indexOf('base64,');
        if (idx != -1) {
          const jsRevert =
              '(function(){try{const h=document.documentElement;const b=document.body||h; if(window.__origBg__){h.style.background=window.__origBg__[0]; b.style.background=window.__origBg__[1];} else {h.style.background=""; b.style.background="";} return "OK";}catch(e){return "OK";}})();';
          await _webViewController!.runJavaScriptReturningResult(jsRevert);
          final b64 = dataUrl.substring(idx + 7);
          return base64Decode(b64);
        }
      }

      // 回退方案：foreignObject，裁剪到当前视口区域
      const jsForeignObjectTemplate = r"""
        (async () => {
          const doc = document.documentElement;
          const body = document.body;
          const vw = Math.floor(window.innerWidth);
          const vh = Math.floor(window.innerHeight);
          const x = Math.floor(window.pageXOffset || 0);
          const y = Math.floor(window.pageYOffset || 0);
          const bg = '__BG__';
          const inner = new XMLSerializer().serializeToString(doc);
          const translated = `<div xmlns=\"http://www.w3.org/1999/xhtml\" style=\"position:relative; width:${doc.scrollWidth}px; height:${doc.scrollHeight}px; transform: translate(${-x}px, ${-y}px); background:${bg};\">${inner}</div>`;
          const svg = `<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"${vw}\" height=\"${vh}\"><foreignObject x=\"0\" y=\"0\" width=\"${vw}\" height=\"${vh}\">${translated}</foreignObject></svg>`;
          const svg64 = btoa(unescape(encodeURIComponent(svg)));
          const image64 = 'data:image/svg+xml;base64,' + svg64;
          const img = new Image();
          img.crossOrigin = 'anonymous';
          await new Promise(resolve => { img.onload = resolve; img.src = image64; });
          const canvas = document.createElement('canvas');
          canvas.width = vw; canvas.height = vh;
          const ctx = canvas.getContext('2d');
          ctx.drawImage(img, 0, 0);
          return canvas.toDataURL('image/png');
        })();
      """;
      final jsForeignObject = jsForeignObjectTemplate.replaceAll(
        '__BG__',
        capBg,
      );
      final result2 = await _webViewController!.runJavaScriptReturningResult(
        jsForeignObject,
      );
      if (result2 is String &&
          result2.isNotEmpty &&
          !result2.contains('ERROR:')) {
        final dataUrl = result2.replaceAll('"', '').replaceAll("\n", '').trim();
        final idx = dataUrl.indexOf('base64,');
        if (idx != -1) {
          // 还原背景
          const String jsRevertBg =
              '(function(){try{const h=document.documentElement;const b=document.body||h; if(window.__origBg__){h.style.background=window.__origBg__[0]; b.style.background=window.__origBg__[1];} else {h.style.background='
              ""
              '; b.style.background='
              ""
              ';} return "OK";}catch(e){return "OK";}})();';
          await _webViewController!.runJavaScriptReturningResult(jsRevertBg);
          final b64 = dataUrl.substring(idx + 7);
          return base64Decode(b64);
        }
      }
      // 还原背景（兜底）
      const jsRevert =
          '(function(){try{const h=document.documentElement;const b=document.body||h; if(window.__origBg__){h.style.background=window.__origBg__[0]; b.style.background=window.__origBg__[1];} else {h.style.background=""; b.style.background="";} return "OK";}catch(e){return "OK";}})();';
      await _webViewController!.runJavaScriptReturningResult(jsRevert);
      return null;
    } catch (_) {
      return null;
    }
  }

  /// 导入HTML文件
  Future<void> _importHtmlFile() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['html', 'htm'],
        allowMultiple: true,
      );

      if (result != null && result.files.isNotEmpty) {
        setState(() => _isLoading = true);
        int imported = 0;
        for (final f in result.files) {
          if (f.path == null) continue;
          final htmlDoc = await _htmlService.importFromFile(File(f.path!));
          if (htmlDoc != null) {
            await _contentService.createTextContent(
              title: htmlDoc.title,
              type: ContentType.html,
              content: htmlDoc.content,
              tags: [],
            );
            imported++;
          }
        }
        if (mounted && imported > 0) {
          _showSuccessDialog(imported);
        }
        await _loadInitial();
      }
    } catch (e) {
      // 重置导入状态
      setState(() {
        _isImporting = false;
        _isLoading = false;
      });

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(_l10n.htmlImportFailed(e.toString())),
            backgroundColor: Colors.red.shade700,
            behavior: SnackBarBehavior.floating,
          ),
        );
      }
    }
  }

  /// 显示导入成功对话框
  void _showSuccessDialog(int count) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
            title: Row(
              children: [
                Icon(
                  Icons.check_circle,
                  color: Colors.green.shade500,
                  size: 28,
                ),
                const SizedBox(width: 8),
                Text(_l10n.htmlImportSuccess),
              ],
            ),
            content: Text(_l10n.htmlImportSuccessMessage(count)),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: Text(_l10n.htmlCancel),
              ),
              FilledButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const ContentHomePage(),
                    ),
                  );
                },
                style: FilledButton.styleFrom(
                  backgroundColor: AppTheme.primaryColor,
                ),
                child: Text(_l10n.htmlViewContentLibrary),
              ),
            ],
          ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;

    return Scaffold(
      backgroundColor:
          isDarkMode ? AppTheme.darkBgColor : AppTheme.bgLightColor,
      appBar: AppBar(
        title: Text(
          _l10n.htmlManagerTitle,
          style: TextStyle(fontWeight: FontWeight.w600, fontSize: 20),
        ),
        backgroundColor:
            isDarkMode ? AppTheme.darkBgLightColor : AppTheme.bgWhiteColor,
        foregroundColor:
            isDarkMode ? AppTheme.darkTextColor : AppTheme.textDarkColor,
        elevation: 0,
        scrolledUnderElevation: 1,
        shadowColor:
            isDarkMode ? AppTheme.darkBorderColor : AppTheme.borderColor,
        actions: [
          if (_selectedDocument != null)
            ContentSaveButton(
              title: _selectedDocument!.title,
              content: _selectedDocument!.content,
              contentType: ContentType.html,
              onSaved: (item) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(_l10n.htmlSavedToLibrary(item.title)),
                    backgroundColor: AppTheme.greenDark,
                    behavior: SnackBarBehavior.floating,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(
                        AppTheme.borderRadiusSM,
                      ),
                    ),
                  ),
                );
              },
            ),
          IconButton(
            icon: const Icon(Icons.add, size: 22),
            onPressed: _isImporting ? null : _createNewHtml,
            tooltip: _l10n.htmlNewHtmlTooltip,
            color: AppTheme.primaryColor,
          ),
        ],
      ),
      body:
          _isLoading
              ? Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const CircularProgressIndicator(),
                    const SizedBox(height: 16),
                    Text(_l10n.htmlProcessing),
                  ],
                ),
              )
              : _buildSinglePreview(),
    );
  }

  Widget _buildSinglePreview() {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final doc = _selectedDocument;
    if (doc == null) return const SizedBox.shrink();

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        children: [
          Expanded(
            child: Container(
              width: double.infinity,
              decoration: BoxDecoration(
                color:
                    isDarkMode
                        ? AppTheme.darkBgLightColor
                        : AppTheme.bgWhiteColor,
                borderRadius: BorderRadius.circular(AppTheme.borderRadiusLG),
                boxShadow: [
                  BoxShadow(
                    color:
                        isDarkMode
                            ? Colors.black.withValues(alpha: 0.3)
                            : Colors.black.withValues(alpha: 0.08),
                    blurRadius: 16,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              clipBehavior: Clip.antiAlias,
              child:
                  _isWebViewInitialized && _webViewController != null
                      ? WebViewWidget(controller: _webViewController!)
                      : const Center(child: CircularProgressIndicator()),
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: _editCurrent,
                  icon: const Icon(Icons.edit_outlined),
                  label: Text(_l10n.edit),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppTheme.primaryColor,
                    foregroundColor: Colors.white,
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: _isImporting ? null : _importHtmlFile,
                  icon: const Icon(Icons.download_outlined),
                  label: Text(_l10n.htmlImportFile),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: _isExportingImage ? null : _shareHtml,
                  icon: const Icon(Icons.code),
                  label: Text(_l10n.htmlShareHtml),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: _isExportingImage ? null : _shareAsImage,
                  icon:
                      _isExportingImage
                          ? const SizedBox(
                            width: 18,
                            height: 18,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                          : const Icon(Icons.image_outlined),
                  label: Text(
                    _isExportingImage
                        ? _l10n.htmlProcessing
                        : _l10n.htmlShareAsImage,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  // 内置简洁HTML模板
  static String _builtinHtml = '';

  static Future<void> _ensureBuiltinHtmlLoaded() async {
    if (_builtinHtml.isNotEmpty) return;
    try {
      String code = 'en';
      try {
        code =
            WidgetsBinding.instance.platformDispatcher.locale.languageCode
                .toLowerCase();
      } catch (_) {}
      String assetPath;
      if (code.startsWith('ja')) {
        assetPath = 'assets/html/builtin_ja.html';
      } else if (code.startsWith('zh')) {
        assetPath = 'assets/html/builtin.html';
      } else {
        assetPath = 'assets/html/builtin_en.html';
      }
      try {
        _builtinHtml = await rootBundle.loadString(assetPath);
      } catch (_) {
        // Fallback to English
        _builtinHtml = await rootBundle.loadString(
          'assets/html/builtin_en.html',
        );
      }
    } catch (_) {
      String msg;
      try {
        final code =
            WidgetsBinding.instance.platformDispatcher.locale.languageCode
                .toLowerCase();
        if (code.startsWith('ja')) {
          msg = '内蔵テンプレートの読み込みに失敗しました';
        } else if (code.startsWith('zh')) {
          msg = '内置模板加载失败';
        } else {
          msg = 'Failed to load built‑in template';
        }
      } catch (_) {
        msg = 'Failed to load built‑in template';
      }
      _builtinHtml =
          '<!doctype html><html><head><meta charset="utf-8"/></head><body><div style="padding:24px;font-family:sans-serif;">$msg</div></body></html>';
    }
  }
}
