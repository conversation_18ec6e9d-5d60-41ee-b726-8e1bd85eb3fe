import 'dart:convert';
import 'dart:io';
import 'dart:ui' as ui;

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';
import 'package:shared_preferences/shared_preferences.dart';

import 'html_document.dart';

/// HTML服务类
class HtmlService {
  /// 存储键
  static const String _keyHtmlDocuments = 'html_documents';

  /// 单例实例
  static final HtmlService _instance = HtmlService._internal();

  /// 工厂构造函数
  factory HtmlService() => _instance;

  /// 内部构造函数
  HtmlService._internal();

  /// 保存的HTML文档列表
  final List<HtmlDocument> _documents = [];

  /// 获取所有HTML文档
  List<HtmlDocument> get documents => List.unmodifiable(_documents);

  /// 初始化服务
  Future<void> initialize() async {
    // 从存储中加载HTML文档
    await _loadDocuments();
  }

  /// 从存储中加载HTML文档
  Future<void> _loadDocuments() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonString = prefs.getString(_keyHtmlDocuments);

      if (jsonString != null) {
        final jsonList = jsonDecode(jsonString) as List;
        _documents.clear();

        for (final item in jsonList) {
          _documents.add(HtmlDocument.fromJson(item as Map<String, dynamic>));
        }
      }
    } catch (e) {
      debugPrint('加载HTML文档失败: $e');
    }
  }

  /// 保存HTML文档到存储
  Future<void> _saveDocuments() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonList = _documents.map((doc) => doc.toJson()).toList();
      final jsonString = jsonEncode(jsonList);

      await prefs.setString(_keyHtmlDocuments, jsonString);
    } catch (e) {
      debugPrint('保存HTML文档失败: $e');
    }
  }

  /// 添加HTML文档
  Future<void> addDocument(HtmlDocument document) async {
    _documents.add(document);
    await _saveDocuments();
  }

  /// 更新HTML文档
  Future<void> updateDocument(HtmlDocument document) async {
    final index = _documents.indexWhere((doc) => doc.id == document.id);

    if (index != -1) {
      _documents[index] = document;
      await _saveDocuments();
    }
  }

  /// 删除HTML文档
  Future<void> deleteDocument(String id) async {
    _documents.removeWhere((doc) => doc.id == id);
    await _saveDocuments();
  }

  /// 获取HTML文档
  HtmlDocument? getDocument(String id) {
    try {
      return _documents.firstWhere((doc) => doc.id == id);
    } catch (e) {
      return null;
    }
  }

  /// 从文件导入HTML
  Future<HtmlDocument?> importFromFile(File file) async {
    try {
      final document = await HtmlDocument.fromFile(file);
      await addDocument(document);
      return document;
    } catch (e) {
      debugPrint('导入HTML文件失败: $e');
      return null;
    }
  }

  /// 从字符串创建HTML
  Future<HtmlDocument> createFromString(
    String htmlContent, {
    String? title,
  }) async {
    final document = HtmlDocument(
      content: htmlContent,
      title: title ?? 'Untitled HTML',
    );

    await addDocument(document);
    return document;
  }

  /// 创建新的HTML文档
  Future<HtmlDocument> createNewDocument({String? title}) async {
    final document = HtmlDocument(
      content: HtmlDocument.getBasicTemplate(),
      title: title ?? 'New HTML Document',
    );

    await addDocument(document);
    return document;
  }

  /// 将HTML转换为图片
  Future<Uint8List?> convertToImage(
    HtmlDocument document, {
    double width = 800,
    double height = 1200, // 增加默认高度
    double scale = 2.0,
  }) async {
    try {
      // 创建一个临时文件来存储HTML内容
      final tempDir = await getTemporaryDirectory();
      final tempFile = File('${tempDir.path}/temp_${document.id}.html');

      // 修改HTML内容，添加更好的样式
      String enhancedHtml = document.content;

      // 如果HTML没有设置合适的样式，添加一些基本样式
      if (!enhancedHtml.contains('<style')) {
        enhancedHtml = enhancedHtml.replaceFirst('</head>', '''
  <style>
    body {
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      line-height: 1.6;
      color: #333;
      max-width: 100%;
      margin: 0 auto;
      padding: 20px;
    }
    img { max-width: 100%; height: auto; }
    pre, code { white-space: pre-wrap; overflow-wrap: break-word; }
    table { width: 100%; border-collapse: collapse; }
    th, td { padding: 8px; border: 1px solid #ddd; }
  </style>
</head>''');
      }

      // 确保内容不会被挤压
      enhancedHtml = enhancedHtml.replaceFirst(
        '<body',
        '<body style="width:100%; box-sizing:border-box; padding:20px;"',
      );

      await tempFile.writeAsString(enhancedHtml);

      // 创建一个临时的PNG文件
      final pngFile = File('${tempDir.path}/temp_${document.id}.png');

      // 尝试使用系统命令将HTML转换为图片
      try {
        // 尝试使用wkhtmltoimage命令（如果系统上安装了）
        final result = await Process.run('wkhtmltoimage', [
          '--width',
          width.toString(),
          '--height',
          height.toString(),
          '--quality',
          '90',
          tempFile.path,
          pngFile.path,
        ]);

        if (result.exitCode == 0 && await pngFile.exists()) {
          final bytes = await pngFile.readAsBytes();

          // 清理临时文件
          await tempFile.delete();
          await pngFile.delete();

          debugPrint('HTML转图片成功 (使用系统命令): ${bytes.length} 字节');
          return bytes;
        }
      } catch (e) {
        debugPrint('系统命令转换失败，尝试使用备用方法: $e');
      }

      // 备用方法：创建一个简单的图像
      final recorder = ui.PictureRecorder();
      final canvas = Canvas(recorder);

      // 绘制白色背景
      final paint = Paint()..color = Colors.white;
      canvas.drawRect(Rect.fromLTWH(0, 0, width, height), paint);

      // 绘制一些文本
      final textPainter = TextPainter(
        text: TextSpan(
          text: 'Cannot render HTML content',
          style: const TextStyle(color: Colors.black, fontSize: 24),
        ),
        textDirection: TextDirection.ltr,
      );

      textPainter.layout(maxWidth: width - 40);
      textPainter.paint(canvas, Offset(20, 20));

      // 完成绘制并获取图像
      final picture = recorder.endRecording();
      final image = await picture.toImage(width.toInt(), height.toInt());

      // 获取图像数据
      final byteData = await image.toByteData(format: ui.ImageByteFormat.png);

      // 清理临时文件
      await tempFile.delete();
      if (await pngFile.exists()) {
        await pngFile.delete();
      }

      // 释放资源
      image.dispose();

      if (byteData != null) {
        debugPrint(
          'HTML转图片成功 (使用备用方法): ${byteData.buffer.asUint8List().length} 字节',
        );
        return byteData.buffer.asUint8List();
      }

      return null;
    } catch (e) {
      debugPrint('HTML转图片失败: $e');
      return null;
    }
  }

  /// 分享为图片
  Future<void> shareAsImage(HtmlDocument document) async {
    try {
      final imageData = await convertToImage(document);

      if (imageData != null) {
        // 保存图片到临时文件
        final tempDir = await getTemporaryDirectory();
        final tempFile = File(
          '${tempDir.path}/${document.title}_${DateTime.now().millisecondsSinceEpoch}.png',
        );

        await tempFile.writeAsBytes(imageData);

        // 分享文件
        await SharePlus.instance.share(
          ShareParams(files: [XFile(tempFile.path)], text: document.title),
        );
      }
    } catch (e) {
      debugPrint('分享HTML图片失败: $e');
    }
  }

  /// 保存为图片
  Future<File?> saveAsImage(HtmlDocument document, {String? outputPath}) async {
    try {
      final imageData = await convertToImage(document);

      if (imageData != null) {
        // 使用用户指定的路径或默认路径
        final String filePath;
        if (outputPath != null) {
          filePath =
              '$outputPath/${document.title}_${DateTime.now().millisecondsSinceEpoch}.png';
        } else {
          // 保存图片到文档目录
          final docDir = await getApplicationDocumentsDirectory();
          filePath =
              '${docDir.path}/${document.title}_${DateTime.now().millisecondsSinceEpoch}.png';
        }

        final imageFile = File(filePath);
        await imageFile.writeAsBytes(imageData);
        return imageFile;
      }

      return null;
    } catch (e) {
      debugPrint('保存HTML图片失败: $e');
      return null;
    }
  }

  /// 从Markdown内容中提取HTML代码
  List<String> extractHtmlFromMarkdown(String markdown) {
    final htmlRegex = RegExp(r'```html\s*([\s\S]*?)\s*```');
    final matches = htmlRegex.allMatches(markdown);

    return matches.map((match) => match.group(1) ?? '').toList();
  }
}
