/// 内容渲染数据模型
/// 用于存储内容的渲染结果（如图片、预览等）
class ContentRenderData {
  /// 渲染类型
  final RenderType renderType;

  /// 主要渲染结果（单张图片或主预览）
  final String? primaryImagePath;

  /// 多个渲染结果（用于分块模式或合集）
  final List<String> imagePaths;

  /// 缩略图路径
  final String? thumbnailPath;

  /// 原始内容数据
  final dynamic originalContent;

  /// 渲染配置信息
  final Map<String, dynamic> renderConfig;


  /// 创建时间
  final DateTime createdAt;

  /// 更新时间
  final DateTime updatedAt;

  ContentRenderData({
    required this.renderType,
    this.primaryImagePath,
    this.imagePaths = const [],
    this.thumbnailPath,
    this.originalContent,
    this.renderConfig = const {},
    DateTime? createdAt,
    DateTime? updatedAt,
  }) : createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();

  /// 是否有渲染结果
  bool get hasRenderResult => primaryImagePath != null || imagePaths.isNotEmpty;

  /// 是否是多图模式
  bool get isMultiImage => imagePaths.length > 1;

  /// 获取显示用的图片路径
  String? get displayImagePath =>
      primaryImagePath ?? (imagePaths.isNotEmpty ? imagePaths.first : null);

  /// 获取所有图片路径
  List<String> get allImagePaths {
    final paths = <String>[];
    if (primaryImagePath != null) paths.add(primaryImagePath!);
    paths.addAll(imagePaths);
    return paths.toSet().toList(); // 去重
  }

  /// 从JSON反序列化
  factory ContentRenderData.fromJson(Map<String, dynamic> json) {
    return ContentRenderData(
      renderType: RenderType.values.byName(json['renderType'] as String),
      primaryImagePath: json['primaryImagePath'] as String?,
      imagePaths: List<String>.from(json['imagePaths'] ?? []),
      thumbnailPath: json['thumbnailPath'] as String?,
      originalContent: json['originalContent'],
      renderConfig: Map<String, dynamic>.from(json['renderConfig'] ?? {}),
      createdAt:
          json['createdAt'] != null
              ? DateTime.parse(json['createdAt'] as String)
              : DateTime.now(),
      updatedAt:
          json['updatedAt'] != null
              ? DateTime.parse(json['updatedAt'] as String)
              : DateTime.now(),
    );
  }

  /// 序列化为JSON
  Map<String, dynamic> toJson() {
    return {
      'renderType': renderType.name,
      'primaryImagePath': primaryImagePath,
      'imagePaths': imagePaths,
      'thumbnailPath': thumbnailPath,
      'originalContent': originalContent,
      'renderConfig': renderConfig,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  /// 复制并修改
  ContentRenderData copyWith({
    RenderType? renderType,
    String? primaryImagePath,
    List<String>? imagePaths,
    String? thumbnailPath,
    dynamic originalContent,
    Map<String, dynamic>? renderConfig,
    DateTime? updatedAt,
  }) {
    return ContentRenderData(
      renderType: renderType ?? this.renderType,
      primaryImagePath: primaryImagePath ?? this.primaryImagePath,
      imagePaths: imagePaths ?? this.imagePaths,
      thumbnailPath: thumbnailPath ?? this.thumbnailPath,
      originalContent: originalContent ?? this.originalContent,
      renderConfig: renderConfig ?? this.renderConfig,
      createdAt: createdAt,
      updatedAt: updatedAt ?? DateTime.now(),
    );
  }

  @override
  String toString() {
    return 'ContentRenderData(renderType: $renderType, hasRender: $hasRenderResult, imageCount: ${allImagePaths.length})';
  }
}

/// 渲染类型枚举
enum RenderType {
  /// 单张图片渲染
  singleImage,

  /// 多张图片渲染（分块模式）
  multipleImages,

  /// 文本卡片渲染
  textCard,

  /// 文本卡片合集渲染
  textCardCollection,

  /// Markdown渲染
  markdown,

  /// Markdown分块渲染
  markdownBlocks,

  /// HTML渲染
  html,

  /// PDF预览
  pdfPreview,


  /// SVG预览
  svgPreview,
}

/// 渲染配置常量
class RenderConfig {
  static const Map<String, dynamic> defaultMarkdown = {
    'theme': 'default',
    'fontSize': 16,
    'lineHeight': 1.6,
    'padding': 20,
    'backgroundColor': '#ffffff',
    'textColor': '#333333',
  };

  static const Map<String, dynamic> defaultTextCard = {
    'template': 'modern',
    'backgroundColor': '#ffffff',
    'textColor': '#333333',
    'fontSize': 16,
    'padding': 24,
    'borderRadius': 12,
  };

  static const Map<String, dynamic> defaultBlocks = {
    'blockSpacing': 16,
    'showBlockTitles': false,
    'showBlockBorders': true,
    'maxBlockHeight': 800,
  };
}
