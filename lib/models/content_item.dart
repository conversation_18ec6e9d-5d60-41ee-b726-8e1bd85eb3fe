import 'dart:convert';

import 'package:uuid/uuid.dart';
import 'content_render_data.dart';

/// 内容类型枚举
enum ContentType {
  markdown,
  markdownBlocks, // 分块模式的Markdown
  textCard,
  textCardCollection, // 文本卡片合集
  image,
  svg,
  html,
  pdf,
}

/// 内容项模型
class ContentItem {
  /// 唯一ID
  final String id;

  /// 标题
  String title;

  /// 内容类型
  final ContentType type;

  /// 内容数据 (可以是字符串或者二进制数据)
  dynamic content;

  /// 缩略图网址（如果有的话）
  String? thumbnailUrl;

  /// 创建时间
  final DateTime createdAt;

  /// 更新时间
  DateTime updatedAt;

  /// 分类标签
  List<String> tags;

  /// 文件路径（如果是从文件加载的）
  String? filePath;

  /// 文件大小（如果适用）
  int? fileSize;

  /// 是否为收藏项
  bool isFavorite;

  /// 渲染数据（用于存储渲染结果）
  ContentRenderData? renderData;

  ContentItem({
    String? id,
    required this.title,
    required this.type,
    required this.content,
    this.thumbnailUrl,
    DateTime? createdAt,
    DateTime? updatedAt,
    List<String>? tags,
    this.filePath,
    this.fileSize,
    this.isFavorite = false,
    this.renderData,
  }) : id = id ?? const Uuid().v4(),
       createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now(),
       tags = tags ?? [];

  /// 从JSON反序列化
  factory ContentItem.fromJson(Map<String, dynamic> json) {
    return ContentItem(
      id: json['id'] as String,
      title: json['title'] as String,
      type: ContentType.values.byName(json['type'] as String),
      content: json['content'],
      thumbnailUrl: json['thumbnailUrl'] as String?,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
      tags: List<String>.from(json['tags'] ?? []),
      filePath: json['filePath'] as String?,
      fileSize: json['fileSize'] as int?,
      isFavorite: json['isFavorite'] as bool? ?? false,
      renderData:
          json['renderData'] != null
              ? ContentRenderData.fromJson(
                json['renderData'] as Map<String, dynamic>,
              )
              : null,
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'type': type.name,
      'content': content,
      'thumbnailUrl': thumbnailUrl,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'tags': tags,
      'filePath': filePath,
      'fileSize': fileSize,
      'isFavorite': isFavorite,
      'renderData': renderData?.toJson(),
    };
  }

  /// 更新内容
  ContentItem copyWith({
    String? title,
    ContentType? type,
    dynamic content,
    String? thumbnailUrl,
    List<String>? tags,
    String? filePath,
    int? fileSize,
    bool? isFavorite,
    ContentRenderData? renderData,
    DateTime? updatedAt,
  }) {
    return ContentItem(
      id: id,
      title: title ?? this.title,
      type: type ?? this.type,
      content: content ?? this.content,
      thumbnailUrl: thumbnailUrl ?? this.thumbnailUrl,
      createdAt: createdAt,
      updatedAt: updatedAt ?? DateTime.now(),
      tags: tags ?? List<String>.from(this.tags),
      filePath: filePath ?? this.filePath,
      fileSize: fileSize ?? this.fileSize,
      isFavorite: isFavorite ?? this.isFavorite,
      renderData: renderData ?? this.renderData,
    );
  }

  /// 获取内容预览文本（用于显示在列表中）
  String get previewText {
    if (type == ContentType.markdown || type == ContentType.html) {
      // Safely handle content conversion to String
      String contentStr;
      if (content is String) {
        contentStr = content as String;
      } else {
        contentStr = content?.toString() ?? '';
      }
      return contentStr.length > 100
          ? '${contentStr.substring(0, 100)}...'
          : contentStr;
    }
    return '不支持的预览';
  }

  /// 计算内容大小（如果是文本内容）
  int get contentSize {
    if (content is String) {
      return utf8.encode(content as String).length;
    }
    return fileSize ?? 0;
  }

  /// 格式化的文件大小
  String get formattedSize {
    final int size = contentSize;
    if (size < 1024) {
      return '$size B';
    } else if (size < 1024 * 1024) {
      return '${(size / 1024).toStringAsFixed(2)} KB';
    } else {
      return '${(size / (1024 * 1024)).toStringAsFixed(2)} MB';
    }
  }

  /// 内容的MIME类型
  String get mimeType {
    switch (type) {
      case ContentType.markdown:
      case ContentType.markdownBlocks:
        return 'text/markdown';
      case ContentType.textCard:
      case ContentType.textCardCollection:
        return 'application/json';
      case ContentType.image:
        return 'image/png'; // 默认为PNG，实际应该根据图像类型确定
      case ContentType.svg:
        return 'image/svg+xml';
      case ContentType.html:
        return 'text/html';
      case ContentType.pdf:
        return 'application/pdf';
    }
  }

  /// 内容的文件扩展名
  String get fileExtension {
    switch (type) {
      case ContentType.markdown:
      case ContentType.markdownBlocks:
        return 'md';
      case ContentType.textCard:
      case ContentType.textCardCollection:
        return 'json';
      case ContentType.image:
        return 'png'; // 默认为PNG，实际应该根据图像类型确定
      case ContentType.svg:
        return 'svg';
      case ContentType.html:
        return 'html';
      case ContentType.pdf:
        return 'pdf';
    }
  }

  /// 是否有渲染结果
  bool get hasRenderResult => renderData?.hasRenderResult ?? false;

  /// 是否是多图模式
  bool get isMultiImage => renderData?.isMultiImage ?? false;

  /// 获取显示用的图片路径
  String? get displayImagePath => renderData?.displayImagePath;

  /// 获取所有渲染图片路径
  List<String> get allRenderImagePaths => renderData?.allImagePaths ?? [];

  /// 是否应该优先显示渲染结果
  bool get shouldShowRenderResult {
    switch (type) {
      case ContentType.markdown:
      case ContentType.markdownBlocks:
      case ContentType.textCard:
      case ContentType.textCardCollection:
        return hasRenderResult;
      case ContentType.image:
      case ContentType.svg:
      case ContentType.pdf:
      case ContentType.html:
        return false; // 这些类型有自己的预览方式
    }
  }

  @override
  String toString() {
    return 'ContentItem{id: $id, title: $title, type: $type, hasRender: $hasRenderResult}';
  }
}
