import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../config/app_theme.dart';
import '../models/content_item.dart';
import '../text_cards/models/text_card_model.dart';
import '../text_cards/widgets/text_card_widget.dart';
import '../generated/l10n/app_localizations.dart';

/// 文本卡片详情页面
/// 显示文本卡片的渲染结果
class TextCardDetailPage extends StatefulWidget {
  final ContentItem item;

  const TextCardDetailPage({super.key, required this.item});

  @override
  State<TextCardDetailPage> createState() => _TextCardDetailPageState();
}

class _TextCardDetailPageState extends State<TextCardDetailPage> {
  List<TextCardModel> _cards = [];
  bool _isLoading = true;
  String _errorMessage = '';

  @override
  void initState() {
    super.initState();
    _loadTextCardData();
  }

  Future<void> _loadTextCardData() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = '';
      });

      // 解析文本卡片数据
      final cardData = _parseTextCardData(widget.item);

      if (widget.item.type == ContentType.textCard) {
        // 单个文本卡片
        final card = _createTextCardFromData(cardData);
        _cards = [card];
      } else if (widget.item.type == ContentType.textCardCollection) {
        // 文本卡片合集
        final cardsData = cardData['cards'] as List<dynamic>? ?? [];
        _cards =
            cardsData.map((data) => _createTextCardFromData(data)).toList();
      }

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = '加载文本卡片失败：$e';
      });
    }
  }

  Map<String, dynamic> _parseTextCardData(ContentItem item) {
    if (item.content is String) {
      return jsonDecode(item.content);
    } else if (item.content is Map<String, dynamic>) {
      return item.content;
    } else {
      throw Exception('无效的文本卡片数据格式');
    }
  }

  TextCardModel _createTextCardFromData(Map<String, dynamic> data) {
    return TextCardModel(
      id: data['id'] ?? '',
      title: data['title'] ?? '',
      content: data['content'] ?? '',
      templateId: data['templateId'] ?? 'default',
      createdAt: DateTime.tryParse(data['createdAt'] ?? '') ?? DateTime.now(),
      updatedAt: DateTime.tryParse(data['updatedAt'] ?? '') ?? DateTime.now(),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFFF8FAFC),
      body: CustomScrollView(
        slivers: [
          // 现代化的应用栏
          _buildSliverAppBar(),

          // 内容区域
          if (_isLoading)
            const SliverFillRemaining(
              child: Center(child: CircularProgressIndicator()),
            )
          else if (_errorMessage.isNotEmpty)
            SliverFillRemaining(child: _buildErrorView())
          else
            _buildContentSliver(),
        ],
      ),
    );
  }

  Widget _buildSliverAppBar() {
    return SliverAppBar(
      expandedHeight: 120,
      floating: false,
      pinned: true,
      elevation: 0,
      backgroundColor: AppTheme.primaryColor,
      systemOverlayStyle: SystemUiOverlayStyle.light,
      leading: IconButton(
        icon: const Icon(Icons.arrow_back, color: Colors.white),
        onPressed: () => Navigator.of(context).pop(),
      ),
      flexibleSpace: FlexibleSpaceBar(
        title: Text(
          widget.item.title,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 18,
            fontWeight: FontWeight.w600,
          ),
        ),
        background: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                AppTheme.primaryColor,
                AppTheme.primaryColor.withValues(alpha: 0.8),
              ],
            ),
          ),
        ),
      ),
      actions: [
        IconButton(
          icon: const Icon(Icons.share, color: Colors.white),
          onPressed: _shareTextCard,
        ),
        IconButton(
          icon: const Icon(Icons.download, color: Colors.white),
          onPressed: _exportTextCard,
        ),
      ],
    );
  }

  Widget _buildErrorView() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.error_outline, size: 80, color: Colors.red.shade300),
          const SizedBox(height: 16),
          Text(
            '加载失败',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.w600,
              color: Colors.grey.shade700,
            ),
          ),
          const SizedBox(height: 8),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 32),
            child: Text(
              _errorMessage,
              style: TextStyle(fontSize: 14, color: Colors.grey.shade600),
              textAlign: TextAlign.center,
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton(
            onPressed: _loadTextCardData,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.primaryColor,
              foregroundColor: Colors.white,
              padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            child: const Text('重试'),
          ),
        ],
      ),
    );
  }

  Widget _buildContentSliver() {
    if (_cards.length == 1) {
      // 单个文本卡片
      return SliverPadding(
        padding: const EdgeInsets.all(20),
        sliver: SliverToBoxAdapter(child: _buildSingleCard(_cards.first)),
      );
    } else {
      // 文本卡片合集
      return SliverPadding(
        padding: const EdgeInsets.all(20),
        sliver: SliverList(
          delegate: SliverChildBuilderDelegate((context, index) {
            return Padding(
              padding: const EdgeInsets.only(bottom: 20),
              child: _buildCardInCollection(_cards[index], index),
            );
          }, childCount: _cards.length),
        ),
      );
    }
  }

  Widget _buildSingleCard(TextCardModel card) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.08),
            offset: const Offset(0, 4),
            blurRadius: 20,
            spreadRadius: 0,
          ),
        ],
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(20),
        child: AspectRatio(
          aspectRatio: 3 / 4, // 卡片比例
          child: TextCardWidget(card: card),
        ),
      ),
    );
  }

  Widget _buildCardInCollection(TextCardModel card, int index) {
    final l10n = AppLocalizations.of(context);
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.06),
            offset: const Offset(0, 2),
            blurRadius: 12,
            spreadRadius: 0,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 卡片序号
          Container(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Container(
                  width: 32,
                  height: 32,
                  decoration: BoxDecoration(
                    color: AppTheme.primaryColor,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Center(
                    child: Text(
                      '${index + 1}',
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    card.title.isNotEmpty
                        ? card.title
                        : l10n.cardNumber(index + 1),
                    style: const TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Colors.black87,
                    ),
                  ),
                ),
              ],
            ),
          ),

          // 卡片内容
          ClipRRect(
            borderRadius: const BorderRadius.vertical(
              bottom: Radius.circular(16),
            ),
            child: AspectRatio(
              aspectRatio: 3 / 4,
              child: TextCardWidget(card: card),
            ),
          ),
        ],
      ),
    );
  }

  void _shareTextCard() {
    // TODO: 实现分享功能
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('分享功能开发中'),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _exportTextCard() {
    // TODO: 实现导出功能
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('导出功能开发中'),
        behavior: SnackBarBehavior.floating,
      ),
    );
  }
}
