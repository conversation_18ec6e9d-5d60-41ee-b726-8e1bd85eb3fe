import 'package:flutter/material.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:webview_flutter/webview_flutter.dart';

import '../config/app_theme.dart';
import '../models/content_item.dart';
import '../generated/l10n/app_localizations.dart';

/// 通用内容编辑器页面
class ContentEditorPage extends StatefulWidget {
  final String title;
  final String initialContent;
  final ContentType contentType;

  const ContentEditorPage({
    super.key,
    required this.title,
    required this.initialContent,
    required this.contentType,
  });

  @override
  State<ContentEditorPage> createState() => _ContentEditorPageState();
}

class _ContentEditorPageState extends State<ContentEditorPage> {
  late TextEditingController _textController;
  late String _previewContent;
  bool _showPreview = false;
  WebViewController? _webViewController;

  @override
  void initState() {
    super.initState();
    _textController = TextEditingController(text: widget.initialContent);
    _previewContent = widget.initialContent;

    if (widget.contentType == ContentType.html) {
      _initializeWebView();
    }
  }

  @override
  void dispose() {
    _textController.dispose();
    super.dispose();
  }

  void _initializeWebView() {
    _webViewController =
        WebViewController()
          ..setBackgroundColor(Colors.white)
          ..setJavaScriptMode(JavaScriptMode.unrestricted)
          ..loadHtmlString(_previewContent);
  }

  void _updatePreview() {
    setState(() {
      _previewContent = _textController.text;
      if (widget.contentType == ContentType.html &&
          _webViewController != null) {
        _webViewController!.loadHtmlString(_previewContent);
      }
    });
  }

  void _togglePreview() {
    setState(() {
      _showPreview = !_showPreview;
      if (_showPreview) {
        _updatePreview();
      }
    });
  }

  void _saveContent() {
    Navigator.pop(context, _textController.text);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.title),
        actions: [
          // 预览切换按钮
          IconButton(
            icon: Icon(_showPreview ? Icons.edit : Icons.preview),
            onPressed: _togglePreview,
            tooltip: _showPreview ? '编辑' : '预览',
          ),
          // 保存按钮
          IconButton(
            icon: const Icon(Icons.save),
            onPressed: _saveContent,
            tooltip: '保存',
          ),
        ],
      ),
      body: _showPreview ? _buildPreviewWidget() : _buildEditorWidget(),
    );
  }

  Widget _buildEditorWidget() {
    return Column(
      children: [
        // 工具栏
        _buildToolbar(),

        // 编辑器
        Expanded(
          child: Container(
            padding: const EdgeInsets.all(16),
            child: TextField(
              controller: _textController,
              maxLines: null,
              expands: true,
              autofocus: true,
              decoration: const InputDecoration(
                border: InputBorder.none,
                hintText: '输入内容...',
              ),
              style: const TextStyle(fontFamily: 'monospace', fontSize: 16),
              onChanged: (value) {
                // 实时更新预览内容，避免预览时再计算
                _previewContent = value;
              },
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildToolbar() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: Colors.grey.shade100,
        border: Border(bottom: BorderSide(color: Colors.grey.shade300)),
      ),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Row(children: _getToolbarButtons()),
      ),
    );
  }

  List<Widget> _getToolbarButtons() {
    final l10n = AppLocalizations.of(context);
    // 为不同类型的内容提供不同的工具栏按钮
    switch (widget.contentType) {
      case ContentType.markdown:
        return [
          _buildToolButton('B', l10n.bold),
          _buildToolButton('I', l10n.italic),
          _buildToolButton('H1', l10n.heading1),
          _buildToolButton('H2', l10n.heading2),
          _buildToolButton('H3', l10n.heading3),
          _buildToolButton('列表', l10n.list),
          _buildToolButton('链接', l10n.link),
          _buildToolButton('图片', l10n.image),
          _buildToolButton('代码', l10n.code),
          _buildToolButton('代码块', l10n.codeBlock),
          _buildToolButton('引用', l10n.quote),
          _buildToolButton('表格', l10n.table),
        ];

      case ContentType.html:
        return [
          _buildToolButton('段落', '<p>段落</p>'),
          _buildToolButton('标题', '<h1>标题</h1>'),
          _buildToolButton('粗体', '<strong>粗体</strong>'),
          _buildToolButton('斜体', '<em>斜体</em>'),
          _buildToolButton('链接', '<a href="URL">链接文本</a>'),
          _buildToolButton('图片', '<img src="图片URL" alt="图片描述">'),
          _buildToolButton('列表', '<ul>\n  <li>列表项</li>\n  <li>列表项</li>\n</ul>'),
          _buildToolButton(
            '表格',
            '<table>\n  <tr>\n    <th>列1</th>\n    <th>列2</th>\n  </tr>\n  <tr>\n    <td>内容1</td>\n    <td>内容2</td>\n  </tr>\n</table>',
          ),
          _buildToolButton('div', '<div>\n  内容\n</div>'),
          _buildToolButton('样式', '<style>\n  /* CSS样式 */\n</style>'),
          _buildToolButton('脚本', '<script>\n  // JavaScript代码\n</script>'),
          _buildToolButton('注释', '<!-- 注释 -->'),
        ];

      case ContentType.svg:
        return [
          _buildToolButton(
            'SVG',
            '<svg width="100" height="100" xmlns="http://www.w3.org/2000/svg"></svg>',
          ),
          _buildToolButton(
            '矩形',
            '<rect x="10" y="10" width="80" height="80" fill="blue" />',
          ),
          _buildToolButton(
            '圆形',
            '<circle cx="50" cy="50" r="40" fill="red" />',
          ),
          _buildToolButton(
            '椭圆',
            '<ellipse cx="50" cy="50" rx="40" ry="20" fill="green" />',
          ),
          _buildToolButton(
            '线条',
            '<line x1="10" y1="10" x2="90" y2="90" stroke="black" stroke-width="2" />',
          ),
          _buildToolButton(
            '多边形',
            '<polygon points="50,10 90,90 10,90" fill="purple" />',
          ),
          _buildToolButton('路径', '<path d="M10,30 L90,30" stroke="black" />'),
          _buildToolButton(
            '文本',
            '<text x="10" y="20" fill="black">SVG文本</text>',
          ),
          _buildToolButton('组', '<g>\n  <!-- 组内元素 -->\n</g>'),
          _buildToolButton('样式', '<style>\n  /* CSS样式 */\n</style>'),
        ];

      default:
        return [];
    }
  }

  Widget _buildToolButton(String label, String insertText) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 2),
      child: ElevatedButton(
        onPressed: () => _insertText(insertText),
        style: ElevatedButton.styleFrom(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 0),
          minimumSize: const Size(40, 36),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(4)),
          backgroundColor: AppTheme.primaryColor,
          foregroundColor: Colors.white,
        ),
        child: Text(label, style: const TextStyle(fontSize: 12)),
      ),
    );
  }

  void _insertText(String text) {
    final currentText = _textController.text;
    final selection = _textController.selection;

    // 如果有选中文本，则使用选中的文本替换插入文本中的占位符
    if (selection.start != selection.end) {
      final selectedText = currentText.substring(
        selection.start,
        selection.end,
      );

      // 根据内容类型处理不同的插入方式
      String finalText;
      final l10n = AppLocalizations.of(context);
      switch (widget.contentType) {
        case ContentType.markdown:
          if (text == l10n.bold) {
            finalText = '**$selectedText**';
          } else if (text == l10n.italic) {
            finalText = '*$selectedText*';
          } else if (text == l10n.code) {
            finalText = '`$selectedText`';
          } else if (text == l10n.codeBlock) {
            finalText = '```\n$selectedText\n```';
          } else if (text == l10n.quote) {
            finalText = '> $selectedText';
          } else if (text == l10n.link) {
            finalText = '[$selectedText](URL)';
          } else {
            finalText = text;
          }
          break;

        case ContentType.html:
          if (text == '<p>段落</p>') {
            finalText = '<p>$selectedText</p>';
          } else if (text == '<strong>粗体</strong>') {
            finalText = '<strong>$selectedText</strong>';
          } else if (text == '<em>斜体</em>') {
            finalText = '<em>$selectedText</em>';
          } else if (text == '<a href="URL">链接文本</a>') {
            finalText = '<a href="URL">$selectedText</a>';
          } else {
            finalText = text;
          }
          break;

        default:
          finalText = text;
          break;
      }

      final newText = currentText.replaceRange(
        selection.start,
        selection.end,
        finalText,
      );
      _textController.text = newText;
      _textController.selection = TextSelection.collapsed(
        offset: selection.start + finalText.length,
      );
    } else {
      // 没有选中文本，直接在光标位置插入
      final newText = currentText.replaceRange(
        selection.start,
        selection.end,
        text,
      );
      _textController.text = newText;
      _textController.selection = TextSelection.collapsed(
        offset: selection.start + text.length,
      );
    }
  }

  Widget _buildPreviewWidget() {
    switch (widget.contentType) {
      case ContentType.markdown:
        return Markdown(
          data: _previewContent,
          selectable: true,
          styleSheet: MarkdownStyleSheet(
            h1: const TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: AppTheme.textDarkColor,
            ),
            h2: const TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: AppTheme.textDarkColor,
            ),
            h3: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppTheme.textDarkColor,
            ),
            p: const TextStyle(
              fontSize: 16,
              height: 1.5,
              color: AppTheme.textDarkColor,
            ),
            blockquoteDecoration: BoxDecoration(
              color: Colors.grey.shade100,
              borderRadius: BorderRadius.circular(4),
              border: Border.all(color: Colors.grey.shade300),
            ),
            blockquote: TextStyle(
              fontSize: 16,
              fontStyle: FontStyle.italic,
              color: Colors.grey.shade700,
            ),
            code: TextStyle(
              fontSize: 14,
              backgroundColor: Colors.grey.shade200,
              fontFamily: 'monospace',
            ),
            codeblockDecoration: BoxDecoration(
              color: Colors.grey.shade100,
              borderRadius: BorderRadius.circular(4),
              border: Border.all(color: Colors.grey.shade300),
            ),
          ),
          padding: const EdgeInsets.all(16.0),
        );

      case ContentType.html:
        return _webViewController != null
            ? WebViewWidget(controller: _webViewController!)
            : const Center(child: Text('无法加载HTML预览'));

      case ContentType.svg:
        try {
          return Center(
            child: SvgPicture.string(
              _previewContent,
              placeholderBuilder:
                  (context) => const Center(child: CircularProgressIndicator()),
            ),
          );
        } catch (e) {
          return Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.error_outline, color: Colors.red, size: 60),
                const SizedBox(height: 16),
                Text(
                  'SVG 渲染错误: $e',
                  style: const TextStyle(color: Colors.red),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          );
        }

      default:
        return const Center(child: Text('不支持的内容类型'));
    }
  }
}
