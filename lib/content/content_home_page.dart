import 'dart:convert';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../generated/l10n/app_localizations.dart';
import '../html/html_manager_screen.dart';
import '../models/content_item.dart';
import '../services/service_locator.dart';
import '../svg/svg_manager_screen.dart';
import '../text_cards/widgets/modern_card_creator.dart';
import '../text_cards/widgets/smart_text_splitter.dart';
import '../text_cards/models/enhanced_card_template.dart';
import '../text_cards/widgets/card_preview_widget.dart';
import '../markdown/markdown_render_screen.dart';
import '../config/app_theme.dart';

/// 内容库首页
class ContentHomePage extends StatefulWidget {
  const ContentHomePage({super.key});

  @override
  State<ContentHomePage> createState() => _ContentHomePageState();
}

class _ContentHomePageState extends State<ContentHomePage> {
  final _contentService = ServiceLocator().contentService;

  bool _isLoading = true;
  List<ContentItem> _allItems = [];
  List<ContentItem> _filteredItems = [];
  String _search = '';
  ContentType? _filterType; // null 为全部
  bool _onlyFavorites = false;
  String? _selectedTag;

  @override
  void initState() {
    super.initState();
    _loadItems();
  }

  Future<void> _loadItems() async {
    setState(() => _isLoading = true);
    // 确保服务初始化
    await ServiceLocator().initServices();

    final items = _contentService.getAllItems();
    setState(() {
      _allItems = items;
      _applyFilter();
      _isLoading = false;
    });
    _ensureMarkdownRenders();
  }

  void _applyFilter() {
    Iterable<ContentItem> data = _allItems;
    if (_filterType != null) {
      data = data.where((e) => e.type == _filterType);
    }
    if (_onlyFavorites) {
      data = data.where((e) => e.isFavorite);
    }
    if (_selectedTag != null && _selectedTag!.isNotEmpty) {
      final tag = _selectedTag!.toLowerCase();
      data = data.where((e) => e.tags.any((t) => t.toLowerCase() == tag));
    }
    if (_search.trim().isNotEmpty) {
      final q = _search.trim().toLowerCase();
      data = data.where((e) {
        final haystack = _extractSearchableText(e).toLowerCase();
        final inTitle = e.title.toLowerCase().contains(q);
        final inTags = e.tags.any((t) => t.toLowerCase().contains(q));
        return inTitle || inTags || haystack.contains(q);
      });
    }
    _filteredItems = data.toList();
  }

  /// 为不同类型内容提取可搜索文本
  String _extractSearchableText(ContentItem item) {
    try {
      switch (item.type) {
        case ContentType.markdown:
        case ContentType.markdownBlocks:
          final raw = (item.content is String) ? item.content as String : '';
          return _extractMarkdown(raw);
        case ContentType.html:
          final raw = (item.content is String) ? item.content as String : '';
          return _stripHtml(raw);
        case ContentType.textCard:
          final data = _parseTextCardData(item);
          return data?.text ?? '';
        case ContentType.textCardCollection:
          final params = _parseTextCardCollection(item);
          return params.cards.join('\n');
        case ContentType.svg:
        case ContentType.image:
        case ContentType.pdf:
          return (item.content is String) ? item.content as String : '';
      }
    } catch (_) {
      return (item.content is String) ? item.content as String : '';
    }
  }

  String _stripHtml(String html) {
    return html
        .replaceAll(
          RegExp(
            r'<script[\s\S]*?</script>',
            multiLine: true,
            caseSensitive: false,
          ),
          '',
        )
        .replaceAll(
          RegExp(
            r'<style[\s\S]*?</style>',
            multiLine: true,
            caseSensitive: false,
          ),
          '',
        )
        .replaceAll(RegExp(r'<[^>]+>'), ' ')
        .replaceAll('&nbsp;', ' ')
        .replaceAll('&amp;', '&')
        .replaceAll('&lt;', '<')
        .replaceAll('&gt;', '>')
        .trim();
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    final isDark = Theme.of(context).brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: isDark ? AppTheme.darkBgColor : AppTheme.bgLightColor,
      appBar: AppBar(
        title: Text(
          l10n.contentLibrary,
          style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 20),
        ),
        centerTitle: false,
        elevation: 0,
        backgroundColor: Theme.of(context).colorScheme.surface.withValues(alpha: 0.95),
        surfaceTintColor: Colors.transparent,
        scrolledUnderElevation: 0.5,
        flexibleSpace: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                Theme.of(context).colorScheme.primary.withValues(alpha: 0.08),
                Theme.of(context).colorScheme.primary.withValues(alpha: 0.03),
              ],
            ),
          ),
        ),
        foregroundColor:
            isDark ? AppTheme.darkTextColor : AppTheme.textDarkColor,
        actions: [
          IconButton(
            tooltip: l10n.search,
            onPressed: () async {
              final text = await showSearch<String?>(
                context: context,
                delegate: _ContentSearchDelegate(initialQuery: _search),
              );
              if (text != null) {
                setState(() {
                  _search = text;
                  _applyFilter();
                });
              }
            },
            icon: const Icon(Icons.search),
          ),
        ],
      ),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : Column(
                children: [
                  _buildFilters(context),
                  const SizedBox(height: 8),
                  Expanded(
                    child:
                        _filteredItems.isEmpty
                            ? _buildEmptyState(context)
                            : Padding(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 12,
                              ),
                              child: GridView.builder(
                                gridDelegate:
                                    const SliverGridDelegateWithFixedCrossAxisCount(
                                      crossAxisCount: 2,
                                      mainAxisSpacing: 12,
                                      crossAxisSpacing: 12,
                                      childAspectRatio: 3 / 4,
                                    ),
                                itemCount: _filteredItems.length,
                                itemBuilder: (context, index) {
                                  final item = _filteredItems[index];
                                  return _buildItemCard(context, item);
                                },
                              ),
                            ),
                  ),
                ],
              ),
    );
  }

  bool get _hasActiveFilters {
    return _filterType != null ||
        _onlyFavorites ||
        (_selectedTag?.isNotEmpty ?? false) ||
        _search.trim().isNotEmpty;
  }

  void _clearFilters() {
    setState(() {
      _filterType = null;
      _onlyFavorites = false;
      _selectedTag = null;
      _search = '';
      _applyFilter();
    });
  }

  Widget _buildEmptyState(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final hasAny = _allItems.isNotEmpty;
    final title = hasAny && _hasActiveFilters ? '没有匹配的内容' : '内容库为空';
    final subtitle =
        hasAny && _hasActiveFilters
            ? '尝试清除筛选条件或更换搜索关键词'
            : '从下方工具开始创建，或在各工具中点击“保存到内容库”';

    return Center(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 24),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 84,
              height: 84,
              decoration: BoxDecoration(
                color: (isDark ? Colors.white12 : const Color(0xFFEFF6FF)),
                shape: BoxShape.circle,
              ),
              child: Icon(
                Icons.inbox_outlined,
                size: 40,
                color: isDark ? Colors.white70 : const Color(0xFF3B82F6),
              ),
            ),
            const SizedBox(height: 16),
            Text(
              title,
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w700,
                color: isDark ? AppTheme.darkTextColor : AppTheme.textDarkColor,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              subtitle,
              textAlign: TextAlign.center,
              style: TextStyle(
                fontSize: 13,
                color: isDark ? Colors.white70 : Colors.black54,
                height: 1.4,
              ),
            ),
            const SizedBox(height: 20),
            if (_hasActiveFilters && hasAny)
              Wrap(
                spacing: 10,
                runSpacing: 10,
                alignment: WrapAlignment.center,
                children: [
                  OutlinedButton.icon(
                    onPressed: _clearFilters,
                    icon: const Icon(Icons.filter_alt_off),
                    label: const Text('清除筛选'),
                  ),
                  OutlinedButton.icon(
                    onPressed: _loadItems,
                    icon: const Icon(Icons.refresh),
                    label: const Text('刷新'),
                  ),
                ],
              )
            else
              Wrap(
                spacing: 10,
                runSpacing: 10,
                alignment: WrapAlignment.center,
                children: [
                  OutlinedButton.icon(
                    onPressed: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (_) => const MarkdownRenderScreen(),
                        ),
                      ).then((_) => _loadItems());
                    },
                    icon: const Icon(Icons.text_snippet_outlined),
                    label: const Text('Markdown'),
                  ),
                  OutlinedButton.icon(
                    onPressed: () {
                      showModalBottomSheet(
                        context: context,
                        isScrollControlled: true,
                        backgroundColor: Colors.transparent,
                        builder:
                            (_) => ModernCardCreator(
                              onCardCreated: (content, template) {},
                            ),
                      ).then((_) => _loadItems());
                    },
                    icon: const Icon(Icons.style_outlined),
                    label: const Text('文本卡片'),
                  ),
                  OutlinedButton.icon(
                    onPressed: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (_) => const SvgManagerScreen(),
                        ),
                      ).then((_) => _loadItems());
                    },
                    icon: const Icon(Icons.image_outlined),
                    label: const Text('SVG'),
                  ),
                  OutlinedButton.icon(
                    onPressed: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (_) => const HtmlManagerScreen(),
                        ),
                      ).then((_) => _loadItems());
                    },
                    icon: const Icon(Icons.html),
                    label: const Text('HTML'),
                  ),
                ],
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildFilters(BuildContext context) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final chipBg = isDark ? AppTheme.darkBgLightColor : Colors.white;
    final chipBorder =
        isDark ? AppTheme.darkBorderColor : const Color(0xFFE2E8F0);

    Widget chip(String label, ContentType? type) {
      final selected = _filterType == type;
      return ChoiceChip(
        label: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              type == null
                  ? Icons.filter_alt
                  : type == ContentType.markdown
                  ? Icons.text_snippet_outlined
                  : type == ContentType.markdownBlocks
                  ? Icons.view_module
                  : type == ContentType.textCard
                  ? Icons.style_outlined
                  : type == ContentType.textCardCollection
                  ? Icons.collections_bookmark_outlined
                  : type == ContentType.svg
                  ? Icons.image_outlined
                  : type == ContentType.html
                  ? Icons.html
                  : Icons.description_outlined,
              size: 14,
              color:
                  selected
                      ? Theme.of(context).colorScheme.primary
                      : (isDark
                          ? AppTheme.darkTextColor
                          : AppTheme.textDarkColor),
            ),
            const SizedBox(width: 4),
            Text(label),
          ],
        ),
        selected: selected,
        onSelected:
            (_) => setState(() {
              _filterType = type;
              _applyFilter();
            }),
        backgroundColor: chipBg,
        selectedColor: Theme.of(
          context,
        ).colorScheme.primary.withValues(alpha: 0.12),
        labelStyle: TextStyle(
          color:
              selected
                  ? Theme.of(context).colorScheme.primary
                  : (isDark ? AppTheme.darkTextColor : AppTheme.textDarkColor),
          fontWeight: selected ? FontWeight.w600 : FontWeight.w400,
        ),
        side: BorderSide(color: chipBorder, width: 1),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(10)),
      );
    }

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 8),
      decoration: BoxDecoration(
        color: isDark ? AppTheme.darkBgLightColor : Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isDark ? AppTheme.darkBorderColor : const Color(0xFFE5E7EB),
          width: 0.8,
        ),
        boxShadow: [
          BoxShadow(
            color:
                isDark
                    ? Colors.black.withValues(alpha: 0.15)
                    : Colors.black.withValues(alpha: 0.04),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: SingleChildScrollView(
        scrollDirection: Axis.horizontal,
        child: Row(
          children: [
            chip(_labelForFilter(context, null), null),
            const SizedBox(width: 8),
            chip(
              _labelForFilter(context, ContentType.markdown),
              ContentType.markdown,
            ),
            const SizedBox(width: 8),
            chip(
              _labelForFilter(context, ContentType.markdownBlocks),
              ContentType.markdownBlocks,
            ),
            const SizedBox(width: 8),
            chip(
              _labelForFilter(context, ContentType.textCard),
              ContentType.textCard,
            ),
            const SizedBox(width: 8),
            chip(
              _labelForFilter(context, ContentType.textCardCollection),
              ContentType.textCardCollection,
            ),
            const SizedBox(width: 8),
            chip(_labelForFilter(context, ContentType.svg), ContentType.svg),
            const SizedBox(width: 8),
            chip(_labelForFilter(context, ContentType.html), ContentType.html),
          ],
        ),
      ),
    );
  }

  // 顶部筛选标签多语言

  String _labelForFilter(BuildContext context, ContentType? type) {
    final l10n = AppLocalizations.of(context);
    if (type == null) {
      return l10n.textCardsAllCategories;
    }
    switch (type) {
      case ContentType.markdown:
        return l10n.markdown;
      case ContentType.markdownBlocks:
        return l10n.blockMarkdown;
      case ContentType.textCard:
        return l10n.textCards;
      case ContentType.textCardCollection:
        return l10n.cardCollection;
      case ContentType.svg:
        return l10n.svg;
      case ContentType.html:
        return l10n.html;
      case ContentType.image:
        return 'Image';
      case ContentType.pdf:
        return 'PDF';
    }
  }

  Widget _buildItemCard(BuildContext context, ContentItem item) {
    final isDark = Theme.of(context).brightness == Brightness.dark;
    final titleStyle = TextStyle(
      fontSize: 14,
      fontWeight: FontWeight.w600,
      color: isDark ? AppTheme.darkTextColor : AppTheme.textDarkColor,
    );
    final subStyle = TextStyle(
      fontSize: 11,
      color: isDark ? Colors.white70 : Colors.black54,
    );

    return InkWell(
      onTap: () => _openItem(context, item),
      onLongPress: () => _showItemActions(item),
      borderRadius: BorderRadius.circular(16),
      child: Container(
        decoration: BoxDecoration(
          color: isDark ? AppTheme.darkBgLightColor : AppTheme.bgWhiteColor,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color:
                  isDark
                      ? Colors.black.withValues(alpha: 0.3)
                      : Colors.black.withValues(alpha: 0.06),
              blurRadius: 14,
              offset: const Offset(0, 4),
            ),
          ],
          border: Border.all(
            color: isDark ? AppTheme.darkBorderColor : AppTheme.borderColor,
            width: 0.8,
          ),
        ),
        clipBehavior: Clip.antiAlias,
        child: Column(
          children: [
            Expanded(
              child: Padding(
                padding: const EdgeInsets.all(10),
                child: _buildItemPreview(context, item),
              ),
            ),
            Container(
              width: double.infinity,
              padding: const EdgeInsets.fromLTRB(12, 8, 12, 10),
              decoration: BoxDecoration(
                color: isDark ? AppTheme.darkBgLightColor : Colors.white,
                border: Border(
                  top: BorderSide(
                    color:
                        isDark
                            ? AppTheme.darkBorderColor
                            : AppTheme.borderColor,
                    width: 0.6,
                  ),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    item.title,
                    maxLines: 1,
                    overflow: TextOverflow.ellipsis,
                    style: titleStyle,
                  ),
                  const SizedBox(height: 2),
                  Row(
                    children: [
                      _TypeBadge(type: item.type),
                      const SizedBox(width: 6),
                      Expanded(
                        child: Text(
                          _formatDate(item.updatedAt),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                          style: subStyle,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _formatDate(DateTime dt) {
    return '${dt.year}-${dt.month.toString().padLeft(2, '0')}-${dt.day.toString().padLeft(2, '0')}';
  }

  Widget _buildItemPreview(BuildContext context, ContentItem item) {
    switch (item.type) {
      case ContentType.svg:
        if (item.content is String) {
          final path = item.content as String;
          return SvgPicture.file(
            File(path),
            fit: BoxFit.contain,
            placeholderBuilder:
                (_) => const Center(
                  child: CircularProgressIndicator(strokeWidth: 2),
                ),
          );
        }
        return const Center(child: Icon(Icons.image_outlined, size: 48));

      case ContentType.html:
        // 优先使用渲染缩略
        final thumb =
            item.renderData?.thumbnailPath ?? item.renderData?.displayImagePath;
        if (thumb != null && thumb.isNotEmpty && File(thumb).existsSync()) {
          return Image.file(File(thumb), fit: BoxFit.cover);
        }
        // 回退：轻量文本摘要
        return _HtmlThumb(
          html: (item.content is String) ? item.content as String : '',
        );

      case ContentType.textCard:
        final data = _parseTextCardData(item);
        if (data != null) {
          return AspectRatio(
            aspectRatio: 4 / 5,
            child: CardPreviewWidget(
              content: data.text,
              template: data.template,
              customStyles: data.customStyles,
            ),
          );
        }
        return const Center(child: Icon(Icons.style_outlined, size: 42));

      case ContentType.textCardCollection:
        return _CollectionThumb(count: _extractCollectionCount(item));

      case ContentType.markdown:
        final thumb =
            item.renderData?.thumbnailPath ?? item.renderData?.displayImagePath;
        if (thumb != null && thumb.isNotEmpty && File(thumb).existsSync()) {
          return Image.file(File(thumb), fit: BoxFit.cover);
        }
        final raw = (item.content is String) ? item.content as String : '';
        final md = _extractMarkdown(raw);
        return _MarkdownThumb(text: md);
      case ContentType.markdownBlocks:
        return _CollectionThumb(count: _estimateMarkdownBlocksCount(item));

      default:
        return const SizedBox.shrink();
    }
  }

  Future<void> _openItem(BuildContext context, ContentItem item) async {
    switch (item.type) {
      case ContentType.svg:
        if (item.content is String) {
          final file = File(item.content as String);
          final content = await file.readAsString().catchError((_) => '');
          if (!mounted) return;
          Navigator.push(
            context,
            MaterialPageRoute(
              builder:
                  (_) => SvgManagerScreen(
                    initialSvgContent: content.isNotEmpty ? content : null,
                    initialTitle: item.title,
                  ),
            ),
          ).then((_) => _loadItems());
        }
        break;

      case ContentType.html:
        Navigator.push(
          context,
          MaterialPageRoute(
            builder:
                (_) => HtmlManagerScreen(
                  initialHtmlContent:
                      (item.content is String) ? item.content as String : null,
                  initialTitle: item.title,
                ),
          ),
        ).then((_) => _loadItems());
        break;

      case ContentType.textCard:
        final data = _parseTextCardData(item);
        if (data != null) {
          showModalBottomSheet(
            context: context,
            isScrollControlled: true,
            backgroundColor: Colors.transparent,
            builder:
                (_) => ModernCardCreator(
                  initialTemplate: data.template,
                  initialText: data.text,
                  initialCustomStyles: data.customStyles,
                  initialStep: 2, // 直接进入预览
                  onCardCreated: (content, template) {
                    // 预览入口，不强制保存；用户点击“保存到内容库”时在创建器里处理
                    Navigator.of(context).maybePop();
                  },
                ),
          ).then((_) => _loadItems());
        }
        break;

      case ContentType.textCardCollection:
        // 解析合集内容，恢复模板与列表，直达预览
        final params = _parseTextCardCollection(item);
        showModalBottomSheet(
          context: context,
          isScrollControlled: true,
          backgroundColor: Colors.transparent,
          builder:
              (_) => SmartTextSplitter(
                onBatchCreate: (_, __, ___) {},
                initialTemplate: params.template,
                initialContents: params.cards,
                initialConfig: null,
                initialFixedPreviewHeight: params.fixedPreviewHeight,
                initialStep: 4, // 直接到预览
              ),
        ).then((_) => _loadItems());
        break;

      case ContentType.markdown:
      case ContentType.markdownBlocks:
        // 支持从JSON读取模板/样式/水印
        final mdParams = _parseMarkdownParams(item);
        Navigator.push(
          context,
          MaterialPageRoute(
            builder:
                (_) => MarkdownRenderScreen(
                  initialMarkdown: mdParams.markdown,
                  initialTemplateId: mdParams.templateId,
                  initialStyleJson: mdParams.style,
                  initialWatermarkJson: mdParams.watermark,
                ),
          ),
        ).then((_) => _loadItems());
        break;

      default:
        break;
    }
  }

  Future<void> _showItemActions(ContentItem item) async {
    final isFav = item.isFavorite;
    await showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (ctx) {
        final isDark = Theme.of(ctx).brightness == Brightness.dark;
        return Container(
          decoration: BoxDecoration(
            color: isDark ? AppTheme.darkBgLightColor : Colors.white,
            borderRadius: const BorderRadius.vertical(top: Radius.circular(16)),
          ),
          padding: const EdgeInsets.symmetric(vertical: 8),
          child: SafeArea(
            top: false,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                ListTile(
                  leading: Icon(
                    isFav ? Icons.favorite : Icons.favorite_border,
                    color: isFav ? Colors.red : null,
                  ),
                  title: Text(isFav ? '取消收藏' : '标记为收藏'),
                  onTap: () async {
                    Navigator.pop(ctx);
                    await _toggleFavorite(item);
                  },
                ),
                ListTile(
                  leading: const Icon(Icons.drive_file_rename_outline),
                  title: const Text('重命名'),
                  onTap: () async {
                    Navigator.pop(ctx);
                    await _renameItem(item);
                  },
                ),
                if (item.type == ContentType.markdown ||
                    item.type == ContentType.markdownBlocks ||
                    item.type == ContentType.textCard ||
                    item.type == ContentType.textCardCollection)
                  ListTile(
                    leading: const Icon(Icons.cached),
                    title: const Text('重新生成预览'),
                    onTap: () async {
                      Navigator.pop(ctx);
                      await _regenerateRender(item);
                    },
                  ),
                ListTile(
                  leading: const Icon(Icons.delete_outline, color: Colors.red),
                  title: const Text('删除', style: TextStyle(color: Colors.red)),
                  onTap: () async {
                    Navigator.pop(ctx);
                    await _deleteItem(item);
                  },
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  Future<void> _toggleFavorite(ContentItem item) async {
    await _contentService.toggleFavorite(item.id);
    await _loadItems();
  }

  Future<void> _renameItem(ContentItem item) async {
    final controller = TextEditingController(text: item.title);
    final ok = await showDialog<bool>(
      context: context,
      builder:
          (ctx) => AlertDialog(
            title: const Text('重命名'),
            content: TextField(
              controller: controller,
              decoration: const InputDecoration(hintText: '输入新的名称'),
              autofocus: true,
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(ctx, false),
                child: const Text('取消'),
              ),
              FilledButton(
                onPressed: () => Navigator.pop(ctx, true),
                child: const Text('确定'),
              ),
            ],
          ),
    );
    if (ok == true) {
      final newTitle = controller.text.trim();
      if (newTitle.isNotEmpty) {
        await _contentService.updateItem(item.copyWith(title: newTitle));
        await _loadItems();
      }
    }
  }

  Future<void> _deleteItem(ContentItem item) async {
    final ok = await showDialog<bool>(
      context: context,
      builder:
          (ctx) => AlertDialog(
            title: const Text('删除内容'),
            content: Text('确定删除“${item.title}”吗？此操作不可撤销。'),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(ctx, false),
                child: const Text('取消'),
              ),
              FilledButton(
                style: FilledButton.styleFrom(backgroundColor: Colors.red),
                onPressed: () => Navigator.pop(ctx, true),
                child: const Text('删除'),
              ),
            ],
          ),
    );
    if (ok == true) {
      await _contentService.deleteItem(item.id);
      await _loadItems();
    }
  }

  Future<void> _regenerateRender(ContentItem item) async {
    await _contentService.reRenderItem(
      item.id,
      isBlockMode:
          item.type == ContentType.markdownBlocks ||
          item.type == ContentType.textCardCollection,
    );
    await _loadItems();
  }

  Future<void> _ensureMarkdownRenders() async {
    // 对无缩略图的 markdown/markdownBlocks 条目触发异步渲染
    final targets = _allItems.where(
      (e) =>
          (e.type == ContentType.markdown ||
              e.type == ContentType.markdownBlocks) &&
          (e.renderData == null || e.renderData?.thumbnailPath == null),
    );
    for (final it in targets) {
      try {
        await _contentService.generateRenderResult(
          it,
          isBlockMode: it.type == ContentType.markdownBlocks,
        );
      } catch (_) {}
    }
    if (mounted) {
      setState(() {});
    }
  }

  // ====== 辅助：解析文本卡片 ======
  _ParsedCardData? _parseTextCardData(ContentItem item) {
    try {
      // 两种可能：
      // 1) content 直接是存有 text/template/customStyles 的 JSON
      // 2) 历史版本：type=markdown 且 tags 含 'text_card'
      final raw = (item.content is String) ? item.content as String : '';
      if (raw.isEmpty) return null;
      final map = jsonDecode(raw);

      // 兼容不同包裹结构
      final contentMap =
          (map is Map<String, dynamic> && map['content'] is Map)
              ? Map<String, dynamic>.from(map['content'] as Map)
              : Map<String, dynamic>.from(map as Map<String, dynamic>);

      final text = (contentMap['text'] as String?) ?? '';
      final templateMap = Map<String, dynamic>.from(
        contentMap['template'] as Map<String, dynamic>,
      );
      final template =
          _templateFromJson(templateMap) ??
          EnhancedCardTemplate.getTemplateById('classic_style')!;
      final customStyles = Map<String, dynamic>.from(
        (contentMap['customStyles'] as Map?) ?? {},
      );

      return _ParsedCardData(
        text: text,
        template: template,
        customStyles: customStyles,
      );
    } catch (_) {
      return null;
    }
  }

  int _extractCollectionCount(ContentItem item) {
    try {
      final raw = (item.content is String) ? item.content as String : '';
      if (raw.isEmpty) return 0;
      final map = jsonDecode(raw);
      if (map is Map<String, dynamic> && map['cards'] is List) {
        return (map['cards'] as List).length;
      }
      if (map is Map<String, dynamic> && map['content'] is Map) {
        final contentMap = Map<String, dynamic>.from(map['content'] as Map);
        if (contentMap['cards'] is List) {
          return (contentMap['cards'] as List).length;
        }
      }
      return 0;
    } catch (_) {
      return 0;
    }
  }

  _CollectionParams _parseTextCardCollection(ContentItem item) {
    try {
      final raw = (item.content is String) ? item.content as String : '';
      if (raw.isEmpty) return const _CollectionParams(cards: []);
      final map = jsonDecode(raw);
      Map<String, dynamic> root;
      if (map is Map<String, dynamic> && map['content'] is Map) {
        root = Map<String, dynamic>.from(map['content'] as Map);
      } else {
        root = Map<String, dynamic>.from(map as Map<String, dynamic>);
      }
      final List cards = (root['cards'] as List?) ?? [];

      // 1) 优先读取 root 层模板
      Map<String, dynamic>? templateMap =
          root['template'] is Map<String, dynamic>
              ? Map<String, dynamic>.from(root['template'] as Map)
              : null;

      // 2) 如果 root 无模板，尝试从第一张卡片中提取模板（新结构）
      if (templateMap == null) {
        for (final c in cards) {
          if (c is Map<String, dynamic>) {
            final contentMap = c['content'];
            if (contentMap is Map && contentMap['template'] is Map) {
              templateMap = Map<String, dynamic>.from(
                contentMap['template'] as Map,
              );
              break;
            }
          }
        }
      }

      final template =
          templateMap != null ? _templateFromJson(templateMap) : null;

      // 3) 提取文本：兼容旧结构与新结构
      final contents = <String>[];
      for (final c in cards) {
        if (c is Map<String, dynamic>) {
          // 新结构：{ type: 'text_card', content: { text: '...', ... } }
          final contentField = c['content'];
          if (contentField is Map<String, dynamic>) {
            final text = (contentField['text'] as String?)?.trim() ?? '';
            if (text.isNotEmpty) {
              contents.add(text);
              continue;
            }
          }

          // 旧结构：{ title: '...', content: '...' }
          final title = (c['title'] as String?) ?? '';
          final body = (c['content'] as String?) ?? '';
          final text = title.trim().isEmpty ? body : '$title\n$body';
          if (text.trim().isNotEmpty) contents.add(text);
        }
      }

      // 4) 读取合集预览高度配置（保存在 meta.fixedPreviewHeight）
      bool? fixedPreviewHeight;
      try {
        final meta = root['meta'];
        if (meta is Map<String, dynamic> &&
            meta['fixedPreviewHeight'] != null) {
          final v = meta['fixedPreviewHeight'];
          if (v is bool) fixedPreviewHeight = v;
        }
      } catch (_) {}

      return _CollectionParams(
        cards: contents,
        template: template,
        fixedPreviewHeight: fixedPreviewHeight,
      );
    } catch (_) {
      return const _CollectionParams(cards: []);
    }
  }

  EnhancedCardTemplate? _templateFromJson(Map<String, dynamic> json) {
    try {
      // 尝试按ID匹配内置模板
      final id = json['id'] as String?;
      if (id != null) {
        final builtIn = EnhancedCardTemplate.getTemplateById(id);
        if (builtIn != null) return builtIn;
      }
    } catch (_) {}

    try {
      // 回退：从颜色和数值还原一个临时模板实例（尽量贴近保存时样式）
      Color colorFromInt(int v) => Color(v);
      LinearGradient gradientFromMap(Map<String, dynamic> g) {
        final colors =
            ((g['colors'] as List?) ?? [])
                .cast<int>()
                .map(colorFromInt)
                .toList();
        return LinearGradient(
          colors: colors.isNotEmpty ? colors : [Colors.white, Colors.white],
        );
      }

      final bg =
          json['backgroundGradient'] is Map<String, dynamic>
              ? gradientFromMap(
                json['backgroundGradient'] as Map<String, dynamic>,
              )
              : const LinearGradient(colors: [Colors.white, Colors.white]);

      return EnhancedCardTemplate(
        id: json['id'] ?? 'custom',
        name: json['name'] ?? '自定义模板',
        category: json['category'] ?? 'custom',
        description: json['description'] ?? '从内容库还原',
        backgroundGradient: bg,
        textColor: colorFromInt(json['textColor'] ?? 0xFF000000),
        titleColor: colorFromInt(json['titleColor'] ?? 0xFF000000),
        accentColor: colorFromInt(json['accentColor'] ?? 0xFF2196F3),
        borderRadius: (json['borderRadius'] as num?)?.toDouble() ?? 16,
        padding: (json['padding'] as num?)?.toDouble() ?? 20,
        fontFamily: json['fontFamily'] ?? 'System',
        titleFontSize: (json['titleFontSize'] as num?)?.toDouble() ?? 20,
        contentFontSize: (json['contentFontSize'] as num?)?.toDouble() ?? 14,
        titleFontWeight: _weightFromIndex(json['titleFontWeight'] ?? 6),
        contentFontWeight: _weightFromIndex(json['contentFontWeight'] ?? 4),
        lineHeight: (json['lineHeight'] as num?)?.toDouble() ?? 1.5,
        textAlign:
            TextAlign.values[(json['textAlign'] as int?) ??
                TextAlign.left.index],
        hasShadow: (json['hasShadow'] as bool?) ?? true,
        hasDecorationElements:
            (json['hasDecorationElements'] as bool?) ?? false,
        styles: const {},
      );
    } catch (_) {
      return null;
    }
  }

  FontWeight _weightFromIndex(int idx) {
    // 简化映射：0..8 映射到 w100..w900
    final weights = <FontWeight>[
      FontWeight.w100,
      FontWeight.w200,
      FontWeight.w300,
      FontWeight.w400,
      FontWeight.w500,
      FontWeight.w600,
      FontWeight.w700,
      FontWeight.w800,
      FontWeight.w900,
    ];
    return weights[(idx).clamp(0, weights.length - 1)];
  }
}

class _MarkdownParams {
  final String? markdown;
  final String? templateId;
  final Map<String, dynamic>? style;
  final Map<String, dynamic>? watermark;
  const _MarkdownParams({
    this.markdown,
    this.templateId,
    this.style,
    this.watermark,
  });
}

_MarkdownParams _parseMarkdownParams(ContentItem item) {
  try {
    if (item.content is String) {
      final raw = item.content as String;
      // 兼容：纯markdown 或 保存为 JSON 的结构
      if (raw.trim().startsWith('{')) {
        final map = jsonDecode(raw) as Map<String, dynamic>;
        final type = map['type'];
        if (type == 'markdown' || type == 'markdownBlocks') {
          return _MarkdownParams(
            markdown: map['content'] as String?,
            templateId: map['templateId'] as String?,
            style: map['style'] as Map<String, dynamic>?,
            watermark: map['watermark'] as Map<String, dynamic>?,
          );
        }
      }
      return _MarkdownParams(markdown: raw);
    }
  } catch (_) {}
  return const _MarkdownParams();
}

int _estimateMarkdownBlocksCount(ContentItem item) {
  try {
    String markdown = '';
    if (item.content is String) {
      final raw = item.content as String;
      if (raw.trim().startsWith('{')) {
        final map = jsonDecode(raw) as Map<String, dynamic>;
        final t = (map['type'] as String?);
        if (t == 'markdown' || t == 'markdownBlocks') {
          markdown = (map['content'] as String?) ?? '';
        }
      } else {
        markdown = raw;
      }
    }
    if (markdown.isEmpty) return 0;
    final lines = markdown.split('\n');
    int count = 0;
    for (final line in lines) {
      final t = line.trimLeft();
      if (t.startsWith('# ')) {
        count++;
      } else if (t.startsWith('## ')) {
        count++;
      } else if (t.startsWith('---')) {
        count++;
      }
    }
    return count == 0 ? 1 : count; // 至少展示1
  } catch (_) {
    return 1;
  }
}

/// 提取纯 Markdown 文本（兼容保存为 JSON 的结构）
String _extractMarkdown(String raw) {
  try {
    final trimmed = raw.trim();
    if (trimmed.startsWith('{')) {
      final map = jsonDecode(trimmed) as Map<String, dynamic>;
      final type = map['type'] as String?;
      if (type == 'markdown' || type == 'markdownBlocks') {
        final content = map['content'];
        if (content is String) return content;
      }
    }
    return raw;
  } catch (_) {
    return raw;
  }
}

class _TypeBadge extends StatelessWidget {
  final ContentType type;
  const _TypeBadge({required this.type});
  @override
  Widget build(BuildContext context) {
    Color color;
    String label;
    final l10n = AppLocalizations.of(context);
    final lang = Localizations.localeOf(context).languageCode.toLowerCase();
    switch (type) {
      case ContentType.svg:
        color = const Color(0xFF10B981);
        label = l10n.svg;
        break;
      case ContentType.html:
        color = const Color(0xFF6366F1);
        label = l10n.html;
        break;
      case ContentType.textCard:
        color = const Color(0xFFEF4444);
        label = l10n.textCards;
        break;
      case ContentType.textCardCollection:
        color = const Color(0xFFF59E0B);
        if (lang.startsWith('zh')) {
          label = '卡片合集';
        } else if (lang.startsWith('ja')) {
          label = 'カードコレクション';
        } else {
          label = 'Card Collection';
        }
        break;
      case ContentType.markdown:
        color = const Color(0xFF3B82F6);
        label = l10n.markdown;
        break;
      case ContentType.markdownBlocks:
        color = const Color(0xFF8B5CF6);
        if (lang.startsWith('zh')) {
          label = '分块';
        } else if (lang.startsWith('ja')) {
          label = '分割';
        } else {
          label = 'Blocks';
        }
        break;
      case ContentType.image:
        color = Colors.teal;
        if (lang.startsWith('zh')) {
          label = '图片';
        } else if (lang.startsWith('ja')) {
          label = '画像';
        } else {
          label = 'Image';
        }
        break;
      case ContentType.pdf:
        color = Colors.redAccent;
        label = 'PDF';
        break;
    }
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 3),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.12),
        borderRadius: BorderRadius.circular(6),
      ),
      child: Text(
        label,
        style: TextStyle(
          fontSize: 10,
          color: color,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }
}

class _HtmlThumb extends StatelessWidget {
  final String html;
  const _HtmlThumb({required this.html});
  @override
  Widget build(BuildContext context) {
    final text = _extractText(html);
    final lang = Localizations.localeOf(context).languageCode.toLowerCase();
    final previewLabel =
        lang.startsWith('zh')
            ? 'HTML 预览'
            : lang.startsWith('ja')
            ? 'HTML プレビュー'
            : 'HTML Preview';
    final emptyDoc =
        lang.startsWith('zh')
            ? '（空白文档）'
            : lang.startsWith('ja')
            ? '（空白ドキュメント）'
            : '(Empty document)';
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xFFE5E7EB)),
      ),
      padding: const EdgeInsets.all(12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.html, size: 16, color: Color(0xFF6366F1)),
              const SizedBox(width: 6),
              Text(previewLabel),
            ],
          ),
          const SizedBox(height: 8),
          Expanded(
            child: Text(
              text.isEmpty ? emptyDoc : text,
              maxLines: 6,
              overflow: TextOverflow.ellipsis,
              style: const TextStyle(
                fontSize: 12,
                height: 1.4,
                color: Color(0xFF1F2937),
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _extractText(String html) {
    final stripped =
        html
            .replaceAll(
              RegExp(
                r'<script[\s\S]*?</script>',
                multiLine: true,
                caseSensitive: false,
              ),
              '',
            )
            .replaceAll(
              RegExp(
                r'<style[\s\S]*?</style>',
                multiLine: true,
                caseSensitive: false,
              ),
              '',
            )
            .replaceAll(RegExp(r'<[^>]+>'), '')
            .replaceAll('&nbsp;', ' ')
            .trim();
    return stripped;
  }
}

class _MarkdownThumb extends StatelessWidget {
  final String text;
  const _MarkdownThumb({required this.text});
  @override
  Widget build(BuildContext context) {
    final firstLines = text
        .split('\n')
        .where((e) => e.trim().isNotEmpty)
        .take(6)
        .join('\n');
    final l10n = AppLocalizations.of(context);
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: const Color(0xFFE5E7EB)),
      ),
      padding: const EdgeInsets.all(12),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              const Icon(Icons.description, size: 16, color: Color(0xFF3B82F6)),
              const SizedBox(width: 6),
              Text(l10n.markdown),
            ],
          ),
          const SizedBox(height: 8),
          Expanded(
            child: Text(
              firstLines,
              maxLines: 6,
              overflow: TextOverflow.ellipsis,
              style: const TextStyle(
                fontSize: 12,
                height: 1.5,
                color: Color(0xFF111827),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class _CollectionThumb extends StatelessWidget {
  final int count;
  const _CollectionThumb({required this.count});
  @override
  Widget build(BuildContext context) {
    final lang = Localizations.localeOf(context).languageCode.toLowerCase();
    final label =
        lang.startsWith('zh')
            ? '共 $count 张'
            : lang.startsWith('ja')
            ? '合計 $count 枚'
            : '$count items';
    return Container(
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: [Color(0xFF6366F1), Color(0xFF8B5CF6)],
        ),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Center(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(Icons.collections, color: Colors.white, size: 36),
            const SizedBox(height: 4),
            Text(
              label,
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

class _ParsedCardData {
  final String text;
  final EnhancedCardTemplate template;
  final Map<String, dynamic> customStyles;
  _ParsedCardData({
    required this.text,
    required this.template,
    required this.customStyles,
  });
}

class _CollectionParams {
  final List<String> cards;
  final EnhancedCardTemplate? template;
  final bool? fixedPreviewHeight;
  const _CollectionParams({
    required this.cards,
    this.template,
    this.fixedPreviewHeight,
  });
}

class _ContentSearchDelegate extends SearchDelegate<String?> {
  _ContentSearchDelegate({String? initialQuery}) {
    query = initialQuery ?? '';
  }
  @override
  List<Widget>? buildActions(BuildContext context) {
    return [
      if (query.isNotEmpty)
        IconButton(onPressed: () => query = '', icon: const Icon(Icons.clear)),
    ];
  }

  @override
  Widget? buildLeading(BuildContext context) {
    return IconButton(
      onPressed: () => close(context, null),
      icon: const Icon(Icons.arrow_back),
    );
  }

  @override
  Widget buildResults(BuildContext context) {
    close(context, query);
    return const SizedBox.shrink();
  }

  @override
  Widget buildSuggestions(BuildContext context) {
    return const SizedBox.shrink();
  }
}
