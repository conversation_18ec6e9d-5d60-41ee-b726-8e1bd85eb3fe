import 'dart:convert';
import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../config/app_theme.dart';
import '../models/content_item.dart';
import '../services/content_service.dart';
import '../services/service_locator.dart';

class SaveContentPage extends StatefulWidget {
  final String initialTitle;
  final dynamic content;
  final ContentType contentType;
  final List<String>? initialTags;

  const SaveContentPage({
    super.key,
    required this.initialTitle,
    required this.content,
    required this.contentType,
    this.initialTags,
  });

  @override
  State<SaveContentPage> createState() => _SaveContentPageState();
}

class _SaveContentPageState extends State<SaveContentPage> {
  final _titleController = TextEditingController();
  final _tagController = TextEditingController();
  final _formKey = GlobalKey<FormState>();
  final List<String> _tags = [];
  final ContentService _contentService = ServiceLocator().contentService;

  bool _isSaving = false;

  @override
  void initState() {
    super.initState();
    _titleController.text = widget.initialTitle;
    if (widget.initialTags != null) {
      _tags.addAll(widget.initialTags!);
    }
  }

  @override
  void dispose() {
    _titleController.dispose();
    _tagController.dispose();
    super.dispose();
  }

  void _addTag() {
    final tag = _tagController.text.trim();
    if (tag.isNotEmpty && !_tags.contains(tag)) {
      setState(() {
        _tags.add(tag);
        _tagController.clear();
      });
    }
  }

  void _removeTag(String tag) {
    setState(() {
      _tags.remove(tag);
    });
  }

  Future<void> _saveContent() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isSaving = true;
    });

    try {
      late ContentItem savedItem;

      // 根据内容类型进行不同的保存处理
      switch (widget.contentType) {
        case ContentType.markdown:
        case ContentType.markdownBlocks:
        case ContentType.textCard:
        case ContentType.textCardCollection:
        case ContentType.html:
          // 文本类型内容
          savedItem = await _contentService.createTextContent(
            title: _titleController.text,
            type: widget.contentType,
            content: widget.content as String,
            tags: _tags,
          );
          break;

        case ContentType.image:
        case ContentType.svg:
          // 图像类型内容
          if (widget.content is String && widget.content.startsWith('http')) {
            // 网络图像，直接使用URL
            savedItem = await _contentService.createTextContent(
              title: _titleController.text,
              type: widget.contentType,
              content: widget.content as String,
              tags: _tags,
            );
          } else if (widget.content is String &&
              (File(widget.content as String).existsSync())) {
            // 本地文件路径
            final file = File(widget.content as String);
            final bytes = await file.readAsBytes();

            savedItem = await _contentService.createImageContent(
              title: _titleController.text,
              type: widget.contentType,
              imageData: bytes,
              tags: _tags,
              filePath: widget.content as String,
            );
          } else if (widget.content is Uint8List) {
            // 二进制数据
            savedItem = await _contentService.createImageContent(
              title: _titleController.text,
              type: widget.contentType,
              imageData: widget.content as Uint8List,
              tags: _tags,
            );
          } else if (widget.content is String &&
              (widget.content as String).startsWith('<svg')) {
            // SVG字符串
            final bytes = utf8.encode(widget.content as String);

            savedItem = await _contentService.createImageContent(
              title: _titleController.text,
              type: widget.contentType,
              imageData: Uint8List.fromList(bytes),
              tags: _tags,
            );
          } else {
            throw Exception('不支持的内容格式');
          }
          break;

        case ContentType.pdf:
          // PDF文件类型
          savedItem = await _contentService.createTextContent(
            title: _titleController.text,
            type: widget.contentType,
            content: widget.content as String,
            tags: _tags,
          );
          break;
      }

      if (mounted) {
        Navigator.of(context).pop(savedItem);

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('内容保存成功'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('保存失败: $e'), backgroundColor: Colors.red),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSaving = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('保存内容'),
        actions: [
          TextButton.icon(
            onPressed: _isSaving ? null : _saveContent,
            icon: const Icon(Icons.save),
            label: const Text('保存'),
          ),
        ],
      ),
      body:
          _isSaving
              ? const Center(child: CircularProgressIndicator())
              : SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Form(
                  key: _formKey,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // 标题输入框
                      TextFormField(
                        controller: _titleController,
                        decoration: const InputDecoration(
                          labelText: '标题',
                          border: OutlineInputBorder(),
                          prefixIcon: Icon(Icons.title),
                        ),
                        validator: (value) {
                          if (value == null || value.trim().isEmpty) {
                            return '请输入标题';
                          }
                          return null;
                        },
                      ),

                      const SizedBox(height: 24),

                      // 标签输入区域
                      const Text(
                        '标签',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          Expanded(
                            child: TextField(
                              controller: _tagController,
                              decoration: const InputDecoration(
                                hintText: '添加标签...',
                                border: OutlineInputBorder(),
                                prefixIcon: Icon(Icons.label),
                              ),
                              onSubmitted: (_) => _addTag(),
                            ),
                          ),
                          const SizedBox(width: 8),
                          ElevatedButton(
                            onPressed: _addTag,
                            style: ElevatedButton.styleFrom(
                              shape: const CircleBorder(),
                              padding: const EdgeInsets.all(12),
                            ),
                            child: const Icon(Icons.add),
                          ),
                        ],
                      ),

                      const SizedBox(height: 16),

                      // 标签显示区域
                      Wrap(
                        spacing: 8,
                        runSpacing: 8,
                        children:
                            _tags.map((tag) {
                              return Chip(
                                label: Text(tag),
                                deleteIcon: const Icon(Icons.close, size: 18),
                                onDeleted: () => _removeTag(tag),
                                backgroundColor: AppTheme.bgIndigo50,
                              );
                            }).toList(),
                      ),

                      const SizedBox(height: 24),

                      // 内容预览
                      const Text(
                        '内容预览',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Container(
                        height: 300,
                        width: double.infinity,
                        decoration: BoxDecoration(
                          border: Border.all(color: Colors.grey.shade300),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: _buildPreview(),
                      ),
                    ],
                  ),
                ),
              ),
    );
  }

  Widget _buildPreview() {
    switch (widget.contentType) {
      case ContentType.markdown:
      case ContentType.markdownBlocks:
        final raw = widget.content as String;
        final display = _extractMarkdownFromContent(raw);
        return Padding(
          padding: const EdgeInsets.all(16),
          child: Container(
            constraints: BoxConstraints(
              maxHeight: MediaQuery.of(context).size.height * 0.3,
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: SingleChildScrollView(
                physics: const ClampingScrollPhysics(),
                child: MarkdownBody(data: display, selectable: true),
              ),
            ),
          ),
        );

      case ContentType.textCard:
      case ContentType.textCardCollection:
        return Padding(
          padding: const EdgeInsets.all(16),
          child: Container(
            constraints: BoxConstraints(
              maxHeight: MediaQuery.of(context).size.height * 0.3,
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: SingleChildScrollView(
                physics: const ClampingScrollPhysics(),
                child: MarkdownBody(
                  data: widget.content as String,
                  selectable: true,
                ),
              ),
            ),
          ),
        );

      case ContentType.html:
        return Center(
          child: Text(
            '(HTML预览)\n${(widget.content as String).length} 字符',
            textAlign: TextAlign.center,
          ),
        );

      case ContentType.image:
        if (widget.content is String && widget.content.startsWith('http')) {
          return Image.network(
            widget.content as String,
            fit: BoxFit.contain,
            loadingBuilder: (context, child, loadingProgress) {
              if (loadingProgress == null) return child;
              return Center(
                child: CircularProgressIndicator(
                  value:
                      loadingProgress.expectedTotalBytes != null
                          ? loadingProgress.cumulativeBytesLoaded /
                              loadingProgress.expectedTotalBytes!
                          : null,
                ),
              );
            },
          );
        } else if (widget.content is String &&
            File(widget.content as String).existsSync()) {
          return Image.file(
            File(widget.content as String),
            fit: BoxFit.contain,
          );
        } else if (widget.content is Uint8List) {
          return Image.memory(widget.content as Uint8List, fit: BoxFit.contain);
        } else {
          return const Center(
            child: Icon(Icons.broken_image, size: 60, color: Colors.grey),
          );
        }

      case ContentType.svg:
        try {
          if (widget.content is String && widget.content.startsWith('http')) {
            return SvgPicture.network(
              widget.content as String,
              fit: BoxFit.contain,
            );
          } else if (widget.content is String &&
              File(widget.content as String).existsSync()) {
            return SvgPicture.file(
              File(widget.content as String),
              fit: BoxFit.contain,
            );
          } else if (widget.content is String &&
              (widget.content as String).startsWith('<svg')) {
            return SvgPicture.string(
              widget.content as String,
              fit: BoxFit.contain,
            );
          } else {
            return const Center(
              child: Icon(Icons.broken_image, size: 60, color: Colors.grey),
            );
          }
        } catch (e) {
          return Center(
            child: Text(
              'SVG 渲染错误: $e',
              style: const TextStyle(color: Colors.red),
              textAlign: TextAlign.center,
            ),
          );
        }

      case ContentType.pdf:
        return Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.picture_as_pdf, size: 60, color: Colors.red),
              const SizedBox(height: 8),
              Text('PDF文档预览'),
            ],
          ),
        );

    }
  }

  /// 提取用于预览的 Markdown 文本
  String _extractMarkdownFromContent(String raw) {
    try {
      final trimmed = raw.trim();
      if (trimmed.startsWith('{')) {
        final map = jsonDecode(trimmed) as Map<String, dynamic>;
        final type = map['type'] as String?;
        if (type == 'markdown' || type == 'markdownBlocks') {
          final content = map['content'];
          if (content is String) return content;
        }
      }
      return raw;
    } catch (_) {
      return raw;
    }
  }
}
