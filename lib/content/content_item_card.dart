import 'dart:io';

import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_markdown/flutter_markdown.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:intl/intl.dart';
import 'package:webview_flutter/webview_flutter.dart';

import '../config/app_theme.dart';
import '../models/content_item.dart';

class ContentItemCard extends StatefulWidget {
  final ContentItem contentItem;
  final VoidCallback onTap;

  const ContentItemCard({
    super.key,
    required this.contentItem,
    required this.onTap,
  });

  @override
  State<ContentItemCard> createState() => _ContentItemCardState();
}

class _ContentItemCardState extends State<ContentItemCard> {
  WebViewController? _webViewController;
  bool _isWebViewLoaded = false;

  @override
  void initState() {
    super.initState();

    // 仅为HTML类型初始化WebView
    if (widget.contentItem.type == ContentType.html) {
      _initWebView();
    }
  }

  @override
  void dispose() {
    _webViewController = null;
    super.dispose();
  }

  void _initWebView() {
    _webViewController =
        WebViewController()
          ..setJavaScriptMode(JavaScriptMode.unrestricted)
          ..setBackgroundColor(Colors.white)
          ..setNavigationDelegate(
            NavigationDelegate(
              onPageFinished: (String url) {
                if (mounted) {
                  setState(() {
                    _isWebViewLoaded = true;
                  });
                }
              },
            ),
          )
          ..loadHtmlString(widget.contentItem.content.toString());
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      onTap: widget.onTap,
      child: Container(
        margin: const EdgeInsets.symmetric(vertical: 8, horizontal: 4),
        decoration: BoxDecoration(
          color: Theme.of(context).cardColor,
          borderRadius: BorderRadius.circular(20),
          boxShadow: [
            BoxShadow(
              color: _getTypeColor().withValues(alpha: 0.08),
              blurRadius: 16,
              offset: const Offset(0, 6),
              spreadRadius: -2,
            ),
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.04),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        clipBehavior: Clip.antiAlias,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [_buildContentPreview(context), _buildCardFooter(context)],
        ),
      ),
    );
  }

  Widget _buildContentPreview(BuildContext context) {
    return Expanded(
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(14),
        decoration: BoxDecoration(gradient: _getGradientByType()),
        child: _buildPreviewContent(context),
      ),
    );
  }

  Widget _buildPreviewContent(BuildContext context) {
    switch (widget.contentItem.type) {
      case ContentType.markdown:
      case ContentType.markdownBlocks:
      case ContentType.textCard:
      case ContentType.textCardCollection:
        return Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.03),
                blurRadius: 12,
                offset: const Offset(0, 4),
                spreadRadius: -4,
              ),
            ],
          ),
          padding: const EdgeInsets.all(16),
          child: Stack(
            children: [
              SingleChildScrollView(
                physics: const NeverScrollableScrollPhysics(),
                child: MarkdownBody(
                  data:
                      widget.contentItem.content.toString().length > 300
                          ? '${widget.contentItem.content.toString().substring(0, 300)}...'
                          : widget.contentItem.content.toString(),
                  styleSheet: MarkdownStyleSheet(
                    p: TextStyle(
                      fontSize: 13,
                      height: 1.6,
                      color: Colors.grey.shade800,
                    ),
                    h1: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w700,
                      color: Colors.grey.shade900,
                      height: 1.3,
                    ),
                    h2: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color: Colors.grey.shade900,
                      height: 1.4,
                    ),
                    h3: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: Colors.grey.shade900,
                      height: 1.5,
                    ),
                    code: TextStyle(
                      fontSize: 12,
                      backgroundColor: _getTypeColor().withValues(alpha: 0.08),
                      color: _getTypeColor(),
                      fontFamily: 'monospace',
                    ),
                    blockquoteDecoration: BoxDecoration(
                      color: Colors.grey.shade50,
                      borderRadius: BorderRadius.circular(4),
                      border: Border(
                        left: BorderSide(
                          color: _getTypeColor().withValues(alpha: 0.4),
                          width: 3,
                        ),
                      ),
                    ),
                    blockquote: TextStyle(
                      fontSize: 13,
                      fontStyle: FontStyle.italic,
                      color: Colors.grey.shade700,
                      height: 1.6,
                    ),
                    listBullet: TextStyle(
                      fontSize: 13,
                      color: _getTypeColor(),
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
              // 渐变遮罩
              Positioned(
                bottom: 0,
                left: 0,
                right: 0,
                height: 60,
                child: Container(
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        Colors.white.withValues(alpha: 0),
                        Colors.white.withValues(alpha: 0.9),
                        Colors.white,
                      ],
                      stops: const [0.1, 0.7, 1.0],
                    ),
                  ),
                ),
              ),
            ],
          ),
        );

      case ContentType.image:
        return Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.03),
                blurRadius: 12,
                offset: const Offset(0, 4),
                spreadRadius: -4,
              ),
            ],
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(16),
            child: _buildImage(),
          ),
        );

      case ContentType.svg:
        return Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.03),
                blurRadius: 12,
                offset: const Offset(0, 4),
                spreadRadius: -4,
              ),
            ],
          ),
          padding: const EdgeInsets.all(16),
          child: _buildSvg(),
        );

      case ContentType.html:
        return Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.03),
                blurRadius: 12,
                offset: const Offset(0, 4),
                spreadRadius: -4,
              ),
            ],
          ),
          clipBehavior: Clip.antiAlias,
          child: Stack(
            children: [
              _buildHtmlPreview(),
              // 顶部标题标识
              Positioned(
                top: 0,
                left: 0,
                right: 0,
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 8,
                  ),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topCenter,
                      end: Alignment.bottomCenter,
                      colors: [
                        _getTypeColor().withValues(alpha: 0.2),
                        _getTypeColor().withValues(alpha: 0.05),
                        Colors.transparent,
                      ],
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(Icons.web, color: _getTypeColor(), size: 16),
                      const SizedBox(width: 8),
                      Text(
                        '网页内容',
                        style: TextStyle(
                          fontSize: 13,
                          fontWeight: FontWeight.w600,
                          color: _getTypeColor(),
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        );

      case ContentType.pdf:
        return Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
          ),
          child: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.picture_as_pdf, size: 48, color: Colors.red),
                const SizedBox(height: 8),
                Text('PDF文档', style: TextStyle(color: Colors.red)),
              ],
            ),
          ),
        );

    }
  }

  Widget _buildHtmlPreview() {
    if (_webViewController == null) {
      return _buildHtmlPlaceholder();
    }

    return Stack(
      children: [
        SizedBox(
          width: double.infinity,
          height: double.infinity,
          child: WebViewWidget(controller: _webViewController!),
        ),
        // 加载指示器，在WebView加载完成前显示
        if (!_isWebViewLoaded)
          Container(
            color: Colors.white,
            child: Center(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  CircularProgressIndicator(color: _getTypeColor()),
                  const SizedBox(height: 8),
                  Text(
                    '正在渲染HTML...',
                    style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
                  ),
                ],
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildHtmlPlaceholder() {
    // 获取HTML的片段摘要，用于占位符显示
    final htmlContent = widget.contentItem.content.toString();
    String htmlPreview = '';

    // 尝试提取HTML正文或者片段
    final bodyMatch = RegExp(
      r'<body[^>]*>(.*?)<\/body>',
      dotAll: true,
    ).firstMatch(htmlContent);
    if (bodyMatch != null && bodyMatch.group(1) != null) {
      // 移除HTML标签，只保留文本
      final bodyText = bodyMatch.group(1)!.replaceAll(RegExp(r'<[^>]*>'), ' ');
      htmlPreview = bodyText.replaceAll(RegExp(r'\s+'), ' ').trim();
    } else {
      // 如果没有找到body标签，直接移除所有HTML标签
      htmlPreview = htmlContent.replaceAll(RegExp(r'<[^>]*>'), ' ');
      htmlPreview = htmlPreview.replaceAll(RegExp(r'\s+'), ' ').trim();
    }

    // 限制长度
    if (htmlPreview.length > 100) {
      htmlPreview = '${htmlPreview.substring(0, 100)}...';
    }

    return Center(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(Icons.web, size: 32, color: _getTypeColor().withValues(alpha: 0.5)),
            const SizedBox(height: 8),
            Text(
              htmlPreview.isEmpty ? '点击查看网页内容' : htmlPreview,
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey.shade600,
                height: 1.5,
              ),
              textAlign: TextAlign.center,
              maxLines: 4,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildImage() {
    final content = widget.contentItem.content;

    if (content is String && content.startsWith('http')) {
      // 网络图片
      return CachedNetworkImage(
        imageUrl: content,
        fit: BoxFit.cover,
        placeholder:
            (context, url) => const Center(child: CircularProgressIndicator()),
        errorWidget:
            (context, url, error) =>
                const Icon(Icons.error_outline, color: Colors.red),
      );
    } else if (content is String &&
        (content.startsWith('/') || content.contains('content_files'))) {
      // 本地图片
      return Image.file(
        File(content),
        fit: BoxFit.cover,
        errorBuilder:
            (context, error, stackTrace) =>
                const Icon(Icons.error_outline, color: Colors.red),
      );
    } else {
      // 未知图片类型
      return const Center(
        child: Icon(
          Icons.image_not_supported_outlined,
          color: Colors.grey,
          size: 40,
        ),
      );
    }
  }

  Widget _buildSvg() {
    final content = widget.contentItem.content;

    if (content is String && content.startsWith('http')) {
      // 网络SVG
      return SvgPicture.network(
        content,
        placeholderBuilder:
            (context) => const Center(child: CircularProgressIndicator()),
      );
    } else if (content is String &&
        (content.startsWith('/') || content.contains('content_files'))) {
      // 本地SVG
      return SvgPicture.file(
        File(content),
        placeholderBuilder:
            (context) => const Center(child: CircularProgressIndicator()),
      );
    } else if (content is String && content.startsWith('<svg')) {
      // SVG字符串
      return SvgPicture.string(
        content,
        placeholderBuilder:
            (context) => const Center(child: CircularProgressIndicator()),
      );
    } else {
      // 未知SVG类型
      return const Center(
        child: Icon(
          Icons.image_not_supported_outlined,
          color: Colors.grey,
          size: 40,
        ),
      );
    }
  }

  Widget _buildCardFooter(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 14),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        border: Border(
          top: BorderSide(color: Colors.grey.withValues(alpha: 0.1), width: 1),
        ),
      ),
      child: Row(
        children: [
          // 标题和信息
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.contentItem.title,
                  style: const TextStyle(
                    fontSize: 15,
                    fontWeight: FontWeight.w600,
                    color: Color(0xFF2D3142),
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 3),
                Text(
                  '${DateFormat('yyyy-MM-dd').format(widget.contentItem.updatedAt)} · ${widget.contentItem.formattedSize}',
                  style: TextStyle(fontSize: 11, color: Colors.grey.shade600),
                ),
              ],
            ),
          ),
          // 收藏图标
          if (widget.contentItem.isFavorite)
            Container(
              padding: const EdgeInsets.all(6),
              decoration: BoxDecoration(
                color: Colors.red.withValues(alpha: 0.1),
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.favorite_rounded,
                color: Colors.red,
                size: 16,
              ),
            ),
        ],
      ),
    );
  }

  Color _getTypeColor() {
    switch (widget.contentItem.type) {
      case ContentType.markdown:
      case ContentType.markdownBlocks:
        return Colors.blue;
      case ContentType.textCard:
      case ContentType.textCardCollection:
        return Colors.green;
      case ContentType.image:
        return Colors.purple;
      case ContentType.svg:
        return Colors.green;
      case ContentType.html:
        return Colors.orange;
      case ContentType.pdf:
        return Colors.red;
    }
  }

  LinearGradient _getGradientByType() {
    switch (widget.contentItem.type) {
      case ContentType.markdown:
      case ContentType.markdownBlocks:
        return AppTheme.blueGradient;
      case ContentType.textCard:
      case ContentType.textCardCollection:
        return AppTheme.greenGradient;
      case ContentType.image:
        return AppTheme.purpleGradient;
      case ContentType.svg:
        return AppTheme.greenGradient;
      case ContentType.html:
        return AppTheme.orangeGradient;
      case ContentType.pdf:
        return AppTheme.redGradient;
    }
  }
}
