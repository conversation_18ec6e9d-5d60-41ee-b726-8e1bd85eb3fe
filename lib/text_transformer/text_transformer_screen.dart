import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../generated/l10n/app_localizations.dart';
import '../traffic_guide/services/traffic_guide_service.dart';
import '../services/service_locator.dart';

class TextTransformerScreen extends StatefulWidget {
  const TextTransformerScreen({super.key});

  @override
  State<TextTransformerScreen> createState() => _TextTransformerScreenState();
}

class _TextTransformerScreenState extends State<TextTransformerScreen> {
  final TrafficGuideService _service = TrafficGuideService();
  final _inputController = TextEditingController();

  bool _isTransforming = false;
  String _selectedTemplate = 'emoji';
  String _outputText = '';

  // 获取本地化模板
  Map<String, Map<String, dynamic>> _getTemplates(AppLocalizations l10n) {
    return {
      'emoji': {
        'name': l10n.textTransformerTemplateEmojiName,
        'description': l10n.textTransformerTemplateEmojiDesc,
        'icon': Icons.emoji_emotions,
        'mode': 'emoji',
      },
      'fancy': {
        'name': l10n.textTransformerTemplateFancyName,
        'description': l10n.textTransformerTemplateFancyDesc,
        'icon': Icons.style,
        'mode': 'fancy',
      },
      'bold': {
        'name': l10n.textTransformerTemplateBoldName,
        'description': l10n.textTransformerTemplateBoldDesc,
        'icon': Icons.format_bold,
        'mode': 'bold',
      },
      'decorative': {
        'name': l10n.textTransformerTemplateDecorativeName,
        'description': l10n.textTransformerTemplateDecorativeDesc,
        'icon': Icons.auto_awesome,
        'mode': 'decorative',
      },
      'mixed': {
        'name': l10n.textTransformerTemplateMixedName,
        'description': l10n.textTransformerTemplateMixedDesc,
        'icon': Icons.shuffle,
        'mode': 'mixed',
      },
      'invisible': {
        'name': l10n.textTransformerTemplateInvisibleName,
        'description': l10n.textTransformerTemplateInvisibleDesc,
        'icon': Icons.visibility_off,
        'mode': 'invisible',
      },
      'unicode': {
        'name': l10n.textTransformerTemplateUnicodeName,
        'description': l10n.textTransformerTemplateUnicodeDesc,
        'icon': Icons.font_download,
        'mode': 'unicode',
      },
    };
  }

  // 获取本地化示例文本
  Map<String, String> _getExampleTexts(AppLocalizations l10n) {
    return {
      'emoji': 'Hello World 123 ${l10n.textTransformerSample}',
      'fancy': 'Beautiful Text 456',
      'bold': 'Strong Text 789',
      'decorative': 'Fancy Text 2024',
      'mixed': 'Mixed Effects Text',
      'invisible': l10n.textTransformerSampleInvisible,
      'unicode': l10n.textTransformerSampleUnicode,
    };
  }

  @override
  void initState() {
    super.initState();
    _inputController.addListener(_transformText);

    // 设置示例文本（受设置控制）
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final settings = ServiceLocator().settingsService.settings;
      if (settings.useDefaultInitialTextTransformer) {
        final l10n = AppLocalizations.of(context);
        _inputController.text = _getExampleTexts(l10n)[_selectedTemplate]!;
      }
    });
  }

  @override
  void dispose() {
    _inputController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    final theme = Theme.of(context);
    return Scaffold(
      appBar: AppBar(
        title: Text(l10n.trafficGuideTextTransformerTitle),
        backgroundColor: theme.colorScheme.surface.withValues(alpha: 0.95),
        foregroundColor: theme.colorScheme.onSurface,
        elevation: 0,
        scrolledUnderElevation: 0.5,
      ),
      body: Column(
        children: [
          // 模板选择区域
          _buildTemplateSelector(),

          // 主要内容区域
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  // 输入区域
                  Expanded(flex: 2, child: _buildInputSection()),

                  // 转换按钮和操作
                  _buildActionButtons(),

                  // 输出区域
                  Expanded(flex: 2, child: _buildOutputSection()),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTemplateSelector() {
    final l10n = AppLocalizations.of(context);
    final theme = Theme.of(context);
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        border: Border(
          bottom: BorderSide(color: theme.colorScheme.outlineVariant, width: 1),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.auto_awesome, color: theme.colorScheme.primary),
              const SizedBox(width: 8),
              Text(
                l10n.textTransformerSelectMode,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: theme.colorScheme.primary,
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          SizedBox(
            height: 100,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: _getTemplates(l10n).length,
              itemBuilder: (context, index) {
                final templates = _getTemplates(l10n);
                final templateKey = templates.keys.elementAt(index);
                final template = templates[templateKey]!;
                final isSelected = _selectedTemplate == templateKey;

                return GestureDetector(
                  onTap: () => _selectTemplate(templateKey),
                  child: Container(
                    width: 120,
                    margin: const EdgeInsets.only(right: 12),
                    decoration: BoxDecoration(
                      color:
                          isSelected
                              ? theme.colorScheme.primary
                              : theme.colorScheme.surface,
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color:
                            isSelected
                                ? theme.colorScheme.primary
                                : theme.colorScheme.outlineVariant,
                        width: 2,
                      ),
                      boxShadow: [
                        BoxShadow(
                          color:
                              theme.brightness == Brightness.dark
                                  ? Colors.black.withValues(alpha: 0.25)
                                  : Colors.black.withValues(alpha: 0.06),
                          blurRadius: 4,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: Padding(
                      padding: const EdgeInsets.all(12),
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            template['icon'] as IconData,
                            color:
                                isSelected
                                    ? theme.colorScheme.onPrimary
                                    : theme.colorScheme.primary,
                            size: 24,
                          ),
                          const SizedBox(height: 4),
                          Text(
                            template['name'] as String,
                            style: TextStyle(
                              fontSize: 12,
                              fontWeight: FontWeight.bold,
                              color:
                                  isSelected
                                      ? theme.colorScheme.onPrimary
                                      : theme.colorScheme.onSurface,
                            ),
                            textAlign: TextAlign.center,
                            maxLines: 2,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ],
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
          const SizedBox(height: 8),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              color: theme.colorScheme.primary.withValues(alpha: 0.06),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: theme.colorScheme.primary.withValues(alpha: 0.2),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.info_outline,
                  size: 16,
                  color: theme.colorScheme.primary,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    _getEffectDescription(l10n),
                    style: TextStyle(
                      fontSize: 12,
                      color: theme.colorScheme.primary,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildInputSection() {
    final l10n = AppLocalizations.of(context);
    final theme = Theme.of(context);
    return Card(
      color: theme.colorScheme.surface,
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    Icon(Icons.input, color: theme.colorScheme.primary),
                    const SizedBox(width: 8),
                    Text(
                      l10n.textTransformerInputText,
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: theme.colorScheme.primary,
                      ),
                    ),
                  ],
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: theme.colorScheme.primary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    '${_inputController.text.length} ${l10n.textTransformerCharacters}',
                    style: TextStyle(
                      fontSize: 12,
                      color: theme.colorScheme.primary,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Expanded(
              child: Container(
                decoration: BoxDecoration(
                  color: theme.colorScheme.surfaceContainerLow,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: theme.colorScheme.outlineVariant),
                ),
                child: TextField(
                  controller: _inputController,
                  maxLines: null,
                  expands: true,
                  decoration: InputDecoration(
                    hintText: l10n.textTransformerHint,
                    border: InputBorder.none,
                    contentPadding: const EdgeInsets.all(16),
                    hintStyle: TextStyle(
                      color: theme.colorScheme.onSurfaceVariant,
                    ),
                  ),
                  style: TextStyle(
                    fontSize: 16,
                    color: theme.colorScheme.onSurface,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButtons() {
    final l10n = AppLocalizations.of(context);
    final theme = Theme.of(context);
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 16),
      child: Row(
        children: [
          Expanded(
            child: ElevatedButton.icon(
              onPressed: _isTransforming ? null : _transformText,
              icon:
                  _isTransforming
                      ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                      : const Icon(Icons.auto_awesome),
              label: Text(
                _isTransforming ? l10n.textTransformerTransforming : l10n.textTransformerTransform,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: theme.colorScheme.primary,
                foregroundColor: theme.colorScheme.onPrimary,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                elevation: 2,
              ),
            ),
          ),
          const SizedBox(width: 12),
          IconButton(
            onPressed: _clearAll,
            icon: Icon(Icons.clear, color: theme.colorScheme.onSurfaceVariant),
            tooltip: l10n.textTransformerClearAll,
            style: IconButton.styleFrom(
              backgroundColor: theme.colorScheme.surfaceContainerHigh,
              padding: const EdgeInsets.all(12),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildOutputSection() {
    final l10n = AppLocalizations.of(context);
    final theme = Theme.of(context);
    return Card(
      color: theme.colorScheme.surface,
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Flexible(
                  child: Row(
                    children: [
                      Icon(Icons.output, color: Theme.of(context).primaryColor),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          l10n.textTransformerOutputResult,
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Theme.of(context).primaryColor,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),
                ),
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: theme.colorScheme.primary.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        '${_outputText.length} ${l10n.textTransformerCharacters}',
                        style: TextStyle(
                          fontSize: 12,
                          color: theme.colorScheme.primary,
                          fontWeight: FontWeight.w500,
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    IconButton(
                      onPressed: _copyOutput,
                      icon: Icon(
                        Icons.copy,
                        size: 20,
                        color: theme.colorScheme.onSurfaceVariant,
                      ),
                      tooltip: l10n.textTransformerCopyResult,
                      style: IconButton.styleFrom(
                        backgroundColor: theme.colorScheme.surfaceContainerHigh,
                        padding: const EdgeInsets.all(8),
                        minimumSize: const Size(32, 32),
                      ),
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 12),
            Expanded(
              child: Container(
                decoration: BoxDecoration(
                  color: theme.colorScheme.surfaceContainerLow,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: theme.colorScheme.outlineVariant),
                ),
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: SingleChildScrollView(
                    child: ConstrainedBox(
                      constraints: const BoxConstraints(minHeight: 100),
                      child: SelectableText(
                        _outputText.isEmpty ? l10n.textTransformerOutputHint : _outputText,
                        style: _getOutputTextStyle(),
                        textAlign: TextAlign.start,
                        textDirection: TextDirection.ltr,
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _selectTemplate(String templateKey) {
    setState(() {
      _selectedTemplate = templateKey;
      // 更新示例文本
      final l10n = AppLocalizations.of(context);
      _inputController.text = _getExampleTexts(l10n)[templateKey]!;
    });
    _transformText();
  }

  void _transformText() {
    final l10n = AppLocalizations.of(context);
    if (_inputController.text.isEmpty) {
      _outputText = '';
      return;
    }

    setState(() => _isTransforming = true);

    try {
      final template = _getTemplates(l10n)[_selectedTemplate]!;
      final mode = template['mode'] as String;

      final transformed = _service.transformTextEnhanced(
        _inputController.text,
        mode,
      );

      setState(() {
        _outputText = transformed;
      });
    } catch (e) {
      debugPrint('转换失败: $e');
      setState(() {
        _outputText = _inputController.text; // 转换失败时显示原文本
      });
    } finally {
      setState(() => _isTransforming = false);
    }
  }

  String _getEffectDescription(AppLocalizations l10n) {
    switch (_selectedTemplate) {
      case 'emoji':
        return l10n.textTransformerEffectEmojiDesc;
      case 'fancy':
        return l10n.textTransformerEffectFancyDesc;
      case 'bold':
        return l10n.textTransformerEffectBoldDesc;
      case 'decorative':
        return l10n.textTransformerEffectDecorativeDesc;
      case 'mixed':
        return l10n.textTransformerEffectMixedDesc;
      case 'invisible':
        return l10n.textTransformerEffectInvisibleDesc;
      case 'unicode':
        return l10n.textTransformerEffectUnicodeDesc;
      default:
        return '';
    }
  }

  TextStyle _getOutputTextStyle() {
    final theme = Theme.of(context);
    final baseStyle = TextStyle(
      fontSize: 16,
      color:
          _outputText.isEmpty
              ? theme.colorScheme.onSurfaceVariant
              : theme.colorScheme.onSurface,
      height: 1.4,
    );

    // 为表情符号转换模式使用等宽字体
    if (_selectedTemplate == 'emoji') {
      return baseStyle.copyWith(
        fontFamily: 'monospace',
        fontSize: 18, // 稍微增大字体以便更好地显示特殊字符
      );
    }

    return baseStyle;
  }

  void _copyOutput() {
    final l10n = AppLocalizations.of(context);
    if (_outputText.isNotEmpty) {
      Clipboard.setData(ClipboardData(text: _outputText));
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(l10n.textTransformerCopied), backgroundColor: Colors.green),
      );
    }
  }

  void _clearAll() {
    _inputController.clear();
    setState(() {
      _outputText = '';
    });
  }
}
