import 'dart:io';
import 'dart:ui' as ui;

import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter/services.dart';
import 'package:path_provider/path_provider.dart';
import 'package:saver_gallery/saver_gallery.dart';
import 'package:share_plus/share_plus.dart';

import '../../common/utils/permission_helper.dart';

/// Markdown导出服务
class MarkdownExportService {
  /// 处理分享
  static Future<void> handleShare({
    required String markdownText,
    required GlobalKey renderKey,
    required BuildContext context,
    required TabController tabController,
    required Function(String) showSnackBar,
  }) async {
    if (markdownText.isEmpty) {
      showSnackBar('请输入Markdown内容');
      return;
    }

    try {
      // 添加重试机制
      String? imagePath;
      int retryCount = 0;
      const maxRetries = 3;
      bool isSuccess = false;

      while (!isSuccess && retryCount < maxRetries) {
        retryCount++;
        try {
          // 显示进度提示
          showSnackBar('正在生成图片... (尝试 $retryCount/$maxRetries)');

          // 确保当前在编辑标签页
          if (tabController.index != 0) {
            tabController.animateTo(0);
            // 等待渲染完成
            await Future.delayed(const Duration(milliseconds: 500));
          }

          // 捕获图片
          imagePath = await _captureWidget(renderKey, showSnackBar);
          if (imagePath != null) {
            // 读取图片文件
            final imageFile = File(imagePath);
            final bytes = await imageFile.readAsBytes();

            // 检查图片大小
            final imageSizeInMB = bytes.length / (1024 * 1024);
            debugPrint('生成的图片大小: ${imageSizeInMB.toStringAsFixed(2)} MB');

            // 如果图片太大，进行压缩
            if (imageSizeInMB > 5) {
              debugPrint('图片过大，进行压缩...');
              // 使用 saver_gallery 的压缩功能
              final result = await SaverGallery.saveImage(
                bytes,
                quality: 80,
                fileName:
                    'markdown_${DateTime.now().millisecondsSinceEpoch}.png',
                skipIfExists: false,
              );

              if (result.isSuccess) {
                showSnackBar('图片已保存到相册');
              } else {
                showSnackBar('保存失败: ${result.errorMessage}');
              }
            }

            // 尝试分享图片
            try {
              await SharePlus.instance.share(
                ShareParams(files: [XFile(imagePath)], text: '分享Markdown内容'),
              );
              isSuccess = true;
              showSnackBar('分享成功');
            } catch (shareError) {
              debugPrint('分享失败，尝试备选方案: $shareError');
              // 备选方案1：复制Markdown内容到剪贴板
              final data = ClipboardData(text: markdownText);
              await Clipboard.setData(data);
              showSnackBar('分享失败，已复制Markdown内容到剪贴板，可以手动粘贴分享');
              isSuccess = true;
            }
          }
        } catch (e) {
          debugPrint('分享尝试 $retryCount 失败: $e');
          if (retryCount < maxRetries) {
            // 等待一段时间后重试
            await Future.delayed(Duration(milliseconds: 500 * retryCount));
          } else {
            // 最后一次尝试失败，使用备选方案
            try {
              // 备选方案2：直接分享Markdown文本
              await SharePlus.instance.share(
                ShareParams(text: markdownText, subject: '分享Markdown内容'),
              );
              isSuccess = true;
            } catch (e) {
              // 备选方案3：复制到剪贴板
              final data = ClipboardData(text: markdownText);
              await Clipboard.setData(data);
              showSnackBar('分享失败，已复制Markdown内容到剪贴板，可以手动粘贴分享');
              isSuccess = true;
            }
          }
        }
      }

      if (!isSuccess) {
        showSnackBar('分享失败，请稍后重试');
      }
    } catch (e) {
      debugPrint('分享过程发生错误: $e');
      showSnackBar('分享失败: $e');
    }
  }

  /// 处理复制
  static Future<void> handleCopy({
    required String markdownText,
    required GlobalKey renderKey,
    required Function(String) showSnackBar,
  }) async {
    if (markdownText.isEmpty) {
      showSnackBar('请输入Markdown内容');
      return;
    }

    try {
      final imagePath = await _captureWidget(renderKey, showSnackBar);
      if (imagePath != null) {
        final data = ClipboardData(text: markdownText);
        await Clipboard.setData(data);
        showSnackBar('已复制Markdown内容到剪贴板');
      }
    } catch (e) {
      showSnackBar('复制失败: $e');
    }
  }

  /// 处理保存 Markdown 文本文件
  static Future<bool> handleSaveMarkdown({
    required String markdownText,
    required BuildContext context,
    required Function(String) showSnackBar,
  }) async {
    if (markdownText.isEmpty) {
      showSnackBar('请输入Markdown内容');
      return false;
    }

    try {
      // 使用系统文件保存对话框
      final result = await _saveMarkdownWithDialog(markdownText, context);
      return result;
    } catch (e) {
      showSnackBar('保存失败: $e');
      return false;
    }
  }

  /// 处理保存
  static Future<void> handleSave({
    required String markdownText,
    required GlobalKey renderKey,
    required BuildContext context,
    required Function(String) showSnackBar,
  }) async {
    if (markdownText.isEmpty) {
      showSnackBar('请输入Markdown内容');
      return;
    }

    try {
      // 非iOS平台使用权限处理工具
      // 直接请求存储权限
      final granted = await PermissionHelper.requestPhotosPermission();
      if (!granted) {
        showSnackBar('保存失败：需要存储权限');
        return;
      }

      // 有权限后保存图片
      await _saveImageToGallery(renderKey, context, showSnackBar);
    } catch (e) {
      showSnackBar('保存失败: $e');
    }
  }

  /// 保存图片到相册的核心逻辑
  static Future<void> _saveImageToGallery(
    GlobalKey renderKey,
    BuildContext context,
    Function(String) showSnackBar,
  ) async {
    // 显示进度提示
    showSnackBar('正在生成并保存图片...');

    // 尝试最多3次
    for (int i = 0; i < 3; i++) {
      try {
        final imagePath = await _captureWidget(renderKey, showSnackBar);
        if (imagePath != null) {
          // 读取图片文件
          final imageFile = File(imagePath);
          final bytes = await imageFile.readAsBytes();

          // 检查平台
          final isIOS = Theme.of(context).platform == TargetPlatform.iOS;

          // 保存到相册
          final SaveResult result;

          if (isIOS) {
            // 为iOS添加特殊处理
            result = await SaverGallery.saveImage(
              bytes,
              quality: 100,
              fileName: 'markdown_${DateTime.now().millisecondsSinceEpoch}.png',
              skipIfExists: false,
            );
          } else {
            // Android等其他平台
            result = await SaverGallery.saveImage(
              bytes,
              quality: 100,
              fileName: 'markdown_${DateTime.now().millisecondsSinceEpoch}.png',
              skipIfExists: false,
            );
          }

          // 显示提示
          if (result.isSuccess) {
            showSnackBar('图片已保存到相册');
            return; // 成功后退出
          } else {
            final error = result.errorMessage ?? '未知错误';
            debugPrint('保存失败 (尝试 ${i + 1}/3): $error');

            // 如果是iOS且失败，尝试使用备用方法
            if (isIOS && i == 1) {
              await _saveImageToGalleryFallback(bytes, showSnackBar);
              return;
            }
          }
        } else {
          debugPrint('无法生成图片 (尝试 ${i + 1}/3)');
          // 等待一段时间后重试
          await Future.delayed(const Duration(milliseconds: 500));
        }
      } catch (e) {
        debugPrint('保存图片异常 (尝试 ${i + 1}/3): $e');
        // 如果是最后一次尝试，显示错误
        if (i == 2) {
          showSnackBar('保存失败: $e');
        }
      }
    }

    showSnackBar('无法保存图片，请稍后重试');
  }

  /// iOS平台的备用保存方法
  static Future<void> _saveImageToGalleryFallback(
    Uint8List bytes,
    Function(String) showSnackBar,
  ) async {
    try {
      // 创建临时文件
      final tempDir = await getTemporaryDirectory();
      final tempFile = File(
        '${tempDir.path}/markdown_temp_${DateTime.now().millisecondsSinceEpoch}.png',
      );
      await tempFile.writeAsBytes(bytes);

      // 使用share_plus分享到"保存图像"操作
      await SharePlus.instance.share(
        ShareParams(files: [XFile(tempFile.path)], text: '保存Markdown图片'),
      );

      showSnackBar('请选择"保存图像"以保存到相册');
    } catch (e) {
      debugPrint('备用保存方法失败: $e');
      showSnackBar('保存失败，请手动截图保存');
    }
  }

  /// 捕获组件为图片
  static Future<String?> _captureWidget(
    GlobalKey renderKey,
    Function(String) showSnackBar,
  ) async {
    try {
      // 等待足够的时间让组件完成渲染
      await Future.delayed(const Duration(milliseconds: 500));

      // 等待下一帧完成渲染
      await SchedulerBinding.instance.endOfFrame;

      // 使用 RenderRepaintBoundary 捕获图像
      final renderObject =
          renderKey.currentContext?.findRenderObject()
              as RenderRepaintBoundary?;

      if (renderObject == null) {
        showSnackBar('无法获取渲染对象，请稍后再试');
        return null;
      }

      // 捕获图像 - 保持高质量
      final image = await renderObject.toImage(pixelRatio: 3.0); // 保持高分辨率
      final byteData = await image.toByteData(format: ui.ImageByteFormat.png);

      if (byteData == null) {
        showSnackBar('无法转换图像数据');
        return null;
      }

      final pngBytes = byteData.buffer.asUint8List();

      // 检查图片大小
      final imageSizeInMB = pngBytes.length / (1024 * 1024);
      debugPrint('生成的图片大小: ${imageSizeInMB.toStringAsFixed(2)} MB');

      // 如果图片太大，可能会导致分享问题，但根据用户需求保持高质量
      if (imageSizeInMB > 10) {
        debugPrint('警告：图片大小超过10MB，可能影响分享');
      }

      // 保存图片到应用文档目录，而不是临时目录
      // 这可能有助于解决某些分享问题
      final directory = await getApplicationDocumentsDirectory();
      final imagePath =
          '${directory.path}/markdown_${DateTime.now().millisecondsSinceEpoch}.png';
      final imageFile = File(imagePath);
      await imageFile.writeAsBytes(pngBytes);

      debugPrint('图片已保存到: $imagePath');

      return imagePath;
    } catch (e) {
      debugPrint('截图失败: $e');
      showSnackBar('截图失败: $e');
      return null;
    }
  }

  /// 使用系统对话框保存 Markdown 文件
  static Future<bool> _saveMarkdownWithDialog(
    String markdownText,
    BuildContext context,
  ) async {
    try {
      // 创建临时文件
      final tempDir = await getTemporaryDirectory();
      final tempFileName =
          'markdown_export_${DateTime.now().millisecondsSinceEpoch}.md';
      final tempFile = File('${tempDir.path}/$tempFileName');
      await tempFile.writeAsString(markdownText);

      // 使用 share_plus 显示系统分享对话框
      await SharePlus.instance.share(
        ShareParams(
          files: [XFile(tempFile.path)],
          text: 'Markdown 文件',
          subject: '导出 Markdown 内容',
        ),
      );

      return true;
    } catch (e) {
      debugPrint('保存 Markdown 文件失败: $e');
      return false;
    }
  }
}
