import 'package:flutter/material.dart';
import 'dart:convert';

import '../config/app_theme.dart';
import '../content/save_content_page.dart';
import '../generated/l10n/app_localizations.dart';
import '../models/content_item.dart';
import 'controllers/markdown_render_controller.dart';
import 'services/markdown_export_service.dart';
import 'widgets/markdown_actions_dialog.dart';
import 'widgets/markdown_render_tabs.dart';
import 'models/markdown_template.dart';
import 'models/markdown_render_style.dart';
import 'models/markdown_watermark.dart';
import '../services/service_locator.dart';
import '../subscription/subscription_screen.dart';
import '../config/constants.dart';

/// Markdown渲染屏幕
class MarkdownRenderScreen extends StatefulWidget {
  /// 初始Markdown文本
  final String? initialMarkdown;

  /// 初始模板ID（可选）
  final String? initialTemplateId;

  /// 初始样式配置（可选，JSON）
  final Map<String, dynamic>? initialStyleJson;

  /// 初始水印配置（可选，JSON）
  final Map<String, dynamic>? initialWatermarkJson;

  const MarkdownRenderScreen({
    super.key,
    this.initialMarkdown,
    this.initialTemplateId,
    this.initialStyleJson,
    this.initialWatermarkJson,
  });

  @override
  State<MarkdownRenderScreen> createState() => _MarkdownRenderScreenState();
}

class _MarkdownRenderScreenState extends State<MarkdownRenderScreen>
    with SingleTickerProviderStateMixin {
  /// 标签控制器
  late TabController _tabController;

  /// 渲染组件的全局键，用于截图
  final GlobalKey _renderKey = GlobalKey();

  /// 内容区域的全局键
  final GlobalKey _contentAreaKey = GlobalKey();

  /// 滚动控制器
  final ScrollController _scrollController = ScrollController();

  /// 控制器
  late MarkdownRenderController _controller;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 5, vsync: this); // 增加到5个标签页
    _controller = MarkdownRenderController(
      initialMarkdown: widget.initialMarkdown,
    );

    // 应用初始配置（模板/样式/水印）
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _applyInitialConfig();
    });

    // 延迟计算内容高度，确保渲染完成
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _calculateContentHeight();
    });
  }

  void _applyInitialConfig() {
    try {
      // 模板
      if (widget.initialTemplateId != null) {
        final list = MarkdownTemplate.getPredefinedTemplates();
        final base = list.firstWhere(
          (t) => t.id == widget.initialTemplateId,
          orElse: () => MarkdownTemplate.modern(),
        );
        _controller.updateTemplate(base);
      }

      // 样式
      if (widget.initialStyleJson != null) {
        final current = _controller.selectedTemplate.style;
        final s = widget.initialStyleJson!;
        final updated = current.copyWith(
          baseFontSize: (s['baseFontSize'] as num?)?.toDouble(),
          borderRadius: (s['borderRadius'] as num?)?.toDouble(),
          headingAlignment:
              s['headingAlignment'] != null
                  ? TextAlign.values[s['headingAlignment']]
                  : null,
          listItemStyle: s['listItemStyle'],
          checkboxUncheckedStyle: s['checkboxUncheckedStyle'],
          checkboxCheckedStyle: s['checkboxCheckedStyle'],
          fontFamily: s['fontFamily'],
          codeFontFamily: s['codeFontFamily'],
        );
        _controller.updateStyle(updated);
      }

      // 水印
      if (widget.initialWatermarkJson != null) {
        final w = widget.initialWatermarkJson!;
        final wm = MarkdownWatermark(
          text: w['text'] ?? '',
          textColor: Color(w['textColor'] ?? 0xFF9E9E9E),
          fontSize: (w['fontSize'] as num?)?.toDouble() ?? 12.0,
          fontFamily: w['fontFamily'] ?? 'Roboto',
          fontStyle:
              (w['fontStyle'] == 1) ? FontStyle.italic : FontStyle.normal,
          fontWeight:
              (w['fontWeight'] == 1) ? FontWeight.bold : FontWeight.normal,
          isVisible: w['isVisible'] ?? false,
          position:
              WatermarkPosition.values[w['position'] ??
                  WatermarkPosition.bottomCenter.index],
          opacity: (w['opacity'] as num?)?.toDouble() ?? 0.7,
        );
        _controller.updateWatermark(wm);
      }
    } catch (_) {
      // 忽略恢复错误
    }
  }

  /// 计算内容区域高度
  void _calculateContentHeight() {
    final RenderBox? renderBox =
        _contentAreaKey.currentContext?.findRenderObject() as RenderBox?;
    if (renderBox != null) {
      _controller.setContentHeight(renderBox.size.height);
    } else {
      _controller.setContentHeight(1000);
      // 延迟再次尝试计算
      WidgetsBinding.instance.addPostFrameCallback((_) {
        _calculateContentHeight();
      });
    }
  }

  @override
  void dispose() {
    // 安全处理ScrollController
    if (_scrollController.hasClients) {
      _scrollController.removeListener(() {});
    }
    _scrollController.dispose();
    _tabController.dispose();
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    return GestureDetector(
      onTap: () {
        // 点击空白处隐藏键盘
        FocusScope.of(context).unfocus();
      },
      child: Scaffold(
        backgroundColor: theme.scaffoldBackgroundColor,
        appBar: AppBar(
          title: Text(
            l10n.markdownTitle,
            style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 22),
          ),
          centerTitle: false,
          backgroundColor: theme.colorScheme.surface.withValues(alpha: 0.95),
          elevation: 0,
          shadowColor: Colors.transparent,
          surfaceTintColor: Colors.transparent,
          scrolledUnderElevation: 0.5,
          flexibleSpace: Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  theme.colorScheme.primary.withValues(alpha: 0.1),
                  theme.colorScheme.primary.withValues(alpha: 0.05),
                ],
              ),
            ),
          ),
          actions: [
            TextButton(
              onPressed: _handleSaveToLibrary,
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(
                    Icons.bookmark_border,
                    size: 20,
                    color: theme.colorScheme.primary,
                  ),
                  const SizedBox(width: 4),
                  Text(l10n.htmlSaveToLibrary),
                ],
              ),
            ),
            // 更多操作按钮
            Container(
              margin: const EdgeInsets.only(right: 8),
              decoration: BoxDecoration(
                color: AppTheme.blueGradient.colors.first.withValues(
                  alpha: 0.1,
                ),
                borderRadius: BorderRadius.circular(12),
              ),
              child: IconButton(
                icon: Icon(
                  Icons.more_vert,
                  color: AppTheme.blueGradient.colors.first,
                ),
                tooltip: l10n.markdownMoreActions,
                onPressed: () => _showActionsDialog(),
              ),
            ),
          ],
          bottom: PreferredSize(
            preferredSize: const Size.fromHeight(52),
            child: Container(
              decoration: BoxDecoration(
                color: theme.colorScheme.surface,
                boxShadow: [
                  if (!isDark)
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.05),
                      offset: const Offset(0, 2),
                      blurRadius: 4,
                    ),
                ],
                border: Border(
                  bottom: BorderSide(
                    color: theme.colorScheme.outlineVariant,
                    width: 0.5,
                  ),
                ),
              ),
              padding: const EdgeInsets.symmetric(horizontal: 8),
              child: AnimatedContainer(
                duration: const Duration(milliseconds: 200),
                child: TabBar(
                  controller: _tabController,
                  labelStyle: const TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 12,
                  ),
                  unselectedLabelStyle: const TextStyle(
                    fontWeight: FontWeight.w400,
                    fontSize: 12,
                  ),
                  labelColor: theme.colorScheme.onPrimary,
                  unselectedLabelColor: theme.colorScheme.onSurfaceVariant,
                  indicator: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                    gradient: LinearGradient(
                      colors: [
                        theme.colorScheme.primary,
                        theme.colorScheme.primaryContainer,
                      ],
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: theme.colorScheme.primary.withValues(alpha: 0.3),
                        offset: const Offset(0, 2),
                        blurRadius: 4,
                      ),
                    ],
                  ),
                  indicatorSize: TabBarIndicatorSize.tab,
                  indicatorPadding: const EdgeInsets.symmetric(
                    horizontal: 6,
                    vertical: 4,
                  ),
                  isScrollable: true,
                  tabAlignment: TabAlignment.start,
                  tabs: [
                    Tab(
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(Icons.edit, size: 14),
                          SizedBox(width: 2),
                          Flexible(
                            child: Text(
                              l10n.markdownEditTab,
                              overflow: TextOverflow.ellipsis,
                              maxLines: 1,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Tab(
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(Icons.description, size: 14),
                          SizedBox(width: 2),
                          Flexible(
                            child: Text(
                              l10n.markdownTemplateTab,
                              overflow: TextOverflow.ellipsis,
                              maxLines: 1,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Tab(
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(Icons.palette, size: 14),
                          SizedBox(width: 2),
                          Flexible(
                            child: Text(
                              l10n.markdownStyleTab,
                              overflow: TextOverflow.ellipsis,
                              maxLines: 1,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Tab(
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(Icons.branding_watermark, size: 14),
                          SizedBox(width: 2),
                          Flexible(
                            child: Text(
                              l10n.markdownWatermarkTab,
                              overflow: TextOverflow.ellipsis,
                              maxLines: 1,
                            ),
                          ),
                        ],
                      ),
                    ),
                    Tab(
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(Icons.view_module, size: 14),
                          SizedBox(width: 2),
                          Flexible(
                            child: Text(
                              l10n.markdownBlockTab,
                              overflow: TextOverflow.ellipsis,
                              maxLines: 1,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
        body: Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              colors: [
                theme.scaffoldBackgroundColor,
                theme.colorScheme.surface,
              ],
            ),
          ),
          child: ClipRRect(
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(0),
              topRight: Radius.circular(0),
            ),
            child: MarkdownRenderTabs(
              controller: _controller,
              renderKey: _renderKey,
              contentAreaKey: _contentAreaKey,
              scrollController: _scrollController,
              tabController: _tabController,
            ),
          ),
        ),
      ),
    );
  }

  /// 处理分享
  Future<void> _handleShare() async {
    await MarkdownExportService.handleShare(
      markdownText: _controller.markdownController.text,
      renderKey: _renderKey,
      context: context,
      tabController: _tabController,
      showSnackBar: _showSnackBar,
    );
  }

  /// 处理复制
  Future<void> _handleCopy() async {
    await MarkdownExportService.handleCopy(
      markdownText: _controller.markdownController.text,
      renderKey: _renderKey,
      showSnackBar: _showSnackBar,
    );
  }

  /// 处理保存
  Future<void> _handleSave() async {
    // 仅对“保存到相册”设置免费10次限制
    final subscriptionService = ServiceLocator().subscriptionService;
    final storage = ServiceLocator().storageService;
    final isPaid =
        subscriptionService.subscription.isActive &&
        subscriptionService.subscription.isPaid;

    if (!isPaid) {
      final used =
          storage.getInt(AppConstants.keyMarkdownExportToGalleryUsed) ?? 0;
      if (used >= 10) {
        // 超过免费次数，跳转订阅
        if (mounted) {
          Navigator.of(context).push(
            MaterialPageRoute(builder: (context) => const SubscriptionScreen()),
          );
        }
        return;
      } else {
        await storage.setInt(
          AppConstants.keyMarkdownExportToGalleryUsed,
          used + 1,
        );
      }
    }

    await MarkdownExportService.handleSave(
      markdownText: _controller.markdownController.text,
      renderKey: _renderKey,
      context: context,
      showSnackBar: _showSnackBar,
    );
  }

  Future<void> _handleSaveToLibrary() async {
    final l10n = AppLocalizations.of(context);
    final isBlockMode = _tabController.index == 4; // 第5个标签页为分块
    final json = _buildMarkdownLibraryJson(isBlockMode: isBlockMode);
    await Navigator.push(
      context,
      MaterialPageRoute(
        builder:
            (_) => SaveContentPage(
              initialTitle: l10n.markdownTitle,
              content: jsonEncode(json),
              contentType:
                  isBlockMode
                      ? ContentType.markdownBlocks
                      : ContentType.markdown,
              initialTags: isBlockMode ? ['markdown', 'blocks'] : ['markdown'],
            ),
      ),
    );
  }

  Map<String, dynamic> _buildMarkdownLibraryJson({required bool isBlockMode}) {
    final tpl = _controller.selectedTemplate;
    Map<String, dynamic> styleToJson(MarkdownRenderStyle s) => {
      'baseFontSize': s.baseFontSize,
      'borderRadius': s.borderRadius,
      'headingAlignment': s.headingAlignment.index,
      'listItemStyle': s.listItemStyle,
      'checkboxUncheckedStyle': s.checkboxUncheckedStyle,
      'checkboxCheckedStyle': s.checkboxCheckedStyle,
      'fontFamily': s.fontFamily,
      'codeFontFamily': s.codeFontFamily,
    };
    Map<String, dynamic> watermarkToJson(MarkdownWatermark w) => {
      'text': w.text,
      'textColor': w.textColor.toARGB32(),
      'fontSize': w.fontSize,
      'fontFamily': w.fontFamily,
      'fontStyle': w.fontStyle == FontStyle.italic ? 1 : 0,
      'fontWeight': w.fontWeight == FontWeight.bold ? 1 : 0,
      'isVisible': w.isVisible,
      'position': w.position.index,
      'opacity': w.opacity,
    };
    return {
      'type': isBlockMode ? 'markdownBlocks' : 'markdown',
      'content': _controller.markdownController.text,
      'templateId': tpl.id,
      'style': styleToJson(tpl.style),
      'watermark': watermarkToJson(tpl.watermark),
      'createdAt': DateTime.now().toIso8601String(),
      'version': '1.0',
    };
  }

  /// 显示提示消息
  void _showSnackBar(String message) {
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(SnackBar(content: Text(message)));
  }

  /// 显示操作对话框
  void _showActionsDialog() {
    showDialog(
      context: context,
      builder:
          (context) => MarkdownActionsDialog(
            onActionSelected: (action) async {
              switch (action) {
                case 'share':
                  await _handleShare();
                  break;
                case 'copy':
                  await _handleCopy();
                  break;
                case 'save':
                  // 确保在编辑标签页，以便 renderKey 能正确工作
                  if (_tabController.index != 0) {
                    _tabController.animateTo(0);
                    // 等待切换完成
                    await Future.delayed(const Duration(milliseconds: 300));
                  }
                  await _handleSave();
                  break;
              }
            },
          ),
    );
  }
}
