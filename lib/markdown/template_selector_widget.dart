import 'package:flutter/material.dart';
import '../../generated/l10n/app_localizations.dart';
import 'models/markdown_template.dart';


/// 模板选择器组件
class TemplateSelectorWidget extends StatelessWidget {
  /// 当前选中的模板
  final MarkdownTemplate selectedTemplate;

  /// 模板列表
  final List<MarkdownTemplate> templates;

  /// 模板选择回调
  final Function(MarkdownTemplate) onTemplateSelected;

  const TemplateSelectorWidget({
    super.key,
    required this.selectedTemplate,
    required this.templates,
    required this.onTemplateSelected,
  });

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0),
            child: Text(
              l10n.markdownSelectTemplate,
              style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
          ),
          SizedBox(
            height: 180,
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: templates.length,
              padding: const EdgeInsets.symmetric(horizontal: 12.0),
              itemBuilder: (context, index) {
                final template = templates[index];
                final isSelected = template.id == selectedTemplate.id;

                return GestureDetector(
                  onTap: () => onTemplateSelected(template),
                  child: Container(
                    width: 130,
                    margin: const EdgeInsets.symmetric(
                      horizontal: 10.0,
                      vertical: 8.0,
                    ),
                    decoration: BoxDecoration(
                      color: template.style.backgroundColor,
                      borderRadius: BorderRadius.circular(16),
                      border: Border.all(
                        color:
                            isSelected
                                ? Theme.of(context).primaryColor
                                : Colors.transparent,
                        width: 2.5,
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withAlpha(20),
                          blurRadius: 8,
                          offset: const Offset(0, 3),
                          spreadRadius: 1,
                        ),
                      ],
                      gradient:
                          template.style.useGradientBackground &&
                                  template.style.gradientColors != null
                              ? LinearGradient(
                                colors: template.style.gradientColors!,
                                begin: template.style.gradientBegin,
                                end: template.style.gradientEnd,
                              )
                              : null,
                      image: null, // 暂时禁用背景图片，直到资源可用
                    ),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Container(
                          width: 70,
                          height: 70,
                          decoration: BoxDecoration(
                            color: template.style.backgroundColor,
                            borderRadius: BorderRadius.circular(12),
                            border:
                                template.showBorder
                                    ? Border.all(
                                      color: template.borderColor,
                                      width: template.borderWidth,
                                    )
                                    : Border.all(
                                      color: template.style.textColor.withAlpha(
                                        25,
                                      ),
                                      width: 1,
                                    ),
                            boxShadow:
                                template.showShadow
                                    ? [
                                      BoxShadow(
                                        color: template.shadowColor.withAlpha(
                                          77,
                                        ),
                                        blurRadius: 4,
                                        offset: const Offset(0, 2),
                                      ),
                                    ]
                                    : null,
                            gradient:
                                template.style.useGradientBackground &&
                                        template.style.gradientColors != null
                                    ? LinearGradient(
                                      colors: template.style.gradientColors!,
                                      begin: template.style.gradientBegin,
                                      end: template.style.gradientEnd,
                                    )
                                    : null,
                            image: null, // 暂时禁用背景图片，直到资源可用
                          ),
                          child: Center(
                            child: Icon(
                              _getTemplateIcon(template.id),
                              color: template.style.textColor,
                              size: 32,
                            ),
                          ),
                        ),
                        const SizedBox(height: 8),
                        Text(
                          template.name,
                          style: TextStyle(
                            color: template.style.textColor,
                            fontSize: 14,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
          ),
          // 模板说明
          Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: 16.0,
              vertical: 8.0,
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 8),
                Card(
                  elevation: 0,
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                    side: BorderSide(color: Colors.grey.withAlpha(51)),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.all(12.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Icon(
                              _getTemplateIcon(selectedTemplate.id),
                              color: Theme.of(context).primaryColor,
                              size: 20,
                            ),
                            const SizedBox(width: 8),
                            Text(
                              selectedTemplate.name,
                              style: const TextStyle(
                                fontWeight: FontWeight.bold,
                                fontSize: 16,
                              ),
                            ),
                          ],
                        ),
                        const SizedBox(height: 8),
                        Text(
                          selectedTemplate.description,
                          style: TextStyle(
                            color: Colors.grey.shade700,
                            fontSize: 14,
                          ),
                        ),
                        const SizedBox(height: 8),
                        Wrap(
                          spacing: 8,
                          runSpacing: 8,
                          children: [
                            if (selectedTemplate.showBorder)
                              _buildFeatureChip(l10n.markdownBorderStyle, Icons.border_all),
                            if (selectedTemplate.showShadow)
                              _buildFeatureChip(l10n.markdownShadowEffect, Icons.blur_on),
                            if (selectedTemplate.showHeader)
                              _buildFeatureChip(l10n.markdownShowHeader, Icons.title),
                            if (selectedTemplate.showInnerShadow)
                              _buildFeatureChip(l10n.markdownInnerShadow, Icons.blur_circular),
                            _buildFeatureChip(
                              '${l10n.markdownHeadingAlignment} ${_getHeadingAlignmentText(selectedTemplate.style.headingAlignment)}',
                              Icons.format_align_center,
                            ),
                            if (selectedTemplate.style.useGradientBackground)
                              _buildFeatureChip(l10n.markdownGradientBackground, Icons.gradient),
                            if (selectedTemplate.style.backgroundImage != null)
                              _buildFeatureChip(l10n.markdownBackgroundPattern, Icons.image),
                            _buildFeatureChip(
                              '${l10n.markdownListItemStyle}: ${selectedTemplate.style.listItemStyle}',
                              Icons.list,
                            ),
                            _buildFeatureChip(
                              '${l10n.markdownCheckboxStyle}: ${selectedTemplate.style.checkboxUncheckedStyle}/${selectedTemplate.style.checkboxCheckedStyle}',
                              Icons.check_box_outlined,
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 获取标题对齐方式的文本描述
  String _getHeadingAlignmentText(TextAlign align) {
    switch (align) {
      case TextAlign.left:
        return '左对齐';
      case TextAlign.center:
        return '居中';
      case TextAlign.right:
        return '右对齐';
      default:
        return '左对齐';
    }
  }

  /// 构建特性标签
  Widget _buildFeatureChip(String label, IconData icon) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
      decoration: BoxDecoration(
        color: Colors.grey.withAlpha(26),
        borderRadius: BorderRadius.circular(20),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 14),
          const SizedBox(width: 4),
          Text(label, style: const TextStyle(fontSize: 12)),
        ],
      ),
    );
  }

  /// 根据模板ID获取对应的图标
  IconData _getTemplateIcon(String templateId) {
    switch (templateId) {
      case 'simple':
        return Icons.article_outlined;
      case 'modern':
        return Icons.style_outlined;
      case 'elegant':
        return Icons.format_quote_outlined;
      case 'code':
        return Icons.code_outlined;
      case 'card':
        return Icons.credit_card_outlined;
      case 'morandi':
        return Icons.invert_colors_outlined;
      case 'chinese_blue_white':
        return Icons.water_outlined;
      case 'chinese_vermilion':
        return Icons.palette_outlined;
      case 'gradient_purple':
        return Icons.gradient;
      case 'festive_red':
        return Icons.celebration_outlined;
      case 'bamboo_slip':
        return Icons.view_stream_outlined;
      default:
        return Icons.article_outlined;
    }
  }
}
