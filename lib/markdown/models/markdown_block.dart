/// Markdown 分块数据模型
library;

/// 分块分隔符类型
enum BlockSeparatorType {
  /// 自定义分隔符（如连续短横线）
  custom,

  /// 一级标题
  h1,

  /// 二级标题
  h2,

  /// 手动分隔（用户拖拽操纵杆创建）
  manual,
}

/// Markdown 分块
class MarkdownBlock {
  /// 分块唯一标识
  final String id;

  /// 分块内容
  final String content;

  /// 分块在原始文本中的起始位置
  final int startPosition;

  /// 分块在原始文本中的结束位置
  final int endPosition;

  /// 分块索引
  final int index;

  /// 分块标题（从内容中提取）
  final String title;

  /// 分隔符类型
  final BlockSeparatorType separatorType;

  /// 是否可见
  final bool isVisible;

  /// 创建时间
  final DateTime createdAt;

  const MarkdownBlock({
    required this.id,
    required this.content,
    required this.startPosition,
    required this.endPosition,
    required this.index,
    required this.title,
    required this.separatorType,
    this.isVisible = true,
    required this.createdAt,
  });

  /// 复制并修改属性
  MarkdownBlock copyWith({
    String? id,
    String? content,
    int? startPosition,
    int? endPosition,
    int? index,
    String? title,
    BlockSeparatorType? separatorType,
    bool? isVisible,
    DateTime? createdAt,
  }) {
    return MarkdownBlock(
      id: id ?? this.id,
      content: content ?? this.content,
      startPosition: startPosition ?? this.startPosition,
      endPosition: endPosition ?? this.endPosition,
      index: index ?? this.index,
      title: title ?? this.title,
      separatorType: separatorType ?? this.separatorType,
      isVisible: isVisible ?? this.isVisible,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  /// 从 JSON 创建
  factory MarkdownBlock.fromJson(Map<String, dynamic> json) {
    return MarkdownBlock(
      id: json['id'] as String,
      content: json['content'] as String,
      startPosition: json['startPosition'] as int,
      endPosition: json['endPosition'] as int,
      index: json['index'] as int,
      title: json['title'] as String,
      separatorType: BlockSeparatorType.values[json['separatorType'] as int],
      isVisible: json['isVisible'] as bool? ?? true,
      createdAt: DateTime.parse(json['createdAt'] as String),
    );
  }

  /// 转换为 JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'content': content,
      'startPosition': startPosition,
      'endPosition': endPosition,
      'index': index,
      'title': title,
      'separatorType': separatorType.index,
      'isVisible': isVisible,
      'createdAt': createdAt.toIso8601String(),
    };
  }

  @override
  String toString() {
    return 'MarkdownBlock(id: $id, title: $title, index: $index, separatorType: $separatorType)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is MarkdownBlock && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

// 移除了 BlockDivider 类，不再需要操纵杆功能

/// 简化的分块渲染配置
class BlockRenderConfig {
  /// 是否启用分块渲染
  final bool enabled;

  /// 分块模式
  final BlockMode mode;

  /// 是否按一级标题分隔
  final bool splitByH1;

  /// 是否按二级标题分隔
  final bool splitByH2;

  /// 分块之间的间距
  final double blockSpacing;

  /// 是否显示分块边框
  final bool showBlockBorders;

  /// 是否显示分块标题（用于UI展示）
  final bool showBlockTitles;

  /// 自定义分隔符模式
  final String customSeparatorPattern;

  const BlockRenderConfig({
    this.enabled = true,
    this.mode = BlockMode.auto,
    this.splitByH1 = true,
    this.splitByH2 = false,
    this.blockSpacing = 16.0,
    this.showBlockBorders = true,
    this.showBlockTitles = true,
    this.customSeparatorPattern = '',
  });

  /// 复制并修改属性
  BlockRenderConfig copyWith({
    bool? enabled,
    BlockMode? mode,
    bool? splitByH1,
    bool? splitByH2,
    double? blockSpacing,
    bool? showBlockBorders,
    bool? showBlockTitles,
    String? customSeparatorPattern,
  }) {
    return BlockRenderConfig(
      enabled: enabled ?? this.enabled,
      mode: mode ?? this.mode,
      splitByH1: splitByH1 ?? this.splitByH1,
      splitByH2: splitByH2 ?? this.splitByH2,
      blockSpacing: blockSpacing ?? this.blockSpacing,
      showBlockBorders: showBlockBorders ?? this.showBlockBorders,
      showBlockTitles: showBlockTitles ?? this.showBlockTitles,
      customSeparatorPattern:
          customSeparatorPattern ?? this.customSeparatorPattern,
    );
  }

  /// 获取有效的分隔符模式
  String get effectiveSeparatorPattern {
    if (customSeparatorPattern.isNotEmpty) {
      // 将简单符号转换为正则表达式
      switch (customSeparatorPattern) {
        case '---':
          return r'^\s*---+\s*.*';
        case '===':
          return r'^\s*===+\s*.*';
        case '***':
          return r'^\s*\*{3,}\s*.*';
        case '>>>':
          return r'^\s*>>>{3,}\s*.*';
        case '###':
          return r'^\s*#{3,}\s*.*';
        default:
          return customSeparatorPattern;
      }
    }
    return '';
  }
}

/// 分块模式
enum BlockMode {
  /// 自动模式：按标题和分隔符自动分块
  auto,

  /// 仅按标题分块
  headings,

  /// 仅按分隔符分块
  separators,

  /// 手动模式：不自动分块
  manual,
}
