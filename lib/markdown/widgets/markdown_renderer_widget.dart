import 'package:flutter/material.dart';

import '../../services/simple_markdown_renderer.dart';
import '../models/markdown_render_style.dart';
import '../models/markdown_template.dart';
import '../models/markdown_watermark.dart';

/// Markdown渲染组件
class MarkdownRendererWidget extends StatelessWidget {
  /// Markdown文本内容
  final String markdownText;

  /// 渲染模板
  final MarkdownTemplate template;

  /// 日期（可选）
  final DateTime? date;

  /// 是否可选择文本
  final bool selectable;

  /// 渲染组件的全局键，用于截图
  final GlobalKey? renderKey;

  /// 最大高度
  final double? maxHeight;

  /// 是否处于分块模式
  final bool isBlockMode;

  const MarkdownRendererWidget({
    super.key,
    required this.markdownText,
    required this.template,
    this.date,
    this.selectable = false,
    this.renderKey,
    this.maxHeight,
    this.isBlockMode = false,
  });

  @override
  Widget build(BuildContext context) {
    final style = template.style;
    final watermark = template.watermark;

    // 验证和清理 Markdown 内容
    String cleanedMarkdownText = _validateAndCleanMarkdown(markdownText);

    // 添加调试信息
    debugPrint('MarkdownRendererWidget build: 列表符号="${style.listItemStyle}"');
    debugPrint('Original markdown length: ${markdownText.length}');
    debugPrint('Cleaned markdown length: ${cleanedMarkdownText.length}');

    Widget contentWidget = RepaintBoundary(
      key: renderKey,
      child: Container(
        width: double.infinity,
        decoration: BoxDecoration(
          color: style.backgroundColor,
          borderRadius: BorderRadius.circular(style.borderRadius),
          border:
              template.showBorder
                  ? Border.all(
                    color: template.borderColor,
                    width: template.borderWidth,
                  )
                  : null,
          boxShadow:
              template.showShadow
                  ? [
                    BoxShadow(
                      color: template.shadowColor,
                      offset: template.shadowOffset,
                      blurRadius: template.shadowBlurRadius,
                      spreadRadius: template.shadowBlurRadius * 0.3,
                    ),
                  ]
                  : null,
        ),
        child: Padding(
          padding: style.padding,
          child: Stack(
            children: [
              // 内容层
              Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 内容
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 24,
                      vertical: 20,
                    ),
                    decoration: BoxDecoration(
                      color: style.backgroundColor,
                      borderRadius: BorderRadius.circular(
                        style.borderRadius * template.contentRadiusRatio,
                      ),
                      boxShadow:
                          template.showInnerShadow
                              ? [
                                BoxShadow(
                                  color: Colors.black.withValues(alpha: 0.03),
                                  blurRadius: 8,
                                  offset: const Offset(0, 2),
                                  spreadRadius: 0,
                                ),
                              ]
                              : null,
                    ),
                    child: _buildMarkdownContent(cleanedMarkdownText, style),
                  ),

                  // 非平铺水印仍然放在底部
                  if (watermark.isVisible &&
                      watermark.position != WatermarkPosition.tiled)
                    _buildSingleWatermark(watermark),
                ],
              ),

              // 平铺水印层 - 覆盖在内容上方
              if (watermark.isVisible &&
                  watermark.position == WatermarkPosition.tiled)
                _buildTiledWatermark(watermark),
            ],
          ),
        ),
      ),
    );

    // 如果设置了最大高度，添加高度限制
    if (maxHeight != null) {
      contentWidget = ConstrainedBox(
        constraints: BoxConstraints(maxHeight: maxHeight!),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(style.borderRadius),
          child: SingleChildScrollView(
            physics: const ClampingScrollPhysics(),
            child: contentWidget,
          ),
        ),
      );
    }

    return contentWidget;
  }

  /// 验证和清理 Markdown 内容
  String _validateAndCleanMarkdown(String markdown) {
    // 如果内容为空，返回默认内容
    if (markdown.trim().isEmpty) {
      return _localizedEmptyMarkdown();
    }

    // 移除可能导致问题的特殊字符
    String cleaned = markdown
        .replaceAll('\r\n', '\n') // 统一换行符
        .replaceAll('\r', '\n') // 统一换行符
        .replaceAll('\u0000', '') // 移除null字符
        .replaceAll('\uFEFF', ''); // 移除BOM字符

    // 确保内容以换行符结尾
    if (!cleaned.endsWith('\n')) {
      cleaned += '\n';
    }

    // 如果清理后内容为空，返回默认内容
    if (cleaned.trim().isEmpty) {
      return _localizedEmptyMarkdown();
    }

    return cleaned;
  }

  String _currentLanguageCode() {
    try {
      return WidgetsBinding.instance.platformDispatcher.locale.languageCode;
    } catch (_) {
      return 'zh';
    }
  }

  String _localizedEmptyMarkdown() {
    final code = _currentLanguageCode();
    switch (code) {
      case 'en':
        return '# Empty Document\n\nThis is an empty Markdown document.';
      case 'ja':
        return '# 空のドキュメント\n\nこれは空のMarkdownドキュメントです。';
      case 'zh':
      default:
        return '# 空文档\n\n这是一个空的Markdown文档。';
    }
  }

  /// 构建 Markdown 内容组件
  Widget _buildMarkdownContent(String content, MarkdownRenderStyle style) {
    final TextStyle baseStyle = TextStyle(
      color: style.textColor,
      fontSize: style.baseFontSize,
      fontFamily: style.fontFamily,
      height: 1.6,
    );

    // 使用我们的简单Markdown渲染器，完全避免flutter_markdown的问题
    return SimpleMarkdownRenderer(
      markdownText: content,
      baseTextStyle: baseStyle,
      selectable: selectable,
      isBlockMode: isBlockMode,
    );
  }

  /// 构建平铺水印
  Widget _buildTiledWatermark(MarkdownWatermark watermark) {
    return Positioned.fill(
      child: IgnorePointer(
        child: ClipRect(
          child: LayoutBuilder(
            builder: (context, constraints) {
              // 根据容器大小计算实际需要的行数和列数
              final containerWidth = constraints.maxWidth;
              final containerHeight = constraints.maxHeight;

              // 计算实际需要的行数和列数，确保覆盖整个区域
              final effectiveColumns =
                  (containerWidth / watermark.tileHorizontalGap).ceil() + 1;
              final effectiveRows =
                  (containerHeight / watermark.tileVerticalGap).ceil() + 1;

              return Stack(
                children: List.generate(effectiveRows * effectiveColumns, (
                  index,
                ) {
                  final row = index ~/ effectiveColumns;
                  final col = index % effectiveColumns;

                  // 计算水印位置，添加一些随机偏移使其看起来更自然
                  final xOffset = col * watermark.tileHorizontalGap;
                  final yOffset = row * watermark.tileVerticalGap;

                  return Positioned(
                    left: xOffset,
                    top: yOffset,
                    child: Opacity(
                      opacity: watermark.opacity,
                      child: Transform.rotate(
                        angle: watermark.rotation,
                        child: Text(
                          watermark.text,
                          style: TextStyle(
                            fontSize: watermark.fontSize,
                            color: watermark.textColor,
                            fontFamily: watermark.fontFamily,
                            fontStyle: watermark.fontStyle,
                            fontWeight: watermark.fontWeight,
                          ),
                        ),
                      ),
                    ),
                  );
                }),
              );
            },
          ),
        ),
      ),
    );
  }

  /// 构建单个水印
  Widget _buildSingleWatermark(MarkdownWatermark watermark) {
    return Padding(
      padding: const EdgeInsets.only(top: 20),
      child: Align(
        alignment: watermark.position.alignment,
        child: Opacity(
          opacity: watermark.opacity,
          child: Transform.rotate(
            angle: watermark.rotation,
            child: Text(
              watermark.text,
              style: TextStyle(
                fontSize: watermark.fontSize,
                color: watermark.textColor,
                fontFamily: watermark.fontFamily,
                fontStyle: watermark.fontStyle,
                fontWeight: watermark.fontWeight,
              ),
            ),
          ),
        ),
      ),
    );
  }
}
