import 'package:flutter/material.dart';

import '../controllers/markdown_render_controller.dart';
import '../controllers/markdown_block_controller.dart';
import '../models/markdown_render_style.dart';
import '../models/markdown_template.dart';
import '../models/markdown_block.dart';
import '../services/markdown_export_service.dart';
import '../style_selector_widget.dart';
import '../template_selector_widget.dart';
import '../watermark_settings_widget.dart';
import 'markdown_renderer_widget.dart';
import 'markdown_block_renderer.dart';
import '../../generated/l10n/app_localizations.dart';

/// Markdown渲染标签页组件
class MarkdownRenderTabs extends StatefulWidget {
  final MarkdownRenderController controller;
  final GlobalKey renderKey;
  final GlobalKey contentAreaKey;
  final ScrollController scrollController;
  final TabController tabController;

  const MarkdownRenderTabs({
    super.key,
    required this.controller,
    required this.renderKey,
    required this.contentArea<PERSON><PERSON>,
    required this.scrollController,
    required this.tabController,
  });

  @override
  State<MarkdownRenderTabs> createState() => _MarkdownRenderTabsState();
}

class _MarkdownRenderTabsState extends State<MarkdownRenderTabs> {
  /// 分块渲染控制器
  late final MarkdownBlockController _blockController;

  /// 是否启用分块模式
  bool _isBlockModeEnabled = false;

  @override
  void initState() {
    super.initState();
    _blockController = MarkdownBlockController();

    // 监听原始控制器的文本变化
    widget.controller.addListener(_onMarkdownTextChanged);

    // 初始化分块控制器
    _initializeBlockController();
  }

  @override
  void dispose() {
    widget.controller.removeListener(_onMarkdownTextChanged);
    _blockController.dispose();
    super.dispose();
  }

  /// 初始化分块控制器
  void _initializeBlockController() {
    _blockController.initialize(
      markdownText: widget.controller.markdownController.text,
      config: const BlockRenderConfig(enabled: false),
    );
  }

  /// 处理 Markdown 文本变化
  void _onMarkdownTextChanged() {
    _blockController.updateMarkdownText(
      widget.controller.markdownController.text,
    );
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: Listenable.merge([widget.controller, _blockController]),
      builder: (context, child) {
        return TabBarView(
          controller: widget.tabController,
          children: [
            _buildEditTab(),
            _buildTemplateTab(),
            _buildStyleTab(),
            _buildWatermarkTab(),
            _buildBlockTab(), // 新增分块标签页
          ],
        );
      },
    );
  }

  /// 构建编辑标签页
  Widget _buildEditTab() {
    return Column(children: [Expanded(child: _buildContentArea())]);
  }

  /// 构建内容区域
  Widget _buildContentArea() {
    final l10n = AppLocalizations.of(context);
    return SingleChildScrollView(
      controller: widget.scrollController,
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Markdown输入
          TextField(
            controller: widget.controller.markdownController,
            decoration: InputDecoration(
              labelText: l10n.markdownContentLabel,
              border: const OutlineInputBorder(),
              alignLabelWithHint: true,
            ),
            maxLines: 10,
            onChanged: (_) => widget.controller.triggerUpdate(),
          ),
          const SizedBox(height: 24),

          // 预览
          Text(
            l10n.commonPreview,
            style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 16),
          Center(
            child: Container(
              key: widget.contentAreaKey,
              child:
                  _isBlockModeEnabled
                      ? MarkdownBlockRenderer(
                        renderKey: widget.renderKey,
                        controller: _blockController,
                        template: widget.controller.selectedTemplate,
                        selectable: true,
                      )
                      : MarkdownRendererWidget(
                        renderKey: widget.renderKey,
                        markdownText: widget.controller.markdownController.text,
                        template: widget.controller.selectedTemplate,
                        selectable: true,
                      ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建模板标签页
  Widget _buildTemplateTab() {
    final l10n = AppLocalizations.of(context);
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          TemplateSelectorWidget(
            selectedTemplate: widget.controller.selectedTemplate,
            templates: MarkdownTemplate.getPredefinedTemplates(),
            onTemplateSelected: widget.controller.updateTemplate,
          ),
          const SizedBox(height: 24),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Text(
              l10n.commonPreview,
              style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
          ),
          const SizedBox(height: 16),
          Center(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child:
                  _isBlockModeEnabled
                      ? MarkdownBlockRenderer(
                        controller: _blockController,
                        template: widget.controller.selectedTemplate,
                        selectable: false,
                      )
                      : MarkdownRendererWidget(
                        markdownText: widget.controller.markdownController.text,
                        template: widget.controller.selectedTemplate,
                        selectable: false,
                      ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建样式标签页
  Widget _buildStyleTab() {
    final l10n = AppLocalizations.of(context);
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          StyleSelectorWidget(
            selectedStyle: widget.controller.selectedTemplate.style,
            styles: MarkdownRenderStyle.getPredefinedStyles(),
            onStyleSelected: widget.controller.updateStyle,
            onStyleUpdated: widget.controller.updateStyle,
          ),
          const SizedBox(height: 24),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Text(
              l10n.commonPreview,
              style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
          ),
          const SizedBox(height: 16),
          Center(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child:
                  _isBlockModeEnabled
                      ? MarkdownBlockRenderer(
                        controller: _blockController,
                        template: widget.controller.selectedTemplate,
                        selectable: false,
                      )
                      : MarkdownRendererWidget(
                        markdownText: widget.controller.markdownController.text,
                        template: widget.controller.selectedTemplate,
                        selectable: false,
                      ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建水印标签页
  Widget _buildWatermarkTab() {
    final l10n = AppLocalizations.of(context);
    return SingleChildScrollView(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          WatermarkSettingsWidget(
            watermark: widget.controller.selectedTemplate.watermark,
            onWatermarkUpdated: widget.controller.updateWatermark,
          ),
          const SizedBox(height: 24),
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Text(
              l10n.commonPreview,
              style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
          ),
          const SizedBox(height: 16),
          Center(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child:
                  _isBlockModeEnabled
                      ? MarkdownBlockRenderer(
                        controller: _blockController,
                        template: widget.controller.selectedTemplate,
                        selectable: false,
                      )
                      : MarkdownRendererWidget(
                        markdownText: widget.controller.markdownController.text,
                        template: widget.controller.selectedTemplate,
                        selectable: false,
                      ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建分块标签页
  Widget _buildBlockTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 智能配置区域
          _buildSmartConfigSection(),
          const SizedBox(height: 24),

          // 实时预览区域
          _buildPreviewSection(),

          // 导出选项区域
          if (_isBlockModeEnabled) _buildExportSection(),
        ],
      ),
    );
  }

  /// 构建智能配置区域
  Widget _buildSmartConfigSection() {
    final l10n = AppLocalizations.of(context);
    final lang = Localizations.localeOf(context).languageCode.toLowerCase();
    String quickSettings;
    if (lang.startsWith('ja')) {
      quickSettings = 'クイック設定';
    } else if (lang.startsWith('zh')) {
      quickSettings = '快速配置';
    } else {
      quickSettings = 'Quick Settings';
    }
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // 标题和开关
            Row(
              children: [
                Icon(
                  Icons.view_module,
                  color: Theme.of(context).primaryColor,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Text(
                  l10n.markdownEnableBlockRender,
                  style: const TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                Switch(
                  value: _isBlockModeEnabled,
                  onChanged: (value) {
                    setState(() {
                      _isBlockModeEnabled = value;
                    });
                    _blockController.updateConfig(
                      _blockController.config.copyWith(enabled: value),
                    );
                  },
                ),
              ],
            ),
            const SizedBox(height: 16),

            // 预设配置
            if (_isBlockModeEnabled) ...[
              Text(
                quickSettings,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const SizedBox(height: 12),
              _buildPresetConfigs(),
              const SizedBox(height: 16),

              // 高级设置
              ExpansionTile(
                title: Text(
                  l10n.trafficGuideAdvancedSettings,
                  style: const TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                children: [_buildAdvancedSettings()],
              ),
            ] else ...[
              // 未启用时的提示和使用指南
              _buildUsageGuide(),
            ],
          ],
        ),
      ),
    );
  }

  /// 构建预设配置
  Widget _buildPresetConfigs() {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final presets = [
      {
        'name': '文章模式',
        'description': '按标题自动分块，适合长文章',
        'icon': Icons.article,
        'config': const BlockRenderConfig(
          enabled: true,
          mode: BlockMode.headings,
          splitByH1: true,
          splitByH2: true,
          blockSpacing: 16.0,
          showBlockBorders: false,
        ),
      },
      {
        'name': '卡片模式',
        'description': '按分隔符分块，适合卡片式内容',
        'icon': Icons.view_carousel,
        'config': const BlockRenderConfig(
          enabled: true,
          mode: BlockMode.separators,
          customSeparatorPattern: '---',
          blockSpacing: 20.0,
          showBlockBorders: true,
        ),
      },
      {
        'name': '混合模式',
        'description': '同时支持标题和分隔符分块',
        'icon': Icons.auto_awesome,
        'config': const BlockRenderConfig(
          enabled: true,
          mode: BlockMode.auto,
          splitByH1: true,
          splitByH2: false,
          customSeparatorPattern: '---',
          blockSpacing: 18.0,
          showBlockBorders: true,
        ),
      },
    ];

    return Wrap(
      spacing: 12,
      runSpacing: 12,
      children:
          presets.map((preset) {
            final isSelected = _isConfigMatch(
              preset['config'] as BlockRenderConfig,
            );
            return InkWell(
              onTap: () {
                final newConfig = preset['config'] as BlockRenderConfig;
                setState(() {
                  _isBlockModeEnabled = newConfig.enabled;
                });
                _blockController.updateConfig(newConfig);
              },
              borderRadius: BorderRadius.circular(12),
              child: Container(
                width: 160,
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color:
                      isSelected
                          ? colorScheme.primary.withValues(alpha: 0.08)
                          : colorScheme.surface,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color:
                        isSelected
                            ? colorScheme.primary
                            : colorScheme.outlineVariant,
                    width: isSelected ? 2 : 1,
                  ),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(
                          preset['icon'] as IconData,
                          color:
                              isSelected
                                  ? colorScheme.primary
                                  : colorScheme.onSurfaceVariant,
                          size: 20,
                        ),
                        const SizedBox(width: 8),
                        Expanded(
                          child: Text(
                            preset['name'] as String,
                            style: TextStyle(
                              fontWeight: FontWeight.w600,
                              color:
                                  isSelected
                                      ? colorScheme.primary
                                      : colorScheme.onSurface,
                            ),
                          ),
                        ),
                        if (isSelected)
                          Icon(
                            Icons.check_circle,
                            color: colorScheme.primary,
                            size: 16,
                          ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      preset['description'] as String,
                      style: TextStyle(
                        fontSize: 12,
                        color: colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ],
                ),
              ),
            );
          }).toList(),
    );
  }

  /// 构建高级设置
  Widget _buildAdvancedSettings() {
    final l10n = AppLocalizations.of(context);
    return Column(
      children: [
        // 分隔符设置
        Text(
          l10n.markdownSeparatorSettings,
          style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
        ),
        const SizedBox(height: 8),
        CheckboxListTile(
          title: Text(l10n.markdownSplitByH1),
          value: _blockController.config.splitByH1,
          onChanged: (value) {
            _blockController.updateConfig(
              _blockController.config.copyWith(splitByH1: value ?? false),
            );
          },
          dense: true,
        ),
        CheckboxListTile(
          title: Text(l10n.markdownSplitByH2),
          value: _blockController.config.splitByH2,
          onChanged: (value) {
            _blockController.updateConfig(
              _blockController.config.copyWith(splitByH2: value ?? false),
            );
          },
          dense: true,
        ),
        const SizedBox(height: 16),

        // 外观设置
        Text(
          l10n.markdownAppearanceSettings,
          style: const TextStyle(fontSize: 14, fontWeight: FontWeight.w500),
        ),
        const SizedBox(height: 8),
        ListTile(
          title: Text(l10n.markdownBlockSpacing),
          subtitle: Slider(
            value: _blockController.config.blockSpacing,
            min: 8.0,
            max: 32.0,
            divisions: 6,
            label: '${_blockController.config.blockSpacing.round()}px',
            onChanged: (value) {
              _blockController.updateConfig(
                _blockController.config.copyWith(blockSpacing: value),
              );
            },
          ),
          dense: true,
        ),

        CheckboxListTile(
          title: const Text('显示分块边框'),
          value: _blockController.config.showBlockBorders,
          onChanged: (value) {
            _blockController.updateConfig(
              _blockController.config.copyWith(
                showBlockBorders: value ?? false,
              ),
            );
          },
          dense: true,
        ),
      ],
    );
  }

  /// 构建使用指南
  Widget _buildUsageGuide() {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // 基本说明
        Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            color: colorScheme.surfaceContainerLow,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Row(
            children: [
              Icon(
                Icons.info_outline,
                color: colorScheme.onSurfaceVariant,
                size: 20,
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  '启用智能分块后，系统将自动识别标题和分隔符，将内容分割成独立的卡片',
                  style: TextStyle(
                    color: colorScheme.onSurfaceVariant,
                    fontSize: 14,
                  ),
                ),
              ),
            ],
          ),
        ),
        const SizedBox(height: 16),

        // 使用指南
        const Text(
          '使用指南',
          style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
        ),
        const SizedBox(height: 12),

        _buildGuideItem(
          icon: Icons.article,
          title: '文章模式',
          description: '适合长文章，按 # 和 ## 标题自动分块',
          example: '# 第一章\n内容...\n\n## 1.1 小节\n内容...',
        ),
        const SizedBox(height: 8),

        _buildGuideItem(
          icon: Icons.view_carousel,
          title: '卡片模式',
          description: '适合卡片式内容，使用分隔符分块',
          example: '第一张卡片内容\n\n---\n\n第二张卡片内容',
        ),
        const SizedBox(height: 8),

        _buildGuideItem(
          icon: Icons.auto_awesome,
          title: '混合模式',
          description: '同时支持标题和分隔符，灵活分块',
          example: '# 标题\n内容...\n\n---\n\n## 子标题\n内容...',
        ),
        const SizedBox(height: 16),

        // 提示信息
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: colorScheme.primary.withValues(alpha: 0.08),
            borderRadius: BorderRadius.circular(8),
            border: Border.all(
              color: colorScheme.primary.withValues(alpha: 0.3),
            ),
          ),
          child: Row(
            children: [
              Icon(
                Icons.lightbulb_outline,
                color: colorScheme.primary,
                size: 18,
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  '提示：启用分块后，您可以在下方实时预览分块效果，并可以单独管理每个分块的显示状态',
                  style: TextStyle(color: colorScheme.primary, fontSize: 13),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// 构建指南项
  Widget _buildGuideItem({
    required IconData icon,
    required String title,
    required String description,
    required String example,
  }) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: colorScheme.surfaceContainerLow,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: colorScheme.outlineVariant),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, size: 18, color: colorScheme.onSurfaceVariant),
              const SizedBox(width: 8),
              Text(
                title,
                style: const TextStyle(
                  fontWeight: FontWeight.w600,
                  fontSize: 14,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            description,
            style: TextStyle(fontSize: 12, color: colorScheme.onSurfaceVariant),
          ),
          const SizedBox(height: 8),
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: colorScheme.surfaceContainerHigh,
              borderRadius: BorderRadius.circular(4),
            ),
            child: Text(
              example,
              style: TextStyle(
                fontSize: 11,
                fontFamily: 'monospace',
                color: colorScheme.onSurface,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建预览区域
  Widget _buildPreviewSection() {
    final l10n = AppLocalizations.of(context);
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 预览标题
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: colorScheme.primary.withValues(alpha: 0.06),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                Icon(Icons.preview, color: colorScheme.primary, size: 20),
                const SizedBox(width: 8),
                Text(
                  l10n.commonPreview,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const Spacer(),
                if (_isBlockModeEnabled) ...[
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: colorScheme.primary.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      l10n.markdownBlockCount(
                        _blockController.visibleBlocks.length,
                      ),
                      style: TextStyle(
                        fontSize: 12,
                        color: colorScheme.primary,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ],
            ),
          ),

          // 预览内容
          Container(
            key: widget.contentAreaKey,
            padding: const EdgeInsets.all(20),
            child:
                _isBlockModeEnabled
                    ? MarkdownBlockRenderer(
                      renderKey: widget.renderKey,
                      controller: _blockController,
                      template: widget.controller.selectedTemplate,
                      selectable: true,
                    )
                    : MarkdownRendererWidget(
                      renderKey: widget.renderKey,
                      markdownText: widget.controller.markdownController.text,
                      template: widget.controller.selectedTemplate,
                      selectable: true,
                    ),
          ),
        ],
      ),
    );
  }

  /// 构建导出区域
  Widget _buildExportSection() {
    final l10n = AppLocalizations.of(context);
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 导出标题
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: colorScheme.secondary.withValues(alpha: 0.08),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                Icon(Icons.download, color: colorScheme.secondary, size: 20),
                const SizedBox(width: 8),
                Text(
                  l10n.markdownExportOptions,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),

          // 导出按钮
          Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              children: [
                // 第一行：主要导出选项
                Row(
                  children: [
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: () => _exportAsMarkdown(),
                        icon: const Icon(Icons.description, size: 18),
                        label: const Text('Markdown'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: colorScheme.secondary,
                          foregroundColor: colorScheme.onSecondary,
                          padding: const EdgeInsets.symmetric(vertical: 12),
                        ),
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: () => _exportAsImage(),
                        icon: const Icon(Icons.image, size: 18),
                        label: Text(l10n.markdownExportAsImage),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: colorScheme.primary,
                          foregroundColor: colorScheme.onPrimary,
                          padding: const EdgeInsets.symmetric(vertical: 12),
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 检查配置是否匹配
  bool _isConfigMatch(BlockRenderConfig config) {
    final current = _blockController.config;
    return current.enabled == config.enabled &&
        current.mode == config.mode &&
        current.splitByH1 == config.splitByH1 &&
        current.splitByH2 == config.splitByH2 &&
        current.blockSpacing == config.blockSpacing &&
        current.showBlockBorders == config.showBlockBorders &&
        current.customSeparatorPattern == config.customSeparatorPattern;
  }

  /// 导出为 Markdown
  void _exportAsMarkdown() async {
    final lang = Localizations.localeOf(context).languageCode.toLowerCase();
    final combinedText = _blockController.getCombinedMarkdown();
    if (combinedText.isEmpty) {
      _showSnackBar(
        lang.startsWith('ja')
            ? 'エクスポートする内容がありません'
            : lang.startsWith('zh')
            ? '没有内容可导出'
            : 'No content to export',
      );
      return;
    }

    try {
      // 使用系统文件保存对话框
      final result = await MarkdownExportService.handleSaveMarkdown(
        markdownText: combinedText,
        context: context,
        showSnackBar: _showSnackBar,
      );

      if (result) {
        _showSnackBar(
          lang.startsWith('ja')
              ? 'Markdown ファイルを保存しました'
              : lang.startsWith('zh')
              ? 'Markdown 文件保存成功'
              : 'Markdown file saved successfully',
        );
      } else {
        _showSnackBar(
          lang.startsWith('ja')
              ? '保存に失敗しました'
              : lang.startsWith('zh')
              ? '保存失败'
              : 'Save failed',
        );
      }
    } catch (e) {
      _showSnackBar(
        lang.startsWith('ja')
            ? 'エクスポートに失敗しました: $e'
            : lang.startsWith('zh')
            ? '导出失败: $e'
            : 'Export failed: $e',
      );
    }
  }

  /// 导出为图片
  void _exportAsImage() async {
    await MarkdownExportService.handleSave(
      markdownText: _blockController.getCombinedMarkdown(),
      renderKey: widget.renderKey,
      context: context,
      showSnackBar: _showSnackBar,
    );
  }

  /// 显示提示消息
  void _showSnackBar(String message) {
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(SnackBar(content: Text(message)));
  }
}
