import 'package:flutter/material.dart';

import '../controllers/markdown_block_controller.dart';
import '../models/markdown_block.dart';
import '../models/markdown_template.dart';

import '../utils/markdown_block_utils.dart';

/// Markdown 分块管理面板
class MarkdownBlockManagerPanel extends StatefulWidget {
  /// 分块控制器
  final MarkdownBlockController controller;

  /// 渲染模板
  final MarkdownTemplate template;

  const MarkdownBlockManagerPanel({
    super.key,
    required this.controller,
    required this.template,
  });

  @override
  State<MarkdownBlockManagerPanel> createState() =>
      _MarkdownBlockManagerPanelState();
}

class _MarkdownBlockManagerPanelState extends State<MarkdownBlockManagerPanel> {
  /// 选中的分块ID列表
  final Set<String> _selectedBlockIds = {};

  /// 排序方式
  BlockSortOrder _sortOrder = BlockSortOrder.byIndex;

  /// 搜索关键词
  final TextEditingController _searchController = TextEditingController();
  String _searchKeyword = '';

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: widget.controller,
      builder: (context, child) {
        final blocks = _getFilteredAndSortedBlocks();
        final statistics = MarkdownBlockUtils.calculateBlockStatistics(
          widget.controller.blocks,
        );

        return Column(
          children: [
            // 标题栏
            _buildHeader(statistics),

            // 搜索和工具栏
            _buildToolbar(),

            const Divider(height: 1),

            // 分块列表
            Expanded(
              child:
                  blocks.isEmpty ? _buildEmptyState() : _buildBlockList(blocks),
            ),

            // 底部操作栏
            if (_selectedBlockIds.isNotEmpty) _buildBottomActionBar(),
          ],
        );
      },
    );
  }

  /// 构建标题栏
  Widget _buildHeader(BlockStatistics statistics) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(12),
          topRight: Radius.circular(12),
        ),
      ),
      child: Row(
        children: [
          Icon(Icons.view_module, color: Theme.of(context).primaryColor),
          const SizedBox(width: 8),
          const Text(
            '分块管理',
            style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
          ),
          const Spacer(),
          _buildStatisticsChips(statistics),
        ],
      ),
    );
  }

  /// 构建统计信息芯片
  Widget _buildStatisticsChips(BlockStatistics statistics) {
    return Row(
      children: [
        _buildStatChip('总分块', '${statistics.totalBlocks}', Colors.blue),
        const SizedBox(width: 8),
        _buildStatChip('可见', '${statistics.visibleBlocks}', Colors.green),
        const SizedBox(width: 8),
        _buildStatChip('隐藏', '${statistics.hiddenBlocks}', Colors.orange),
      ],
    );
  }

  /// 构建统计芯片
  Widget _buildStatChip(String label, String value, Color color) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            label,
            style: TextStyle(fontSize: 12, color: color.withValues(alpha: 0.8)),
          ),
          const SizedBox(width: 4),
          Text(
            value,
            style: TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建工具栏
  Widget _buildToolbar() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // 搜索框
          TextField(
            controller: _searchController,
            decoration: InputDecoration(
              hintText: '搜索分块标题...',
              prefixIcon: const Icon(Icons.search),
              suffixIcon:
                  _searchKeyword.isNotEmpty
                      ? IconButton(
                        icon: const Icon(Icons.clear),
                        onPressed: () {
                          _searchController.clear();
                          setState(() {
                            _searchKeyword = '';
                          });
                        },
                      )
                      : null,
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
            onChanged: (value) {
              setState(() {
                _searchKeyword = value;
              });
            },
          ),
          const SizedBox(height: 12),

          // 操作按钮
          Row(
            children: [
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: () => _toggleAllBlocks(true),
                  icon: const Icon(Icons.visibility, size: 16),
                  label: const Text('显示全部'),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: () => _toggleAllBlocks(false),
                  icon: const Icon(Icons.visibility_off, size: 16),
                  label: const Text('隐藏全部'),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: _selectAllBlocks,
                  icon: const Icon(Icons.select_all, size: 16),
                  label: const Text('全选'),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),

          // 排序选项
          Row(
            children: [
              const Text('排序:', style: TextStyle(fontSize: 14)),
              const SizedBox(width: 8),
              DropdownButton<BlockSortOrder>(
                value: _sortOrder,
                items:
                    BlockSortOrder.values.map((order) {
                      return DropdownMenuItem(
                        value: order,
                        child: Text(_getSortOrderText(order)),
                      );
                    }).toList(),
                onChanged: (value) {
                  if (value != null) {
                    setState(() {
                      _sortOrder = value;
                    });
                  }
                },
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 构建空状态
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.inbox_outlined, size: 64, color: Colors.grey.shade400),
          const SizedBox(height: 16),
          Text(
            _searchKeyword.isNotEmpty ? '没有找到匹配的分块' : '暂无分块',
            style: TextStyle(fontSize: 16, color: Colors.grey.shade600),
          ),
          if (_searchKeyword.isNotEmpty) ...[
            const SizedBox(height: 8),
            Text(
              '尝试调整搜索关键词',
              style: TextStyle(fontSize: 14, color: Colors.grey.shade500),
            ),
          ],
        ],
      ),
    );
  }

  /// 构建分块列表
  Widget _buildBlockList(List<MarkdownBlock> blocks) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: blocks.length,
      itemBuilder: (context, index) {
        final block = blocks[index];
        final isSelected = _selectedBlockIds.contains(block.id);

        return Card(
          margin: const EdgeInsets.only(bottom: 8),
          child: ListTile(
            leading: Checkbox(
              value: isSelected,
              onChanged: (value) {
                setState(() {
                  if (value == true) {
                    _selectedBlockIds.add(block.id);
                  } else {
                    _selectedBlockIds.remove(block.id);
                  }
                });
              },
            ),
            title: Row(
              children: [
                Expanded(
                  child: Text(
                    block.title.isNotEmpty ? block.title : '无标题分块',
                    style: TextStyle(
                      fontWeight: FontWeight.w500,
                      color:
                          block.isVisible
                              ? Colors.black87
                              : Colors.grey.shade600,
                    ),
                  ),
                ),
                // 简化的类型标识
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 6,
                    vertical: 2,
                  ),
                  decoration: BoxDecoration(
                    color: Colors.grey.shade100,
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: Text(
                    _getSeparatorTypeText(block.separatorType),
                    style: TextStyle(
                      fontSize: 10,
                      color: Colors.grey.shade600,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 4),
                Text(
                  '索引: ${block.index + 1} | 字符数: ${block.content.length}',
                  style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
                ),
                const SizedBox(height: 4),
                Text(
                  block.content.length > 100
                      ? '${block.content.substring(0, 100)}...'
                      : block.content,
                  style: TextStyle(fontSize: 12, color: Colors.grey.shade700),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                IconButton(
                  icon: Icon(
                    block.isVisible ? Icons.visibility : Icons.visibility_off,
                    color: block.isVisible ? Colors.green : Colors.grey,
                  ),
                  onPressed: () {
                    widget.controller.toggleBlockVisibility(block.id);
                  },
                  tooltip: block.isVisible ? '隐藏分块' : '显示分块',
                ),
                IconButton(
                  icon: const Icon(Icons.edit, size: 20),
                  onPressed: () => _editBlock(block),
                  tooltip: '编辑分块',
                ),
              ],
            ),
            onTap: () {
              setState(() {
                if (isSelected) {
                  _selectedBlockIds.remove(block.id);
                } else {
                  _selectedBlockIds.add(block.id);
                }
              });
            },
          ),
        );
      },
    );
  }

  /// 构建底部操作栏
  Widget _buildBottomActionBar() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        border: const Border(top: BorderSide(color: Colors.grey, width: 0.5)),
      ),
      child: Row(
        children: [
          Text(
            '已选择 ${_selectedBlockIds.length} 个分块',
            style: const TextStyle(fontWeight: FontWeight.w500),
          ),
          const Spacer(),
          TextButton.icon(
            onPressed: _toggleSelectedBlocks,
            icon: const Icon(Icons.visibility, size: 16),
            label: const Text('切换显示'),
          ),
          const SizedBox(width: 8),
          ElevatedButton.icon(
            onPressed: _exportSelectedBlocks,
            icon: const Icon(Icons.download, size: 16),
            label: const Text('导出选中'),
          ),
        ],
      ),
    );
  }

  /// 获取过滤和排序后的分块
  List<MarkdownBlock> _getFilteredAndSortedBlocks() {
    List<MarkdownBlock> blocks = widget.controller.blocks;

    // 搜索过滤
    if (_searchKeyword.isNotEmpty) {
      blocks =
          blocks.where((block) {
            return block.title.toLowerCase().contains(
                  _searchKeyword.toLowerCase(),
                ) ||
                block.content.toLowerCase().contains(
                  _searchKeyword.toLowerCase(),
                );
          }).toList();
    }

    // 排序
    switch (_sortOrder) {
      case BlockSortOrder.byIndex:
        blocks.sort((a, b) => a.index.compareTo(b.index));
        break;
      case BlockSortOrder.byTitle:
        blocks.sort((a, b) => a.title.compareTo(b.title));
        break;
      case BlockSortOrder.byLength:
        blocks.sort((a, b) => a.content.length.compareTo(b.content.length));
        break;
      case BlockSortOrder.byType:
        blocks.sort(
          (a, b) => a.separatorType.index.compareTo(b.separatorType.index),
        );
        break;
    }

    return blocks;
  }

  /// 获取排序方式文本
  String _getSortOrderText(BlockSortOrder order) {
    switch (order) {
      case BlockSortOrder.byIndex:
        return '按索引';
      case BlockSortOrder.byTitle:
        return '按标题';
      case BlockSortOrder.byLength:
        return '按长度';
      case BlockSortOrder.byType:
        return '按类型';
    }
  }

  /// 获取分隔符类型文本
  String _getSeparatorTypeText(BlockSeparatorType type) {
    switch (type) {
      case BlockSeparatorType.h1:
        return 'H1';
      case BlockSeparatorType.h2:
        return 'H2';
      case BlockSeparatorType.custom:
        return '自定义';
      case BlockSeparatorType.manual:
        return '手动';
    }
  }

  /// 切换所有分块可见性
  void _toggleAllBlocks(bool visible) {
    for (final block in widget.controller.blocks) {
      if (block.isVisible != visible) {
        widget.controller.toggleBlockVisibility(block.id);
      }
    }
  }

  /// 全选分块
  void _selectAllBlocks() {
    setState(() {
      _selectedBlockIds.addAll(
        widget.controller.blocks.map((block) => block.id),
      );
    });
  }

  /// 切换选中分块的可见性
  void _toggleSelectedBlocks() {
    for (final blockId in _selectedBlockIds) {
      widget.controller.toggleBlockVisibility(blockId);
    }
    setState(() {
      _selectedBlockIds.clear();
    });
  }

  /// 编辑分块
  void _editBlock(MarkdownBlock block) {
    // TODO: 实现分块编辑功能
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(SnackBar(content: Text('编辑分块: ${block.title}')));
  }

  /// 导出选中的分块
  void _exportSelectedBlocks() {
    final selectedBlocks =
        widget.controller.blocks
            .where((block) => _selectedBlockIds.contains(block.id))
            .toList();

    if (selectedBlocks.isEmpty) return;

    // TODO: 实现导出功能
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(SnackBar(content: Text('导出 ${selectedBlocks.length} 个分块')));
  }
}

/// 分块排序方式
enum BlockSortOrder {
  /// 按索引排序
  byIndex,

  /// 按标题排序
  byTitle,

  /// 按类型排序
  byType,

  /// 按长度排序
  byLength,
}
