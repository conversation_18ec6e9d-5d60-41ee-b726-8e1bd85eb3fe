import 'package:flutter/material.dart';

import '../models/markdown_block.dart';
import '../services/markdown_block_service.dart';

/// Markdown 分块渲染控制器
class MarkdownBlockController extends ChangeNotifier {
  /// 分块服务
  final MarkdownBlockService _blockService = MarkdownBlockService();

  /// 原始 Markdown 文本
  String _markdownText = '';
  String get markdownText => _markdownText;

  /// 分块列表
  List<MarkdownBlock> _blocks = [];
  List<MarkdownBlock> get blocks => List.unmodifiable(_blocks);

  // 移除了分隔杆相关的属性

  /// 渲染配置
  BlockRenderConfig _config = const BlockRenderConfig();
  BlockRenderConfig get config => _config;

  /// 是否正在加载
  bool _isLoading = false;
  bool get isLoading => _isLoading;

  /// 行高（用于位置计算）
  double _lineHeight = 24.0;
  double get lineHeight => _lineHeight;

  /// 容器高度
  double _containerHeight = 0;
  double get containerHeight => _containerHeight;

  /// 初始化
  void initialize({required String markdownText, BlockRenderConfig? config}) {
    _markdownText = markdownText;
    if (config != null) {
      _config = config;
    }
    _refreshBlocks();
  }

  /// 更新 Markdown 文本
  void updateMarkdownText(String text) {
    if (_markdownText != text) {
      _markdownText = text;
      _refreshBlocks();
    }
  }

  /// 更新配置
  void updateConfig(BlockRenderConfig config) {
    if (_config != config) {
      debugPrint(
        '分块控制器: 更新配置 - enabled: ${config.enabled}, mode: ${config.mode}',
      );
      _config = config;
      _refreshBlocks();
    }
  }

  /// 设置行高
  void setLineHeight(double height) {
    if (_lineHeight != height) {
      _lineHeight = height;
      // 移除了分隔杆位置更新的调用
    }
  }

  /// 设置容器高度
  void setContainerHeight(double height) {
    _containerHeight = height;
    notifyListeners();
  }

  /// 刷新分块
  void _refreshBlocks() {
    _setLoading(true);

    try {
      // 根据配置分块
      _blocks = _blockService.splitMarkdownIntoBlocks(_markdownText, _config);
      debugPrint(
        '分块控制器: 刷新分块完成 - 总分块数: ${_blocks.length}, 可见分块数: ${visibleBlocks.length}',
      );

      // 移除了手动分隔杆的逻辑
    } catch (e) {
      debugPrint('分块处理错误: $e');
    } finally {
      _setLoading(false);
    }
  }

  // 移除了分隔杆位置更新的方法

  // 移除了所有分隔杆相关的方法

  /// 切换分块可见性
  void toggleBlockVisibility(String blockId) {
    final blockIndex = _blocks.indexWhere((b) => b.id == blockId);
    if (blockIndex == -1) return;

    _blocks[blockIndex] = _blocks[blockIndex].copyWith(
      isVisible: !_blocks[blockIndex].isVisible,
    );

    notifyListeners();
  }

  /// 获取可见的分块
  List<MarkdownBlock> get visibleBlocks {
    return _blocks.where((block) => block.isVisible).toList();
  }

  /// 获取合并后的 Markdown 文本
  String getCombinedMarkdown() {
    return _blockService.combineBlocksToMarkdown(visibleBlocks);
  }

  /// 设置加载状态
  void _setLoading(bool loading) {
    if (_isLoading != loading) {
      _isLoading = loading;
      notifyListeners();
    }
  }

  /// 重置到初始状态
  void reset() {
    _blocks.clear();
    _markdownText = '';
    _config = const BlockRenderConfig();
    _isLoading = false;
    notifyListeners();
  }

  @override
  void dispose() {
    reset();
    super.dispose();
  }
}
