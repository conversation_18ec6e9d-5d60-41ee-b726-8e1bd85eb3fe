import 'package:flutter/material.dart';
import '../../services/service_locator.dart';
import '../../services/settings_service.dart';
import '../../services/storage_service.dart';
import '../models/markdown_template.dart';
import '../models/markdown_watermark.dart';
import '../models/markdown_render_style.dart';

/// Markdown渲染控制器
class MarkdownRenderController extends ChangeNotifier {
  /// 文本编辑控制器
  late final TextEditingController markdownController;

  /// 当前选中的模板
  late MarkdownTemplate _selectedTemplate;
  MarkdownTemplate get selectedTemplate => _selectedTemplate;

  /// 服务定位器
  final _serviceLocator = ServiceLocator();

  /// 存储服务
  late final StorageService _storageService;

  /// 设置服务
  late final SettingsService _settingsService;

  /// 保存的模板键
  static const String _keySavedTemplate = 'saved_markdown_template';

  /// 内容区域的高度
  double _contentHeight = 0;
  double get contentHeight => _contentHeight;

  /// 是否正在加载
  bool _isLoading = false;
  bool get isLoading => _isLoading;

  MarkdownRenderController({String? initialMarkdown}) {
    // 初始化服务
    _storageService = _serviceLocator.storageService;
    _settingsService = _serviceLocator.settingsService;

    // 验证和清理初始Markdown内容
    String cleanedMarkdown = _validateAndCleanMarkdown(initialMarkdown);
    markdownController = TextEditingController(text: cleanedMarkdown);

    // 加载保存的模板或使用默认模板
    _loadSavedTemplate();
  }

  /// 验证和清理Markdown内容
  String _validateAndCleanMarkdown(String? markdown) {
    // 如果内容为空或null，根据设置决定是否返回默认内容
    if (markdown == null || markdown.trim().isEmpty) {
      final s = _settingsService.settings;
      final enable = s.useDefaultInitialTextMarkdown;
      if (enable) {
        return _localizedDefaultInitialMarkdown();
      } else {
        return '';
      }
    }

    // 移除可能导致问题的特殊字符
    String cleaned = markdown
        .replaceAll('\r\n', '\n') // 统一换行符
        .replaceAll('\r', '\n') // 统一换行符
        .replaceAll('\u0000', '') // 移除null字符
        .replaceAll('\uFEFF', ''); // 移除BOM字符

    // 移除其他控制字符
    cleaned = cleaned.replaceAll(
      RegExp(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]'),
      '',
    );

    // 确保内容以换行符结尾
    if (!cleaned.endsWith('\n')) {
      cleaned += '\n';
    }

    // 如果清理后内容为空，根据设置决定是否返回默认内容
    if (cleaned.trim().isEmpty) {
      final s = _settingsService.settings;
      final enable = s.useDefaultInitialTextMarkdown;
      if (enable) {
        return _localizedDefaultInitialMarkdown();
      } else {
        return '';
      }
    }

    return cleaned;
  }

  String _currentLanguageCode() {
    try {
      return WidgetsBinding.instance.platformDispatcher.locale.languageCode;
    } catch (_) {
      return 'zh';
    }
  }

  String _localizedDefaultInitialMarkdown() {
    final code = _currentLanguageCode();
    switch (code) {
      case 'en':
        return '# Welcome to ContentPal\n\nThis is a Markdown editor. You can write and preview Markdown here.\n\n## Features\n\n- Live preview\n- Multiple templates\n- Watermark support\n- Export options';
      case 'ja':
        return '# ContentPal へようこそ\n\nこれはMarkdownエディターです。ここでMarkdownの作成とプレビューができます。\n\n## 機能\n\n- リアルタイムプレビュー\n- 複数のテンプレート\n- 透かしのサポート\n- エクスポート機能';
      case 'zh':
      default:
        return '# 欢迎使用 内容君\n\n这是一个Markdown编辑器。您可以在这里编写和预览Markdown内容。\n\n## 功能特性\n\n- 实时预览\n- 多种模板\n- 水印支持\n- 导出功能';
    }
  }

  /// 设置内容高度
  void setContentHeight(double height) {
    _contentHeight = height > 0 ? height : 1000;
    debugPrint("内容高度设置为: $_contentHeight");
  }

  /// 设置加载状态
  void setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  /// 更新Markdown文本
  void updateMarkdownText(String text) {
    markdownController.text = text;
    notifyListeners();
  }

  /// 触发UI更新
  void triggerUpdate() {
    notifyListeners();
  }

  /// 更新模板
  void updateTemplate(MarkdownTemplate template) {
    _selectedTemplate = template;
    _saveTemplate();
    notifyListeners();
  }

  /// 更新样式
  void updateStyle(MarkdownRenderStyle style) {
    _selectedTemplate = _selectedTemplate.copyWith(style: style);
    _saveTemplate();
    notifyListeners();
  }

  /// 更新水印
  void updateWatermark(MarkdownWatermark watermark) {
    _selectedTemplate = _selectedTemplate.copyWith(watermark: watermark);
    _saveTemplate();
    notifyListeners();
  }

  /// 加载保存的模板
  void _loadSavedTemplate() {
    final savedTemplateJson = _storageService.getJson(_keySavedTemplate);
    if (savedTemplateJson != null) {
      try {
        // 从JSON中恢复水印设置
        final watermarkJson =
            savedTemplateJson['watermark'] as Map<String, dynamic>?;
        MarkdownWatermark watermark;

        if (watermarkJson != null) {
          watermark = MarkdownWatermark(
            text:
                watermarkJson['text'] ??
                _settingsService.settings.watermark.text,
            textColor: Color(
              watermarkJson['textColor'] ?? Colors.grey.toARGB32(),
            ),
            fontSize: watermarkJson['fontSize'] ?? 12.0,
            fontFamily: watermarkJson['fontFamily'] ?? 'Roboto',
            fontStyle:
                watermarkJson['fontStyle'] == 1
                    ? FontStyle.italic
                    : FontStyle.normal,
            fontWeight:
                watermarkJson['fontWeight'] == 1
                    ? FontWeight.bold
                    : FontWeight.normal,
            isVisible: watermarkJson['isVisible'] ?? false,
            position:
                WatermarkPosition.values[watermarkJson['position'] ??
                    WatermarkPosition.bottomCenter.index],
            opacity: watermarkJson['opacity'] ?? 0.7,
          );
        } else {
          watermark = _settingsService.settings.watermark;
        }

        // 从JSON中恢复样式设置
        final styleJson = savedTemplateJson['style'] as Map<String, dynamic>?;

        // 创建模板
        final templateId = savedTemplateJson['id'] as String? ?? 'modern';
        MarkdownTemplate baseTemplate =
            MarkdownTemplate.getPredefinedTemplates().firstWhere(
              (t) => t.id == templateId,
              orElse: () => MarkdownTemplate.modern(),
            );

        // 如果有保存的样式修改，应用这些修改
        MarkdownRenderStyle style = baseTemplate.style;
        if (styleJson != null) {
          style = style.copyWith(
            baseFontSize: styleJson['baseFontSize']?.toDouble(),
            borderRadius: styleJson['borderRadius']?.toDouble(),
            headingAlignment:
                styleJson['headingAlignment'] != null
                    ? TextAlign.values[styleJson['headingAlignment']]
                    : null,
            listItemStyle: styleJson['listItemStyle'],
            checkboxUncheckedStyle: styleJson['checkboxUncheckedStyle'],
            checkboxCheckedStyle: styleJson['checkboxCheckedStyle'],
            fontFamily: styleJson['fontFamily'],
            codeFontFamily: styleJson['codeFontFamily'],
          );
        }

        _selectedTemplate = baseTemplate.copyWith(
          watermark: watermark,
          style: style,
        );
      } catch (e) {
        debugPrint('加载保存的模板失败: $e');
        _selectedTemplate = MarkdownTemplate.modern().copyWith(
          watermark: _settingsService.settings.watermark,
        );
      }
    } else {
      _selectedTemplate = MarkdownTemplate.modern().copyWith(
        watermark: _settingsService.settings.watermark,
      );
    }
  }

  /// 保存当前模板
  Future<void> _saveTemplate() async {
    try {
      // 将模板转换为JSON
      final templateJson = {
        'id': _selectedTemplate.id,
        'name': _selectedTemplate.name,
        'description': _selectedTemplate.description,
        'style': {
          'baseFontSize': _selectedTemplate.style.baseFontSize,
          'borderRadius': _selectedTemplate.style.borderRadius,
          'headingAlignment': _selectedTemplate.style.headingAlignment.index,
          'listItemStyle': _selectedTemplate.style.listItemStyle,
          'checkboxUncheckedStyle':
              _selectedTemplate.style.checkboxUncheckedStyle,
          'checkboxCheckedStyle': _selectedTemplate.style.checkboxCheckedStyle,
          'fontFamily': _selectedTemplate.style.fontFamily,
          'codeFontFamily': _selectedTemplate.style.codeFontFamily,
        },
        'watermark': {
          'text': _selectedTemplate.watermark.text,
          'textColor': _selectedTemplate.watermark.textColor.toARGB32(),
          'fontSize': _selectedTemplate.watermark.fontSize,
          'fontFamily': _selectedTemplate.watermark.fontFamily,
          'fontStyle':
              _selectedTemplate.watermark.fontStyle == FontStyle.italic ? 1 : 0,
          'fontWeight':
              _selectedTemplate.watermark.fontWeight == FontWeight.bold ? 1 : 0,
          'isVisible': _selectedTemplate.watermark.isVisible,
          'position': _selectedTemplate.watermark.position.index,
          'opacity': _selectedTemplate.watermark.opacity,
        },
      };

      // 保存到存储
      await _storageService.setJson(_keySavedTemplate, templateJson);

      // 同时更新应用设置中的水印
      await _settingsService.updateWatermark(_selectedTemplate.watermark);

      debugPrint('模板和水印设置已保存');
    } catch (e) {
      debugPrint('保存模板失败: $e');
    }
  }

  @override
  void dispose() {
    markdownController.dispose();
    super.dispose();
  }
}
