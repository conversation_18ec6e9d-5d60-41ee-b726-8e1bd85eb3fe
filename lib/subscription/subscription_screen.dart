import 'dart:async';
import 'dart:math';
import 'dart:ui';


import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:in_app_purchase/in_app_purchase.dart';

import '../../services/service_locator.dart';
import '../../widgets/marquee_text.dart';
import 'subscription_model.dart';
import 'subscription_service.dart';
import '../../generated/l10n/app_localizations.dart';

/// 订阅页面 - 展示订阅计划并处理购买
class SubscriptionScreen extends StatefulWidget {
  const SubscriptionScreen({super.key});

  @override
  State<SubscriptionScreen> createState() => _SubscriptionScreenState();
}

class _SubscriptionScreenState extends State<SubscriptionScreen>
    with SingleTickerProviderStateMixin {
  final SubscriptionService _subscriptionService =
      ServiceLocator().subscriptionService;
  late List<SubscriptionPlan> _plans;
  late SubscriptionPlan _selectedPlan;
  bool _isLoading = false;
  bool _isPurchasing = false;
  String? _errorMessage;
  StreamSubscription<UserSubscription>? _subscriptionSub;
  StreamSubscription<PurchaseStatus>? _purchaseStatusSub;

  // 动画控制器
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _scaleAnimation;

  // 滚动控制器
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();



    // 初始化动画控制器
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 1000),
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.0, 0.6, curve: Curves.easeOut),
      ),
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.1),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.2, 1.0, curve: Curves.easeOutCubic),
      ),
    );

    _scaleAnimation = Tween<double>(begin: 0.95, end: 1.0).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.0, 0.8, curve: Curves.easeOutCubic),
      ),
    );

    // 加载订阅计划
    _loadSubscriptionPlans();

    // 监听订阅状态变化以刷新按钮状态
    _subscriptionSub = _subscriptionService.subscriptionStream.listen((_) {
      if (mounted) setState(() {});
    });

    // 监听购买结果，成功后给出提示
    _purchaseStatusSub = _subscriptionService.purchaseStatusStream.listen((
      status,
    ) {
      if (!mounted) return;
      if (status == PurchaseStatus.purchased) {
        _showPurchaseSuccessSnack();
      }
    });

    // 启动动画
    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    _scrollController.dispose();
    _subscriptionSub?.cancel();
    _purchaseStatusSub?.cancel();
    super.dispose();
  }

  void _showPurchaseSuccessSnack() {
    final l10n = AppLocalizations.of(context);
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(l10n.purchaseSuccessMessage),
        duration: const Duration(seconds: 3),
      ),
    );

    // 购买成功后延迟返回上一页
    Future.delayed(const Duration(seconds: 3), () {
      if (mounted) {
        Navigator.of(context).pop();
      }
    });
  }

  /// 加载订阅计划
  Future<void> _loadSubscriptionPlans() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // 获取订阅计划
      _plans =
          _subscriptionService.plans
              .where((plan) => plan.type != SubscriptionType.free)
              .toList();

      // 默认选择年度计划
      _selectedPlan = _plans.firstWhere(
        (plan) => plan.type == SubscriptionType.yearly,
        orElse: () => _plans.first,
      );
    } catch (e) {
      _errorMessage = '加载订阅计划失败，请稍后再试';
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  /// 处理订阅购买
  Future<void> _handlePurchase() async {
    setState(() {
      _isPurchasing = true;
      _errorMessage = null;
    });

    try {
      // 发起购买
      final success = await _subscriptionService.purchaseSubscription(
        _selectedPlan,
      );

      if (success) {
        // 购买请求已成功发起，保持在当前页面，等待系统购买流程与订阅状态更新
        // 不再自动关闭当前页面
        if (mounted) {
          setState(() {
            _errorMessage = null;
          });
        }
      } else {
        setState(() {
          _errorMessage = '购买失败，请稍后再试。';
        });


      }
    } catch (e) {
      setState(() {
        _errorMessage = '购买过程中出错: $e';
      });
    } finally {
      if (mounted) {
        setState(() {
          _isPurchasing = false;
        });
      }
    }
  }

  /// 处理恢复购买
  Future<void> _handleRestore() async {
    setState(() {
      _isPurchasing = true;
      _errorMessage = null;
    });

    final l10n = AppLocalizations.of(context);

    // 显示恢复购买进度对话框
    if (mounted) {
      showDialog(
        context: context,
        barrierDismissible: false,
        builder: (context) {
          return AlertDialog(
            title: Text(l10n.restoringPurchase),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const CircularProgressIndicator(),
                const SizedBox(height: 16),
                Text(l10n.communicatingWithAppStore),
              ],
            ),
          );
        },
      );
    }

    try {
      final success = await _subscriptionService.restorePurchases();

      if (!mounted) return;
      // 关闭进度对话框
      Navigator.of(context).pop();

      // 展示结果弹窗，不自动退出当前页
      if (success) {
        await _showRestoreResultDialog(success: true);
      } else {
        setState(() {
          _errorMessage = l10n.noRestorablePurchasesFound;
        });
        await _showRestoreResultDialog(success: false);
      }
    } catch (e) {
      if (!mounted) return;
      // 关闭进度对话框
      Navigator.of(context).pop();
      setState(() {
        _errorMessage = '${l10n.restorePurchase}: $e';
      });
      await _showRestoreResultDialog(
        success: false,
        errorMessage: e.toString(),
      );
    } finally {
      if (mounted) {
        setState(() {
          _isPurchasing = false;
        });
      }
    }
  }

  Future<void> _showRestoreResultDialog({
    required bool success,
    String? errorMessage,
  }) async {
    final l10n = AppLocalizations.of(context);
    final String title =
        success ? l10n.restoreCompletedTitle : l10n.restoreFailedTitle;
    final String message =
        success
            ? l10n.restoreCompletedMessage
            : (errorMessage != null
                ? l10n.restoreFailedMessageWithError(errorMessage)
                : l10n.noRestorablePurchasesFound);

    final List<Color> colors =
        success
            ? [const Color(0xFF10B981), const Color(0xFF34D399)]
            : [const Color(0xFFEF4444), const Color(0xFFF97316)];

    return showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: Row(
            children: [
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  gradient: LinearGradient(colors: colors),
                  shape: BoxShape.circle,
                ),
                child: Icon(
                  success ? Icons.check : Icons.error_outline,
                  color: Colors.white,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  title,
                  style: const TextStyle(fontWeight: FontWeight.bold),
                ),
              ),
            ],
          ),
          content: Text(message),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(l10n.ok),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    // 设置状态栏为透明
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.light,
      ),
    );

    return Scaffold(
      backgroundColor: Colors.black,
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.close, color: Colors.white),
          onPressed: () => Navigator.of(context).pop(),
        ),
        actions: [
          TextButton(
            onPressed: _isPurchasing ? null : _handleRestore,
            child: Text(
              AppLocalizations.of(context).restorePurchase,
              style: const TextStyle(color: Colors.white70),
            ),
          ),
        ],
      ),
      body:
          _isLoading
              ? const Center(
                child: CircularProgressIndicator(color: Colors.white),
              )
              : Stack(
                children: [
                  // 背景渐变
                  Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                        colors: [
                          const Color(0xFF1A1A2E),
                          const Color(0xFF16213E),
                          const Color(0xFF0F3460),
                        ],
                      ),
                    ),
                  ),

                  // 背景装饰元素
                  Positioned.fill(child: _buildBackgroundDecoration()),

                  // 主内容
                  SafeArea(
                    child: AnimatedBuilder(
                      animation: _animationController,
                      builder: (context, child) {
                        return FadeTransition(
                          opacity: _fadeAnimation,
                          child: SlideTransition(
                            position: _slideAnimation,
                            child: ScaleTransition(
                              scale: _scaleAnimation,
                              child: child,
                            ),
                          ),
                        );
                      },
                      child: SingleChildScrollView(
                        controller: _scrollController,
                        physics: const BouncingScrollPhysics(),
                        padding: const EdgeInsets.symmetric(horizontal: 24),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            const SizedBox(height: 20),



                            // 标题
                            _buildHeader(),

                            const SizedBox(height: 40),

                            // 订阅计划选择
                            _buildSubscriptionPlans(),

                            const SizedBox(height: 30),

                            // 特性列表
                            _buildFeaturesList(),

                            const SizedBox(height: 30),

                            // 购买按钮
                            _buildPurchaseButton(),

                            const SizedBox(height: 16),

                            // 错误消息
                            if (_errorMessage != null)
                              Center(
                                child: Padding(
                                  padding: const EdgeInsets.only(bottom: 16),
                                  child: Text(
                                    _errorMessage!,
                                    style: const TextStyle(
                                      color: Colors.red,
                                      fontSize: 14,
                                    ),
                                    textAlign: TextAlign.center,
                                  ),
                                ),
                              ),



                            // 隐私政策和服务条款
                            _buildTermsAndPrivacy(),

                            const SizedBox(height: 30),
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
    );
  }

  /// 构建背景装饰
  Widget _buildBackgroundDecoration() {
    return Stack(
      children: [
        // 顶部光晕
        Positioned(
          top: -100,
          right: -100,
          child: Container(
            width: 300,
            height: 300,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              gradient: RadialGradient(
                colors: [Colors.purple.withAlpha(76), Colors.transparent],
              ),
            ),
          ),
        ),

        // 底部光晕
        Positioned(
          bottom: -150,
          left: -100,
          child: Container(
            width: 350,
            height: 350,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              gradient: RadialGradient(
                colors: [Colors.blue.withAlpha(51), Colors.transparent],
              ),
            ),
          ),
        ),

        // 装饰性粒子
        ...List.generate(20, (index) {
          final random = index * 0.05;
          return Positioned(
            top: 100 + (index * 30) % 600,
            left: (index * 40) % 400,
            child: AnimatedBuilder(
              animation: _animationController,
              builder: (context, child) {
                return Transform.translate(
                  offset: Offset(
                    0,
                    5 *
                        (0.5 -
                            0.5 *
                                (0.5 +
                                    0.5 *
                                        sin(
                                          random +
                                              _animationController.value * 3,
                                        ))),
                  ),
                  child: Opacity(
                    opacity:
                        0.1 +
                        0.1 * sin(random + _animationController.value * 2),
                    child: Container(
                      width: 4 + (index % 4),
                      height: 4 + (index % 4),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(2),
                      ),
                    ),
                  ),
                );
              },
            ),
          );
        }),

        // 模糊效果
        BackdropFilter(
          filter: ImageFilter.blur(sigmaX: 0, sigmaY: 0),
          child: Container(color: Colors.transparent),
        ),
      ],
    );
  }

  /// 构建页面标题
  Widget _buildHeader() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          AppLocalizations.of(context).subscriptionUpgradeTitle,
          style: const TextStyle(
            fontSize: 32,
            fontWeight: FontWeight.bold,
            color: Colors.white,
            letterSpacing: 0.5,
          ),
        ),
        const SizedBox(height: 12),
        Text(
          AppLocalizations.of(context).subscriptionUpgradeSubtitle,
          style: TextStyle(
            fontSize: 16,
            color: Colors.white.withAlpha(179),
            letterSpacing: 0.3,
          ),
        ),
      ],
    );
  }

  String _localizedPlanName(AppLocalizations l10n, SubscriptionPlan plan) {
    // 若商店返回了本地化标题，优先展示
    if (plan.storeTitle != null && plan.storeTitle!.trim().isNotEmpty) {
      return plan.storeTitle!;
    }
    switch (plan.id) {
      case 'monthly_premium':
        return l10n.subscriptionPlanMonthlyName;
      case 'yearly_premium':
        return l10n.subscriptionPlanYearlyName;
      case 'lifetime_premium':
        return l10n.subscriptionPlanLifetimeName;
      case 'free':
        return l10n.subscriptionPlanFreeName;
      default:
        return plan.name;
    }
  }

  String _localizedPlanDesc(AppLocalizations l10n, SubscriptionPlan plan) {
    // 若商店返回了本地化描述，优先展示
    if (plan.storeDescription != null &&
        plan.storeDescription!.trim().isNotEmpty) {
      return plan.storeDescription!;
    }
    switch (plan.id) {
      case 'monthly_premium':
        return l10n.subscriptionPlanMonthlyDesc;
      case 'yearly_premium':
        return l10n.subscriptionPlanYearlyDesc;
      case 'lifetime_premium':
        return l10n.subscriptionPlanLifetimeDesc;
      case 'free':
        return l10n.subscriptionPlanFreeDesc;
      default:
        return plan.description;
    }
  }

  String _localizedFeatureName(
    AppLocalizations l10n,
    String featureId,
    String fallback,
  ) {
    switch (featureId) {
      case 'unlimited_exports':
        return l10n.subscriptionFeatureUnlimitedExportsName;
      case 'batch_processing':
        return l10n.subscriptionFeatureBatchProcessingName;
      case 'advanced_tools':
        return l10n.subscriptionFeatureAdvancedToolsName;
      case 'no_watermark':
        return l10n.subscriptionFeatureNoWatermarkName;
      case 'priority_support':
        return l10n.subscriptionFeaturePrioritySupportName;
      case 'future_updates':
        return l10n.subscriptionFeatureFutureUpdatesName;
      case 'basic_processing':
        return l10n.subscriptionFeatureBasicProcessingName;
      case 'watermarked_exports':
        return l10n.subscriptionFeatureWatermarkedExportsName;
      default:
        return fallback;
    }
  }

  String _localizedFeatureDesc(
    AppLocalizations l10n,
    String featureId,
    String fallback,
  ) {
    switch (featureId) {
      case 'unlimited_exports':
        return l10n.subscriptionFeatureUnlimitedExportsDesc;
      case 'batch_processing':
        return l10n.subscriptionFeatureBatchProcessingDesc;
      case 'advanced_tools':
        return l10n.subscriptionFeatureAdvancedToolsDesc;
      case 'no_watermark':
        return l10n.subscriptionFeatureNoWatermarkDesc;
      case 'priority_support':
        return l10n.subscriptionFeaturePrioritySupportDesc;
      case 'future_updates':
        return l10n.subscriptionFeatureFutureUpdatesDesc;
      case 'basic_processing':
        return l10n.subscriptionFeatureBasicProcessingDesc;
      case 'watermarked_exports':
        return l10n.subscriptionFeatureWatermarkedExportsDesc;
      default:
        return fallback;
    }
  }

  String _periodTextForPlan(AppLocalizations l10n, SubscriptionPlan plan) {
    switch (plan.type) {
      case SubscriptionType.monthly:
        return l10n.subscriptionPeriodPerMonthSuffix;
      case SubscriptionType.yearly:
        return l10n.subscriptionPeriodPerYearSuffix;
      case SubscriptionType.lifetime:
        return l10n.subscriptionPeriodLifetime;
      case SubscriptionType.free:
        return l10n.subscriptionPeriodFree;
    }
  }

  /// 获取等价价格文本
  String _getEquivalentPriceText(SubscriptionPlan plan, AppLocalizations l10n) {
    if (plan.type == SubscriptionType.monthly) {
      // 月度订阅显示每天价格
      final dailyPrice = plan.price / 30;
      return AppLocalizations.of(context).subscriptionEquivalentToPerMonth(
        '${plan.currencySymbol}${dailyPrice.toStringAsFixed(2)}/天',
      );
    } else if (plan.type == SubscriptionType.yearly) {
      // 年度订阅显示每月价格
      final monthlyPrice = plan.price / 12;
      return AppLocalizations.of(context).subscriptionEquivalentToPerMonth(
        '${plan.currencySymbol}${monthlyPrice.toStringAsFixed(2)}${l10n.subscriptionPeriodPerMonthSuffix}',
      );
    }
    return '';
  }

  /// 获取按钮文本
  String _getButtonText(bool isCurrentPlanActive, bool isPriceLoaded) {
    final l10n = AppLocalizations.of(context);

    if (isCurrentPlanActive) {
      return l10n.currentPlan;
    }

    if (!isPriceLoaded) {
      return l10n.subscriptionLoadingPrice;
    }

    return l10n.subscriptionSubscribeNowWithPrice(_selectedPlan.formattedPrice);
  }

  /// 从订阅计划获取订阅类型
  SubscriptionType _getSubscriptionTypeFromPlan(SubscriptionPlan plan) {
    return plan.type;
  }

  /// 构建订阅计划选择
  Widget _buildSubscriptionPlans() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          AppLocalizations.of(context).subscriptionChoosePlan,
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: Colors.white.withAlpha(230),
          ),
        ),
        const SizedBox(height: 16),
        ...List.generate(_plans.length, (index) {
          final plan = _plans[index];
          final isSelected = plan.id == _selectedPlan.id;
          final l10n = AppLocalizations.of(context);

          // 计算折扣文本（仅对年度计划）
          String? discountText;
          if (plan.type == SubscriptionType.yearly) {
            final monthlyPlan = _plans.firstWhere(
              (p) => p.type == SubscriptionType.monthly,
              orElse: () => plan,
            );

            if (plan != monthlyPlan &&
                plan.monthlyPrice != null &&
                monthlyPlan.price > 0) {
              final discount =
                  (1 - (plan.monthlyPrice! / monthlyPlan.price)) * 100;
              if (discount > 0) {
                discountText = AppLocalizations.of(
                  context,
                ).subscriptionDiscountSavePercent(discount.round());
              }
            }
          }

          return GestureDetector(
            onTap: () {
              setState(() {
                _selectedPlan = plan;
              });
            },
            child: Container(
              margin: const EdgeInsets.only(bottom: 16),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color:
                      isSelected
                          ? plan.accentColor ?? Colors.blue
                          : Colors.white24,
                  width: isSelected ? 2 : 1,
                ),
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors:
                      isSelected
                          ? [
                            (plan.accentColor ?? Colors.blue).withAlpha(51),
                            (plan.accentColor ?? Colors.blue).withAlpha(13),
                          ]
                          : [
                            Colors.white.withAlpha(13),
                            Colors.white.withAlpha(5),
                          ],
                ),
                boxShadow:
                    isSelected
                        ? [
                          BoxShadow(
                            color: (plan.accentColor ?? Colors.blue).withAlpha(
                              76,
                            ),
                            blurRadius: 20,
                            spreadRadius: -5,
                          ),
                        ]
                        : null,
              ),
              child: Padding(
                padding: const EdgeInsets.all(20),
                child: Row(
                  children: [
                    // 选择指示器
                    Container(
                      width: 24,
                      height: 24,
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        border: Border.all(
                          color:
                              isSelected
                                  ? plan.accentColor ?? Colors.blue
                                  : Colors.white38,
                          width: 2,
                        ),
                      ),
                      child:
                          isSelected
                              ? Center(
                                child: Container(
                                  width: 12,
                                  height: 12,
                                  decoration: BoxDecoration(
                                    shape: BoxShape.circle,
                                    color: plan.accentColor ?? Colors.blue,
                                  ),
                                ),
                              )
                              : null,
                    ),
                    const SizedBox(width: 16),

                    // 计划信息
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Expanded(
                                child: SuperSimpleMarqueeText(
                                  _localizedPlanName(l10n, plan),
                                  style: TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                    color:
                                        isSelected
                                            ? Colors.white
                                            : Colors.white70,
                                  ),
                                  speed: 50.0,
                                  pauseDuration: 1.0,
                                ),
                              ),
                              if (discountText != null) ...[
                                const SizedBox(width: 8),
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 8,
                                    vertical: 2,
                                  ),
                                  decoration: BoxDecoration(
                                    color: Colors.green,
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  child: Text(
                                    discountText,
                                    style: const TextStyle(
                                      fontSize: 10,
                                      fontWeight: FontWeight.bold,
                                      color: Colors.white,
                                    ),
                                  ),
                                ),
                              ],
                            ],
                          ),
                          const SizedBox(height: 4),
                          Text(
                            _localizedPlanDesc(l10n, plan),
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.white.withAlpha(153),
                            ),
                          ),
                          const SizedBox(height: 8),
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.end,
                            children: [
                              Text(
                                plan.storeFormattedPrice != null
                                    ? plan.formattedPrice
                                    : AppLocalizations.of(
                                      context,
                                    ).subscriptionLoadingPrice,
                                style: TextStyle(
                                  fontSize: 22,
                                  fontWeight: FontWeight.bold,
                                  color:
                                      isSelected
                                          ? plan.accentColor ?? Colors.blue
                                          : Colors.white70,
                                ),
                              ),
                              const SizedBox(width: 4),
                              Text(
                                _periodTextForPlan(l10n, plan),
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Colors.white.withAlpha(153),
                                ),
                              ),
                            ],
                          ),
                          if (plan.type != SubscriptionType.lifetime &&
                              plan.storeFormattedPrice != null) ...[
                            const SizedBox(height: 4),
                            Text(
                              _getEquivalentPriceText(plan, l10n),
                              style: TextStyle(
                                fontSize: 13,
                                color: Colors.white.withAlpha(128),
                              ),
                            ),
                          ],
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ),
          );
        }),
      ],
    );
  }

  /// 构建特性列表
  Widget _buildFeaturesList() {
    // 获取所选计划的特性
    final features = _selectedPlan.features;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          AppLocalizations.of(context).subscriptionIncludedFeatures,
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.w600,
            color: Colors.white.withAlpha(230),
          ),
        ),
        const SizedBox(height: 16),
        ...features.map((feature) {
          final l10n = AppLocalizations.of(context);
          return Padding(
            padding: const EdgeInsets.only(bottom: 16),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  margin: const EdgeInsets.only(top: 2),
                  padding: const EdgeInsets.all(4),
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    color: (_selectedPlan.accentColor ?? Colors.blue).withAlpha(
                      51,
                    ),
                  ),
                  child: Icon(
                    Icons.check,
                    size: 14,
                    color: _selectedPlan.accentColor ?? Colors.blue,
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        _localizedFeatureName(l10n, feature.id, feature.name),
                        style: const TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w600,
                          color: Colors.white,
                        ),
                      ),
                      const SizedBox(height: 2),
                      Text(
                        _localizedFeatureDesc(
                          l10n,
                          feature.id,
                          feature.description,
                        ),
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.white.withAlpha(153),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          );
        }),
      ],
    );
  }

  /// 构建购买按钮
  Widget _buildPurchaseButton() {
    final subscription = _subscriptionService.subscription;
    final bool isCurrentPlanActive =
        subscription.isActive &&
        subscription.isPaid &&
        _getSubscriptionTypeFromPlan(_selectedPlan) == subscription.type;

    // 检查价格是否已加载
    final bool isPriceLoaded = _selectedPlan.storeFormattedPrice != null;
    final bool isButtonEnabled =
        !_isPurchasing && !isCurrentPlanActive && isPriceLoaded;

    return Column(
      children: [
        // 价格未加载时的提示
        if (!isPriceLoaded && !_isPurchasing)
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            margin: const EdgeInsets.only(bottom: 12),
            decoration: BoxDecoration(
              color: Colors.orange.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.orange.withValues(alpha: 0.3)),
            ),
            child: Row(
              children: [
                Icon(Icons.info_outline, color: Colors.orange, size: 16),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    '正在加载价格信息，请稍候...',
                    style: TextStyle(
                      color: Colors.orange.shade700,
                      fontSize: 12,
                    ),
                  ),
                ),
              ],
            ),
          ),

        SizedBox(
          width: double.infinity,
          height: 56,
          child: ElevatedButton(
            onPressed: isButtonEnabled ? _handlePurchase : null,
            style: ElevatedButton.styleFrom(
              backgroundColor: _selectedPlan.accentColor ?? Colors.blue,
              disabledBackgroundColor:
                  (_selectedPlan.accentColor ?? Colors.blue).withAlpha(204),
              disabledForegroundColor: Colors.white,
              foregroundColor: Colors.white,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(16),
              ),
              elevation: 0,
              padding: const EdgeInsets.symmetric(vertical: 16),
            ),
            child:
                _isPurchasing
                ? const SizedBox(
                      width: 24,
                      height: 24,
                      child: CircularProgressIndicator(
                        color: Colors.white,
                        strokeWidth: 2,
                      ),
                    )
                : Text(
                      _getButtonText(isCurrentPlanActive, isPriceLoaded),
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        letterSpacing: 0.5,
                      ),
                    ),
          ),
        ),
      ],
    );
  }

  /// 构建隐私政策和服务条款
  Widget _buildTermsAndPrivacy() {
    return Center(
      child: Wrap(
        alignment: WrapAlignment.center,
        crossAxisAlignment: WrapCrossAlignment.center,
        children: [
          Text(
            AppLocalizations.of(context).subscriptionAgreementPrefix,
            style: TextStyle(fontSize: 12, color: Colors.white.withAlpha(102)),
          ),
          TextButton(
            onPressed: () {
              // 打开服务条款
            },
            style: TextButton.styleFrom(
              padding: const EdgeInsets.symmetric(horizontal: 4),
              minimumSize: Size.zero,
              tapTargetSize: MaterialTapTargetSize.shrinkWrap,
            ),
            child: Text(
              AppLocalizations.of(context).termsOfService,
              style: const TextStyle(fontSize: 12),
            ),
          ),
          Text(
            AppLocalizations.of(context).andText,
            style: TextStyle(fontSize: 12, color: Colors.white.withAlpha(102)),
          ),
          TextButton(
            onPressed: () {
              // 打开隐私政策
            },
            style: TextButton.styleFrom(
              padding: const EdgeInsets.symmetric(horizontal: 4),
              minimumSize: Size.zero,
              tapTargetSize: MaterialTapTargetSize.shrinkWrap,
            ),
            child: Text(
              AppLocalizations.of(context).privacyPolicy,
              style: const TextStyle(fontSize: 12),
            ),
          ),
          Text(
            AppLocalizations.of(context).subscriptionAgreementSuffix,
            style: TextStyle(fontSize: 12, color: Colors.white.withAlpha(102)),
          ),
        ],
      ),
    );
  }


}
