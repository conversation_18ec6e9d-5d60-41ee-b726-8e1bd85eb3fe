import 'dart:async';
import 'dart:convert';
import 'dart:io';

import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:in_app_purchase/in_app_purchase.dart';
import 'package:in_app_purchase_storekit/in_app_purchase_storekit.dart';
import 'package:in_app_purchase_storekit/store_kit_wrappers.dart';

import '../config/constants.dart';
import '../services/service_locator.dart';
import '../services/storage_service.dart';
import 'subscription_model.dart';

/// 订阅服务，负责管理用户订阅状态和处理订阅相关操作
class SubscriptionService {
  late StorageService _storage;
  late UserSubscription _subscription;
  late List<SubscriptionPlan> _plans;

  // 应用内购买相关
  final InAppPurchase _inAppPurchase = InAppPurchase.instance;
  StreamSubscription<List<PurchaseDetails>>? _purchaseSubscription;
  List<ProductDetails> _products = [];
  bool _isAvailable = false;

  // 产品ID
  static const String _kMonthlyId = 'jk.lcc.contentpal.monthly';
  static const String _kYearlyId = 'jk.lcc.contentpal.yearly';
  static const String _kLifetimeId = 'jk.lcc.contentpal.lifetime';
  static const List<String> _kProductIds = <String>[
    _kMonthlyId,
    _kYearlyId,
    _kLifetimeId,
  ];

  // 订阅状态变化流
  final _subscriptionController =
      StreamController<UserSubscription>.broadcast();
  Stream<UserSubscription> get subscriptionStream =>
      _subscriptionController.stream;

  // 购买状态流
  final _purchaseStatusController =
      StreamController<PurchaseStatus>.broadcast();
  Stream<PurchaseStatus> get purchaseStatusStream =>
      _purchaseStatusController.stream;

  // 单例模式
  static final SubscriptionService _instance = SubscriptionService._internal();
  factory SubscriptionService() => _instance;
  SubscriptionService._internal();

  /// 初始化订阅服务
  Future<void> init() async {
    _storage = ServiceLocator().storageService;
    _loadSubscription();
    _initSubscriptionPlans();
    await _initInAppPurchase();
  }

  /// 加载用户订阅信息
  void _loadSubscription() {
    final subscriptionJson = _storage.getString(AppConstants.keySubscription);
    if (subscriptionJson != null) {
      try {
        final Map<String, dynamic> subscriptionMap = json.decode(
          subscriptionJson,
        );
        _subscription = UserSubscription.fromJson(subscriptionMap);

        // 检查订阅是否已过期
        _checkSubscriptionExpiry();
      } catch (e) {
        debugPrint('加载订阅信息失败: $e');
        _subscription = UserSubscription.free(); // 使用默认免费订阅
      }
    } else {
      _subscription = UserSubscription.free(); // 使用默认免费订阅
    }

    // 通知订阅状态变化
    _notifySubscriptionChanged();
  }

  /// 初始化订阅计划
  void _initSubscriptionPlans() {
    // 初始化基础计划结构，价格等信息将从App Store动态获取
    _initBasicPlanStructures();
  }

  /// 初始化基础计划结构
  void _initBasicPlanStructures() {
    _plans = [
      // 月度订阅（仅初始化基础结构，价格将从App Store获取）
      SubscriptionPlan(
        id: 'monthly_premium',
        name: '内容君专业版月度',
        description: '解锁所有高级内容处理功能',
        type: SubscriptionType.monthly,
        price: 0, // 价格将从App Store动态获取
        currencyCode: 'CNY', // 货币代码将更新
        currencySymbol: '¥', // 货币符号将更新
        accentColor: Colors.blue,
        productId: _kMonthlyId,
        features: [
          const SubscriptionFeature(
            id: 'unlimited_exports',
            name: '无限导出',
            description: '无限制导出处理后的内容',
            isAvailableInFree: false,
          ),
          const SubscriptionFeature(
            id: 'batch_processing',
            name: '批量处理',
            description: '支持批量处理多个文件',
            isAvailableInFree: false,
          ),
          const SubscriptionFeature(
            id: 'advanced_tools',
            name: '高级工具',
            description: '访问所有高级编辑和处理工具',
            isAvailableInFree: false,
          ),
          const SubscriptionFeature(
            id: 'no_watermark',
            name: '无水印',
            description: '导出内容不含水印',
            isAvailableInFree: false,
          ),
        ],
      ),

      // 年度订阅（仅初始化基础结构，价格将从App Store获取）
      SubscriptionPlan(
        id: 'yearly_premium',
        name: '内容君专业版年度',
        description: '年度订阅，节省25%',
        type: SubscriptionType.yearly,
        price: 0, // 价格将从App Store动态获取
        currencyCode: 'CNY', // 货币代码将更新
        currencySymbol: '¥', // 货币符号将更新
        accentColor: Colors.purple,
        productId: _kYearlyId,
        features: [
          const SubscriptionFeature(
            id: 'unlimited_exports',
            name: '无限导出',
            description: '无限制导出处理后的内容',
            isAvailableInFree: false,
          ),
          const SubscriptionFeature(
            id: 'batch_processing',
            name: '批量处理',
            description: '支持批量处理多个文件',
            isAvailableInFree: false,
          ),
          const SubscriptionFeature(
            id: 'advanced_tools',
            name: '高级工具',
            description: '访问所有高级编辑和处理工具',
            isAvailableInFree: false,
          ),
          const SubscriptionFeature(
            id: 'no_watermark',
            name: '无水印',
            description: '导出内容不含水印',
            isAvailableInFree: false,
          ),
          const SubscriptionFeature(
            id: 'priority_support',
            name: '优先支持',
            description: '获得优先客户支持',
            isAvailableInFree: false,
          ),
        ],
      ),

      // 终身买断（仅初始化基础结构，价格将从App Store获取）
      SubscriptionPlan(
        id: 'lifetime_premium',
        name: '内容君专业版永久',
        description: '一次性付费，终身使用',
        type: SubscriptionType.lifetime,
        price: 0, // 价格将从App Store动态获取
        currencyCode: 'CNY', // 货币代码将更新
        currencySymbol: '¥', // 货币符号将更新
        accentColor: Colors.amber,
        productId: _kLifetimeId,
        features: [
          const SubscriptionFeature(
            id: 'unlimited_exports',
            name: '无限导出',
            description: '无限制导出处理后的内容',
            isAvailableInFree: false,
          ),
          const SubscriptionFeature(
            id: 'batch_processing',
            name: '批量处理',
            description: '支持批量处理多个文件',
            isAvailableInFree: false,
          ),
          const SubscriptionFeature(
            id: 'advanced_tools',
            name: '高级工具',
            description: '访问所有高级编辑和处理工具',
            isAvailableInFree: false,
          ),
          const SubscriptionFeature(
            id: 'no_watermark',
            name: '无水印',
            description: '导出内容不含水印',
            isAvailableInFree: false,
          ),
          const SubscriptionFeature(
            id: 'priority_support',
            name: '优先支持',
            description: '获得优先客户支持',
            isAvailableInFree: false,
          ),
          const SubscriptionFeature(
            id: 'future_updates',
            name: '未来更新',
            description: '获得所有未来功能更新',
            isAvailableInFree: false,
          ),
        ],
      ),

      // 免费计划
      SubscriptionPlan(
        id: 'free',
        name: '免费版',
        description: '基础内容处理功能',
        type: SubscriptionType.free,
        price: 0,
        currencyCode: 'CNY',
        currencySymbol: '¥',
        accentColor: Colors.grey,
        features: [
          const SubscriptionFeature(
            id: 'basic_processing',
            name: '基础处理',
            description: '基础内容处理功能',
            isAvailableInFree: true,
          ),
          const SubscriptionFeature(
            id: 'watermarked_exports',
            name: '带水印导出',
            description: '导出内容带水印',
            isAvailableInFree: true,
          ),
        ],
      ),
    ];
  }

  /// 初始化应用内购买
  Future<void> _initInAppPurchase() async {
    try {
      debugPrint('开始初始化应用内购买...');

      // 监听购买更新
      final Stream<List<PurchaseDetails>> purchaseUpdated =
          _inAppPurchase.purchaseStream;
      _purchaseSubscription = purchaseUpdated.listen(
        _listenToPurchaseUpdated,
        onDone: () {
          debugPrint('购买流已关闭');
          _purchaseSubscription?.cancel();
        },
        onError: (error) {
          debugPrint('购买流错误: $error');
        },
      );

      // 检查应用内购买是否可用
      debugPrint('检查应用内购买是否可用...');
      final bool isAvailable = await _inAppPurchase.isAvailable();
      debugPrint('应用内购买可用状态: $isAvailable');

      if (!isAvailable) {
        debugPrint('应用内购买不可用');
        _isAvailable = false;
        return;
      }

      _isAvailable = true;

      // 配置平台特定设置
      if (Platform.isIOS) {
        debugPrint('配置iOS支付队列代理...');
        // 配置iOS支付队列代理
        final InAppPurchaseStoreKitPlatformAddition iosPlatformAddition =
            _inAppPurchase
                .getPlatformAddition<InAppPurchaseStoreKitPlatformAddition>();
        await iosPlatformAddition.setDelegate(_PaymentQueueDelegate());
      }

      // 加载产品信息
      debugPrint('开始加载产品信息...');
      await _loadProducts();

      // 检查待处理的购买
      if (Platform.isAndroid) {
        debugPrint('恢复Android购买...');
        await _inAppPurchase.restorePurchases();
      }

      debugPrint('应用内购买初始化完成');
    } catch (e, stackTrace) {
      debugPrint('初始化应用内购买失败: $e');
      debugPrint('错误堆栈: $stackTrace');
      _isAvailable = false;
    }
  }

  /// 加载产品信息
  Future<void> _loadProducts() async {
    try {
      debugPrint('开始查询产品，产品ID: $_kProductIds');
      final ProductDetailsResponse response = await _inAppPurchase
          .queryProductDetails(_kProductIds.toSet());

      if (response.notFoundIDs.isNotEmpty) {
        debugPrint('警告！未找到以下产品ID: ${response.notFoundIDs}');
        debugPrint('请检查App Store Connect中的产品配置是否正确，并确保产品状态为"已批准"');
      }

      _products = response.productDetails;
      debugPrint('成功加载产品数量: ${_products.length}/${_kProductIds.length}');

      if (_products.isEmpty) {
        debugPrint('未找到产品');
        if (kDebugMode) {
          // 不使用硬编码价格
        } else {
          // 线上环境不使用回退价格，停留在“价格加载中”状态
          _notifySubscriptionChanged();
        }
        return;
      }

      // 打印所有加载的产品详情
      for (var product in _products) {
        debugPrint('----------产品详情----------');
        debugPrint('产品ID: ${product.id}');
        debugPrint('产品标题: ${product.title}');
        debugPrint('产品描述: ${product.description}');
        debugPrint('产品价格: ${product.price} (${product.currencyCode})');
        debugPrint('原始价格: ${product.rawPrice}');

        // 尝试获取更多平台特定的信息
        if (Platform.isIOS) {
          debugPrint('平台: iOS App Store');
        } else if (Platform.isAndroid) {
          debugPrint('平台: Google Play Store');
        }
      }

      // 更新订阅计划的价格信息
      debugPrint('开始更新订阅计划价格信息...');
      for (final product in _products) {
        final planIndex = _plans.indexWhere(
          (plan) => plan.productId == product.id,
        );

        if (planIndex != -1) {
          // 从App Store获取价格信息（使用格式化价格展示）
          final price = product.rawPrice;
          final currencyCode = product.currencyCode;
          final currencySymbol = _getCurrencySymbol(currencyCode);
          final formatted = product.price; // 已本地化格式化的价格字符串

          debugPrint(
            '更新计划 ${_plans[planIndex].id} 的价格信息: $price $currencyCode',
          );

          // 创建新的计划对象，更新价格/币种/标题/描述信息
          _plans[planIndex] = SubscriptionPlan(
            id: _plans[planIndex].id,
            name: _plans[planIndex].name,
            description: _plans[planIndex].description,
            type: _plans[planIndex].type,
            price: price,
            currencyCode: currencyCode,
            currencySymbol: currencySymbol,
            storeFormattedPrice: formatted,
            storeTitle: product.title,
            storeDescription: product.description,
            accentColor: _plans[planIndex].accentColor,
            productId: _plans[planIndex].productId,
            features: _plans[planIndex].features,
          );

          debugPrint('计划 ${_plans[planIndex].id} 价格更新为: ${product.price}');
        } else {
          debugPrint('警告: 找不到匹配产品ID ${product.id} 的计划');
        }
      }

      debugPrint('订阅计划价格更新完成');

      // 通知订阅状态变化
      _notifySubscriptionChanged();
    } catch (e, stackTrace) {
      debugPrint('加载产品信息失败: $e');
      debugPrint('错误堆栈: $stackTrace');

      // 不使用回退价格，保持加载状态
      _notifySubscriptionChanged();
    }
  }

  /// 获取货币符号
  String _getCurrencySymbol(String currencyCode) {
    switch (currencyCode) {
      case 'CNY':
        return '¥';
      case 'USD':
        return '\$';
      case 'EUR':
        return '€';
      case 'GBP':
        return '£';
      case 'JPY':
        return '¥';
      default:
        return currencyCode;
    }
  }

  /// 安全地添加月份
  DateTime _addMonths(DateTime date, int months) {
    int newYear = date.year;
    int newMonth = date.month + months;

    // 处理月份溢出
    while (newMonth > 12) {
      newYear++;
      newMonth -= 12;
    }
    while (newMonth < 1) {
      newYear--;
      newMonth += 12;
    }

    // 处理日期溢出（如31号在2月不存在）
    int newDay = date.day;
    int daysInNewMonth = DateTime(newYear, newMonth + 1, 0).day;
    if (newDay > daysInNewMonth) {
      newDay = daysInNewMonth;
    }

    return DateTime(
      newYear,
      newMonth,
      newDay,
      date.hour,
      date.minute,
      date.second,
    );
  }

  /// 处理购买更新
  Future<void> _listenToPurchaseUpdated(
    List<PurchaseDetails> purchaseDetailsList,
  ) async {
    debugPrint('收到购买更新，数量: ${purchaseDetailsList.length}');

    for (final PurchaseDetails purchaseDetails in purchaseDetailsList) {
      debugPrint(
        '处理购买更新: ${purchaseDetails.productID}, 状态: ${purchaseDetails.status}',
      );

      if (purchaseDetails.status == PurchaseStatus.pending) {
        // 购买正在进行中
        _purchaseStatusController.add(PurchaseStatus.pending);
        debugPrint('购买正在进行中...');
      } else {
        if (purchaseDetails.status == PurchaseStatus.error) {
          // 购买失败
          _purchaseStatusController.add(PurchaseStatus.error);
          debugPrint(
            '购买失败: ${purchaseDetails.error?.message}, 错误代码: ${purchaseDetails.error?.code}, 详情: ${purchaseDetails.error?.details}',
          );
        } else if (purchaseDetails.status == PurchaseStatus.purchased ||
            purchaseDetails.status == PurchaseStatus.restored) {
          // 购买成功或恢复成功
          debugPrint(
            '购买${purchaseDetails.status == PurchaseStatus.restored ? "恢复" : "成功"}: ${purchaseDetails.productID}',
          );
          _purchaseStatusController.add(purchaseDetails.status);
          await _handlePurchaseComplete(purchaseDetails);
        } else if (purchaseDetails.status == PurchaseStatus.canceled) {
          // 购买被取消
          _purchaseStatusController.add(PurchaseStatus.canceled);
          debugPrint('购买被取消: ${purchaseDetails.productID}');
        }

        // 如果购买已完成，确认购买
        if (purchaseDetails.pendingCompletePurchase) {
          debugPrint('完成购买流程: ${purchaseDetails.productID}');
          await _inAppPurchase.completePurchase(purchaseDetails);
        }
      }
    }
  }

  /// 验证购买凭证
  Future<bool> _verifyPurchase(PurchaseDetails purchaseDetails) async {
    if (Platform.isIOS || Platform.isMacOS) {
      final String receiptData =
          purchaseDetails.verificationData.serverVerificationData;

      if (kDebugMode) {
        debugPrint('收据数据: $receiptData');
        return true;
      }

      try {
        final decodedReceipt = base64.decode(receiptData);

        // 基本验证
        if (decodedReceipt.isEmpty) {
          debugPrint('收据为空');
          return false;
        }

        // 验证产品ID
        if (!_kProductIds.contains(purchaseDetails.productID)) {
          debugPrint('未知的产品ID');
          return false;
        }

        // 验证购买状态
        if (purchaseDetails.status != PurchaseStatus.purchased) {
          debugPrint('购买状态无效');
          return false;
        }

        // 存储交易ID防止重复使用
        final String? lastTransactionId = _storage.getString(
          'last_transaction_id',
        );
        if (lastTransactionId == purchaseDetails.purchaseID) {
          debugPrint('重复的交易ID');
          return false;
        }

        // 保存新的交易ID
        await _storage.setString(
          'last_transaction_id',
          purchaseDetails.purchaseID ?? '',
        );

        return true;
      } catch (e) {
        debugPrint('验证收据时出错: $e');
        return false;
      }
    }

    return purchaseDetails.status == PurchaseStatus.purchased;
  }

  /// 处理购买完成
  Future<void> _handlePurchaseComplete(PurchaseDetails purchaseDetails) async {
    if (await _verifyPurchase(purchaseDetails)) {
      final String productId = purchaseDetails.productID;

      // 存储收据数据
      if (Platform.isIOS || Platform.isMacOS) {
        await _storage.setString(
          'last_receipt_data',
          purchaseDetails.verificationData.serverVerificationData,
        );
      }

      // 更新订阅状态
      await _updateSubscription(productId);

      // 通知UI更新
      _purchaseStatusController.add(PurchaseStatus.purchased);
    } else {
      debugPrint('购买验证失败');
      _purchaseStatusController.add(PurchaseStatus.error);
    }
  }

  /// 检查订阅是否已过期
  void _checkSubscriptionExpiry() {
    if (_subscription.type == SubscriptionType.lifetime) {
      // 终身订阅永不过期
      return;
    }

    if (_subscription.type == SubscriptionType.free) {
      // 免费订阅不会过期
      return;
    }

    // 检查结束日期
    if (_subscription.endDate != null &&
        _subscription.endDate!.isBefore(DateTime.now())) {
      // 订阅已过期，更新状态
      _subscription = _subscription.copyWith(
        status: SubscriptionStatus.expired,
      );
      _saveSubscription();
    }
  }

  /// 保存订阅信息
  Future<void> _saveSubscription() async {
    final subscriptionJson = json.encode(_subscription.toJson());
    await _storage.setString(AppConstants.keySubscription, subscriptionJson);

    // 通知订阅状态变化
    _notifySubscriptionChanged();
  }

  /// 通知订阅状态变化
  void _notifySubscriptionChanged() {
    _subscriptionController.add(_subscription);
  }

  /// 获取当前订阅信息
  UserSubscription get subscription => _subscription;

  /// 获取所有订阅计划
  List<SubscriptionPlan> get plans => _plans;

  /// 获取指定类型的订阅计划
  SubscriptionPlan? getPlanByType(SubscriptionType type) {
    try {
      return _plans.firstWhere((plan) => plan.type == type);
    } catch (e) {
      return null;
    }
  }

  /// 获取指定ID的订阅计划
  SubscriptionPlan? getPlanById(String id) {
    try {
      return _plans.firstWhere((plan) => plan.id == id);
    } catch (e) {
      return null;
    }
  }

  /// 检查用户是否有权访问特定功能
  bool canAccessFeature(String featureId) {
    // 如果是终身或有效的付费订阅，可以访问所有功能
    if (_subscription.isActive && _subscription.isPaid) {
      return true;
    }

    // 如果是免费订阅，检查该功能是否在免费计划中可用
    final freePlan = getPlanByType(SubscriptionType.free);
    if (freePlan != null) {
      return freePlan.features.any(
        (feature) => feature.id == featureId && feature.isAvailableInFree,
      );
    }

    return false;
  }

  /// 处理订阅购买
  Future<bool> purchaseSubscription(SubscriptionPlan plan) async {
    debugPrint('开始购买订阅: ${plan.id}, 产品ID: ${plan.productId}');

    if (!_isAvailable) {
      debugPrint('应用内购买不可用，无法继续购买');
      return false;
    }

    if (plan.productId == null) {
      debugPrint('产品ID为空，无法继续购买');
      return false;
    }

    try {
      // 查找产品详情
      debugPrint('查找产品详情...');

      // 如果产品列表为空，无法进行购买
      if (_products.isEmpty) {
        debugPrint('产品列表为空，无法进行购买');
        return false;
      }

      // 正常流程，查找产品详情
      final productDetails = _products.firstWhere(
        (product) => product.id == plan.productId,
        orElse: () {
          debugPrint(
            '未找到匹配的产品详情，可用产品: ${_products.map((p) => p.id).join(', ')}',
          );
          throw Exception('未找到产品详情');
        },
      );

      debugPrint('找到产品详情: ${productDetails.id}, 价格: ${productDetails.price}');

      // 发起购买
      final PurchaseParam purchaseParam = PurchaseParam(
        productDetails: productDetails,
        applicationUserName: null,
      );

      debugPrint('开始执行购买操作...');
      if (plan.type == SubscriptionType.lifetime) {
        // 一次性购买
        debugPrint('执行一次性购买: ${plan.productId}');
        return await _inAppPurchase.buyNonConsumable(
          purchaseParam: purchaseParam,
        );
      } else {
        // 订阅购买
        debugPrint('执行订阅购买: ${plan.productId}');
        return await _inAppPurchase.buyNonConsumable(
          purchaseParam: purchaseParam,
        );
      }
    } catch (e, stackTrace) {
      debugPrint('购买订阅失败: $e');
      debugPrint('错误堆栈: $stackTrace');
      return false;
    }
  }

  /// 恢复购买
  Future<bool> restorePurchases() async {
    if (!_isAvailable) {
      debugPrint('应用内购买不可用');
      return false;
    }

    try {
      // 产品列表为空时，无法恢复购买
      if (_products.isEmpty) {
        debugPrint('产品列表为空，无法恢复购买');
        return false;
      }

      // 监听恢复结果（等待 restored 事件，设置超时）
      final completer = Completer<bool>();
      bool completed = false;
      late StreamSubscription<PurchaseStatus> statusSub;
      statusSub = purchaseStatusStream.listen((status) {
        if (status == PurchaseStatus.restored && !completed) {
          completed = true;
          completer.complete(true);
        }
      });

      // 触发恢复
      await _inAppPurchase.restorePurchases();

      // 超时未收到 restored 事件则认为没有可恢复的购买
      Future.delayed(const Duration(seconds: 8)).then((_) {
        if (!completed && !completer.isCompleted) {
          completer.complete(false);
        }
      });

      final result = await completer.future;
      await statusSub.cancel();
      return result;
    } catch (e) {
      debugPrint('恢复购买失败: $e');
      return false;
    }
  }

  /// 取消订阅
  Future<bool> cancelSubscription() async {
    // 注意：在iOS和macOS上，用户需要通过App Store取消订阅
    // 这个方法主要用于更新本地状态
    debugPrint('取消订阅...');

    // 更新订阅状态为已过期
    _subscription = _subscription.copyWith(status: SubscriptionStatus.expired);
    await _saveSubscription();

    return true;
  }

  /// 检查是否需要显示订阅页面
  bool shouldShowSubscriptionScreen() {
    return !_subscription.isActive ||
        _subscription.type == SubscriptionType.free;
  }

  /// 检查是否需要显示续订页面
  bool shouldShowRenewalScreen() {
    return _subscription.isExpired &&
        _subscription.type != SubscriptionType.free;
  }

  /// 处理应用启动时的订阅检查
  Future<void> handleAppStart() async {
    // 检查订阅状态
    _checkSubscriptionExpiry();

    // 如果是iOS或macOS，验证订阅收据
    if ((Platform.isIOS || Platform.isMacOS) &&
        _subscription.isActive &&
        _subscription.isPaid &&
        _subscription.receiptData != null) {
      // 验证收据
      // 这里需要集成实际的收据验证逻辑
    }
  }

  /// 更新订阅状态（供开发调试使用）
  Future<void> updateSubscription(UserSubscription subscription) async {
    _subscription = subscription;
    await _saveSubscription();
    _notifySubscriptionChanged();
  }

  /// 释放资源
  void dispose() {
    _purchaseSubscription?.cancel();
    _subscriptionController.close();
    _purchaseStatusController.close();
  }

  /// 更新订阅状态
  Future<void> _updateSubscription(String productId) async {
    final now = DateTime.now();
    DateTime? endDate;
    SubscriptionType type;

    switch (productId) {
      case _kMonthlyId:
        type = SubscriptionType.monthly;
        // 安全地添加一个月
        endDate = _addMonths(now, 1);
        break;
      case _kYearlyId:
        type = SubscriptionType.yearly;
        // 安全地添加一年
        endDate = _addMonths(now, 12);
        break;
      case _kLifetimeId:
        type = SubscriptionType.lifetime;
        // 终身订阅没有结束日期
        endDate = null;
        break;
      default:
        type = SubscriptionType.free;
        // 免费订阅没有结束日期
        endDate = null;
        break;
    }

    // 获取收据数据
    final receiptData = _storage.getString('last_receipt_data');

    _subscription = UserSubscription(
      status: SubscriptionStatus.active,
      type: type,
      startDate: now,
      endDate: endDate,
      transactionId: DateTime.now().millisecondsSinceEpoch.toString(),
      receiptData: receiptData,
    );

    await _saveSubscription();
    _notifySubscriptionChanged();
  }

  /// 检查IAP服务状态
  Future<String> checkIAPServiceStatus() async {
    final StringBuffer status = StringBuffer();

    status.writeln('===== IAP服务状态检查 =====');

    // 检查IAP服务是否可用
    final bool isAvailable = await _inAppPurchase.isAvailable();
    status.writeln('IAP服务可用: $isAvailable');

    if (!isAvailable) {
      status.writeln('错误: IAP服务不可用，无法进行购买');
      return status.toString();
    }

    // 检查产品ID是否正确
    status.writeln('产品ID列表: $_kProductIds');

    // 查询产品详情
    final ProductDetailsResponse response = await _inAppPurchase
        .queryProductDetails(_kProductIds.toSet());

    status.writeln('查询到的产品数量: ${response.productDetails.length}');
    status.writeln('未找到的产品ID: ${response.notFoundIDs}');

    if (response.productDetails.isEmpty) {
      status.writeln('错误: 没有找到任何产品，请检查产品ID配置和App Store Connect设置');
    } else {
      status.writeln('---产品详情---');
      for (final product in response.productDetails) {
        status.writeln('产品: ${product.id}, 价格: ${product.price}');
      }
    }

    // 检查当前设备和环境
    status.writeln('---环境信息---');
    status.writeln('平台: ${Platform.operatingSystem}');
    status.writeln('调试模式: $kDebugMode');

    if (Platform.isIOS) {
      try {
        status.writeln('检查iOS特定配置...');
        status.writeln('注意: 请确保已配置StoreKit，并且使用的是有效的沙盒测试账号');
        status.writeln('注意: 请确保App Store Connect中产品状态为"已批准"');
      } catch (e) {
        status.writeln('iOS特定检查出错: $e');
      }
    }

    status.writeln('===== 检查完成 =====');

    return status.toString();
  }

  /// 解决常见的购买问题
  Future<bool> troubleshootPurchaseIssues() async {
    debugPrint('开始排查购买问题...');

    // 1. 检查IAP服务是否可用
    if (!_isAvailable) {
      debugPrint('IAP服务不可用，尝试重新初始化...');
      await _initInAppPurchase();
      if (!_isAvailable) {
        debugPrint('重新初始化后IAP服务仍不可用');
        return false;
      }
    }

    // 2. 确保产品已正确加载
    if (_products.isEmpty) {
      debugPrint('没有找到产品，尝试重新加载产品...');
      await _loadProducts();
      if (_products.isEmpty) {
        debugPrint('重新加载后仍未找到产品');
        return false;
      }
    }

    // 3. 如果是iOS，重新设置delegate
    if (Platform.isIOS) {
      debugPrint('重新配置iOS支付队列代理...');
      try {
        final InAppPurchaseStoreKitPlatformAddition iosPlatformAddition =
            _inAppPurchase
                .getPlatformAddition<InAppPurchaseStoreKitPlatformAddition>();
        await iosPlatformAddition.setDelegate(_PaymentQueueDelegate());
      } catch (e) {
        debugPrint('设置iOS代理失败: $e');
      }
    }

    // 4. 在iOS上，尝试恢复收据
    if (Platform.isIOS) {
      debugPrint('尝试恢复收据...');
      await _inAppPurchase.restorePurchases();
    }

    debugPrint('排查完成');
    return true;
  }

  /// 打印产品ID用于调试
  void printProductIdsForDebug() {
    final StringBuffer sb = StringBuffer();
    sb.writeln('===== 产品ID (请复制以下内容用于核对) =====');
    for (final id in _kProductIds) {
      sb.writeln(id);
    }
    sb.writeln('=========================================');
    debugPrint(sb.toString());
  }
}

/// iOS支付队列代理
class _PaymentQueueDelegate implements SKPaymentQueueDelegateWrapper {
  @override
  bool shouldContinueTransaction(
    SKPaymentTransactionWrapper transaction,
    SKStorefrontWrapper storefront,
  ) {
    return true;
  }

  @override
  bool shouldShowPriceConsent() {
    return false;
  }
}
