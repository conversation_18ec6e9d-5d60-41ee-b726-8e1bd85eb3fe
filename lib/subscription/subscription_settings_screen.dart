import 'dart:math' as math;

import 'package:flutter/material.dart';

import '../config/app_theme.dart';
import '../generated/l10n/app_localizations.dart';
import '../services/service_locator.dart';
import 'subscription_model.dart';
import 'subscription_screen.dart';
import 'subscription_service.dart';

class SubscriptionSettingsScreen extends StatefulWidget {
  const SubscriptionSettingsScreen({super.key});

  @override
  State<SubscriptionSettingsScreen> createState() =>
      _SubscriptionSettingsScreenState();
}

class _SubscriptionSettingsScreenState extends State<SubscriptionSettingsScreen>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  final ScrollController _scrollController = ScrollController();
  bool _isScrolled = false;
  late SubscriptionService _subscriptionService;

  @override
  void initState() {
    super.initState();
    _subscriptionService = ServiceLocator().subscriptionService;
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 20),
    )..repeat();

    _scrollController.addListener(_onScroll);
  }

  void _onScroll() {
    if (_scrollController.offset > 0 && !_isScrolled) {
      setState(() => _isScrolled = true);
    } else if (_scrollController.offset <= 0 && _isScrolled) {
      setState(() => _isScrolled = false);
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.bgLightColor,
      body: Stack(
        children: [
          // 背景渐变动画
          Positioned.fill(
            child: AnimatedBuilder(
              animation: _animationController,
              builder: (context, child) {
                return CustomPaint(
                  painter: SubscriptionBackgroundPainter(
                    _animationController.value,
                  ),
                );
              },
            ),
          ),
          // 主内容
          SafeArea(
            child: CustomScrollView(
              controller: _scrollController,
              slivers: [
                // 应用栏
                SliverAppBar(
                  expandedHeight: 160,
                  floating: false,
                  pinned: true,
                  elevation: _isScrolled ? 4 : 0,
                  backgroundColor:
                      _isScrolled
                          ? AppTheme.bgWhiteColor.withValues(alpha: 0.95)
                          : Colors.transparent,
                  leading: IconButton(
                    icon: Icon(
                      Icons.arrow_back,
                      color:
                          _isScrolled
                              ? AppTheme.textDarkColor
                              : AppTheme.bgWhiteColor,
                    ),
                    onPressed: () => Navigator.of(context).pop(),
                  ),
                  flexibleSpace: FlexibleSpaceBar(
                    title: Text(
                      AppLocalizations.of(context).subscriptionManagement,
                      style: TextStyle(
                        color:
                            _isScrolled
                                ? AppTheme.textDarkColor
                                : AppTheme.bgWhiteColor,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    background: Stack(
                      fit: StackFit.expand,
                      children: [
                        // 顶部装饰图案
                        Positioned(
                          top: -30,
                          right: -30,
                          child: Transform.rotate(
                            angle: math.pi / 3,
                            child: Container(
                              width: 180,
                              height: 180,
                              decoration: BoxDecoration(
                                gradient: AppTheme.orangeGradient,
                                borderRadius: BorderRadius.circular(40),
                              ),
                            ),
                          ),
                        ),
                        Positioned(
                          top: 50,
                          left: -50,
                          child: Transform.rotate(
                            angle: -math.pi / 5,
                            child: Opacity(
                              opacity: 0.7,
                              child: Container(
                                width: 120,
                                height: 120,
                                decoration: BoxDecoration(
                                  gradient: AppTheme.yellowGradient,
                                  borderRadius: BorderRadius.circular(30),
                                ),
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),

                // 主要内容区域
                SliverPadding(
                  padding: const EdgeInsets.all(16.0),
                  sliver: SliverList(
                    delegate: SliverChildListDelegate([
                      const SizedBox(height: 8),

                      // 当前订阅信息
                      _buildSubscriptionInfoCard(),

                      const SizedBox(height: 16),

                      // 订阅管理选项
                      _buildSectionHeader(AppLocalizations.of(context).subscriptionManagement, Icons.subscriptions),

                      _buildActionCard(
                        AppLocalizations.of(context).upgradeSubscription,
                        AppLocalizations.of(context).upgradeSubscriptionDesc,
                        Icons.upgrade,
                        AppTheme.primaryGradient,
                        () {
                          // 导航到订阅购买页面
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (context) => const SubscriptionScreen(),
                            ),
                          );
                        },
                      ),

                      _buildActionCard(
                        AppLocalizations.of(context).restorePurchase,
                        AppLocalizations.of(context).restorePurchaseDesc,
                        Icons.restore,
                        AppTheme.blueGradient,
                        () {
                          // 恢复购买逻辑
                          _showRestoringDialog();
                        },
                      ),

                      const SizedBox(height: 16),

                      // 帮助和支持
                      _buildSectionHeader(AppLocalizations.of(context).helpAndSupport, Icons.help),

                      _buildSimpleActionTile(
                        AppLocalizations.of(context).frequentlyAskedQuestions,
                        AppLocalizations.of(context).frequentlyAskedQuestionsDesc,
                        Icons.help_outline,
                        onTap: () {
                          // 打开FAQ页面
                        },
                      ),

                      _buildSimpleActionTile(
                        AppLocalizations.of(context).contactCustomerService,
                        AppLocalizations.of(context).contactCustomerServiceDesc,
                        Icons.contact_support,
                        onTap: () {
                          // 打开联系页面
                        },
                      ),

                      _buildSimpleActionTile(
                        AppLocalizations.of(context).refundPolicy,
                        AppLocalizations.of(context).refundPolicyDesc,
                        Icons.policy,
                        onTap: () {
                          // 打开退款政策页面
                        },
                      ),

                      const SizedBox(height: 100), // 底部留白
                    ]),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  // 显示恢复购买对话框
  void _showRestoringDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder:
          (context) => AlertDialog(
            title: Text(AppLocalizations.of(context).restoringPurchase),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const CircularProgressIndicator(),
                const SizedBox(height: 16),
                Text(AppLocalizations.of(context).communicatingWithAppStore),
              ],
            ),
          ),
    );

    // 模拟恢复过程
    Future.delayed(const Duration(seconds: 2), () {
      Navigator.of(context).pop();
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(AppLocalizations.of(context).noRestorablePurchasesFound),
          backgroundColor: AppTheme.redDark,
        ),
      );
    });
  }

  // 构建订阅信息卡片
  Widget _buildSubscriptionInfoCard() {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      child: Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16),
          gradient: LinearGradient(
            colors: [
              AppTheme.orangeLight.withValues(alpha: 0.3),
              AppTheme.orangeLight.withValues(alpha: 0.1),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    gradient: AppTheme.orangeGradient,
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: const Icon(
                    Icons.workspace_premium,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        AppLocalizations.of(context).currentSubscription,
                        style: const TextStyle(
                          fontSize: 16,
                          color: AppTheme.textMediumColor,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        _getSubscriptionDisplayName(),
                        style: const TextStyle(
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                          color: AppTheme.textDarkColor,
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    color: _getSubscriptionStatusColor(),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Text(
                    _getSubscriptionStatusText(),
                    style: TextStyle(
                      color: _getSubscriptionStatusTextColor(),
                      fontWeight: FontWeight.bold,
                      fontSize: 12,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 20),
            Text(
              AppLocalizations.of(context).availableFeatures,
              style: const TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: AppTheme.textDarkColor,
              ),
            ),
            const SizedBox(height: 12),
            _buildFeatureItem(AppLocalizations.of(context).basicProcessing, true),
            _buildFeatureItem(AppLocalizations.of(context).exportWithWatermark, true),
            _buildFeatureItem(AppLocalizations.of(context).unlimitedExport, false),
            _buildFeatureItem(AppLocalizations.of(context).batchProcessing, false),
            _buildFeatureItem(AppLocalizations.of(context).advancedTools, false),
          ],
        ),
      ),
    );
  }

  // 构建功能项
  Widget _buildFeatureItem(String feature, bool isAvailable) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        children: [
          Icon(
            isAvailable ? Icons.check_circle : Icons.cancel,
            color: isAvailable ? AppTheme.greenDark : AppTheme.textLightColor,
            size: 20,
          ),
          const SizedBox(width: 8),
          Text(
            feature,
            style: TextStyle(
              fontSize: 14,
              color:
                  isAvailable
                      ? AppTheme.textDarkColor
                      : AppTheme.textLightColor,
            ),
          ),
        ],
      ),
    );
  }

  // 构建区块标题
  Widget _buildSectionHeader(String title, IconData icon) {
    return Padding(
      padding: const EdgeInsets.only(left: 8, top: 8, bottom: 12),
      child: Row(
        children: [
          Icon(icon, size: 20, color: AppTheme.primaryColor),
          const SizedBox(width: 8),
          Text(
            title,
            style: const TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: AppTheme.textDarkColor,
            ),
          ),
        ],
      ),
    );
  }

  // 构建操作卡片
  Widget _buildActionCard(
    String title,
    String description,
    IconData icon,
    LinearGradient gradient,
    VoidCallback onTap,
  ) {
    return Card(
      elevation: 1,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
      margin: const EdgeInsets.only(bottom: 12),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(16),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(16),
            gradient: LinearGradient(
              colors: [
                gradient.colors.first.withValues(alpha: 0.1),
                gradient.colors.last.withValues(alpha: 0.05),
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
          ),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(10),
                decoration: BoxDecoration(
                  gradient: gradient,
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(icon, color: Colors.white, size: 22),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                        color: AppTheme.textDarkColor,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      description,
                      style: const TextStyle(
                        fontSize: 14,
                        color: AppTheme.textMediumColor,
                      ),
                    ),
                  ],
                ),
              ),
              const Icon(
                Icons.arrow_forward_ios,
                color: AppTheme.textLightColor,
                size: 16,
              ),
            ],
          ),
        ),
      ),
    );
  }

  // 构建简单操作项
  Widget _buildSimpleActionTile(
    String title,
    String description,
    IconData icon, {
    required VoidCallback onTap,
  }) {
    return Card(
      elevation: 0,
      color: AppTheme.bgWhiteColor,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(16),
        side: const BorderSide(color: AppTheme.borderColor),
      ),
      margin: const EdgeInsets.only(bottom: 8),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(16),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Icon(icon, color: AppTheme.primaryColor, size: 22),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                        color: AppTheme.textDarkColor,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      description,
                      style: const TextStyle(
                        fontSize: 14,
                        color: AppTheme.textMediumColor,
                      ),
                    ),
                  ],
                ),
              ),
              const Icon(
                Icons.arrow_forward_ios,
                color: AppTheme.textLightColor,
                size: 16,
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 获取订阅显示名称
  String _getSubscriptionDisplayName() {
    final subscription = _subscriptionService.subscription;
    final l10n = AppLocalizations.of(context);

    if (!subscription.isActive || subscription.type == SubscriptionType.free) {
      return l10n.freeVersion;
    }

    switch (subscription.type) {
      case SubscriptionType.monthly:
        return l10n.subscriptionPlanMonthlyName;
      case SubscriptionType.yearly:
        return l10n.subscriptionPlanYearlyName;
      case SubscriptionType.lifetime:
        return l10n.subscriptionPlanLifetimeName;
      case SubscriptionType.free:
        return l10n.subscriptionPlanFreeName;
    }
  }

  /// 获取订阅状态文本
  String _getSubscriptionStatusText() {
    final subscription = _subscriptionService.subscription;
    final l10n = AppLocalizations.of(context);

    if (subscription.isActive) {
      return l10n.active;
    } else if (subscription.isExpired) {
      return l10n.expired;
    } else {
      return l10n.inactive;
    }
  }

  /// 获取订阅状态背景颜色
  Color _getSubscriptionStatusColor() {
    final subscription = _subscriptionService.subscription;

    if (subscription.isActive) {
      return AppTheme.greenLight;
    } else if (subscription.isExpired) {
      return AppTheme.redLight;
    } else {
      return AppTheme.bgLightColor;
    }
  }

  /// 获取订阅状态文本颜色
  Color _getSubscriptionStatusTextColor() {
    final subscription = _subscriptionService.subscription;

    if (subscription.isActive) {
      return AppTheme.greenDark;
    } else if (subscription.isExpired) {
      return AppTheme.redDark;
    } else {
      return AppTheme.textMediumColor;
    }
  }
}

// 背景动画绘制器
class SubscriptionBackgroundPainter extends CustomPainter {
  final double animationValue;

  SubscriptionBackgroundPainter(this.animationValue);

  @override
  void paint(Canvas canvas, Size size) {
    final paint =
        Paint()
          ..shader = LinearGradient(
            colors: [
              AppTheme.orangeDark.withValues(alpha: 0.2),
              AppTheme.yellowDark.withValues(alpha: 0.1),
            ],
            begin: Alignment.topRight,
            end: Alignment.bottomLeft,
          ).createShader(Rect.fromLTWH(0, 0, size.width, size.height));

    final path = Path();

    // 绘制波浪背景
    for (int i = 0; i < 5; i++) {
      final wavePhase = i * 0.5 + animationValue * math.pi * 2;
      final waveAmplitude = 20.0 - i * 3.0;
      final waveFrequency = 0.02 + i * 0.005;

      path.reset();
      path.moveTo(0, size.height * 0.25 + math.sin(wavePhase) * waveAmplitude);

      for (double x = 0; x <= size.width; x += 5) {
        double y =
            size.height * 0.25 +
            math.sin(wavePhase + x * waveFrequency) * waveAmplitude;
        path.lineTo(x, y);
      }

      path.lineTo(size.width, size.height);
      path.lineTo(0, size.height);
      path.close();

      canvas.drawPath(
        path,
        paint..color = AppTheme.orangeDark.withValues(alpha: 0.05 - i * 0.01),
      );
    }

    // 添加浮动的圆点
    for (int i = 0; i < 12; i++) {
      final x =
          (size.width * 0.2) +
          math.sin(animationValue * 2 + i) * (size.width * 0.3);
      final y =
          (size.height * 0.1) +
          math.cos(animationValue * 2 + i * 0.8) * (size.height * 0.2);
      final radius = 2.0 + (math.sin(animationValue * 3 + i * 0.5) + 1) * 4;

      canvas.drawCircle(
        Offset(x, y),
        radius,
        Paint()
          ..color = AppTheme.orangeDark.withValues(alpha: 0.15)
          ..style = PaintingStyle.fill,
      );
    }
  }

  @override
  bool shouldRepaint(SubscriptionBackgroundPainter oldDelegate) =>
      oldDelegate.animationValue != animationValue;
}
