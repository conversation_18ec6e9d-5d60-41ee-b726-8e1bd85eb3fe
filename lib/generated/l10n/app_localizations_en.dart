// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get appName => 'ContentPal';

  @override
  String get appNameChinese => '内容君';

  @override
  String get appDescription =>
      'Professional content processing tool that makes content creation easier';

  @override
  String get home => 'Home';

  @override
  String get settings => 'Settings';

  @override
  String get language => 'Language';

  @override
  String get theme => 'Theme';

  @override
  String get lightTheme => 'Light';

  @override
  String get darkTheme => 'Dark';

  @override
  String get systemTheme => 'System';

  @override
  String get sharedText => 'Shared Text';

  @override
  String failedToGetInitialIntent(Object error) {
    return 'Failed to get initial intent: $error';
  }

  @override
  String failedToLoadThemeSettings(Object error) {
    return 'Failed to load theme settings: $error';
  }

  @override
  String get themeMaterialYou => 'Material You';

  @override
  String get themeMorandi => 'Morandi Style';

  @override
  String get themeMonochrome => 'Minimalist Black & White';

  @override
  String get themeNature => 'Natural Colors';

  @override
  String get themeTech => 'Tech Style';

  @override
  String get themeChinese => 'Traditional Chinese Colors';

  @override
  String get themeMaterialYouDesc =>
      'Dynamic theme automatically extracted from wallpaper';

  @override
  String get themeMorandiDesc => 'Soft and elegant Morandi color palette';

  @override
  String get themeMonochromeDesc =>
      'Simple and pure black and white color scheme';

  @override
  String get themeNatureDesc =>
      'Comfortable and natural ecological color system';

  @override
  String get themeTechDesc => 'Futuristic technology colors';

  @override
  String get themeChineseDesc =>
      'Eastern aesthetics combining tradition and modernity';

  @override
  String get markdown => 'Markdown';

  @override
  String get textCards => 'Text Cards';

  @override
  String get textCardSelectColor => 'Select Color';

  @override
  String get textCardStyleApplied => 'Style Applied';

  @override
  String textCardSplitFailed(Object error) {
    return 'Split failed: $error';
  }

  @override
  String textCardCreateFailed(Object error) {
    return 'Create failed: $error';
  }

  @override
  String get textCardEditCard => 'Edit Card';

  @override
  String get textCardPreviousStep => 'Previous Step';

  @override
  String get textCardSaveToContentLibrary => 'Save to Content Library';

  @override
  String get textCardStartExportingImage => 'Starting image export...';

  @override
  String get textCardImageSavedSuccess =>
      '✅ Image successfully saved to photo album';

  @override
  String get textCardPleaseEnterContent => 'Please enter card content';

  @override
  String get textCardDeleteCard => 'Delete Card';

  @override
  String get textCardDeleteConfirm =>
      'Are you sure you want to delete this card? This action cannot be undone.';

  @override
  String textCardCategory(Object category) {
    return 'Category: $category';
  }

  @override
  String textCardDescription(Object description) {
    return 'Description: $description';
  }

  @override
  String get textCardClose => 'Close';

  @override
  String get textCardUseTemplate => 'Use Template';

  @override
  String get textCardConfirmExport => 'Confirm Export';

  @override
  String get textCardQuality => 'Quality';

  @override
  String get textCardIncludeWatermark => 'Include Watermark';

  @override
  String get textCardPreviewInfo => 'Preview Info';

  @override
  String textCardRatio(Object ratio) {
    return 'Ratio: $ratio';
  }

  @override
  String textCardQualityPercent(Object quality) {
    return 'Quality: $quality%';
  }

  @override
  String textCardWatermarkStatus(Object status) {
    return 'Watermark: $status';
  }

  @override
  String get textCardDimensions => 'Dimensions';

  @override
  String get textCardInclude => 'Include';

  @override
  String get textCardNotInclude => 'Not Include';

  @override
  String get textCardAddCard => 'Add Card';

  @override
  String get textCardAddNewCard => 'Add New Card';

  @override
  String get textCardEdit => 'Edit';

  @override
  String get textCardExportingImage => 'Exporting image...';

  @override
  String get textCardExportSuccess =>
      '✅ Export successful! Saved to photo album';

  @override
  String textCardExportFailed(Object error) {
    return 'Export failed:';
  }

  @override
  String get textCardAddWatermark => 'Add Watermark';

  @override
  String get textCardAddWatermarkDesc => 'Add app watermark to image corner';

  @override
  String get textCardIncludeTitle => 'Include Title';

  @override
  String get textCardIncludeTitleDesc => 'Show title in exported image';

  @override
  String get textCardIncludeTimestamp => 'Include Timestamp';

  @override
  String get textCardIncludeTimestampDesc => 'Show creation time in image';

  @override
  String get textCardImageQuality => 'Image Quality';

  @override
  String textCardTextStyleCustomization(Object title) {
    return 'Text Style Customization';
  }

  @override
  String get textCardExportCurrentCard => 'Export Current Card';

  @override
  String get textCardBatchExport => 'Batch Export';

  @override
  String get textCardClearSelection => 'Clear Selection';

  @override
  String get textCardResetStyle => 'Reset Style';

  @override
  String get textCardResetStyleConfirm =>
      'Are you sure you want to clear all text styles? This action cannot be undone.';

  @override
  String get textCardExportAsImage => 'Export as Image';

  @override
  String get textCardExportAsImageDesc => 'Save this card as image';

  @override
  String get textCardEditCardDesc => 'Modify title and content';

  @override
  String get textCardDeleteCardDesc => 'Remove from document';

  @override
  String textCardDeleteCardConfirm(Object title) {
    return 'Are you sure you want to delete card \"$title\"? This action cannot be undone.';
  }

  @override
  String get textCardEditDocument => 'Edit Document';

  @override
  String get textCardUniformStyle => 'Uniform Style';

  @override
  String get textCardExportImage => 'Export Image';

  @override
  String get textCardInsertSeparator => 'Insert Separator';

  @override
  String get textCardPleaseEnterTitleAndCards =>
      'Please enter title and ensure at least one card';

  @override
  String textCardSaveFailed(Object error) {
    return 'Save failed';
  }

  @override
  String get textCardContentRendering => 'Content Rendering';

  @override
  String textCardExportWithTitle(Object title) {
    return 'Export - $title';
  }

  @override
  String get textCardShare => 'Share';

  @override
  String get textCardSaveToAlbum => 'Save to Album';

  @override
  String get textCardUnderstood => 'Understood';

  @override
  String get textCardStartExperience => 'Start Experience';

  @override
  String get textCardFeatureDemo => 'Feature Demo';

  @override
  String get textCardGotIt => 'Got it';

  @override
  String get textCardStartUsing => 'Start Using';

  @override
  String get textCardPreviewEffect => 'Preview Effect';

  @override
  String get textCardExportInfo => 'Export Info';

  @override
  String get textCardImageDimensions => 'Image Dimensions';

  @override
  String get textCardAspectRatio => 'Aspect Ratio';

  @override
  String get textCardFileSize => 'File Size';

  @override
  String get textCardUsageScenario => 'Usage Scenario';

  @override
  String get textCardBestQuality => 'Best Quality (100%)';

  @override
  String get textCardWatermarkDescription =>
      'Add app identifier at the bottom of the image';

  @override
  String get pdf => 'PDF';

  @override
  String get voice => 'Voice';

  @override
  String get html => 'HTML';

  @override
  String get svg => 'SVG';

  @override
  String get content => 'Content';

  @override
  String get trafficGuide => 'Traffic Guide';

  @override
  String get create => 'Create';

  @override
  String get edit => 'Edit';

  @override
  String get delete => 'Delete';

  @override
  String get save => 'Save';

  @override
  String get cancel => 'Cancel';

  @override
  String get confirm => 'OK';

  @override
  String get yes => 'Yes';

  @override
  String get no => 'No';

  @override
  String get ok => 'OK';

  @override
  String get loading => 'Loading...';

  @override
  String get error => 'Error';

  @override
  String get success => 'Success';

  @override
  String get warning => 'Warning';

  @override
  String get info => 'Info';

  @override
  String get search => 'Search';

  @override
  String get searchHint => 'Enter search terms...';

  @override
  String get noResults => 'No results found';

  @override
  String get tryAgain => 'Try Again';

  @override
  String get refresh => 'Refresh';

  @override
  String get share => 'Share';

  @override
  String get export => 'Export';

  @override
  String get import => 'Import';

  @override
  String get copy => 'Copy';

  @override
  String get paste => 'Paste';

  @override
  String get cut => 'Cut';

  @override
  String get undo => 'Undo';

  @override
  String get redo => 'Redo';

  @override
  String get selectAll => 'Select All';

  @override
  String get close => 'Close';

  @override
  String get back => 'Back';

  @override
  String get next => 'Next';

  @override
  String get previous => 'Previous';

  @override
  String get done => 'Done';

  @override
  String get finish => 'Finish';

  @override
  String get skip => 'Skip';

  @override
  String get continueAction => 'Continue';

  @override
  String get retry => 'Retry';

  @override
  String get reset => 'Reset';

  @override
  String get clear => 'Clear';

  @override
  String get apply => 'Apply';

  @override
  String get preview => 'Preview';

  @override
  String get download => 'Download';

  @override
  String get upload => 'Upload';

  @override
  String get file => 'File';

  @override
  String get folder => 'Folder';

  @override
  String get name => 'Name';

  @override
  String get title => 'Title';

  @override
  String get description => 'Description';

  @override
  String get size => 'Size';

  @override
  String get date => 'Date';

  @override
  String get time => 'Time';

  @override
  String get type => 'Type';

  @override
  String get status => 'Status';

  @override
  String get version => 'Version';

  @override
  String get author => 'Author';

  @override
  String get tags => 'Tags';

  @override
  String get category => 'Category';

  @override
  String get priority => 'Priority';

  @override
  String get high => 'High';

  @override
  String get medium => 'Medium';

  @override
  String get low => 'Low';

  @override
  String get enabled => 'Enabled';

  @override
  String get disabled => 'Disabled';

  @override
  String get online => 'Online';

  @override
  String get offline => 'Offline';

  @override
  String get connected => 'Connected';

  @override
  String get disconnected => 'Disconnected';

  @override
  String get available => 'Available';

  @override
  String get unavailable => 'Unavailable';

  @override
  String get active => 'Active';

  @override
  String get inactive => 'Inactive';

  @override
  String get public => 'Public';

  @override
  String get private => 'Private';

  @override
  String get draft => 'Draft';

  @override
  String get published => 'Published';

  @override
  String get archived => 'Archived';

  @override
  String get pdfProfessionalTool => 'PDF Professional Tool';

  @override
  String get pdfToolDescription =>
      'Powerful PDF processing capabilities that make document management easier';

  @override
  String get securityEncryption => 'Security Encryption';

  @override
  String get passwordProtectionPermissionControl =>
      'Password Protection\nPermission Control';

  @override
  String get intelligentAnnotation => 'Intelligent Annotation';

  @override
  String get highlightMarkingTextAnnotation =>
      'Highlight Marking\nText Annotation';

  @override
  String get quickSearch => 'Quick Search';

  @override
  String get fullTextSearchContentLocation =>
      'Full-text Search\nContent Location';

  @override
  String get convenientSharing => 'Convenient Sharing';

  @override
  String get multipleFormatsOneClickExport =>
      'Multiple Formats\nOne-click Export';

  @override
  String get welcomeToPdfTool => 'Welcome to PDF Professional Tool!';

  @override
  String get importFirstPdfDocument => 'Import First PDF Document';

  @override
  String get appearance => 'Appearance';

  @override
  String get followSystem => 'Follow System';

  @override
  String get languageChangeEffect =>
      'Language changes will take effect immediately';

  @override
  String get contentLibrary => 'Content Library';

  @override
  String get manageAllCards => 'Manage All Cards';

  @override
  String get templateLibrary => 'Template Library';

  @override
  String get browseBeautifulTemplates => 'Browse Beautiful Templates';

  @override
  String get inputTextToSplit => 'Input Text to Split';

  @override
  String get pasteOrInputLongText => 'Paste or input long text content';

  @override
  String get pasteClipboard => 'Paste Clipboard';

  @override
  String get clearContent => 'Clear Content';

  @override
  String cardNumber(int number) {
    return 'Card $number';
  }

  @override
  String get loadingDemoData => 'Loading demo data...';

  @override
  String get modernUIDesign =>
      '✨ Modern UI Design\n🖼️ Render Result Preview\n📱 Block Mode Support\n⚡ High Performance Experience';

  @override
  String editFunction(String title) {
    return 'Edit function: $title';
  }

  @override
  String deleted(String title) {
    return 'Deleted: $title';
  }

  @override
  String shareFunction(String title) {
    return 'Share function: $title';
  }

  @override
  String get createNewContent => 'Create New Content';

  @override
  String get selectContentType =>
      'Select the type of content you want to create';

  @override
  String get bold => '**Bold**';

  @override
  String get italic => '*Italic*';

  @override
  String get heading1 => '# Heading 1';

  @override
  String get heading2 => '## Heading 2';

  @override
  String get heading3 => '### Heading 3';

  @override
  String get list => '- List item\n- List item';

  @override
  String get link => '[Link text](URL)';

  @override
  String get image => '![Image description](Image URL)';

  @override
  String get code => '`Code`';

  @override
  String get codeBlock => '```\nCode block\n```';

  @override
  String get quote => '> Quote text';

  @override
  String get table =>
      '| Column 1 | Column 2 |\n| --- | --- |\n| Content 1 | Content 2 |';

  @override
  String get myContentLibrary => 'My Content Library';

  @override
  String get manageAndBrowseContent => 'Manage and browse all your content';

  @override
  String get contentTools => 'Content Tools';

  @override
  String get recommendedTools => 'Recommended Tools';

  @override
  String get markdownTitle => 'Markdown';

  @override
  String get markdownDescription => 'Document editing and rendering';

  @override
  String get textCardsTitle => 'Text Cards';

  @override
  String get textCardsDescription =>
      'Knowledge card customization and rendering';

  @override
  String get trafficGuideTitle => 'Traffic Guide';

  @override
  String get trafficGuideDescription => 'Traffic image and text processing';

  @override
  String get fileTools => 'File Tools';

  @override
  String get svgTitle => 'SVG';

  @override
  String get svgDescription => 'Vector graphics processing';

  @override
  String get htmlTitle => 'HTML';

  @override
  String get htmlDescription => 'Web content editing';

  @override
  String get loadingContent => 'Loading content...';

  @override
  String languageChangedTo(String language) {
    return 'Language changed to $language';
  }

  @override
  String get developer => 'Developer';

  @override
  String get contentLibraryDemo => 'Content Library Demo';

  @override
  String get viewNewContentLibraryFeatures =>
      'View new content library features';

  @override
  String get i18nDemo => 'Internationalization Demo';

  @override
  String get viewMultiLanguageSupport => 'View multi-language support effects';

  @override
  String get about => 'About';

  @override
  String get versionInfo => 'Version Info';

  @override
  String get helpAndFeedback => 'Help & Feedback';

  @override
  String get getHelpOrProvideFeedback => 'Get help or provide feedback';

  @override
  String get helpAndFeedbackContent =>
      'If you have any questions or suggestions, please contact us through the following methods:\n\nEmail: <EMAIL>';

  @override
  String get selectTheme => 'Select Theme';

  @override
  String get lightMode => 'Light Mode';

  @override
  String get darkMode => 'Dark Mode';

  @override
  String get systemMode => 'Follow System';

  @override
  String get personalizeYourAppExperience => 'Personalize your app experience';

  @override
  String get useDefaultInitialText => 'Use Default Initial Text';

  @override
  String get useDefaultInitialTextDescription =>
      'Auto-fill default example content when entering modules';

  @override
  String get contentSettings => 'Content Settings';

  @override
  String get trafficGuideImageGenerator => 'Image Generator';

  @override
  String get trafficGuideImageGeneratorSubtitle =>
      'Generate images for traffic across platforms';

  @override
  String get trafficGuideTabText => 'Text';

  @override
  String get trafficGuideTabTemplate => 'Template';

  @override
  String get trafficGuideTabEffects => 'Effects';

  @override
  String get trafficGuideTextContent => 'Text Content';

  @override
  String get trafficGuideTextHint => 'Enter text to display...';

  @override
  String get trafficGuideFontSettings => 'Font Settings';

  @override
  String get trafficGuideFontSize => 'Font Size';

  @override
  String get trafficGuideColorSettings => 'Color Settings';

  @override
  String get trafficGuideTextColor => 'Text Color';

  @override
  String get trafficGuideBackgroundColor => 'Background Color';

  @override
  String get trafficGuideVisualEffects => 'Visual Effects';

  @override
  String get trafficGuideNoiseLevel => 'Noise Level';

  @override
  String get trafficGuideDistortionLevel => 'Distortion Level';

  @override
  String get trafficGuideAddWatermark => 'Add Watermark';

  @override
  String get trafficGuideWatermarkText => 'Watermark Text';

  @override
  String get trafficGuideWatermarkHint => 'Enter watermark text...';

  @override
  String get trafficGuideExport => 'Export';

  @override
  String get trafficGuideSelectTemplateFirst =>
      'Please select a template first';

  @override
  String get trafficGuideImageSavedSuccess => 'Image saved successfully';

  @override
  String trafficGuideSaveFailed(Object error) {
    return 'Save failed: $error';
  }

  @override
  String get trafficGuidePermissionPermanentlyDenied =>
      'Permission permanently denied';

  @override
  String get trafficGuidePermissionRequired => 'Permission required';

  @override
  String trafficGuideSaveFailedWithMessage(Object message) {
    return 'Save failed: $message';
  }

  @override
  String get trafficGuideSaveFailedEmptyResult => 'Save failed: empty result';

  @override
  String get markdownPreview => 'Preview';

  @override
  String get markdownContentLabel => 'Markdown Content';

  @override
  String get markdownRenderModeLabel => 'Render Mode:';

  @override
  String get markdownNormalMode => 'Normal Mode';

  @override
  String get markdownBlockMode => 'Block Mode';

  @override
  String get markdownConfigTab => 'Config';

  @override
  String get markdownManageTab => 'Manage';

  @override
  String get markdownPreviewTab => 'Preview';

  @override
  String get markdownBlockInfo => 'Block Information';

  @override
  String get markdownTotalBlocks => 'Total Blocks';

  @override
  String get markdownVisibleBlocks => 'Visible Blocks';

  @override
  String get markdownEditorTitle => 'Markdown Editor';

  @override
  String get markdownPreviewTitle => 'Markdown Preview';

  @override
  String get markdownTitleLabel => 'Title';

  @override
  String get markdownSubtitleLabel => 'Subtitle (Optional)';

  @override
  String get markdownUntitledDocument => 'Untitled Document';

  @override
  String get markdownUntitledSection => 'Untitled Section';

  @override
  String get markdownSplitSections => 'Split Sections';

  @override
  String get markdownSaveDocument => 'Save Document';

  @override
  String get markdownActionOptions => 'Action Options';

  @override
  String get markdownShareImage => 'Share Image';

  @override
  String get markdownCopyContent => 'Copy Content';

  @override
  String get markdownSaveToAlbum => 'Save to Album';

  @override
  String get commonCancel => 'Cancel';

  @override
  String get commonReset => 'Reset';

  @override
  String get commonSelectAll => 'Select All';

  @override
  String get commonDeselectAll => 'Deselect All';

  @override
  String get markdownShowSelected => 'Show Selected';

  @override
  String get markdownHideSelected => 'Hide Selected';

  @override
  String get markdownExportSelected => 'Export Selected';

  @override
  String get markdownHideBlock => 'Hide Block';

  @override
  String get markdownShowBlock => 'Show Block';

  @override
  String get markdownExportAsImage => 'Export as Image';

  @override
  String get markdownExportAsMarkdown => 'Export as Markdown';

  @override
  String get commonGotIt => 'Got it';

  @override
  String get markdownBlockRenderSettings => 'Block Render Settings';

  @override
  String get markdownBasicSettings => 'Basic Settings';

  @override
  String get markdownEnableBlockRender => 'Enable Block Rendering';

  @override
  String get markdownSeparatorSettings => 'Separator Settings';

  @override
  String get markdownSplitByH1 => 'Split by H1 Headers';

  @override
  String get markdownSplitByH2 => 'Split by H2 Headers';

  @override
  String get markdownCustomSeparatorPattern =>
      'Custom Separator Pattern (Regex)';

  @override
  String get markdownAppearanceSettings => 'Appearance Settings';

  @override
  String get markdownBlockSpacing => 'Block Spacing';

  @override
  String get markdownSectionSplitSettings => 'Section Split Settings';

  @override
  String get markdownSplitByHorizontalRule => 'Split by Horizontal Rule';

  @override
  String get markdownMaxSectionLength => 'Maximum Section Length';

  @override
  String get commonUnlimited => 'Unlimited';

  @override
  String get markdownSetMaxSectionLength => 'Set Maximum Section Length';

  @override
  String get markdownMaxCharacters => 'Maximum Characters';

  @override
  String get markdownLeaveEmptyUnlimited => 'Leave empty for unlimited';

  @override
  String get templateSimpleName => 'Simple';

  @override
  String get templateSimpleDescription => 'Clean and minimalist design style';

  @override
  String get templateModernName => 'Modern';

  @override
  String get templateModernDescription =>
      'Modern design style with shadow effects';

  @override
  String get templateElegantName => 'Elegant';

  @override
  String get templateElegantDescription =>
      'Elegant design style with thin borders';

  @override
  String get templateCodeName => 'Code';

  @override
  String get templateCodeDescription => 'Dark theme suitable for code display';

  @override
  String get templateCardName => 'Card';

  @override
  String get templateCardDescription => 'Social media card style design';

  @override
  String get templateMorandiName => 'Morandi';

  @override
  String get templateMorandiDescription =>
      'Premium Morandi color palette, soft and elegant';

  @override
  String get templateChineseBlueName => 'Chinese Blue';

  @override
  String get templateChineseBlueDescription =>
      'Traditional Chinese porcelain color and pattern design';

  @override
  String get templateChineseVermilionName => 'Chinese Vermilion';

  @override
  String get templateChineseVermilionDescription =>
      'Traditional Chinese vermilion color, elegant and solemn';

  @override
  String get templateGradientPurpleName => 'Gradient Purple';

  @override
  String get templateGradientPurpleDescription =>
      'Modern purple-blue gradient background, stylish and elegant';

  @override
  String get templateFestiveRedName => 'Festive Red';

  @override
  String get templateFestiveRedDescription =>
      'Festive theme, suitable for occasions like Spring Festival';

  @override
  String get templateBambooSlipName => 'Bamboo Slip';

  @override
  String get templateBambooSlipDescription =>
      'Traditional bamboo slip style, rich in ancient charm';

  @override
  String get watermarkPositionTopLeft => 'Top Left';

  @override
  String get watermarkPositionTopCenter => 'Top Center';

  @override
  String get watermarkPositionTopRight => 'Top Right';

  @override
  String get watermarkPositionBottomLeft => 'Bottom Left';

  @override
  String get watermarkPositionBottomCenter => 'Bottom Center';

  @override
  String get watermarkPositionBottomRight => 'Bottom Right';

  @override
  String get watermarkPositionTiled => 'Tiled';

  @override
  String get markdownEnterContentFirst => 'Please enter Markdown content first';

  @override
  String markdownSplitSuccess(int count) {
    return 'Successfully split into $count sections';
  }

  @override
  String markdownSplitError(String error) {
    return 'Error splitting sections: $error';
  }

  @override
  String get markdownNoSectionsToPreview => 'No sections to preview';

  @override
  String get markdownSplitContentFirst => 'Please split Markdown content first';

  @override
  String get markdownDocumentSaveSuccess => 'Document saved successfully';

  @override
  String markdownDocumentSaveError(String error) {
    return 'Failed to save document: $error';
  }

  @override
  String get errorStoragePermissionRequired =>
      'Storage permission required to save images';

  @override
  String get markdownNoContentToPreview => 'No content to preview';

  @override
  String get markdownImageGenerationFailed =>
      'Image generation failed, unable to share';

  @override
  String markdownShareError(String error) {
    return 'Share failed: $error';
  }

  @override
  String get markdownEnableBlockModeFirst => 'Please enable block mode first';

  @override
  String get markdownNoBlocks => 'No blocks available';

  @override
  String get markdownEnableBlockRenderToList =>
      'Enable block rendering to view block list';

  @override
  String get commonContentCopied => 'Content copied to clipboard';

  @override
  String get markdownResetDemo => 'Reset Demo';

  @override
  String get commonHelp => 'Help';

  @override
  String get markdownBlockRenderHelp => 'Block Render Help';

  @override
  String get markdownFeatureDescription => 'Feature Description:';

  @override
  String get markdownOperationMethod => 'Operation Method:';

  @override
  String get commonTips => 'Tips:';

  @override
  String get markdownSectionSettings => 'Section Settings';

  @override
  String get markdownSelectTemplate => 'Select Template';

  @override
  String get markdownSelectHtmlTemplate => 'Select HTML Template';

  @override
  String get commonPreview => 'Preview';

  @override
  String get markdownSaveImage => 'Save Image';

  @override
  String get commonShare => 'Share';

  @override
  String get commonShowAll => 'Show All';

  @override
  String get commonShowVisibleOnly => 'Show Visible Only';

  @override
  String get commonSortByIndex => 'Sort by Index';

  @override
  String get commonSortByTitle => 'Sort by Title';

  @override
  String get commonSortByType => 'Sort by Type';

  @override
  String get commonSortByLength => 'Sort by Length';

  @override
  String markdownBlockCount(int count) {
    return '$count blocks';
  }

  @override
  String commonCharacterCount(int count) {
    return '$count characters';
  }

  @override
  String markdownSelectedBlockCount(int count) {
    return '$count blocks selected';
  }

  @override
  String get commonTotal => 'Total';

  @override
  String get commonVisible => 'Visible';

  @override
  String get commonHidden => 'Hidden';

  @override
  String get markdownContentPlaceholder => 'Enter Markdown content here...';

  @override
  String get markdownClickSplitButton =>
      'Click split button to split Markdown into sections';

  @override
  String get markdownHorizontalRuleHelper =>
      'Split when encountering three or more -, *, or _ symbols';

  @override
  String get markdownH1SplitHelper => 'Split when encountering # H1 headers';

  @override
  String markdownCharacterCount(int count) {
    return '$count characters';
  }

  @override
  String get markdownAutoSplitHelper =>
      'Automatically split long sections into multiple sections';

  @override
  String get markdownSeparatorExample =>
      'Example: Three or more consecutive hyphens';

  @override
  String get markdownH1SeparatorHelper =>
      'Use # H1 headers as block separators';

  @override
  String get markdownH2SeparatorHelper =>
      'Use ## H2 headers as block separators';

  @override
  String get markdownBlockRenderHelper =>
      'When enabled, Markdown content will be displayed in blocks according to set rules';

  @override
  String get markdownExportBlocks => 'Export Blocks';

  @override
  String get markdownGenerateSummary => 'Generate Summary Report';

  @override
  String markdownImageExportSuccess(String filePath) {
    return 'Image exported: $filePath';
  }

  @override
  String markdownExportError(String error) {
    return 'Export failed: $error';
  }

  @override
  String markdownMarkdownExportSuccess(String filePath) {
    return 'Markdown file exported: $filePath';
  }

  @override
  String markdownSummaryGenerated(String filePath) {
    return 'Summary report generated: $filePath';
  }

  @override
  String markdownSummaryError(String error) {
    return 'Report generation failed: $error';
  }

  @override
  String markdownGeneratingImage(int current, int total) {
    return 'Generating image ($current/$total)';
  }

  @override
  String markdownImagesSavedSuccess(int count) {
    return 'Successfully saved $count images to album';
  }

  @override
  String get templateChineseBlueWatermark => '青花';

  @override
  String get templateChineseVermilionWatermark => '赤';

  @override
  String get templateFestiveRedWatermark => '福';

  @override
  String get templateBambooSlipWatermark => '竹';

  @override
  String get markdownBlockManagement => 'Block Management';

  @override
  String get markdownExportOptions => 'Export Options';

  @override
  String get commonDeselect => 'Deselect';

  @override
  String get commonSelect => 'Select';

  @override
  String get commonLoading => 'Loading...';

  @override
  String get commonConfirm => 'Confirm';

  @override
  String get commonEdit => 'Edit';

  @override
  String get commonDelete => 'Delete';

  @override
  String get commonAdd => 'Add';

  @override
  String get commonRemove => 'Remove';

  @override
  String get commonApply => 'Apply';

  @override
  String get commonClose => 'Close';

  @override
  String get commonOpen => 'Open';

  @override
  String get commonView => 'View';

  @override
  String get commonBrowse => 'Browse';

  @override
  String get commonSearch => 'Search';

  @override
  String get commonFilter => 'Filter';

  @override
  String get commonSort => 'Sort';

  @override
  String get commonRefresh => 'Refresh';

  @override
  String get commonReload => 'Reload';

  @override
  String get commonRetry => 'Retry';

  @override
  String get commonContinue => 'Continue';

  @override
  String get commonFinish => 'Finish';

  @override
  String get commonSkip => 'Skip';

  @override
  String get commonBack => 'Back';

  @override
  String get commonNext => 'Next';

  @override
  String get commonPrevious => 'Previous';

  @override
  String get commonDone => 'Done';

  @override
  String get commonStart => 'Start';

  @override
  String get commonStop => 'Stop';

  @override
  String get commonPause => 'Pause';

  @override
  String get commonResume => 'Resume';

  @override
  String get commonPlay => 'Play';

  @override
  String get commonMute => 'Mute';

  @override
  String get commonUnmute => 'Unmute';

  @override
  String get commonVolumeUp => 'Volume Up';

  @override
  String get commonVolumeDown => 'Volume Down';

  @override
  String get commonFullscreen => 'Fullscreen';

  @override
  String get commonExitFullscreen => 'Exit Fullscreen';

  @override
  String get commonZoomIn => 'Zoom In';

  @override
  String get commonZoomOut => 'Zoom Out';

  @override
  String get commonZoomReset => 'Zoom Reset';

  @override
  String get commonRotateLeft => 'Rotate Left';

  @override
  String get commonRotateRight => 'Rotate Right';

  @override
  String get commonFlipHorizontal => 'Flip Horizontal';

  @override
  String get commonFlipVertical => 'Flip Vertical';

  @override
  String get commonCrop => 'Crop';

  @override
  String get commonResize => 'Resize';

  @override
  String get commonRotate => 'Rotate';

  @override
  String get commonFlip => 'Flip';

  @override
  String get commonMirror => 'Mirror';

  @override
  String get commonSkew => 'Skew';

  @override
  String get commonDistort => 'Distort';

  @override
  String get commonBlur => 'Blur';

  @override
  String get commonSharpen => 'Sharpen';

  @override
  String get commonBrightness => 'Brightness';

  @override
  String get commonContrast => 'Contrast';

  @override
  String get commonSaturation => 'Saturation';

  @override
  String get commonHue => 'Hue';

  @override
  String get commonGamma => 'Gamma';

  @override
  String get commonExposure => 'Exposure';

  @override
  String get commonVignette => 'Vignette';

  @override
  String get commonGrain => 'Grain';

  @override
  String get commonNoise => 'Noise';

  @override
  String get commonPixelate => 'Pixelate';

  @override
  String get commonPosterize => 'Posterize';

  @override
  String get commonDither => 'Dither';

  @override
  String get commonThreshold => 'Threshold';

  @override
  String get commonQuantize => 'Quantize';

  @override
  String get commonDesaturate => 'Desaturate';

  @override
  String get commonSaturate => 'Saturate';

  @override
  String get commonInvert => 'Invert';

  @override
  String get commonGrayscale => 'Grayscale';

  @override
  String get commonSepia => 'Sepia';

  @override
  String get commonVintage => 'Vintage';

  @override
  String get commonRetro => 'Retro';

  @override
  String get commonBlackAndWhite => 'Black and White';

  @override
  String get commonCool => 'Cool';

  @override
  String get commonWarm => 'Warm';

  @override
  String get commonFade => 'Fade';

  @override
  String get commonDuotone => 'Duotone';

  @override
  String get commonTricolor => 'Tricolor';

  @override
  String get commonMonochrome => 'Monochrome';

  @override
  String get commonPolychrome => 'Polychrome';

  @override
  String get commonRainbow => 'Rainbow';

  @override
  String get commonGradient => 'Gradient';

  @override
  String get commonPattern => 'Pattern';

  @override
  String get commonTexture => 'Texture';

  @override
  String get commonBorder => 'Border';

  @override
  String get commonFrame => 'Frame';

  @override
  String get commonShadow => 'Shadow';

  @override
  String get commonGlow => 'Glow';

  @override
  String get commonNeon => 'Neon';

  @override
  String get commonLight => 'Light';

  @override
  String get commonDark => 'Dark';

  @override
  String get commonBright => 'Bright';

  @override
  String get commonDim => 'Dim';

  @override
  String get commonClear => 'Clear';

  @override
  String get commonCloudy => 'Cloudy';

  @override
  String get commonFoggy => 'Foggy';

  @override
  String get commonHazy => 'Hazy';

  @override
  String get commonSmoky => 'Smoky';

  @override
  String get commonDusty => 'Dusty';

  @override
  String get commonMisty => 'Misty';

  @override
  String get commonFrosty => 'Frosty';

  @override
  String get commonIcy => 'Icy';

  @override
  String get commonSnowy => 'Snowy';

  @override
  String get commonRainy => 'Rainy';

  @override
  String get commonStormy => 'Stormy';

  @override
  String get commonWindy => 'Windy';

  @override
  String get commonBreezy => 'Breezy';

  @override
  String get commonCalm => 'Calm';

  @override
  String get commonStill => 'Still';

  @override
  String get commonQuiet => 'Quiet';

  @override
  String get commonSilent => 'Silent';

  @override
  String get commonPeaceful => 'Peaceful';

  @override
  String get commonSerene => 'Serene';

  @override
  String get commonTranquil => 'Tranquil';

  @override
  String get commonPlacid => 'Placid';

  @override
  String get commonSmooth => 'Smooth';

  @override
  String get commonRough => 'Rough';

  @override
  String get commonCoarse => 'Coarse';

  @override
  String get commonFine => 'Fine';

  @override
  String get commonSoft => 'Soft';

  @override
  String get commonHard => 'Hard';

  @override
  String get commonTough => 'Tough';

  @override
  String get commonStrong => 'Strong';

  @override
  String get commonWeak => 'Weak';

  @override
  String get commonGentle => 'Gentle';

  @override
  String get commonMild => 'Mild';

  @override
  String get commonHarsh => 'Harsh';

  @override
  String get commonSevere => 'Severe';

  @override
  String get commonExtreme => 'Extreme';

  @override
  String get commonIntense => 'Intense';

  @override
  String get commonModerate => 'Moderate';

  @override
  String get commonAverage => 'Average';

  @override
  String get commonNormal => 'Normal';

  @override
  String get commonStandard => 'Standard';

  @override
  String get commonRegular => 'Regular';

  @override
  String get commonTypical => 'Typical';

  @override
  String get commonUsual => 'Usual';

  @override
  String get commonCommon => 'Common';

  @override
  String get commonOrdinary => 'Ordinary';

  @override
  String get commonGeneral => 'General';

  @override
  String get commonBasic => 'Basic';

  @override
  String get commonSimple => 'Simple';

  @override
  String get commonEasy => 'Easy';

  @override
  String get commonDifficult => 'Difficult';

  @override
  String get commonComplex => 'Complex';

  @override
  String get commonComplicated => 'Complicated';

  @override
  String get commonAdvanced => 'Advanced';

  @override
  String get commonExpert => 'Expert';

  @override
  String get commonProfessional => 'Professional';

  @override
  String get commonSpecialized => 'Specialized';

  @override
  String get commonTechnical => 'Technical';

  @override
  String get commonScientific => 'Scientific';

  @override
  String get commonAcademic => 'Academic';

  @override
  String get commonEducational => 'Educational';

  @override
  String get commonInstructional => 'Instructional';

  @override
  String get commonTutorial => 'Tutorial';

  @override
  String get commonGuide => 'Guide';

  @override
  String get commonManual => 'Manual';

  @override
  String get commonHandbook => 'Handbook';

  @override
  String get commonReference => 'Reference';

  @override
  String get commonDocumentation => 'Documentation';

  @override
  String get commonSupport => 'Support';

  @override
  String get commonAssistance => 'Assistance';

  @override
  String get commonAid => 'Aid';

  @override
  String get commonService => 'Service';

  @override
  String get commonMaintenance => 'Maintenance';

  @override
  String get commonRepair => 'Repair';

  @override
  String get commonFix => 'Fix';

  @override
  String get commonSolve => 'Solve';

  @override
  String get commonResolve => 'Resolve';

  @override
  String get commonAddress => 'Address';

  @override
  String get commonHandle => 'Handle';

  @override
  String get commonManage => 'Manage';

  @override
  String get commonControl => 'Control';

  @override
  String get commonDirect => 'Direct';

  @override
  String get commonLead => 'Lead';

  @override
  String get commonConduct => 'Conduct';

  @override
  String get commonOperate => 'Operate';

  @override
  String get commonRun => 'Run';

  @override
  String get commonExecute => 'Execute';

  @override
  String get commonPerform => 'Perform';

  @override
  String get commonImplement => 'Implement';

  @override
  String get commonCarryOut => 'Carry Out';

  @override
  String get commonAccomplish => 'Accomplish';

  @override
  String get commonAchieve => 'Achieve';

  @override
  String get commonAttain => 'Attain';

  @override
  String get commonReach => 'Reach';

  @override
  String get commonObtain => 'Obtain';

  @override
  String get commonGet => 'Get';

  @override
  String get commonAcquire => 'Acquire';

  @override
  String get commonGain => 'Gain';

  @override
  String get commonReceive => 'Receive';

  @override
  String get commonCollect => 'Collect';

  @override
  String get commonGather => 'Gather';

  @override
  String get commonAssemble => 'Assemble';

  @override
  String get commonCompile => 'Compile';

  @override
  String get commonCombine => 'Combine';

  @override
  String get commonMerge => 'Merge';

  @override
  String get commonJoin => 'Join';

  @override
  String get commonUnite => 'Unite';

  @override
  String get commonConnect => 'Connect';

  @override
  String get commonLink => 'Link';

  @override
  String get commonAttach => 'Attach';

  @override
  String get commonFasten => 'Fasten';

  @override
  String get commonSecure => 'Secure';

  @override
  String get commonTie => 'Tie';

  @override
  String get commonBind => 'Bind';

  @override
  String get commonWrap => 'Wrap';

  @override
  String get commonCover => 'Cover';

  @override
  String get commonEnclose => 'Enclose';

  @override
  String get commonSurround => 'Surround';

  @override
  String get commonEnvelop => 'Envelop';

  @override
  String get commonContain => 'Contain';

  @override
  String get commonInclude => 'Include';

  @override
  String get commonInvolve => 'Involve';

  @override
  String get commonEmbrace => 'Embrace';

  @override
  String get commonEncompass => 'Encompass';

  @override
  String get commonSpan => 'Span';

  @override
  String get commonExtend => 'Extend';

  @override
  String get commonStretch => 'Stretch';

  @override
  String get commonExpand => 'Expand';

  @override
  String get commonGrow => 'Grow';

  @override
  String get commonIncrease => 'Increase';

  @override
  String get commonEnlarge => 'Enlarge';

  @override
  String get commonMagnify => 'Magnify';

  @override
  String get commonAmplify => 'Amplify';

  @override
  String get commonBoost => 'Boost';

  @override
  String get commonEnhance => 'Enhance';

  @override
  String get commonImprove => 'Improve';

  @override
  String get commonBetter => 'Better';

  @override
  String get commonUpgrade => 'Upgrade';

  @override
  String get commonAdvance => 'Advance';

  @override
  String get commonProgress => 'Progress';

  @override
  String get commonDevelop => 'Develop';

  @override
  String get commonEvolve => 'Evolve';

  @override
  String get commonMature => 'Mature';

  @override
  String get commonRipe => 'Ripe';

  @override
  String get commonPerfect => 'Perfect';

  @override
  String get complete => 'Complete';

  @override
  String get commonComplete => 'Complete';

  @override
  String get commonEnd => 'End';

  @override
  String get commonTerminate => 'Terminate';

  @override
  String get commonConclude => 'Conclude';

  @override
  String get commonFinalize => 'Finalize';

  @override
  String get commonShut => 'Shut';

  @override
  String get commonSeal => 'Seal';

  @override
  String get commonLock => 'Lock';

  @override
  String get commonTighten => 'Tighten';

  @override
  String get commonOrganize => 'Organize';

  @override
  String get commonArrange => 'Arrange';

  @override
  String get commonOrder => 'Order';

  @override
  String get commonClassify => 'Classify';

  @override
  String get commonCategorize => 'Categorize';

  @override
  String get commonGroup => 'Group';

  @override
  String get commonCluster => 'Cluster';

  @override
  String get commonBunch => 'Bunch';

  @override
  String get commonBundle => 'Bundle';

  @override
  String get commonPack => 'Pack';

  @override
  String get commonPackage => 'Package';

  @override
  String get commonHold => 'Hold';

  @override
  String get commonCarry => 'Carry';

  @override
  String get commonBear => 'Bear';

  @override
  String get commonSustain => 'Sustain';

  @override
  String get commonMaintain => 'Maintain';

  @override
  String get commonKeep => 'Keep';

  @override
  String get commonRetain => 'Retain';

  @override
  String get commonPreserve => 'Preserve';

  @override
  String get commonConserve => 'Conserve';

  @override
  String get commonSave => 'Save';

  @override
  String get commonStore => 'Store';

  @override
  String get commonReserve => 'Reserve';

  @override
  String get commonSetAside => 'Set Aside';

  @override
  String get commonPutAway => 'Put Away';

  @override
  String get commonPlace => 'Place';

  @override
  String get commonPosition => 'Position';

  @override
  String get commonLocate => 'Locate';

  @override
  String get commonSituate => 'Situate';

  @override
  String get commonInstall => 'Install';

  @override
  String get commonSet => 'Set';

  @override
  String get commonEstablish => 'Establish';

  @override
  String get commonFound => 'Found';

  @override
  String get commonCreate => 'Create';

  @override
  String get commonMake => 'Make';

  @override
  String get commonBuild => 'Build';

  @override
  String get commonConstruct => 'Construct';

  @override
  String get commonForm => 'Form';

  @override
  String get commonShape => 'Shape';

  @override
  String get commonMold => 'Mold';

  @override
  String get commonFashion => 'Fashion';

  @override
  String get commonDesign => 'Design';

  @override
  String get commonPlan => 'Plan';

  @override
  String get commonDevise => 'Devise';

  @override
  String get commonConceive => 'Conceive';

  @override
  String get commonImagine => 'Imagine';

  @override
  String get commonEnvision => 'Envision';

  @override
  String get commonVisualize => 'Visualize';

  @override
  String get commonDream => 'Dream';

  @override
  String get commonThink => 'Think';

  @override
  String get commonConsider => 'Consider';

  @override
  String get commonPonder => 'Ponder';

  @override
  String get commonReflect => 'Reflect';

  @override
  String get commonMeditate => 'Meditate';

  @override
  String get commonContemplate => 'Contemplate';

  @override
  String get commonStudy => 'Study';

  @override
  String get commonLearn => 'Learn';

  @override
  String get commonDiscover => 'Discover';

  @override
  String get commonFind => 'Find';

  @override
  String get commonUncover => 'Uncover';

  @override
  String get commonReveal => 'Reveal';

  @override
  String get commonExpose => 'Expose';

  @override
  String get commonDisclose => 'Disclose';

  @override
  String get commonDivulge => 'Divulge';

  @override
  String get commonTell => 'Tell';

  @override
  String get commonInform => 'Inform';

  @override
  String get commonNotify => 'Notify';

  @override
  String get commonAnnounce => 'Announce';

  @override
  String get commonDeclare => 'Declare';

  @override
  String get commonProclaim => 'Proclaim';

  @override
  String get commonPronounce => 'Pronounce';

  @override
  String get commonState => 'State';

  @override
  String get commonExpress => 'Express';

  @override
  String get commonVoice => 'Voice';

  @override
  String get commonArticulate => 'Articulate';

  @override
  String get commonUtter => 'Utter';

  @override
  String get commonSay => 'Say';

  @override
  String get commonSpeak => 'Speak';

  @override
  String get commonTalk => 'Talk';

  @override
  String get commonConverse => 'Converse';

  @override
  String get commonCommunicate => 'Communicate';

  @override
  String get commonCorrespond => 'Correspond';

  @override
  String get commonContact => 'Contact';

  @override
  String get commonApproach => 'Approach';

  @override
  String get commonAccost => 'Accost';

  @override
  String get commonGreet => 'Greet';

  @override
  String get commonWelcome => 'Welcome';

  @override
  String get commonAccept => 'Accept';

  @override
  String get commonTake => 'Take';

  @override
  String get commonProcure => 'Procure';

  @override
  String get commonPurchase => 'Purchase';

  @override
  String get commonBuy => 'Buy';

  @override
  String get commonShop => 'Shop';

  @override
  String get commonTrade => 'Trade';

  @override
  String get commonExchange => 'Exchange';

  @override
  String get commonSwap => 'Swap';

  @override
  String get commonSwitch => 'Switch';

  @override
  String get commonChange => 'Change';

  @override
  String get commonAlter => 'Alter';

  @override
  String get commonModify => 'Modify';

  @override
  String get commonAdjust => 'Adjust';

  @override
  String get commonTweak => 'Tweak';

  @override
  String get commonFineTune => 'Fine Tune';

  @override
  String get commonOptimize => 'Optimize';

  @override
  String get commonRefine => 'Refine';

  @override
  String get commonPolish => 'Polish';

  @override
  String get commonCease => 'Cease';

  @override
  String get commonHalt => 'Halt';

  @override
  String get commonBreak => 'Break';

  @override
  String get commonInterrupt => 'Interrupt';

  @override
  String get commonSuspend => 'Suspend';

  @override
  String get commonDelay => 'Delay';

  @override
  String get commonPostpone => 'Postpone';

  @override
  String get commonDefer => 'Defer';

  @override
  String get commonWait => 'Wait';

  @override
  String get commonRemain => 'Remain';

  @override
  String get commonStay => 'Stay';

  @override
  String get commonProceed => 'Proceed';

  @override
  String get commonMove => 'Move';

  @override
  String get commonGo => 'Go';

  @override
  String get commonTravel => 'Travel';

  @override
  String get commonJourney => 'Journey';

  @override
  String get commonPass => 'Pass';

  @override
  String get commonCross => 'Cross';

  @override
  String get commonTransit => 'Transit';

  @override
  String get commonTransfer => 'Transfer';

  @override
  String get commonConvey => 'Convey';

  @override
  String get commonTransport => 'Transport';

  @override
  String get commonBring => 'Bring';

  @override
  String get commonFetch => 'Fetch';

  @override
  String get commonSalute => 'Salute';

  @override
  String get commonHail => 'Hail';

  @override
  String get commonNigh => 'Nigh';

  @override
  String get commonDrawNear => 'Draw Near';

  @override
  String get commonCome => 'Come';

  @override
  String get commonArrive => 'Arrive';

  @override
  String get commonLand => 'Land';

  @override
  String get commonEnter => 'Enter';

  @override
  String get commonAccess => 'Access';

  @override
  String get commonGoOn => 'Go On';

  @override
  String get commonKeepOn => 'Keep On';

  @override
  String get commonCarryOn => 'Carry On';

  @override
  String get commonPersist => 'Persist';

  @override
  String get commonPersevere => 'Persevere';

  @override
  String get commonEndure => 'Endure';

  @override
  String get commonLast => 'Last';

  @override
  String get markdownSaveButton => 'Save';

  @override
  String get markdownCancelButton => 'Cancel';

  @override
  String get markdownConfirmButton => 'Confirm';

  @override
  String get markdownResetButton => 'Reset';

  @override
  String get markdownApplyButton => 'Apply';

  @override
  String get markdownCloseButton => 'Close';

  @override
  String get markdownSelectButton => 'Select';

  @override
  String get markdownBrowseButton => 'Browse';

  @override
  String get markdownSearchButton => 'Search';

  @override
  String get markdownClearButton => 'Clear';

  @override
  String get markdownDeleteButton => 'Delete';

  @override
  String get markdownEditButton => 'Edit';

  @override
  String get markdownExportButton => 'Export';

  @override
  String get markdownImportButton => 'Import';

  @override
  String get markdownShareButton => 'Share';

  @override
  String get markdownCopyButton => 'Copy';

  @override
  String get markdownPasteButton => 'Paste';

  @override
  String get markdownCutButton => 'Cut';

  @override
  String get markdownUndoButton => 'Undo';

  @override
  String get markdownRedoButton => 'Redo';

  @override
  String get markdownEditTab => 'Edit';

  @override
  String get markdownTemplateTab => 'Template';

  @override
  String get markdownStyleTab => 'Style';

  @override
  String get markdownWatermarkTab => 'Watermark';

  @override
  String get markdownBlockTab => 'Block';

  @override
  String get markdownTemplateSelector => 'Template Selector';

  @override
  String get markdownStyleSelector => 'Style Selector';

  @override
  String get markdownWatermarkSettings => 'Watermark Settings';

  @override
  String get markdownBlockSettings => 'Block Settings';

  @override
  String get markdownBlockConfigPanel => 'Block Configuration Panel';

  @override
  String get markdownBlockManagerPanel => 'Block Management Panel';

  @override
  String get markdownTextLabel => 'Text';

  @override
  String get markdownMarkdownContent => 'Markdown Content';

  @override
  String get markdownWatermarkText => 'Watermark Text';

  @override
  String get markdownEnterWatermarkText => 'Enter watermark text';

  @override
  String get markdownEnterMarkdownContent => 'Enter Markdown content...';

  @override
  String get markdownFontSettings => 'Font Settings';

  @override
  String get markdownFontSize => 'Font Size';

  @override
  String get markdownFontFamily => 'Font Family';

  @override
  String get markdownCodeFont => 'Code Font';

  @override
  String get markdownColorSettings => 'Color Settings';

  @override
  String get markdownTextColor => 'Text Color';

  @override
  String get markdownBackgroundColor => 'Background Color';

  @override
  String get markdownBorderColor => 'Border Color';

  @override
  String get markdownBorderWidth => 'Border Width';

  @override
  String get markdownShadowSettings => 'Shadow Settings';

  @override
  String get markdownShadowColor => 'Shadow Color';

  @override
  String get markdownBorderRadius => 'Border Radius';

  @override
  String get markdownPadding => 'Padding';

  @override
  String get markdownMargin => 'Margin';

  @override
  String get markdownWatermarkContent => 'Watermark Content';

  @override
  String get markdownWatermarkTextStyle => 'Text Style';

  @override
  String get markdownWatermarkNormal => 'Normal';

  @override
  String get markdownWatermarkBold => 'Bold';

  @override
  String get markdownWatermarkItalic => 'Italic';

  @override
  String get markdownWatermarkPosition => 'Display Position';

  @override
  String get markdownWatermarkTextColor => 'Text Color';

  @override
  String get markdownWatermarkOpacity => 'Opacity';

  @override
  String get markdownWatermarkFontSize => 'Font Size';

  @override
  String get markdownWatermarkRotation => 'Rotation';

  @override
  String get markdownWatermarkTileSettings => 'Tile Settings';

  @override
  String get markdownWatermarkHorizontalSpacing => 'Horizontal Spacing';

  @override
  String get markdownWatermarkVerticalSpacing => 'Vertical Spacing';

  @override
  String get markdownSelectWatermarkColor => 'Select Watermark Color';

  @override
  String get markdownResetToAppName => 'Reset to App Name';

  @override
  String get markdownShowBlockTitle => 'Show Block Title';

  @override
  String get markdownShowBlockBorder => 'Show Block Border';

  @override
  String get markdownSortByIndex => 'Sort by Index';

  @override
  String get markdownSortByTitle => 'Sort by Title';

  @override
  String get markdownSortByType => 'Sort by Type';

  @override
  String get markdownSortByLength => 'Sort by Length';

  @override
  String get markdownShareResult => 'Share Result';

  @override
  String get markdownExportResult => 'Export Result';

  @override
  String get markdownSaveSuccess => 'Save Success';

  @override
  String get markdownSaveFailed => 'Save Failed';

  @override
  String get markdownTemplateDescription => 'Template Description';

  @override
  String get markdownTemplateFeatures => 'Template Features';

  @override
  String get markdownBorderStyle => 'Border Style';

  @override
  String get markdownShadowEffect => 'Shadow Effect';

  @override
  String get markdownShowHeader => 'Show Header';

  @override
  String get markdownInnerShadow => 'Inner Shadow';

  @override
  String get markdownHeadingAlignment => 'Heading Alignment';

  @override
  String get markdownLeftAlign => 'Left Align';

  @override
  String get markdownCenterAlign => 'Center Align';

  @override
  String get markdownRightAlign => 'Right Align';

  @override
  String get markdownGradientBackground => 'Gradient Background';

  @override
  String get markdownBackgroundPattern => 'Background Pattern';

  @override
  String get markdownListItemStyle => 'List Item Style';

  @override
  String get markdownCheckboxStyle => 'Checkbox Style';

  @override
  String get markdownMoreActions => 'More Actions';

  @override
  String get markdownShareImageSubtitle => 'Share rendered result with others';

  @override
  String get markdownCopyContentSubtitle => 'Copy Markdown text to clipboard';

  @override
  String get markdownSaveToAlbumSubtitle => 'Save image to local album';

  @override
  String get markdownOperationOptions => 'Operation Options';

  @override
  String get markdownSelectColor => 'Select Color';

  @override
  String get markdownChooseColor => 'Choose Color';

  @override
  String get markdownColorPicker => 'Color Picker';

  @override
  String get markdownResetSettings => 'Reset Settings';

  @override
  String get markdownApplySettings => 'Apply Settings';

  @override
  String get markdownLoading => 'Loading...';

  @override
  String get markdownGenerating => 'Generating...';

  @override
  String get markdownProcessing => 'Processing...';

  @override
  String get markdownSaving => 'Saving...';

  @override
  String get markdownExporting => 'Exporting...';

  @override
  String get markdownSharing => 'Sharing...';

  @override
  String get markdownCopying => 'Copying...';

  @override
  String get markdownSuccess => 'Success';

  @override
  String get markdownError => 'Error';

  @override
  String get markdownWarning => 'Warning';

  @override
  String get markdownInfo => 'Info';

  @override
  String get markdownComplete => 'Complete';

  @override
  String get markdownFailed => 'Failed';

  @override
  String get markdownCancelled => 'Cancelled';

  @override
  String get markdownContentSaved => 'Content saved to content library';

  @override
  String markdownTemplateSelected(String name) {
    return 'Template \"$name\" selected';
  }

  @override
  String markdownSaveError(String error) {
    return 'Save failed: $error';
  }

  @override
  String markdownLoadError(String error) {
    return 'Load error: $error';
  }

  @override
  String markdownProcessError(String error) {
    return 'Process error: $error';
  }

  @override
  String get markdownBlockModeEnabled => 'Block mode enabled';

  @override
  String get markdownBlockModeDisabled => 'Block mode disabled';

  @override
  String get markdownBlockAdded => 'Block added';

  @override
  String get markdownBlockRemoved => 'Block removed';

  @override
  String get markdownBlockUpdated => 'Block updated';

  @override
  String get markdownBlockHidden => 'Block hidden';

  @override
  String get markdownBlockShown => 'Block shown';

  @override
  String get markdownBlockSelected => 'Block selected';

  @override
  String get markdownBlockDeselected => 'Block deselected';

  @override
  String get markdownBlockMoved => 'Block moved';

  @override
  String get markdownBlockResized => 'Block resized';

  @override
  String get markdownBlockReordered => 'Block reordered';

  @override
  String get markdownBlockExported => 'Block exported';

  @override
  String get markdownBlockImported => 'Block imported';

  @override
  String get markdownBlockRenderFeature1 =>
      '• Block rendering can split long documents into multiple independent blocks';

  @override
  String get markdownBlockRenderFeature2 =>
      '• Each block can be individually displayed or hidden';

  @override
  String get markdownBlockRenderFeature3 =>
      '• Support multiple separation methods: headers, custom separators, manual separation';

  @override
  String get markdownBlockRenderFeature4 =>
      '• Adjust block settings in the left configuration panel';

  @override
  String get markdownBlockRenderFeature5 =>
      '• Click in the preview area to add new separator bars';

  @override
  String get markdownBlockRenderFeature6 =>
      '• Drag separator bars to readjust block positions';

  @override
  String get markdownBlockRenderFeature7 =>
      '• Click the eye icon on block title bars to hide/show blocks';

  @override
  String get markdownBlockRenderFeature8 =>
      '• Different types of blocks are distinguished by different colored borders';

  @override
  String get markdownBlockRenderFeature9 => '• Blue: H1 header blocks';

  @override
  String get markdownBlockRenderFeature10 => '• Green: H2 header blocks';

  @override
  String get markdownBlockRenderFeature11 =>
      '• Orange: Custom separator blocks';

  @override
  String get markdownBlockRenderFeature12 => '• Gray: Manual separator blocks';

  @override
  String get markdownGotIt => 'Got It';

  @override
  String get markdownIKnow => 'I Know';

  @override
  String get markdownUnderstood => 'Understood';

  @override
  String get markdownAlignLeft => 'Left';

  @override
  String get markdownAlignCenter => 'Center';

  @override
  String get markdownAlignRight => 'Right';

  @override
  String get markdownPositionTopLeft => 'Top Left';

  @override
  String get markdownPositionTopCenter => 'Top Center';

  @override
  String get markdownPositionTopRight => 'Top Right';

  @override
  String get markdownPositionBottomLeft => 'Bottom Left';

  @override
  String get markdownPositionBottomCenter => 'Bottom Center';

  @override
  String get markdownPositionBottomRight => 'Bottom Right';

  @override
  String get markdownPositionTiled => 'Tiled';

  @override
  String get markdownExportingBlocks => 'Exporting blocks...';

  @override
  String get markdownGeneratingReport => 'Generating report...';

  @override
  String get markdownProcessingComplete => 'Processing complete';

  @override
  String get markdownOperationSuccessful => 'Operation successful';

  @override
  String get markdownOperationFailed => 'Operation failed';

  @override
  String get markdownWatermarkVisible => 'Show Watermark';

  @override
  String get markdownWatermarkHidden => 'Hide Watermark';

  @override
  String get markdownWatermarkPositionAppearance => 'Position & Appearance';

  @override
  String get markdownWatermarkDisplayPosition => 'Display Position';

  @override
  String get markdownWatermarkHorizontalGap => 'Horizontal Gap';

  @override
  String get markdownWatermarkVerticalGap => 'Vertical Gap';

  @override
  String get markdownWatermarkSelectColor => 'Select Watermark Color';

  @override
  String get markdownWatermarkCancel => 'Cancel';

  @override
  String get markdownWatermarkConfirm => 'Confirm';

  @override
  String voiceInitializationFailed(Object error) {
    return 'Initialization failed: $error';
  }

  @override
  String get voicePlayingAllRecordings => 'Playing all recordings';

  @override
  String get voiceMyRecordings => 'My Recordings';

  @override
  String get voicePlayAll => 'Play All';

  @override
  String get voiceNoRecordings => 'No voice recordings';

  @override
  String get voiceStartRecording => 'Start Recording';

  @override
  String get voiceTapToStartRecording =>
      'Tap the button below to record your first voice';

  @override
  String get voiceConfirmDelete => 'Confirm Delete';

  @override
  String get voiceDeleteConfirmation =>
      'Are you sure you want to delete this voice recording?';

  @override
  String get voiceCancel => 'Cancel';

  @override
  String get voiceDelete => 'Delete';

  @override
  String get voiceRecordingDeleted => 'Recording deleted';

  @override
  String get voiceTranscriptionContent => 'Transcription:';

  @override
  String get voiceToday => 'Today';

  @override
  String get voiceYesterday => 'Yesterday';

  @override
  String get voiceRecording => 'Recording';

  @override
  String get voiceRecordingPageTitle => 'Voice Recording';

  @override
  String get voiceRequestPermission => 'Request Permission';

  @override
  String get voiceOpenSettings => 'Open Settings';

  @override
  String get voiceRecordingInProgress => 'Recording in progress...';

  @override
  String get voiceReadyToRecord => 'Ready to start recording';

  @override
  String get voiceStartSpeaking => 'Please start speaking...';

  @override
  String get voiceClickToStart => 'Click to start';

  @override
  String get voiceClickToStop => 'Click to stop';

  @override
  String get voiceRecordingStopped => 'Recording stopped';

  @override
  String get voiceRecordingFailed => 'Recording failed';

  @override
  String voiceStopRecordingFailed(Object error) {
    return 'Failed to stop recording: $error';
  }

  @override
  String get voiceRecordingFileInvalid =>
      'Recording file is invalid, please try again';

  @override
  String get voiceRecordingFileNotFound =>
      'Recording file not found, recording may have failed';

  @override
  String get voiceSaveRecording => 'Save Recording';

  @override
  String get voiceTitle => 'Title';

  @override
  String get voiceDuration => 'Duration';

  @override
  String get voiceTranscription => 'Transcription';

  @override
  String get voiceRecordingAndTranscriptionSaved =>
      'Recording and transcription text saved';

  @override
  String get voiceRecordingSaved => 'Recording saved';

  @override
  String voiceSaveRecordingFailed(Object error) {
    return 'Failed to save recording: $error';
  }

  @override
  String get voiceNoMicrophonePermission =>
      'No microphone permission, cannot record';

  @override
  String get voicePermissionRequired => 'Permission Required';

  @override
  String get voicePermissionInstructions =>
      'Please follow these steps to enable permissions:';

  @override
  String get voicePermissionStep1 => '1. Click \"Go to Settings\" button';

  @override
  String get voicePermissionStep2 =>
      '2. Click \"Privacy & Security\" in Settings';

  @override
  String get voicePermissionStep3 =>
      '3. Click \"Microphone\" and \"Speech Recognition\" respectively';

  @override
  String get voicePermissionStep4 =>
      '4. Find \"内容君\" in the list and enable permissions';

  @override
  String get voicePermissionNote =>
      'Note: If you don\'t see the app, please return to the app and click \"Request Permission\" again, then check settings again';

  @override
  String get voiceGoToSettings => 'Go to Settings';

  @override
  String get voiceEnableMicrophonePermission =>
      'Please enable microphone permission in settings to record';

  @override
  String get voiceInitializationError => 'Initialization Error';

  @override
  String get voiceRequestPermissionAgain => 'Request Permission Again';

  @override
  String voiceStartRecordingFailed(Object error) {
    return 'Failed to start recording: $error';
  }

  @override
  String get voiceNeedMicrophonePermission =>
      'Need microphone permission to use recording feature';

  @override
  String get voiceNeedMicrophonePermissionForRecording =>
      'Need microphone permission to record';

  @override
  String get voiceSpeechRecognitionInitFailed =>
      'Speech recognition initialization failed, please ensure microphone permission is granted';

  @override
  String get voiceRecordingTitle => 'Voice Recording';

  @override
  String get voicePermissionGuide =>
      'Please follow these steps to enable permissions:';

  @override
  String get voicePermissionGuideStep1 => '1. Click \"Go to Settings\" button';

  @override
  String get voicePermissionGuideStep2 =>
      '2. Click \"Privacy & Security\" in settings';

  @override
  String get voicePermissionGuideStep3 =>
      '3. Click \"Microphone\" and \"Speech Recognition\" respectively';

  @override
  String get voicePermissionGuideStep4 =>
      '4. Find \"内容君\" in the list and enable permissions';

  @override
  String get voicePermissionGuideNote =>
      'Note: If you cannot find the app, please return to the app and click \"Request Permission\" again, then check settings again';

  @override
  String get voiceRecordingTitleLabel => 'Title';

  @override
  String voiceRecordingDuration(Object duration) {
    return 'Duration: $duration';
  }

  @override
  String voiceRecordingTranscription(Object transcription) {
    return 'Transcription: $transcription';
  }

  @override
  String get voiceRecordingFileNotExist =>
      'Recording file does not exist, recording may have failed';

  @override
  String get voicePleaseStartSpeaking => 'Please start speaking...';

  @override
  String get voiceClickToStartRecording =>
      'Click button below to start recording\nVoice will be converted to text in real-time';

  @override
  String get voiceSpeechRecognitionInProgress =>
      'Speech recognition in progress, real-time transcription displayed above';

  @override
  String get voiceSave => 'Save';

  @override
  String get trafficGuideContentTools => 'Content Tools';

  @override
  String get trafficGuideToolsDescription =>
      'Choose the right tool to create traffic content';

  @override
  String get trafficGuideTextTransformer => 'Text Transformer';

  @override
  String get trafficGuideTextTransformerSubtitle =>
      'Emoji conversion and character obfuscation';

  @override
  String get trafficGuideWatermarkProcessor => 'Watermark Processor';

  @override
  String get trafficGuideWatermarkProcessorSubtitle =>
      'Add and remove invisible watermarks';

  @override
  String get trafficGuideNewProject => 'New Project';

  @override
  String get trafficGuideNewProjectSubtitle =>
      'Create traffic project configuration';

  @override
  String get trafficGuideMyProjects => 'My Projects';

  @override
  String get trafficGuideProjectsDescription =>
      'Manage your traffic project configurations';

  @override
  String get trafficGuideNoProjects => 'No Projects';

  @override
  String get trafficGuideNoProjectsDescription =>
      'Click \'New Project\' to create your first traffic project';

  @override
  String get trafficGuideRefresh => 'Refresh';

  @override
  String get trafficGuideLoading => 'Loading...';

  @override
  String trafficGuideLastUpdated(Object date) {
    return 'Updated: $date';
  }

  @override
  String get trafficGuideConfirmDelete => 'Confirm Delete';

  @override
  String trafficGuideDeleteConfirmation(Object name) {
    return 'Are you sure you want to delete project \"$name\"?';
  }

  @override
  String trafficGuideProjectDeleted(Object name) {
    return 'Project \"$name\" deleted';
  }

  @override
  String get trafficGuideEdit => 'Edit';

  @override
  String get trafficGuideDelete => 'Delete';

  @override
  String get trafficGuideProjectEditor => 'Project Editor';

  @override
  String get trafficGuideBasicInfo => 'Basic Information';

  @override
  String get trafficGuideProjectName => 'Project Name';

  @override
  String get trafficGuideProjectNameHint => 'Enter project name';

  @override
  String get trafficGuideProjectDescription => 'Project Description';

  @override
  String get trafficGuideProjectDescriptionHint => 'Enter project description';

  @override
  String get trafficGuideProjectNameRequired => 'Please enter project name';

  @override
  String get trafficGuideImageConfig => 'Image Configuration';

  @override
  String get trafficGuideDefaultText => 'Default Text';

  @override
  String get trafficGuideDefaultTextHint => 'Enter default display text';

  @override
  String get trafficGuideFontFamily => 'Font Family';

  @override
  String get trafficGuideTextTransformConfig => 'Text Transform Configuration';

  @override
  String get trafficGuideEmojiConversion => 'Emoji Conversion';

  @override
  String get trafficGuideEmojiConversionSubtitle =>
      'Convert numbers and letters to special Unicode characters';

  @override
  String get trafficGuideUnicodeVariation => 'Unicode Variation';

  @override
  String get trafficGuideUnicodeVariationSubtitle =>
      'Add diacritical characters and special Unicode';

  @override
  String get trafficGuideInvisibleChars => 'Invisible Characters';

  @override
  String get trafficGuideInvisibleCharsSubtitle =>
      'Insert invisible characters in text';

  @override
  String get trafficGuideSensitiveWordMasking => 'Sensitive Word Masking';

  @override
  String get trafficGuideSensitiveWordMaskingSubtitle =>
      'Apply character obfuscation to sensitive words';

  @override
  String get trafficGuideSensitiveWords => 'Sensitive Words';

  @override
  String get trafficGuideSensitiveWordsHint =>
      'Enter sensitive words, separated by commas';

  @override
  String get trafficGuideWatermarkConfig => 'Watermark Configuration';

  @override
  String get trafficGuideWatermarkTextHint => 'Enter watermark content';

  @override
  String get trafficGuideInvisibleWatermark => 'Invisible Watermark';

  @override
  String get trafficGuideOpacity => 'Opacity';

  @override
  String get trafficGuideWatermarkFontSize => 'Font Size';

  @override
  String get trafficGuideProjectSaved => 'Project saved successfully';

  @override
  String get trafficGuideSaveProject => 'Save Project';

  @override
  String get trafficGuideSaving => 'Saving...';

  @override
  String get trafficGuideNewProjectName => 'New Project';

  @override
  String get trafficGuideNewProjectDescription =>
      'Traffic project configuration';

  @override
  String get trafficGuideImageGeneratorTitle => 'Traffic Image Generator';

  @override
  String get trafficGuideImageConfiguration => 'Image Configuration';

  @override
  String get trafficGuideTextRequired => 'Please enter text content';

  @override
  String get trafficGuideInterferenceSettings => 'Interference Settings';

  @override
  String get trafficGuideInterferenceLevel => 'Interference Level';

  @override
  String get trafficGuideWatermarkSettings => 'Watermark Settings';

  @override
  String get trafficGuideWatermarkContent => 'Watermark Content';

  @override
  String get trafficGuideWatermarkContentHint => 'Enter watermark content...';

  @override
  String get trafficGuidePreview => 'Preview';

  @override
  String get trafficGuideSaveToAlbum => 'Save to Album';

  @override
  String get trafficGuideShare => 'Share';

  @override
  String get trafficGuideSelectColor => 'Select Color';

  @override
  String get trafficGuideBlack => 'Black';

  @override
  String get trafficGuideWhite => 'White';

  @override
  String get trafficGuideRed => 'Red';

  @override
  String get trafficGuideGreen => 'Green';

  @override
  String get trafficGuideBlue => 'Blue';

  @override
  String get trafficGuideYellow => 'Yellow';

  @override
  String get trafficGuidePurple => 'Purple';

  @override
  String get trafficGuideCyan => 'Cyan';

  @override
  String get trafficGuideGenerateImage => 'Generate Image';

  @override
  String get trafficGuideGenerating => 'Generating...';

  @override
  String trafficGuideImageGenerationFailed(Object error) {
    return 'Image generation failed: $error';
  }

  @override
  String get trafficGuideLongPressToSave => 'Long press image to save to album';

  @override
  String get trafficGuideShareFeatureInProgress =>
      'Share feature in development...';

  @override
  String get trafficGuideTextTransformerTitle => 'Text Transformer';

  @override
  String get trafficGuideTransformSettings => 'Transform Settings';

  @override
  String get trafficGuideTransformText => 'Transform Text';

  @override
  String get trafficGuideTransforming => 'Transforming...';

  @override
  String get trafficGuideInputText => 'Input Text';

  @override
  String get trafficGuideCharacters => 'characters';

  @override
  String get trafficGuideInputHint => 'Enter text to transform...';

  @override
  String get trafficGuideTransformResult => 'Transform Result';

  @override
  String get trafficGuideResultHint => 'Transformed text will appear here...';

  @override
  String trafficGuideTransformFailed(Object error) {
    return 'Transform failed: $error';
  }

  @override
  String get trafficGuideCopyResult => 'Copy Result';

  @override
  String get trafficGuideClear => 'Clear';

  @override
  String get trafficGuideSettings => 'Settings';

  @override
  String get trafficGuideAdvancedSettings => 'Advanced Settings';

  @override
  String get trafficGuideCopiedToClipboard => 'Copied to clipboard';

  @override
  String get trafficGuideCustomCharacterMapping => 'Custom Character Mapping';

  @override
  String get trafficGuideMappingFormat =>
      'Format: original=target (one per line)';

  @override
  String get trafficGuideMappingExample => 'Example:\na=ᴀ\nb=ʙ';

  @override
  String get trafficGuideConfirm => 'Confirm';

  @override
  String get trafficGuideProcessingMode => 'Processing Mode';

  @override
  String get trafficGuideAddWatermarkMode => 'Add Watermark';

  @override
  String get trafficGuideRemoveWatermarkMode => 'Remove Watermark';

  @override
  String get trafficGuideProcessText => 'Process Text';

  @override
  String get trafficGuideProcessing => 'Processing...';

  @override
  String get trafficGuideOriginalText => 'Original Text';

  @override
  String get trafficGuideWatermarkedText => 'Watermarked Text';

  @override
  String get trafficGuideProcessHint =>
      'Processing result will be displayed here';

  @override
  String get trafficGuideWatermarkIdentifier => 'Watermark Identifier';

  @override
  String get trafficGuideWatermarkIdentifierHint =>
      'Enter watermark identifier to remove...';

  @override
  String get trafficGuideRotationAngle => 'Rotation Angle';

  @override
  String get trafficGuideEnterTextToProcess => 'Please enter text to process';

  @override
  String get trafficGuideEnterWatermarkContent =>
      'Please enter watermark content';

  @override
  String get trafficGuideSensitiveWordsList => 'Sensitive Words List';

  @override
  String get trafficGuideSensitiveWordsListHint =>
      'Enter sensitive words, separated by commas';

  @override
  String get trafficGuideWatermarkAddHint =>
      'Enter text content that needs watermark added';

  @override
  String get trafficGuideWatermarkRemoveHint =>
      'Enter text content that needs watermark removed';

  @override
  String get textCardsHomePageTitle => 'Text Cards';

  @override
  String get textCardsHomePageSubtitle =>
      'Modern Style • Inline Editing • HD Export';

  @override
  String get textCardsStartCreating => 'Start Creating';

  @override
  String get textCardsQuickActions => 'Quick Actions';

  @override
  String get textCardsTemplateLibrary => 'Template Library';

  @override
  String get textCardsTemplateLibrarySubtitle => '16+ Beautiful Templates';

  @override
  String get textCardsSmartSplit => 'Smart Split';

  @override
  String get textCardsSmartSplitSubtitle => 'Long Text Segmentation';

  @override
  String get textCardsContentLibrary => 'Content Library';

  @override
  String get textCardsContentLibrarySubtitle => 'Manage All Cards';

  @override
  String get textCardsShare => 'Share';

  @override
  String get textCardsShareSubtitle => 'Export HD Images';

  @override
  String get textCardsFeatures => 'Features';

  @override
  String get textCardsModernTemplates => 'Modern Style Templates';

  @override
  String get textCardsModernTemplatesDesc =>
      'Carefully designed modern social and reading style templates to make your content more attractive';

  @override
  String get textCardsInlineEditing => 'Inline Text Editing';

  @override
  String get textCardsInlineEditingDesc =>
      'Select any text fragment, adjust font, color, size in real-time, what you see is what you get';

  @override
  String get textCardsHDExport => 'HD Image Export';

  @override
  String get textCardsHDExportDesc =>
      'Support multiple resolutions and aspect ratios, save to album with one click, perfect for all platforms';

  @override
  String get textCardsViewContentLibrary => 'View My Content Library';

  @override
  String get textCardsManageCards => 'Manage and browse all created cards';

  @override
  String get textCardsCreate => 'Create';

  @override
  String get textCardsPleaseCreateCardFirst => 'Please create a card first';

  @override
  String get textCardsCardCreatedSuccess =>
      'Card created successfully! Saved to content library';

  @override
  String textCardsBatchCreateSuccess(Object count) {
    return 'Batch creation successful! Created $count cards';
  }

  @override
  String textCardsBatchCreatePartial(Object success, Object total) {
    return 'Batch creation completed! Successfully created $success/$total cards';
  }

  @override
  String textCardsCreateFailed(Object error) {
    return 'Creation failed: $error';
  }

  @override
  String textCardsBatchCreateFailed(Object error) {
    return 'Batch creation failed: $error';
  }

  @override
  String get textCardsEditorTitle => 'Card Editor';

  @override
  String get textCardsCreateCard => 'Create Card';

  @override
  String get textCardsEditCard => 'Edit Card';

  @override
  String get textCardsSave => 'Save';

  @override
  String get textCardsEdit => 'Edit';

  @override
  String get textCardsPreview => 'Preview';

  @override
  String get textCardsEnterCardTitle => 'Enter card title...';

  @override
  String get textCardsEnterContent =>
      'Enter content...\n\nSupports Markdown format:\n• **Bold**\n• *Italic*\n• • Unordered list\n• 1. Ordered list';

  @override
  String get textCardsContentRequired => 'Please enter content';

  @override
  String textCardsSaveFailed(Object error) {
    return 'Save failed: $error';
  }

  @override
  String get textCardsChangeTemplate => 'Change Template';

  @override
  String get textCardsHideTitle => 'Hide Title';

  @override
  String get textCardsShowTitle => 'Show Title';

  @override
  String get textCardsBoldText => 'Bold text';

  @override
  String get textCardsItalicText => 'Italic text';

  @override
  String get textCardsUnderlineText => 'Underline text';

  @override
  String get textCardsPreviewPlaceholder =>
      'Enter content in the edit tab to see preview...';

  @override
  String get textCardsContentEditor => 'Content Editor';

  @override
  String get textCardsEnterTitle => 'Enter title (optional)';

  @override
  String get textCardsAddSplitMarker => 'Add Split Marker';

  @override
  String get textCardsEnterRenderer => 'Enter Renderer';

  @override
  String textCardsSplitMarkerInfo(Object count, Object sections) {
    return 'Set $count split markers, will generate $sections cards';
  }

  @override
  String get textCardsEnterContentHint =>
      'Enter or paste your content here...\n\nTips:\n- Use # to create headings\n- Use - or * to create lists\n- Use > to create quotes\n- Click split button to add split marker at cursor position';

  @override
  String get textCardsTextPreview => 'Text Preview';

  @override
  String get textCardsPreviewWillAppear =>
      'Preview will appear when content is entered';

  @override
  String get textCardsSelectSplitMarker => 'Select Split Marker';

  @override
  String get textCardsPredefinedMarkers => 'Predefined Markers:';

  @override
  String get textCardsCustomMarker => 'Custom Marker:';

  @override
  String get textCardsEnterCustomMarker => 'Enter custom split marker';

  @override
  String get textCardsUseCustom => 'Use Custom';

  @override
  String get textCardsUnnamedCard => 'Unnamed Card';

  @override
  String get textCardsUnnamedDocument => 'Unnamed Document';

  @override
  String get svgEditorTitle => 'SVG Editor';

  @override
  String get svgManagerTitle => 'SVG Manager';

  @override
  String get svgUntitled => 'Untitled SVG';

  @override
  String get svgEditTab => 'Edit';

  @override
  String get svgPreviewTab => 'Preview';

  @override
  String get svgMoreActions => 'More Actions';

  @override
  String get svgImportFile => 'Import SVG File';

  @override
  String get svgSave => 'Save';

  @override
  String get svgExportPng => 'Export as PNG';

  @override
  String get svgSharePng => 'Share as PNG';

  @override
  String get svgShareSvg => 'Share SVG';

  @override
  String get svgRename => 'Rename';

  @override
  String get svgSaveChanges => 'Save Changes';

  @override
  String get svgEnterFileName => 'Enter SVG File Name';

  @override
  String get svgFileName => 'File Name';

  @override
  String get svgFileNameHint => 'Enter SVG file name';

  @override
  String get svgFileNameRequired => 'File name cannot be empty';

  @override
  String get svgProcessing => 'Processing...';

  @override
  String get svgLoading => 'Loading...';

  @override
  String get svgDocumentNotFound => 'Document not found';

  @override
  String svgLoadDocumentFailed(Object error) {
    return 'Failed to load document: $error';
  }

  @override
  String svgCreateDocumentFailed(Object error) {
    return 'Failed to create document: $error';
  }

  @override
  String svgSaveDocumentFailed(Object error) {
    return 'Failed to save document: $error';
  }

  @override
  String get svgSaveSuccess => 'Saved successfully';

  @override
  String svgImportFailed(Object error) {
    return 'Failed to import SVG file: $error';
  }

  @override
  String svgExportPngSuccess(Object path) {
    return 'PNG export successful: $path';
  }

  @override
  String get svgExportPngFailed => 'PNG export failed';

  @override
  String svgShareSvgFailed(Object error) {
    return 'Failed to share SVG: $error';
  }

  @override
  String svgSharePngFailed(Object error) {
    return 'Failed to share PNG: $error';
  }

  @override
  String svgInvalidSvg(Object error) {
    return 'Invalid SVG: $error';
  }

  @override
  String get svgSaveFirst => 'Please save document first';

  @override
  String get svgEnterSvgCode => 'Enter SVG code';

  @override
  String get svgNoContent => 'No SVG content';

  @override
  String get svgEnterCodeInEditor => 'Please enter SVG code in the editor tab';

  @override
  String get svgCreateNew => 'Create New SVG';

  @override
  String get svgImport => 'Import SVG';

  @override
  String get svgImportSvgFile => 'Import SVG File';

  @override
  String get svgImportTooltip => 'Import SVG';

  @override
  String get svgNewTooltip => 'New SVG';

  @override
  String get svgCreateNewTooltip => 'Create new SVG';

  @override
  String get svgNoDocuments => 'No SVG Documents';

  @override
  String get svgNoDocumentsDesc =>
      'Create a new SVG document or import existing files to get started';

  @override
  String svgLoadFailed(Object error) {
    return 'Failed to load documents: $error';
  }

  @override
  String get svgDeleteConfirm => 'Confirm Delete';

  @override
  String svgDeleteConfirmMessage(Object title) {
    return 'Are you sure you want to delete \"$title\"?';
  }

  @override
  String svgDeleteFailed(Object error) {
    return 'Failed to delete document: $error';
  }

  @override
  String get svgEdit => 'Edit';

  @override
  String get svgShare => 'Share';

  @override
  String get svgShareAsPng => 'Share as PNG';

  @override
  String get svgDelete => 'Delete';

  @override
  String get svgSavedToLibrary => 'SVG saved to content library';

  @override
  String svgCreatedAt(Object date) {
    return 'Created: $date';
  }

  @override
  String get pdfManagerTitle => 'PDF Document Management';

  @override
  String get pdfSearch => 'Search...';

  @override
  String get pdfSearchHint => 'Search...';

  @override
  String get pdfNoDocuments => 'No PDF documents found';

  @override
  String get pdfTryDifferentSearch => 'Try using different search keywords';

  @override
  String get pdfConfirmDelete => 'Confirm Delete';

  @override
  String pdfDeleteConfirm(Object filename) {
    return 'Are you sure you want to delete \"$filename\"? This action cannot be undone.';
  }

  @override
  String pdfBatchDeleteConfirm(Object count) {
    return 'Are you sure you want to delete $count selected files? This action cannot be undone.';
  }

  @override
  String get pdfCancel => 'Cancel';

  @override
  String get pdfDelete => 'Delete';

  @override
  String get pdfImport => 'Import PDF';

  @override
  String get pdfImportTooltip => 'Import PDF';

  @override
  String get pdfSecuritySettings => 'PDF Security Settings';

  @override
  String get pdfSelect => 'Select';

  @override
  String get pdfMerge => 'Merge';

  @override
  String get pdfSelectAtLeastTwo =>
      'Please select at least two PDF files to merge';

  @override
  String get pdfMergeSuccess => 'PDF merge successful';

  @override
  String get pdfMergeFailed => 'PDF merge failed';

  @override
  String get pdfIntelligentCenter => 'PDF Intelligent Management Center';

  @override
  String get pdfCenterSubtitle =>
      'Professional PDF tool integrating reading, editing, security, and sharing';

  @override
  String get pdfVersion => 'v2.0 Professional';

  @override
  String get pdfCoreFeatures => 'Core Features';

  @override
  String get pdfProfessional => 'Professional';

  @override
  String get pdfSecurityEncryption => 'Security Encryption';

  @override
  String get pdfPasswordProtection => 'Password Protection';

  @override
  String get pdfPermissionControl => 'Permission Control';

  @override
  String get pdfSmartAnnotations => 'Smart Annotations';

  @override
  String get pdfHighlight => 'Highlight';

  @override
  String get pdfTextAnnotations => 'Text Annotations';

  @override
  String get pdfFastSearch => 'Fast Search';

  @override
  String get pdfFullTextSearch => 'Full Text Search';

  @override
  String get pdfPreciseLocation => 'Precise Location';

  @override
  String get pdfDocumentMerge => 'Document Merge';

  @override
  String get pdfMultiFileMerge => 'Multi-file Merge';

  @override
  String get pdfEasySharing => 'Easy Sharing';

  @override
  String get pdfOneClickShare => 'One-click Share';

  @override
  String get pdfCloudSync => 'Cloud Sync';

  @override
  String get pdfAutoSync => 'Auto Sync';

  @override
  String get pdfQuickStart => 'Quick Start';

  @override
  String get pdfImportDocument => 'Import PDF Document';

  @override
  String get pdfViewDemo => 'View Demo';

  @override
  String get pdfHelp => 'Help';

  @override
  String get pdfSupportInfo => 'Supports .pdf format files, up to 100MB';

  @override
  String pdfModified(Object date) {
    return 'Modified: $date';
  }

  @override
  String get pdfJustNow => 'Just now';

  @override
  String pdfMinutesAgo(Object minutes) {
    return '$minutes minutes ago';
  }

  @override
  String pdfHoursAgo(Object hours) {
    return '$hours hours ago';
  }

  @override
  String pdfDaysAgo(Object days) {
    return '$days days ago';
  }

  @override
  String pdfPages(Object count) {
    return '$count pages';
  }

  @override
  String get pdfSecurityStatus => 'Security Status';

  @override
  String get pdfProtected => 'Protected';

  @override
  String get pdfRestricted => 'Restricted';

  @override
  String get pdfUnprotected => 'Unprotected';

  @override
  String get pdfUsageStats => 'Usage Statistics';

  @override
  String get pdfTotalDocuments => 'Total Documents';

  @override
  String get pdfTodayProcessed => 'Today Processed';

  @override
  String get pdfStorageSpace => 'Storage Space';

  @override
  String get pdfTips => 'Usage Tips';

  @override
  String get pdfLongPressSelect => 'Long Press to Select';

  @override
  String get pdfLongPressDesc =>
      'Long press document cards to enter multi-select mode for batch operations';

  @override
  String get pdfSecurityTip => 'Security Encryption';

  @override
  String get pdfSecurityTipDesc =>
      'Set password protection for important documents to ensure information security';

  @override
  String get pdfMergeTip => 'Document Merge';

  @override
  String get pdfMergeTipDesc =>
      'Select multiple PDF documents to merge into a single file with one click';

  @override
  String get pdfViewerTitle => 'PDF Viewer';

  @override
  String get pdfSearchText => 'Search text';

  @override
  String get pdfShowAnnotations => 'Show Annotations';

  @override
  String get pdfHideAnnotations => 'Hide Annotations';

  @override
  String get pdfDocumentInfo => 'Document Info';

  @override
  String get pdfAnnotationDetails => 'Annotation Details';

  @override
  String pdfAuthor(Object author) {
    return 'Author: $author';
  }

  @override
  String pdfCreatedAt(Object datetime, Object time) {
    return 'Created: $time';
  }

  @override
  String pdfContent(Object content) {
    return 'Content: $content';
  }

  @override
  String pdfHighlightedText(Object text) {
    return 'Highlighted text: $text';
  }

  @override
  String get pdfFileName => 'File Name';

  @override
  String get pdfFileSize => 'Size';

  @override
  String get pdfPageCount => 'Pages';

  @override
  String get pdfCreatedDate => 'Created';

  @override
  String get pdfModifiedDate => 'Modified';

  @override
  String get pdfAnnotationCount => 'Annotations';

  @override
  String get pdfClose => 'Close';

  @override
  String get pdfSecurityTitle => 'PDF Security Settings';

  @override
  String get pdfDocumentInfoSection => 'Document Information';

  @override
  String pdfStatus(Object status) {
    return 'Status: $status';
  }

  @override
  String get pdfDecryptPdf => 'Decrypt PDF';

  @override
  String get pdfEncryptedDesc =>
      'This PDF is encrypted, please enter password to decrypt';

  @override
  String get pdfCurrentPassword => 'Current Password';

  @override
  String get pdfCurrentPasswordHint => 'Enter current password';

  @override
  String get pdfDecryptButton => 'Decrypt';

  @override
  String get pdfEncryptionSettings => 'Encryption Settings';

  @override
  String get pdfUserPassword => 'User Password *';

  @override
  String get pdfUserPasswordHint => 'Password to open PDF';

  @override
  String get pdfOwnerPassword => 'Owner Password (Optional)';

  @override
  String get pdfOwnerPasswordHint => 'Password to modify permissions';

  @override
  String get pdfPermissionSettings => 'Permission Settings';

  @override
  String get pdfAllowPrint => 'Allow Printing';

  @override
  String get pdfAllowPrintDesc => 'Allow users to print PDF document';

  @override
  String get pdfAllowCopy => 'Allow Copying';

  @override
  String get pdfAllowCopyDesc => 'Allow users to copy PDF content';

  @override
  String get pdfAllowEdit => 'Allow Editing';

  @override
  String get pdfAllowEditDesc => 'Allow users to edit PDF document';

  @override
  String get pdfAllowEditAnnotations => 'Allow Edit Annotations';

  @override
  String get pdfAllowEditAnnotationsDesc =>
      'Allow users to add or edit annotations';

  @override
  String get pdfAllowFillForms => 'Allow Fill Forms';

  @override
  String get pdfAllowFillFormsDesc => 'Allow users to fill form fields';

  @override
  String get pdfAllowExtractPages => 'Allow Extract Pages';

  @override
  String get pdfAllowExtractPagesDesc => 'Allow users to extract page content';

  @override
  String get pdfAllowAssembleDocument => 'Allow Assemble Document';

  @override
  String get pdfAllowAssembleDocumentDesc =>
      'Allow users to insert, delete, rotate pages';

  @override
  String get pdfAllowHighQualityPrint => 'Allow High Quality Print';

  @override
  String get pdfAllowHighQualityPrintDesc =>
      'Allow users to print in high quality';

  @override
  String get pdfPresetPermissions => 'Preset Permissions';

  @override
  String get pdfAllPermissions => 'All Permissions';

  @override
  String get pdfBasicPermissions => 'Basic Permissions';

  @override
  String get pdfReadOnly => 'Read Only';

  @override
  String get pdfSetPermissionsOnly => 'Set Permissions Only';

  @override
  String get pdfEncryptAndSetPermissions => 'Encrypt and Set Permissions';

  @override
  String get pdfEnterUserPassword => 'Please enter user password';

  @override
  String get pdfEncryptSuccess => 'PDF encrypted successfully';

  @override
  String get pdfEncryptFailed => 'PDF encryption failed';

  @override
  String pdfEncryptionFailed(Object error) {
    return 'Encryption failed: $error';
  }

  @override
  String get pdfEnterCurrentPassword => 'Please enter current password';

  @override
  String get pdfDecryptSuccess => 'PDF decrypted successfully';

  @override
  String get pdfDecryptFailed => 'PDF decryption failed, please check password';

  @override
  String pdfDecryptionFailed(Object error) {
    return 'Decryption failed: $error';
  }

  @override
  String get pdfPermissionsSetSuccess => 'Permissions set successfully';

  @override
  String get pdfPermissionsSetFailed => 'Permissions set failed';

  @override
  String pdfSetPermissionsFailed(Object error) {
    return 'Settings failed: $error';
  }

  @override
  String get htmlManagerTitle => 'HTML Management';

  @override
  String get htmlManagerDescription =>
      'Create and edit HTML documents, all content will be automatically saved to the content library for unified management';

  @override
  String get htmlCreateNew => 'Create New HTML';

  @override
  String get htmlImportFile => 'Import HTML File';

  @override
  String get htmlImporting => 'Importing...';

  @override
  String get htmlImportSuccess => 'Import Successful';

  @override
  String htmlImportSuccessMessage(Object count) {
    return 'Successfully imported $count HTML files to content library';
  }

  @override
  String htmlImportFailed(Object error) {
    return 'Failed to import HTML file: $error';
  }

  @override
  String htmlImportingProgress(Object imported, Object total) {
    return 'Importing HTML files ($imported/$total)';
  }

  @override
  String get htmlViewContentLibrary => 'View Content Library';

  @override
  String get htmlEditorTitle => 'HTML Editor';

  @override
  String get htmlNewDocument => 'New HTML Document';

  @override
  String get htmlInputFilename => 'Enter HTML Filename';

  @override
  String get htmlFilename => 'Filename';

  @override
  String get htmlFilenameHint => 'Please enter HTML filename';

  @override
  String get htmlFilenameEmpty => 'Filename cannot be empty';

  @override
  String get htmlCancel => 'Cancel';

  @override
  String get htmlConfirm => 'Confirm';

  @override
  String get htmlSaveSuccess => 'Save successful';

  @override
  String get htmlDocumentNotFound => 'Document not found';

  @override
  String htmlLoadDocumentFailed(Object error) {
    return 'Failed to load document: $error';
  }

  @override
  String htmlCreateDocumentFailed(Object error) {
    return 'Failed to create document: $error';
  }

  @override
  String htmlSaveDocumentFailed(Object error) {
    return 'Failed to save document: $error';
  }

  @override
  String htmlImportFileFailed(Object error) {
    return 'Failed to import HTML file: $error';
  }

  @override
  String htmlExportImageFailed(Object error) {
    return 'Failed to export image: $error';
  }

  @override
  String htmlShareHtmlFailed(Object error) {
    return 'Failed to share HTML: $error';
  }

  @override
  String htmlShareImageFailed(Object error) {
    return 'Failed to share image: $error';
  }

  @override
  String htmlSaveToLibraryFailed(Object error) {
    return 'Failed to save to content library: $error';
  }

  @override
  String get htmlPleaseSaveFirst => 'Please save document first';

  @override
  String get htmlSelectSaveLocation => 'Select save location';

  @override
  String htmlExportImageSuccess(Object path) {
    return 'Export image successful: $path';
  }

  @override
  String htmlSavedToLibrary(Object title) {
    return 'Saved to content library: $title';
  }

  @override
  String get htmlProcessing => 'Processing...';

  @override
  String get htmlProcessingLargeText => 'Processing large text...';

  @override
  String get htmlInputHtmlCode => 'Enter HTML code';

  @override
  String get htmlNoContent => 'No HTML content';

  @override
  String get htmlPleaseInputHtml => 'Please enter HTML code';

  @override
  String get htmlEditMode => 'Edit Mode';

  @override
  String get htmlPreviewMode => 'Preview Mode';

  @override
  String get htmlSingleScreenMode => 'Single Screen Mode';

  @override
  String get htmlSplitScreenMode => 'Split Screen Mode';

  @override
  String get htmlMoreActions => 'More Actions';

  @override
  String get htmlImportHtmlFile => 'Import HTML File';

  @override
  String get htmlExportAsImage => 'Export as Image';

  @override
  String get htmlShareAsImage => 'Share as Image';

  @override
  String get htmlShareHtml => 'Share HTML';

  @override
  String get htmlRename => 'Rename';

  @override
  String get htmlSaveToLibrary => 'Save to Content Library';

  @override
  String get htmlSaveToLibraryTooltip => 'Save to Content Library';

  @override
  String get htmlSaveTooltip => 'Save';

  @override
  String get htmlSaveToGallery => 'Save to Gallery';

  @override
  String get htmlNewHtmlTooltip => 'New HTML';

  @override
  String get htmlImportHtmlTooltip => 'Import HTML';

  @override
  String get settingsLanguagePreference => 'Select your preferred app language';

  @override
  String get settingsStorageManagement => 'Storage Management';

  @override
  String get settingsHelpCenter => 'Help Center';

  @override
  String get settingsFeedback => 'Feedback';

  @override
  String get settingsVersionInfo => 'Version Info';

  @override
  String get settingsHelpAndFeedback => 'Help & Feedback';

  @override
  String get settingsGetHelpOrProvideFeedback => 'Get help or provide feedback';

  @override
  String get settingsHelpAndFeedbackContent =>
      'If you need help or have suggestions, please contact us through the feedback page.';

  @override
  String get settingsOk => 'OK';

  @override
  String get settingsSelectTheme => 'Select Theme';

  @override
  String get settingsSystemMode => 'System';

  @override
  String get settingsLightMode => 'Light';

  @override
  String get settingsDarkMode => 'Dark';

  @override
  String get storageLoadingStorageInfo => 'Loading storage information';

  @override
  String storageLoadStorageInfoFailed(Object error) {
    return 'Failed to load storage information: $error';
  }

  @override
  String get storageTotalUsage => 'Total Storage Usage';

  @override
  String get storageDetails => 'Storage Details';

  @override
  String get storageAppData => 'App Data';

  @override
  String get storageCacheFiles => 'Cache Files';

  @override
  String get storageContentData => 'Content Data';

  @override
  String get storageVoiceFiles => 'Voice Files';

  @override
  String get storageImageFiles => 'Image Files';

  @override
  String get storageSettingsData => 'Settings Data';

  @override
  String get storageCleanupOptions => 'Cleanup Options';

  @override
  String get storageClearCache => 'Clear Cache';

  @override
  String get storageClearCacheDesc => 'Delete temporary files and cache data';

  @override
  String get storageClearTempFiles => 'Clear Temporary Files';

  @override
  String get storageClearTempFilesDesc =>
      'Delete temporary files generated during processing';

  @override
  String get storageDataManagement => 'Data Management';

  @override
  String get storageExportData => 'Export Data';

  @override
  String get storageExportDataDesc => 'Export app data to files';

  @override
  String get storageImportData => 'Import Data';

  @override
  String get storageImportDataDesc => 'Import app data from files';

  @override
  String get storageResetAppData => 'Reset App Data';

  @override
  String get storageResetAppDataDesc =>
      'Clear all data and restore default settings';

  @override
  String get storageCacheCleared => 'Cache cleared successfully';

  @override
  String storageClearCacheFailed(Object error) {
    return 'Failed to clear cache: $error';
  }

  @override
  String get storageTempFilesCleared => 'Temporary files cleared successfully';

  @override
  String storageClearTempFilesFailed(Object error) {
    return 'Failed to clear temporary files: $error';
  }

  @override
  String get storageVoiceManagementInDevelopment =>
      'Voice file management feature is in development';

  @override
  String get storageImageManagementInDevelopment =>
      'Image file management feature is in development';

  @override
  String get storageDataExportInDevelopment =>
      'Data export feature is in development';

  @override
  String get storageDataImportInDevelopment =>
      'Data import feature is in development';

  @override
  String get storageResetDataTitle => 'Reset App Data';

  @override
  String get storageResetDataMessage =>
      'This operation will delete all data and restore default settings. This cannot be undone. Are you sure you want to continue?';

  @override
  String get storageCancel => 'Cancel';

  @override
  String get storageConfirm => 'Confirm';

  @override
  String get storageDataResetComplete => 'App data reset completed';

  @override
  String storageDataResetFailed(Object error) {
    return 'Failed to reset app data: $error';
  }

  @override
  String get iosSettingsGeneral => 'General';

  @override
  String get iosSettingsLanguageRegion => 'Language & Region';

  @override
  String get iosSettingsDisplayBrightness => 'Display & Brightness';

  @override
  String get iosSettingsAppearance => 'Appearance';

  @override
  String get iosSettingsPrivacySecurity => 'Privacy & Security';

  @override
  String get iosSettingsPrivacySettings => 'Privacy Settings';

  @override
  String get iosSettingsStorage => 'Storage';

  @override
  String get iosSettingsStorageManagement => 'Storage Management';

  @override
  String get iosSettingsViewStorageUsage => 'View storage usage';

  @override
  String get iosSettingsDataImportExport => 'Data Import/Export';

  @override
  String get iosSettingsBackupRestoreData => 'Backup and restore data';

  @override
  String get iosSettingsSupport => 'Support';

  @override
  String get iosSettingsHelpCenter => 'Help Center';

  @override
  String get iosSettingsFeedback => 'Feedback';

  @override
  String get iosSettingsRateApp => 'Rate App';

  @override
  String get iosSettingsAbout => 'About';

  @override
  String get iosSettingsAboutApp => 'About App';

  @override
  String get iosSettingsCurrentLanguage => 'English';

  @override
  String get iosSettingsLightTheme => 'Light';

  @override
  String get iosSettingsDarkTheme => 'Dark';

  @override
  String get iosSettingsSystemTheme => 'System';

  @override
  String get iosSettingsSelectLanguage => 'Select Language';

  @override
  String get iosSettingsSimplifiedChinese => '简体中文';

  @override
  String get iosSettingsTraditionalChinese => '繁體中文';

  @override
  String get iosSettingsEnglish => 'English';

  @override
  String iosSettingsLanguageSelected(Object language) {
    return 'Language selected: $language';
  }

  @override
  String get iosSettingsSelectAppearance => 'Select Appearance';

  @override
  String get iosSettingsPrivacyContent =>
      'We value your privacy. All data processing is done locally and will not be uploaded to servers.';

  @override
  String get iosSettingsPrivacyManage =>
      'You can manage your data in settings at any time.';

  @override
  String get iosSettingsUnderstand => 'Understand';

  @override
  String get iosSettingsDataManagementTitle => 'Data Management';

  @override
  String get iosSettingsSelectOperation => 'Select operation to perform';

  @override
  String get iosSettingsExportData => 'Export Data';

  @override
  String get iosSettingsImportData => 'Import Data';

  @override
  String get iosSettingsCreateBackup => 'Create Backup';

  @override
  String get iosSettingsRateAppTitle => 'Rate App';

  @override
  String get iosSettingsRateAppMessage =>
      'Do you like this app? Please rate us on the App Store!';

  @override
  String get iosSettingsLater => 'Later';

  @override
  String get iosSettingsRateNow => 'Rate Now';

  @override
  String get iosSettingsCannotOpenAppStore => 'Cannot open App Store';

  @override
  String get iosSettingsAppStoreError => 'Error opening App Store';

  @override
  String iosSettingsVersion(Object version) {
    return 'Version: $version';
  }

  @override
  String get iosSettingsAppDescription =>
      'A powerful content management tool to help you create and manage various formats of content more efficiently.';

  @override
  String get iosSettingsCopyright => '© 2023-2024 ContentPal Team';

  @override
  String get iosSettingsClose => 'Close';

  @override
  String get iosSettingsOK => 'OK';

  @override
  String get iosSettingsError => 'Error';

  @override
  String get iosSettingsDataExportInDevelopment =>
      'Data export feature is in development...';

  @override
  String get iosSettingsDataImportInDevelopment =>
      'Data import feature is in development...';

  @override
  String get iosSettingsBackupInDevelopment =>
      'Backup feature is in development...';

  @override
  String get helpCenterTitle => 'Help Center';

  @override
  String get helpCenterNeedHelp => 'Need Help?';

  @override
  String get helpCenterDescription =>
      'Find answers to common questions or contact us for support';

  @override
  String get helpCenterContactSupport => 'Contact Support';

  @override
  String get helpCenterUserManual => 'User Manual';

  @override
  String get helpCenterGettingStarted => 'Getting Started';

  @override
  String get helpCenterGettingStartedContent =>
      'Welcome to 内容君! You can select feature modules from the home page, such as text cards, Markdown editing, PDF processing, etc.';

  @override
  String get helpCenterTextCards => 'Text Cards';

  @override
  String get helpCenterTextCardsContent =>
      'Text cards feature helps you convert text content into beautiful card images, supporting multiple templates and style customization.';

  @override
  String get helpCenterMarkdownEditing => 'Markdown Editing';

  @override
  String get helpCenterMarkdownEditingContent =>
      'Markdown editor supports real-time preview, multiple themes, export to HTML/PDF and other features, making your document writing more efficient.';

  @override
  String get helpCenterTrafficGuideGeneration => 'Traffic Guide Generation';

  @override
  String get helpCenterTrafficGuideGenerationContent =>
      'Traffic guide generation feature can create eye-catching marketing images, supporting anti-theft features like interference and watermarks.';

  @override
  String get helpCenterVoiceFeatures => 'Voice Features';

  @override
  String get helpCenterVoiceFeaturesContent =>
      'Voice features include recording, transcription, text-to-speech, etc., supporting multiple languages and high-quality voice processing.';

  @override
  String get helpCenterPDFProcessing => 'PDF Processing';

  @override
  String get helpCenterPDFProcessingContent =>
      'PDF processing features support viewing, annotation, security settings, etc., allowing you to better manage PDF documents.';

  @override
  String get helpCenterDataSyncBackup => 'Data Sync & Backup';

  @override
  String get helpCenterDataSyncBackupContent =>
      'Your data is automatically saved locally. It is recommended to regularly use the export feature to backup important content.';

  @override
  String get helpCenterPrivacySecurity => 'Privacy & Security';

  @override
  String get helpCenterPrivacySecurityContent =>
      'We value your privacy, all data processing is done locally and will not be uploaded to servers.';

  @override
  String get helpCenterSearchHelp => 'Search Help';

  @override
  String get helpCenterSearchPlaceholder => 'Enter keywords to search...';

  @override
  String get helpCenterSearchCancel => 'Cancel';

  @override
  String get helpCenterSearch => 'Search';

  @override
  String get helpCenterSupportRequestSubject => 'ContentPal Support Request';

  @override
  String get helpCenterSupportRequestBody =>
      'Please describe the issue you encountered...';

  @override
  String get helpCenterCannotOpenEmailApp => 'Cannot open email app';

  @override
  String get helpCenterEmailError => 'Error sending email';

  @override
  String get helpCenterCannotOpenManual => 'Cannot open user manual';

  @override
  String get helpCenterManualError => 'Error opening user manual';

  @override
  String get feedbackTitle => 'Feedback';

  @override
  String get feedbackSubtitle => 'Your opinion matters';

  @override
  String get feedbackDescription =>
      'Tell us your thoughts and help us improve the app';

  @override
  String get feedbackType => 'Feedback Type';

  @override
  String get feedbackTitleLabel => 'Title';

  @override
  String get feedbackDetailedDescription => 'Detailed Description';

  @override
  String get feedbackContactEmail => 'Contact Email (Optional)';

  @override
  String get feedbackIncludeSystemInfo => 'Include System Info';

  @override
  String get feedbackIncludeSystemInfoDesc => 'Help us better diagnose issues';

  @override
  String get feedbackSubmit => 'Submit Feedback';

  @override
  String get feedbackBugReport => 'Bug Report';

  @override
  String get feedbackBugReportDesc => 'Report errors or anomalies in the app';

  @override
  String get feedbackFeatureSuggestion => 'Feature Suggestion';

  @override
  String get feedbackFeatureSuggestionDesc =>
      'Suggest new features or improvements';

  @override
  String get feedbackComplaint => 'Complaint';

  @override
  String get feedbackComplaintDesc => 'Complaints or issues with the app';

  @override
  String get feedbackPraise => 'Praise';

  @override
  String get feedbackPraiseDesc => 'Praise or positive feedback for the app';

  @override
  String get feedbackTitleHint => 'Please briefly describe your feedback';

  @override
  String get feedbackTitleRequired => 'Please enter feedback title';

  @override
  String get feedbackDescriptionHint =>
      'Please describe your issue, suggestion, or thoughts in detail...';

  @override
  String get feedbackDescriptionRequired => 'Please enter detailed description';

  @override
  String get feedbackDescriptionTooShort =>
      'Description must be at least 10 characters';

  @override
  String get feedbackEmailHint => '<EMAIL>';

  @override
  String get feedbackEmailInvalid => 'Please enter a valid email address';

  @override
  String get feedbackSubmitting => 'Submitting...';

  @override
  String feedbackSubmissionError(Object error) {
    return 'Error submitting feedback: $error';
  }

  @override
  String get feedbackSubmissionSuccessTitle =>
      'Feedback Submitted Successfully';

  @override
  String get feedbackSubmissionSuccessMessage =>
      'Thank you for your feedback! We will carefully consider your suggestions.';

  @override
  String get feedbackConfirm => 'Confirm';

  @override
  String get feedbackSystemInfoFailed => 'Failed to get system information';

  @override
  String feedbackAppVersion(Object version) {
    return 'App Version: $version';
  }

  @override
  String feedbackBuildNumber(Object buildNumber) {
    return 'Build Number: $buildNumber';
  }

  @override
  String feedbackDevice(Object device) {
    return 'Device: $device';
  }

  @override
  String feedbackSystemVersion(Object version) {
    return 'System Version: $version';
  }

  @override
  String feedbackDeviceModel(Object model) {
    return 'Device Model: $model';
  }

  @override
  String feedbackManufacturer(Object manufacturer) {
    return 'Manufacturer: $manufacturer';
  }

  @override
  String feedbackEmailSubject(Object feedbackType) {
    return '内容君 - $feedbackType';
  }

  @override
  String get feedbackCannotOpenEmailApp => 'Cannot open email app';

  @override
  String get newContentLibraryExperience => 'New Content Library Experience';

  @override
  String get supportMultipleContentTypes =>
      'Support multiple content types, prioritize rendering results';

  @override
  String get contentLibraryDemoPage => 'Content Library Demo';

  @override
  String contentServiceLoadItemFailed(Object error) {
    return 'Failed to load content item: $error';
  }

  @override
  String get contentServiceMustBeTextType => 'Must be text type content';

  @override
  String get contentServiceMustBeImageType => 'Must be image type content';

  @override
  String get contentServiceItemNotFound => 'Content item not found';

  @override
  String get contentServiceNotInitialized => 'ContentService not initialized';

  @override
  String contentServiceRenderFailed(Object error) {
    return 'Render failed: $error';
  }

  @override
  String permissionHelperRequestStorageFailed(Object error) {
    return 'Request storage permission failed: $error';
  }

  @override
  String permissionHelperRequestCameraFailed(Object error) {
    return 'Request camera permission failed: $error';
  }

  @override
  String permissionHelperRequestMultipleFailed(Object error) {
    return 'Request multiple permissions failed: $error';
  }

  @override
  String permissionHelperIosPermissionCheckFailed(Object error) {
    return 'iOS permission check failed: $error';
  }

  @override
  String get chineseTraditionalColorTitle => 'Traditional Chinese Colors';

  @override
  String get chineseTraditionalColorSubtitle =>
      'Choose your favorite traditional color theme';

  @override
  String get chineseTraditionalColorSystemTheme => 'Follow System Theme';

  @override
  String get chineseTraditionalColorSystemThemeDesc =>
      'Use app default theme colors';

  @override
  String chineseTraditionalColorSwitchedToTheme(Object themeName) {
    return 'Switched to \"$themeName\" theme';
  }

  @override
  String get chineseTraditionalColorSwitchedToSystem =>
      'Switched to system default theme';

  @override
  String get subscriptionManagement => 'Subscription Management';

  @override
  String get upgradeSubscription => 'Upgrade Subscription';

  @override
  String get upgradeSubscriptionDesc =>
      'View and purchase higher-tier subscription plans';

  @override
  String get restorePurchase => 'Restore Purchase';

  @override
  String get restorePurchaseDesc =>
      'Restore your previous subscription purchases';

  @override
  String get helpAndSupport => 'Help and Support';

  @override
  String get frequentlyAskedQuestions => 'FAQ';

  @override
  String get frequentlyAskedQuestionsDesc =>
      'View frequently asked questions about subscriptions';

  @override
  String get contactCustomerService => 'Contact Support';

  @override
  String get contactCustomerServiceDesc => 'Get help with subscription issues';

  @override
  String get refundPolicy => 'Refund Policy';

  @override
  String get refundPolicyDesc =>
      'Learn about our refund and cancellation policies';

  @override
  String get restoringPurchase => 'Restoring Purchase';

  @override
  String get communicatingWithAppStore => 'Communicating with App Store...';

  @override
  String get noRestorablePurchasesFound => 'No restorable purchases found';

  @override
  String get currentSubscription => 'Current Subscription';

  @override
  String get freeVersion => 'Free Version';

  @override
  String get availableFeatures => 'Available Features';

  @override
  String get basicProcessing => 'Basic Processing';

  @override
  String get exportWithWatermark => 'Export with Watermark';

  @override
  String get unlimitedExport => 'Unlimited Export';

  @override
  String get batchProcessing => 'Batch Processing';

  @override
  String get advancedTools => 'Advanced Tools';

  @override
  String get markdownSavedMarkdown => 'Saved Markdown';

  @override
  String get textCardNoCardsYet => 'No cards yet';

  @override
  String get textCardTitle => 'Title';

  @override
  String get textCardContent => 'Content';

  @override
  String get textCardNeedPhotoPermission =>
      'Photo album permission is required to save images, please enable permission in settings';

  @override
  String get textCardScreenshotFailed => 'Screenshot failed, please try again';

  @override
  String get textCardPermissionDenied =>
      'Permission denied, please enable photo album permission in settings';

  @override
  String get textCardExportCard => 'Export Card';

  @override
  String get textCardExportDocument => 'Export Document';

  @override
  String get textCardPreview => 'Preview';

  @override
  String get textCardExporting => 'Exporting...';

  @override
  String get textCardStartExport => 'Start Export';

  @override
  String get textCardExportSize => 'Export Size';

  @override
  String get textCardTargetPlatform => 'Target Platform';

  @override
  String get textCardFileFormat => 'File Format';

  @override
  String get textCardWatermarkSettings => 'Watermark Settings';

  @override
  String get textCardAddWatermarkSubtitle =>
      'Add app watermark to image corner';

  @override
  String get textCardCustomWatermarkText => 'Custom Watermark Text';

  @override
  String get textCardCustomWatermarkHint =>
      'Leave empty to use default watermark';

  @override
  String get textCardAdvancedOptions => 'Advanced Options';

  @override
  String get textCardIncludeTitleSubtitle => 'Show title in exported image';

  @override
  String get textCardIncludeTimestampSubtitle => 'Show creation time in image';

  @override
  String get textCardMore => 'More';

  @override
  String get textCardPart => 'Part';

  @override
  String get textCardSection => 'Section';

  @override
  String get textCardSelectionInstructions =>
      'Select any range of text below to apply styles to the selected content';

  @override
  String get textCardExportOptions => 'Export Options';

  @override
  String get textCardSaveToGallery => 'Save to Gallery';

  @override
  String get textCardExportSingleCard => 'Will export 1 card image';

  @override
  String get textCardExportMultipleCards => 'Will export';

  @override
  String get textCardImages => 'card images';

  @override
  String get textCardCard => 'Card';

  @override
  String get textCardCardNumber => '';

  @override
  String get textCardShareFromApp => 'From 内容君';

  @override
  String get textCardShareFailed => 'Share failed';

  @override
  String get textCardSavedToGallery => 'Saved';

  @override
  String get textCardCardsToGallery => 'cards to gallery';

  @override
  String get textCardCreateDocument => 'Create Document';

  @override
  String get textCardTextEditMode => 'Text Edit Mode';

  @override
  String get textCardPreviewMode => 'Preview Mode';

  @override
  String get textCardTotalCards => 'Total';

  @override
  String get textCardCards => 'cards';

  @override
  String get textCardUseSeparator => 'Use';

  @override
  String get textCardSeparatorHint => 'to separate cards';

  @override
  String get textCardDocumentTitle => 'Document Title';

  @override
  String get textCardDocumentTitleHint => 'Give this set of cards a title...';

  @override
  String get textCardDocumentContentHint =>
      'Enter or paste long text...\n\n💡 Tips:\n• Click \"Insert Separator\" where you want to split\n• First line will be auto-recognized as title if it looks like one\n• Click \"Preview\" in top right to see split effect';

  @override
  String get textCardGoBackToEditMode =>
      'Go back to edit mode and add separators to create cards';

  @override
  String get textCardListView => 'List View';

  @override
  String get textCardGridView => 'Grid View';

  @override
  String get textCardTemplate => 'Template';

  @override
  String get textCardNoCardsInDocument => 'No cards in document';

  @override
  String get textCardEditDocumentToAddCards => 'Edit document to add cards';

  @override
  String get textCardDaysAgo => 'days ago';

  @override
  String get textCardHoursAgo => 'hours ago';

  @override
  String get textCardMinutesAgo => 'minutes ago';

  @override
  String get textCardJustNow => 'just now';

  @override
  String get textCardPureTextCustomRendering => 'Pure Text Custom Rendering';

  @override
  String get textCardRenderContentToCards =>
      'Render your content into beautiful cards';

  @override
  String get textCardDesignDescription =>
      'This is a separated design: simple editor for content editing and splitting, powerful visual renderer for style customization and final display.';

  @override
  String get textCardSimpleEdit => 'Simple Edit';

  @override
  String get textCardSimpleEditDesc =>
      'Focus on pure text editing and content splitting, no complex format interference';

  @override
  String get textCardVisualRendering => 'Visual Rendering';

  @override
  String get textCardVisualRenderingDesc =>
      'WYSIWYG style customization, select text to directly modify styles';

  @override
  String get textCardSmartRecognition => 'Smart Recognition';

  @override
  String get textCardSmartRecognitionDesc =>
      'Automatically recognize content types like headings, lists, quotes and render beautifully';

  @override
  String get textCardExportShare => 'Export & Share';

  @override
  String get textCardExportShareDesc =>
      'Support single card and batch export, easily share beautiful content';

  @override
  String get textCardCoreFeatures => 'Core Features';

  @override
  String get textCardMarkdownTip =>
      'Tip: Supports Markdown format text input, including headings, lists, quotes, etc.';

  @override
  String get textCardStartCreating => 'Start Creating';

  @override
  String get textCardClickToEditContent => 'Click to edit content';

  @override
  String get textCardsLightCategory => 'Light';

  @override
  String get textCardsDarkCategory => 'Dark';

  @override
  String get textCardsNatureCategory => 'Nature';

  @override
  String get textCardsWarmCategory => 'Warm';

  @override
  String get textCardsTechCategory => 'Tech';

  @override
  String get textCardsElegantCategory => 'Elegant';

  @override
  String get textCardsVintageCategory => 'Vintage';

  @override
  String get textCardsPreviewEffect => 'Preview Effect';

  @override
  String get textCardCreateBeautifulCard => 'Create Beautiful Card';

  @override
  String get textCardsSelectTextToModifyStyle =>
      'Select text to modify font, color and size';

  @override
  String get textCardsSelectTemplate => 'Select Template';

  @override
  String get textCardsTemplateGallery => 'Template Gallery';

  @override
  String get blockMarkdown => 'Block Markdown';

  @override
  String get cardCollection => 'Card Collection';

  @override
  String get contentDefaultsTitle => 'Default Sample Content';

  @override
  String get markdownDefaultSampleTitle => 'Markdown Default Sample';

  @override
  String get markdownDefaultSampleDesc =>
      'Auto-fill example content in Markdown';

  @override
  String get textCardsDefaultSampleTitle => 'Text Cards Default Sample';

  @override
  String get textCardsDefaultSampleDesc =>
      'Auto-fill example content in Text Cards';

  @override
  String get svgBuiltInPresetTitle => 'SVG Built-in Preset';

  @override
  String get svgBuiltInPresetDesc =>
      'Show built-in SVG preset when no document exists';

  @override
  String get htmlDefaultSampleTitle => 'HTML Default Sample';

  @override
  String get htmlDefaultSampleDesc =>
      'Load built-in HTML sample when no document exists';

  @override
  String get transformerExampleInputTitle => 'Transformer Example Input';

  @override
  String get transformerExampleInputDesc =>
      'Auto-fill example input in Text Transformer';

  @override
  String get privacyPolicyTitle => 'Privacy Policy';

  @override
  String get privacyPolicySubtitle => 'Read our privacy policy';

  @override
  String get openSourceLicensesTitle => 'Open Source Licenses';

  @override
  String get openSourceLicensesSubtitle => 'View third‑party licenses';

  @override
  String get subscriptionUpgradeTitle => 'Upgrade to Premium';

  @override
  String get subscriptionUpgradeSubtitle =>
      'Unlock all premium features and enhance your AI experience';

  @override
  String get subscriptionChoosePlan => 'Choose your subscription plan';

  @override
  String subscriptionDiscountSavePercent(Object percent) {
    return 'Save $percent%';
  }

  @override
  String get subscriptionLoadingPrice => 'Loading price…';

  @override
  String subscriptionEquivalentToPerMonth(Object price) {
    return 'Equivalent to $price';
  }

  @override
  String get subscriptionIncludedFeatures => 'Included features';

  @override
  String subscriptionSubscribeNowWithPrice(Object price) {
    return 'Subscribe Now $price';
  }

  @override
  String get subscriptionAgreementPrefix => 'By subscribing, you agree to our ';

  @override
  String get termsOfService => 'Terms of Service';

  @override
  String get privacyPolicy => 'Privacy Policy';

  @override
  String get subscriptionAgreementSuffix =>
      '. Subscriptions auto‑renew and can be cancelled anytime.';

  @override
  String get subscriptionDevModeNotice =>
      'Development mode: Subscription uses mock data for now; real purchases will be enabled after App Store Connect approval';

  @override
  String get diagnosticsTitle => 'Diagnostics';

  @override
  String get diagnosticsClose => 'Close';

  @override
  String get subscriptionDiagnosticsButton => 'Diagnose Purchase Service';

  @override
  String get restoreCompletedTitle => 'Restore Completed';

  @override
  String get restoreFailedTitle => 'Restore Failed';

  @override
  String get restoreCompletedMessage =>
      'Your purchases have been successfully restored.';

  @override
  String restoreFailedMessageWithError(Object error) {
    return 'An error occurred while restoring: $error';
  }

  @override
  String get andText => 'and';

  @override
  String get subscriptionPlanMonthlyName => 'ContentPal Premium (Monthly)';

  @override
  String get subscriptionPlanMonthlyDesc =>
      'Unlock all advanced content processing features';

  @override
  String get subscriptionPlanYearlyName => 'ContentPal Premium (Yearly)';

  @override
  String get subscriptionPlanYearlyDesc =>
      'Annual subscription, More economical!';

  @override
  String get subscriptionPlanLifetimeName => 'ContentPal Premium (Lifetime)';

  @override
  String get subscriptionPlanLifetimeDesc =>
      'One-time purchase, lifetime access';

  @override
  String get subscriptionPlanFreeName => 'Free';

  @override
  String get subscriptionPlanFreeDesc => 'Basic content processing features';

  @override
  String get subscriptionFeatureUnlimitedExportsName => 'Unlimited exports';

  @override
  String get subscriptionFeatureUnlimitedExportsDesc =>
      'Export processed content without limits';

  @override
  String get subscriptionFeatureBatchProcessingName => 'Batch processing';

  @override
  String get subscriptionFeatureBatchProcessingDesc =>
      'Process multiple files at once';

  @override
  String get subscriptionFeatureAdvancedToolsName => 'Advanced tools';

  @override
  String get subscriptionFeatureAdvancedToolsDesc =>
      'Access all advanced editing and processing tools';

  @override
  String get subscriptionFeatureNoWatermarkName => 'No watermark';

  @override
  String get subscriptionFeatureNoWatermarkDesc =>
      'Exports do not include watermarks';

  @override
  String get subscriptionFeaturePrioritySupportName => 'Priority support';

  @override
  String get subscriptionFeaturePrioritySupportDesc =>
      'Get priority customer support';

  @override
  String get subscriptionFeatureFutureUpdatesName => 'Future updates';

  @override
  String get subscriptionFeatureFutureUpdatesDesc =>
      'Receive all future feature updates';

  @override
  String get subscriptionFeatureBasicProcessingName => 'Basic processing';

  @override
  String get subscriptionFeatureBasicProcessingDesc =>
      'Basic content processing features';

  @override
  String get subscriptionFeatureWatermarkedExportsName => 'Watermarked exports';

  @override
  String get subscriptionFeatureWatermarkedExportsDesc =>
      'Exports include watermark';

  @override
  String get subscriptionPeriodPerMonthSuffix => '/month';

  @override
  String get subscriptionPeriodPerYearSuffix => '/year';

  @override
  String get subscriptionPeriodLifetime => 'Lifetime';

  @override
  String get subscriptionPeriodFree => 'Free';

  @override
  String get purchaseSuccessMessage =>
      'Purchase successful. Your subscription is now active. Thank you!';

  @override
  String get currentPlan => 'Current Plan';

  @override
  String get expired => 'Expired';

  @override
  String daysUntilExpiry(int days) {
    return '$days days until expiry';
  }

  @override
  String get subscriptionExpired => 'Expired';

  @override
  String get textCardsBrowseTemplates => 'Browse Templates';

  @override
  String get textCardsBeautifulTemplates => 'beautiful templates';

  @override
  String get textCardsAllCategories => 'All';

  @override
  String get textCardsBusinessCategory => 'Business';

  @override
  String get textCardsAcademicCategory => 'Academic';

  @override
  String get textCardsCreativeCategory => 'Creative';

  @override
  String get textCardsMinimalCategory => 'Minimal';

  @override
  String get textCardsModernCategory => 'Modern';

  @override
  String get textCardExportSettings => 'Export Settings';

  @override
  String get textCardImageSize => 'Image Size';

  @override
  String get textCardImageRatio => 'Image Ratio';

  @override
  String get smartTextSplitter => 'Smart Text Splitter';

  @override
  String get smartTextSplitterSubtitle =>
      'Split long text into multiple cards intelligently';

  @override
  String characterCount(int count) {
    return 'Character count: $count';
  }

  @override
  String get splitConfig => 'Split Configuration';

  @override
  String get splitConfigDescription =>
      'Choose split mode and related parameters';

  @override
  String get splitMode => 'Split Mode';

  @override
  String get customSeparator => 'Custom Separator';

  @override
  String get customSeparatorHint => 'Enter separator, such as: ---';

  @override
  String get advancedOptions => 'Advanced Options';

  @override
  String get autoDetectTitles => 'Auto-detect Titles';

  @override
  String get autoDetectTitlesDescription =>
      'Intelligently identify title content in text';

  @override
  String get preserveFormatting => 'Preserve Formatting';

  @override
  String get preserveFormattingDescription =>
      'Preserve original text Markdown formatting';

  @override
  String get smartMerge => 'Smart Merge';

  @override
  String get smartMergeDescription => 'Automatically merge short paragraphs';

  @override
  String get maxLength => 'Max Length';

  @override
  String get maxLengthDescription => 'Maximum characters per card';

  @override
  String get splitPreview => 'Split Preview';

  @override
  String totalCards(int count) {
    return 'Total $count cards';
  }

  @override
  String get splitPreviewDescription =>
      'Preview split results, can edit, merge or delete cards';

  @override
  String get deselectAll => 'Deselect All';

  @override
  String get deleteSelected => 'Delete Selected';

  @override
  String get noSplitResults => 'No Split Results';

  @override
  String get noSplitResultsDescription =>
      'Please return to previous step to check input text and configuration';

  @override
  String get previousStep => 'Previous';

  @override
  String get nextStep => 'Next';

  @override
  String get startSplitting => 'Start Splitting';

  @override
  String get createCards => 'Create Cards';

  @override
  String get inputTextHint => 'Enter or paste text content here...';

  @override
  String get titleOptional => 'Title (Optional)';

  @override
  String get exportSettings => 'Export Settings';

  @override
  String get confirmExport => 'Confirm Export';

  @override
  String get previewInfo => 'Preview Info';

  @override
  String get dimensions => 'Dimensions';

  @override
  String get ratio => 'Ratio';

  @override
  String get qualityPercent => 'Quality';

  @override
  String get watermarkStatus => 'Watermark';

  @override
  String get include => 'Include';

  @override
  String get notInclude => 'Not Include';

  @override
  String get pixels => 'pixels';

  @override
  String get pdfCreatedTime => 'Created';

  @override
  String get pdfModifiedTime => 'Modified';

  @override
  String get pdfPermissionsSuccess => 'Permissions set successfully';

  @override
  String get pdfPermissionsFailed => 'Permissions set failed';

  @override
  String pdfSettingsFailed(Object error) {
    return 'Settings failed: $error';
  }

  @override
  String get pdfDocumentInformation => 'Document Information';

  @override
  String get pdfEncryptedMessage =>
      'This PDF is encrypted, please enter password to decrypt';

  @override
  String get pdfSecurityEncrypted => 'Encrypted';

  @override
  String get pdfSecurityReadOnly => 'Read Only';

  @override
  String get pdfSecurityRestricted => 'Restricted';

  @override
  String get pdfSecurityOpen => 'Open';

  @override
  String get pdfAllowCopying => 'Allow Copying';

  @override
  String get pdfMultipleFormats => 'Multiple Formats';

  @override
  String get pdfMultiDeviceSync => 'Multi-device Sync';

  @override
  String get pdfProtectYourDocuments => 'Protect your important documents';

  @override
  String get pdfPasswordProtectionDesc =>
      'Set user and owner passwords for PDF documents to ensure document security';

  @override
  String get pdfPermissionControlDesc =>
      'Fine-grained control over document printing, copying, editing and other permissions';

  @override
  String get pdfEncryptionAlgorithm => 'Encryption Algorithm';

  @override
  String get pdfEncryptionAlgorithmDesc =>
      'Adopt industry-standard AES encryption algorithm to ensure document security';

  @override
  String get pdfImportToStart => 'Import PDF to Start';

  @override
  String get pdfUsageTips => 'Usage Tips';

  @override
  String get htmlUntitled => 'Untitled HTML';

  @override
  String get htmlNewDocumentTitle => 'New HTML Document';

  @override
  String get htmlCopy => 'Copy';

  @override
  String get htmlRenderError => 'Cannot render HTML content';

  @override
  String get voiceHomeTitle => 'Voice Assistant';

  @override
  String get voiceHomeSubtitle =>
      'Record ideas, convert to text, smart reading';

  @override
  String get voiceUsageStats => 'Usage Statistics';

  @override
  String get voiceRecordingCount => 'Recording Count';

  @override
  String get voiceRecordingsUnit => 'recordings';

  @override
  String get voiceTotalDuration => 'Total Duration';

  @override
  String get voiceCumulativeDuration => 'Cumulative Duration';

  @override
  String get voiceQuickActions => 'Quick Actions';

  @override
  String get voiceRecordNewVoice => 'Record new voice';

  @override
  String get voiceTextToSpeech => 'Text to Speech';

  @override
  String get voiceConvertTextToVoice => 'Convert text to voice';

  @override
  String get voicePowerfulFeatures => 'Powerful Features';

  @override
  String get voiceSmartTranscription => 'Smart Transcription';

  @override
  String get voiceSmartTranscriptionDesc =>
      'Automatically convert voice to text';

  @override
  String get voiceAudioAdjustment => 'Audio Adjustment';

  @override
  String get voiceAudioAdjustmentDesc => 'Adjust speed, pitch and volume';

  @override
  String get voicePlaylist => 'Playlist';

  @override
  String get voicePlaylistDesc => 'Manage and play multiple audio files';

  @override
  String get voiceCloudSync => 'Cloud Sync';

  @override
  String get voiceCloudSyncDesc => 'Sync your recordings across devices';

  @override
  String get voiceRecentRecordings => 'Recent Recordings';

  @override
  String get voiceViewAll => 'View All';

  @override
  String get voiceNoRecordingsYet => 'No recordings yet';

  @override
  String get voiceStartFirstRecording =>
      'Click the button below to start your first recording';

  @override
  String get voiceRecordDetailTitle => 'Voice Details';

  @override
  String get voiceSaveAllChanges => 'Save all changes';

  @override
  String get voiceRecordingFileMayBeCorrupted =>
      'Recording file may be corrupted, cannot play';

  @override
  String get voiceAudioFileNotExistOrCorrupted =>
      'Audio file does not exist or is corrupted';

  @override
  String voiceFilePath(Object path) {
    return 'File path: $path';
  }

  @override
  String get voiceRecheck => 'Recheck';

  @override
  String voiceFileStatusRechecked(Object corrupted, Object status) {
    return 'File status rechecked: $status$corrupted';
  }

  @override
  String get voiceFileExists => 'file exists';

  @override
  String get voiceFileNotExists => 'file does not exist';

  @override
  String get voiceFileButCorrupted => ', but file may be corrupted';

  @override
  String get voiceVoiceTranscription => 'Voice Transcription';

  @override
  String get voiceReadText => 'Read Text';

  @override
  String get voiceNoTranscriptionText => 'No transcription text yet';

  @override
  String get voiceSaveTranscriptionText => 'Save Transcription Text';

  @override
  String voiceCreateTime(Object time) {
    return 'Create Time: $time';
  }

  @override
  String get voicePlaying => 'Playing...';

  @override
  String get voicePaused => 'Paused';

  @override
  String get voiceAudioFileNotExist => 'Audio file does not exist, cannot play';

  @override
  String get voiceAudioDurationAbnormal =>
      'Audio duration abnormal, may not play properly';

  @override
  String voiceLoadAudioFailed(Object error) {
    return 'Failed to load audio: $error';
  }

  @override
  String voicePlayFailed(Object error) {
    return 'Play failed: $error';
  }

  @override
  String get voiceTitleSaved => 'Title saved';

  @override
  String get voiceTranscriptionTextSaved => 'Transcription text saved';

  @override
  String get voiceTtsPlayerTitle => 'Text to Speech';

  @override
  String get voiceInputTextToRead => 'Input text to read';

  @override
  String get voiceInputTextHint =>
      'Input text to read, click \"Add\" button to add to playlist';

  @override
  String get voiceAddToPlaylist => 'Add to playlist';

  @override
  String get voiceAddedToPlaylist => 'Added to playlist';

  @override
  String get voiceTtsSettings => 'TTS Settings';

  @override
  String get voiceSpeechRate => 'Speech Rate:';

  @override
  String get voicePitch => 'Pitch:';

  @override
  String get voiceVolume => 'Volume:';

  @override
  String get voiceLanguage => 'Language:';

  @override
  String get voicePlaylistTitle => 'Playlist';

  @override
  String get voiceStop => 'Stop';

  @override
  String get voicePlaylistEmpty => 'Playlist is empty';

  @override
  String get voiceTranscriptionTitle => 'Smart Transcription';

  @override
  String get voiceRealtimeTranscription => 'Realtime Transcription';

  @override
  String get voiceFileTranscription => 'File Transcription';

  @override
  String get voiceBatchTranscription => 'Batch Transcription';

  @override
  String get voiceSelectAudioFile => 'Select audio file for transcription';

  @override
  String get voiceFileSelected => 'File Selected';

  @override
  String get voiceSelectFile => 'Select File';

  @override
  String get voiceReselectFile => 'Reselect File';

  @override
  String get voiceTranscriptionResult => 'Transcription Result';

  @override
  String get voiceBatchTranscriptionFeature => 'Batch Transcription Feature';

  @override
  String get voiceSelectMultipleFiles =>
      'Select multiple files for batch transcription';

  @override
  String get voiceSelectMultipleFilesBtn => 'Select Multiple Files';

  @override
  String get voiceBatchProcessingProgress => 'Batch Processing Progress';

  @override
  String get voiceBatchTranscriptionInDev =>
      'Batch transcription feature in development...';

  @override
  String get voiceReadyToStart => 'Ready to start';

  @override
  String get voiceTranscribing => 'Transcribing...';

  @override
  String get voiceClickToStartRealtime =>
      'Click button below to start realtime transcription';

  @override
  String get voiceExport => 'Export';

  @override
  String get voiceShare => 'Share';

  @override
  String get voiceNoTranscriptionContent =>
      'No transcription content to export';

  @override
  String get voiceNoTranscriptionContentToShare =>
      'No transcription content to share';

  @override
  String get voiceExportFeatureInDev => 'Export feature in development...';

  @override
  String get voiceShareFeatureInDev => 'Share feature in development...';

  @override
  String get voiceTranscriptionSettings => 'Transcription Settings';

  @override
  String get voiceEnablePunctuation => 'Enable Punctuation';

  @override
  String get voiceAutoAddPunctuation => 'Automatically add punctuation';

  @override
  String get voiceSpeakerDetection => 'Speaker Detection';

  @override
  String get voiceDetectDifferentSpeakers => 'Detect different speakers';

  @override
  String get voiceConfidenceThreshold => 'Confidence Threshold';

  @override
  String get voicePermissionRequiredMessage =>
      'Voice transcription feature requires microphone permission. Please enable microphone access in settings.';

  @override
  String get voiceRecordingComplete => 'Recording complete';

  @override
  String get voiceRecordingFailedRetry => 'Recording failed, please retry';

  @override
  String voiceSelectFileFailed(Object error) {
    return 'Failed to select file: $error';
  }

  @override
  String voiceProcessAudioFileFailed(Object error) {
    return 'Failed to process audio file: $error';
  }

  @override
  String voiceSelectBatchFilesFailed(Object error) {
    return 'Failed to select batch files: $error';
  }

  @override
  String voiceFilesSelected(Object count) {
    return 'Selected $count files';
  }

  @override
  String get voiceNoTranscriptionContentToSave =>
      'No transcription content to save';

  @override
  String get voiceSaveTranscriptionResult => 'Save Transcription Result';

  @override
  String get voiceTranscriptionContentPreview =>
      'Transcription content preview:';

  @override
  String get voiceTranscriptionResultSaved => 'Transcription result saved';

  @override
  String voiceSaveFailed(Object error) {
    return 'Save failed: $error';
  }

  @override
  String get voiceTranscriptionFailedRetry =>
      'Transcription failed, please retry';

  @override
  String get voiceSmartTranscriptionPageTitle => 'Smart Transcription';

  @override
  String get voiceInitializationFailedCheckPermission =>
      'Initialization failed, please check microphone permission';

  @override
  String voiceInitializationException(Object error) {
    return 'Initialization exception: $error';
  }

  @override
  String get voiceServiceInitializationFailed =>
      'Service initialization failed';

  @override
  String get voiceStartTranscriptionFailed => 'Failed to start transcription';

  @override
  String get voiceTranscriptionIdle => 'Ready';

  @override
  String get voiceTranscriptionInProgress => 'Transcribing...';

  @override
  String get voiceTranscriptionCompleted => 'Transcription Completed';

  @override
  String get voiceTranscriptionError => 'Transcription Error';

  @override
  String get voiceLanguageSelector => 'Language:';

  @override
  String get voiceTranscriptionResultTitle => 'Transcription Result';

  @override
  String get voiceClickToStartTranscription =>
      'Click start button to begin transcription...';

  @override
  String get voiceStopTranscription => 'Stop Transcription';

  @override
  String get voiceClear => 'Clear';

  @override
  String get voiceIosPermissionTestTitle => 'iOS Permission Test';

  @override
  String get voiceDirectIosPermissionTest => 'Direct iOS Permission Test';

  @override
  String get voicePageLoaded => 'Page loaded';

  @override
  String voiceInitialMicPermissionStatus(Object status) {
    return 'Initial microphone permission status: $status';
  }

  @override
  String voiceInitialSpeechPermissionStatus(Object status) {
    return 'Initial speech recognition permission status: $status';
  }

  @override
  String get voiceNonIosPlatform =>
      'Non-iOS platform, not checking permissions';

  @override
  String get voiceRequestMicPermission => 'Request Microphone Permission';

  @override
  String voiceMicPermissionStatus(Object status) {
    return 'Microphone permission status: $status';
  }

  @override
  String get voiceRequestSpeechPermission =>
      'Request Speech Recognition Permission';

  @override
  String voiceSpeechPermissionStatus(Object status) {
    return 'Speech recognition permission status: $status';
  }

  @override
  String get voiceTestRecording => 'Test Recording';

  @override
  String get voiceTestRecordingFunction => 'Test recording function...';

  @override
  String get voiceRecorderInstanceCreated => 'Recorder instance created';

  @override
  String get voiceRecorderInitialized => 'Recorder initialized';

  @override
  String get voiceRecorderTestComplete => 'Recorder test complete';

  @override
  String get voiceRecorderClosed => 'Recorder closed';

  @override
  String voiceRecorderError(Object error) {
    return 'Recorder error: $error';
  }

  @override
  String get voiceTestSpeechRecognition => 'Test Speech Recognition';

  @override
  String get voiceTestSpeechRecognitionFunction => 'Test speech recognition...';

  @override
  String voiceSpeechRecognitionError(Object error) {
    return 'Speech recognition error: $error';
  }

  @override
  String voiceSpeechRecognitionStatus(Object status) {
    return 'Speech recognition status: $status';
  }

  @override
  String voiceSpeechRecognitionInit(Object success) {
    return 'Speech recognition init: $success';
  }

  @override
  String get voiceStartListening => 'Start listening...';

  @override
  String voiceRecognitionResult(Object result) {
    return 'Recognition result: $result';
  }

  @override
  String get voiceStopListening => 'Stop listening';

  @override
  String voiceSpeechRecognitionTestError(Object error) {
    return 'Speech recognition test error: $error';
  }

  @override
  String get voiceOpenAppSettings => 'Open App Settings';

  @override
  String get voiceOperationLog => 'Operation Log:';

  @override
  String get voiceIosPageLoaded => 'Page loaded';

  @override
  String get voiceIosInitialMicrophonePermission =>
      'Initial microphone permission status';

  @override
  String get voiceIosInitialSpeechPermission =>
      'Initial speech recognition permission status';

  @override
  String get voiceIosNonIosPlatform =>
      'Non-iOS platform, not checking permissions';

  @override
  String get voiceIosRequestMicrophonePermission =>
      'Requesting microphone permission...';

  @override
  String get voiceIosMicrophonePermissionStatus =>
      'Microphone permission status';

  @override
  String get voiceIosRequestSpeechPermission =>
      'Requesting speech recognition permission...';

  @override
  String get voiceIosSpeechPermissionStatus =>
      'Speech recognition permission status';

  @override
  String get voiceIosTestRecordingFunction => 'Testing recording function...';

  @override
  String get voiceIosRecorderInstanceCreated => 'Recorder instance created';

  @override
  String get voiceIosRecorderInitialized => 'Recorder initialized';

  @override
  String get voiceIosRecorderTestCompleted => 'Recorder test completed';

  @override
  String get voiceIosRecorderClosed => 'Recorder closed';

  @override
  String get voiceIosRecorderError => 'Recorder error';

  @override
  String get voiceIosTestSpeechRecognition => 'Testing speech recognition...';

  @override
  String get voiceIosSpeechRecognitionError => 'Speech recognition error';

  @override
  String get voiceIosSpeechRecognitionStatus => 'Speech recognition status';

  @override
  String get voiceIosSpeechRecognitionInitialization =>
      'Speech recognition initialization';

  @override
  String get voiceIosSuccess => 'Success';

  @override
  String get voiceIosFailed => 'Failed';

  @override
  String get voiceIosStartListening => 'Start listening...';

  @override
  String get voiceIosRecognitionResult => 'Recognition result';

  @override
  String get voiceIosStopListening => 'Stop listening';

  @override
  String get voiceIosSpeechRecognitionTestError =>
      'Speech recognition test error';

  @override
  String get voiceIosOpenAppSettings => 'Open app settings';

  @override
  String get voiceIosPermissionTest => 'iOS Permission Test';

  @override
  String get voiceIosDirectPermissionTest => 'Direct iOS Permission Test';

  @override
  String get voiceIosOperationLogs => 'Operation Logs';

  @override
  String get voiceOK => 'OK';

  @override
  String get voiceMicrophonePermissionRequired =>
      'Voice transcription requires microphone permission. Please allow microphone access in settings.';

  @override
  String get voiceLanguageChineseSimplified => '中文（简体）';

  @override
  String get voiceLanguageChineseTraditional => '中文（繁体）';

  @override
  String get voiceLanguageEnglish => 'English (US)';

  @override
  String get voiceLanguageJapanese => '日本語';

  @override
  String get voiceLanguageKorean => '한국어';

  @override
  String get trafficGuideWatermarkTitle => 'Text Watermark Processing';

  @override
  String get trafficGuideAddWatermarkModeSubtitle =>
      'Add visible or invisible watermark to text';

  @override
  String get trafficGuideRemoveWatermarkModeSubtitle =>
      'Remove added watermark from text';

  @override
  String get trafficGuideVisibleWatermark => 'Visible Watermark';

  @override
  String get trafficGuideWatermarkType => 'Watermark Type';

  @override
  String trafficGuideProcessFailed(Object error) {
    return 'Processing failed: $error';
  }

  @override
  String get trafficGuideShowPreview => 'Show Preview';

  @override
  String get trafficGuideHidePreview => 'Hide Preview';

  @override
  String get trafficGuideProcessSuccess => 'Processing successful';

  @override
  String get trafficGuideDetectedWatermark => 'Detected Watermark';

  @override
  String get trafficGuideUnknownWatermark => 'Unknown Watermark';

  @override
  String get trafficGuideNoWatermarkDetected => 'No Watermark Detected';

  @override
  String get trafficGuideProcessedText => 'Processed Text';

  @override
  String get trafficGuideInvisibleWatermarkInfo =>
      'This will add an invisible watermark using special Unicode characters that won\'t be visible to readers but can be detected by this tool.';

  @override
  String get trafficGuideWatermarkRemovedSuccess =>
      'Watermark removed successfully!';

  @override
  String get trafficGuideWatermarkAddedSuccess =>
      'Watermark added successfully!';

  @override
  String get exportSocialWeChatMoments => 'WeChat Moments';

  @override
  String get exportSocialWeibo => 'Weibo Image';

  @override
  String get exportSocialXiaohongshu => 'Xiaohongshu';

  @override
  String get exportSocialInstagram => 'Instagram';

  @override
  String get exportSocialTwitter => 'Twitter';

  @override
  String get exportWidthLabel => 'Width';

  @override
  String get exportHeightLabel => 'Height';

  @override
  String get exportOptimizeForSocial => 'Social Media Optimization';

  @override
  String get exportOptimizeForSocialSubtitle =>
      'Optimize size for social platforms';

  @override
  String get exportSocialPlatformSizes => 'Social Platform Sizes';

  @override
  String get exportSizeSmall => 'Small (400×300)';

  @override
  String get exportSizeMedium => 'Medium (800×600)';

  @override
  String get exportSizeLarge => 'Large (1200×900)';

  @override
  String get exportSizeCustom => 'Custom';

  @override
  String get general => 'General';

  @override
  String get appearanceAndBrightness => 'Display & Brightness';

  @override
  String get privacyAndSecurity => 'Privacy & Security';

  @override
  String get storage => 'Storage';

  @override
  String get support => 'Support';

  @override
  String get languageAndRegion => 'Language & Region';

  @override
  String get privacySettings => 'Privacy Settings';

  @override
  String get storageManagement => 'Storage Management';

  @override
  String get viewStorageUsage => 'View storage usage';

  @override
  String get dataImportExport => 'Data Import & Export';

  @override
  String get backupAndRestoreData => 'Backup and restore data';

  @override
  String get helpCenter => 'Help Center';

  @override
  String get feedback => 'Feedback';

  @override
  String get rateApp => 'Rate App';

  @override
  String get aboutApp => 'About App';

  @override
  String get dataManagement => 'Data Management';

  @override
  String get selectOperation => 'Select operation to perform';

  @override
  String get exportData => 'Export Data';

  @override
  String get importData => 'Import Data';

  @override
  String get createBackup => 'Create Backup';

  @override
  String get dataExportInProgress => 'Data export feature is in development...';

  @override
  String get dataImportInProgress => 'Data import feature is in development...';

  @override
  String get backupInProgress => 'Backup feature is in development...';

  @override
  String get doYouLikeThisApp =>
      'Do you like this app? Please rate us on the App Store!';

  @override
  String get later => 'Later';

  @override
  String get goToRate => 'Rate Now';

  @override
  String get cannotOpenAppStore => 'Cannot open App Store';

  @override
  String get errorOpeningAppStore => 'Error occurred while opening App Store';

  @override
  String get understand => 'Understood';

  @override
  String get weValueYourPrivacy =>
      'We value your privacy. All data processing is done locally and will not be uploaded to servers.';

  @override
  String get manageDataAnytime =>
      'You can manage your data in settings at any time.';

  @override
  String languageSelected(Object language) {
    return 'Language selected: $language';
  }

  @override
  String get light => 'Light';

  @override
  String get dark => 'Dark';

  @override
  String get selectLanguage => 'Select Language';

  @override
  String get selectAppearance => 'Select Appearance';

  @override
  String get traditionalChinese => 'Traditional Chinese';

  @override
  String get simplifiedChinese => 'Simplified Chinese';

  @override
  String get contentSaveButtonFavorite => 'Favorite';

  @override
  String get textTransformerSelectMode => 'Select Transformation Mode';

  @override
  String get textTransformerInputText => 'Input Text';

  @override
  String get textTransformerOutputResult => 'Transformation Result';

  @override
  String get textTransformerHint => 'Enter text to transform here...';

  @override
  String get textTransformerOutputHint =>
      'Transformed text will appear here...';

  @override
  String get textTransformerCharacters => 'characters';

  @override
  String get textTransformerTransform => 'Transform';

  @override
  String get textTransformerTransforming => 'Transforming...';

  @override
  String get textTransformerClearAll => 'Clear All';

  @override
  String get textTransformerCopyResult => 'Copy Result';

  @override
  String get textTransformerCopied => 'Copied to clipboard';

  @override
  String get textTransformerTemplateEmojiName => 'Emoji Conversion';

  @override
  String get textTransformerTemplateEmojiDesc =>
      'Convert text to special emoji characters';

  @override
  String get textTransformerTemplateFancyName => 'Fancy Letters';

  @override
  String get textTransformerTemplateFancyDesc =>
      'Transform to elegant fancy letters';

  @override
  String get textTransformerTemplateBoldName => 'Bold Text';

  @override
  String get textTransformerTemplateBoldDesc =>
      'Convert to bold Unicode characters';

  @override
  String get textTransformerTemplateDecorativeName => 'Decorative Text';

  @override
  String get textTransformerTemplateDecorativeDesc => 'Add decorative symbols';

  @override
  String get textTransformerTemplateMixedName => 'Mixed Effects';

  @override
  String get textTransformerTemplateMixedDesc =>
      'Randomly combine multiple transformation effects';

  @override
  String get textTransformerTemplateInvisibleName => 'Invisible Characters';

  @override
  String get textTransformerTemplateInvisibleDesc =>
      'Add invisible characters to bypass detection';

  @override
  String get textTransformerTemplateUnicodeName => 'Unicode Variants';

  @override
  String get textTransformerTemplateUnicodeDesc =>
      'Use Unicode variant characters';

  @override
  String get textTransformerEffectEmojiDesc =>
      'Convert numbers and letters to special Unicode characters';

  @override
  String get textTransformerEffectFancyDesc =>
      'Transform to elegant fancy letters';

  @override
  String get textTransformerEffectBoldDesc =>
      'Convert to bold Unicode characters';

  @override
  String get textTransformerEffectDecorativeDesc => 'Add decorative symbols';

  @override
  String get textTransformerEffectMixedDesc =>
      'Randomly combine multiple transformation effects';

  @override
  String get textTransformerEffectInvisibleDesc =>
      'Add invisible characters between characters to bypass detection';

  @override
  String get textTransformerEffectUnicodeDesc =>
      'Add diacritical marks to change character appearance';

  @override
  String get textTransformerSample => 'Sample Text';

  @override
  String get textTransformerSampleInvisible =>
      'Sensitive content detection bypass test';

  @override
  String get textTransformerSampleUnicode =>
      'Special character conversion test';
}
