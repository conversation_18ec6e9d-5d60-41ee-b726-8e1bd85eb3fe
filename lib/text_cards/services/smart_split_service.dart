import '../models/split_config.dart';

/// 智能文本拆分服务
class SmartSplitService {
  static const String _defaultSeparator = '---CARD---';

  /// 根据配置拆分文本
  static List<SplitResultItem> splitText(String text, SplitConfig config) {
    if (text.trim().isEmpty) return [];

    List<SplitResultItem> results;
    switch (config.mode) {
      case SplitMode.paragraph:
        results = _splitByParagraph(text, config);
        break;
      case SplitMode.sentence:
        results = _splitBySentence(text, config);
        break;
      case SplitMode.length:
        results = _splitByLength(text, config);
        break;
      case SplitMode.custom:
        results = _splitByCustomSeparator(text, config);
        break;
      case SplitMode.smart:
        results = _smartSplit(text, config);
        break;
    }

    // 智能合并过短项
    results = _applySmartMerge(results, config);
    return results;
  }

  /// 按段落拆分
  static List<SplitResultItem> _splitByParagraph(
    String text,
    SplitConfig config,
  ) {
    final paragraphs =
        text
            .split(RegExp(r'\n\s*\n'))
            .where((p) => p.trim().isNotEmpty)
            .toList();
    final results = <SplitResultItem>[];

    for (int i = 0; i < paragraphs.length; i++) {
      final paragraph = paragraphs[i].trim();
      if (paragraph.isEmpty) continue;

      final item = _createSplitItem(paragraph, i, config);
      results.add(item);
    }

    return results;
  }

  /// 按句子拆分
  static List<SplitResultItem> _splitBySentence(
    String text,
    SplitConfig config,
  ) {
    final results = <SplitResultItem>[];
    final buffer = StringBuffer();
    for (int i = 0; i < text.length; i++) {
      final ch = text[i];
      buffer.write(ch);
      if (RegExp(r'[.!?。！？]').hasMatch(ch)) {
        final chunk = buffer.toString().trim();
        if (chunk.isNotEmpty) {
          results.add(_createSplitItem(chunk, results.length, config));
        }
        buffer.clear();
      }
    }
    final remaining = buffer.toString().trim();
    if (remaining.isNotEmpty) {
      results.add(_createSplitItem(remaining, results.length, config));
    }
    return results;
  }

  /// 按长度拆分
  static List<SplitResultItem> _splitByLength(String text, SplitConfig config) {
    final results = <SplitResultItem>[];
    final maxLength = config.maxLength;
    int index = 0;

    for (int i = 0; i < text.length; i += maxLength) {
      final end = (i + maxLength < text.length) ? i + maxLength : text.length;
      final chunk = text.substring(i, end).trim();

      if (chunk.isNotEmpty) {
        final item = _createSplitItem(chunk, index++, config);
        results.add(item);
      }
    }

    return results;
  }

  /// 按自定义分隔符拆分
  static List<SplitResultItem> _splitByCustomSeparator(
    String text,
    SplitConfig config,
  ) {
    final separator =
        config.customSeparator.isNotEmpty
            ? config.customSeparator
            : _defaultSeparator;

    // 优先按“整行等于分隔符（忽略两端空白）”拆分，更符合常见用法
    final linePattern = RegExp(
      r'^\s*' + RegExp.escape(separator) + r'\s*$',
      multiLine: true,
    );
    var parts =
        text.split(linePattern).where((p) => p.trim().isNotEmpty).toList();

    // 如果整行匹配没有生效，再尝试按字面量直接拆分（兼容内联分隔符）
    if (parts.length <= 1) {
      parts = text.split(separator).where((p) => p.trim().isNotEmpty).toList();
    }
    final results = <SplitResultItem>[];

    for (int i = 0; i < parts.length; i++) {
      final part = parts[i].trim();
      if (part.isEmpty) continue;

      final item = _createSplitItem(part, i, config);
      results.add(item);
    }

    return results;
  }

  /// 智能拆分
  static List<SplitResultItem> _smartSplit(String text, SplitConfig config) {
    final results = <SplitResultItem>[];
    final lines = text.split('\n');
    String currentContent = '';
    int index = 0;

    for (int i = 0; i < lines.length; i++) {
      final line = lines[i].trim();

      // 空行处理
      if (line.isEmpty) {
        if (currentContent.isNotEmpty) {
          final item = _createSplitItem(currentContent.trim(), index++, config);
          results.add(item);
          currentContent = '';
        }
        continue;
      }

      // 检查是否是标题
      if (_isLikelyTitle(line, config)) {
        // 保存之前的内容
        if (currentContent.isNotEmpty) {
          final item = _createSplitItem(currentContent.trim(), index++, config);
          results.add(item);
          currentContent = '';
        }

        // 创建标题项
        final titleItem = SplitResultItem(
          id: 'split_${DateTime.now().millisecondsSinceEpoch}_$index',
          content: line,
          title: line,
          originalIndex: index++,
          isTitle: true,
          type: SplitItemType.title,
        );
        results.add(titleItem);
      } else {
        // 累积内容
        currentContent += (currentContent.isEmpty ? '' : '\n') + line;

        // 检查是否达到长度限制
        if (currentContent.length > config.maxLength) {
          final item = _createSplitItem(currentContent.trim(), index++, config);
          results.add(item);
          currentContent = '';
        }
      }
    }

    // 处理剩余内容
    if (currentContent.isNotEmpty) {
      final item = _createSplitItem(currentContent.trim(), index, config);
      results.add(item);
    }

    return results;
  }

  /// 创建拆分项
  static SplitResultItem _createSplitItem(
    String content,
    int index,
    SplitConfig config,
  ) {
    String working = content;
    String? title;
    SplitItemType type = SplitItemType.content;

    // 标题检测：仅提取标题元数据，不将类型标注为标题，避免标题与正文重复
    if (config.autoDetectTitles && config.mode != SplitMode.custom) {
      final lines =
          working.split('\n').where((l) => l.trim().isNotEmpty).toList();
      if (lines.isNotEmpty) {
        final firstLine = lines.first.trim();
        if (_isLikelyTitle(firstLine, config)) {
          title = firstLine;
          // 去除首行标题，正文仅保留其后内容
          working = lines.length > 1 ? lines.skip(1).join('\n').trim() : '';
        }
      }
    }

    // preserveFormatting: 控制是否压缩多余换行与空白
    if (!config.preserveFormatting) {
      // 将多余空白压缩为单个空格，合并多行
      working = working.replaceAll(RegExp(r'[\t ]+'), ' ');
      working = working.replaceAll(RegExp(r'\s*\n\s*'), '\n').trim();
    }

    // 检测其他类型（仅在未识别为标题项时）
    final trimmed = working.trimLeft();
    if (trimmed.startsWith('- ') ||
        trimmed.startsWith('* ') ||
        trimmed.contains('\n- ')) {
      type = SplitItemType.list;
    } else if (trimmed.startsWith('> ') || trimmed.contains('\n> ')) {
      type = SplitItemType.quote;
    } else if (trimmed.startsWith('```') || trimmed.contains('```')) {
      type = SplitItemType.code;
    }

    return SplitResultItem(
      id: 'split_${DateTime.now().millisecondsSinceEpoch}_$index',
      content: working,
      title: title,
      originalIndex: index,
      isTitle: false,
      type: type,
    );
  }

  /// 判断是否像标题
  static bool _isLikelyTitle(String line, SplitConfig config) {
    if (line.isEmpty) return false;

    // 检查长度
    if (line.length > 50) return false;

    // 检查标点符号
    if (line.endsWith('？') ||
        line.endsWith('?') ||
        line.endsWith('：') ||
        line.endsWith(':')) {
      return true;
    }

    // 检查Markdown标题
    if (line.startsWith('#')) return true;

    // 检查是否全大写或包含特殊格式
    if (line == line.toUpperCase() && line.length < 20) return true;

    // 检查是否包含关键词
    final titleKeywords = ['什么是', '如何', '为什么', '怎么', '介绍', '概述', '总结'];
    for (final keyword in titleKeywords) {
      if (line.contains(keyword)) return true;
    }

    return false;
  }

  /// 根据配置合并过短项
  static List<SplitResultItem> _applySmartMerge(
    List<SplitResultItem> items,
    SplitConfig config,
  ) {
    // 自定义分隔符模式下不做智能合并，确保结果精确可控
    if (!config.smartMerge || items.isEmpty || config.mode == SplitMode.custom) {
      return items;
    }
    final merged = <SplitResultItem>[];
    const int minLength = 40; // 经验阈值，可与配置关联

    for (final item in items) {
      if (merged.isEmpty) {
        merged.add(item);
        continue;
      }
      final last = merged.last;
      if (item.type == SplitItemType.title) {
        // 标题单独保留
        merged.add(item);
        continue;
      }
      if (item.content.trim().length < minLength) {
        // 过短则并入上一个非标题项
        if (last.type != SplitItemType.title) {
          final newContent =
              (last.content.trim().isEmpty
                      ? item.content.trim()
                      : '${last.content}\n\n${item.content.trim()}')
                  .trim();
          merged[merged.length - 1] = last.copyWith(content: newContent);
        } else {
          // 上一个是标题，则并入后面（此处简化为附加到标题后的新段落）
          merged.add(item);
        }
      } else {
        merged.add(item);
      }
    }

    return merged;
  }

  /// 合并拆分项
  static List<SplitResultItem> mergeItems(
    List<SplitResultItem> items,
    int index1,
    int index2,
  ) {
    if (index1 >= items.length || index2 >= items.length || index1 == index2) {
      return items;
    }

    final newItems = List<SplitResultItem>.from(items);
    final item1 = newItems[index1];
    final item2 = newItems[index2];

    // 合并内容
    final mergedContent = '${item1.content}\n\n${item2.content}';
    final mergedItem = item1.copyWith(
      content: mergedContent,
      title: item1.title ?? item2.title,
    );

    // 移除较大索引的项，更新较小索引的项
    final minIndex = index1 < index2 ? index1 : index2;
    final maxIndex = index1 > index2 ? index1 : index2;

    newItems[minIndex] = mergedItem;
    newItems.removeAt(maxIndex);

    return newItems;
  }

  /// 删除拆分项
  static List<SplitResultItem> removeItem(
    List<SplitResultItem> items,
    int index,
  ) {
    if (index >= items.length) return items;

    final newItems = List<SplitResultItem>.from(items);
    newItems.removeAt(index);
    return newItems;
  }
}
