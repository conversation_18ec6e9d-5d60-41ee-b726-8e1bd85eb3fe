import 'dart:typed_data';
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:image_gallery_saver/image_gallery_saver.dart';
import 'package:permission_handler/permission_handler.dart';
import '../models/enhanced_card_template.dart';
import '../models/export_config.dart';

/// 卡片导出助手类
/// 提供静态方法来导出卡片为图片
class CardExportHelper {
  /// 导出卡片为图片
  static Future<void> exportCard({
    required BuildContext context,
    required String content,
    required EnhancedCardTemplate template,
    Map<String, dynamic>? customStyles,
    ExportConfig? exportConfig,
  }) async {
    try {
      debugPrint('🔥 导出助手开始执行');

      // 请求存储权限
      bool hasPermission = false;

      debugPrint('🔥 检查平台权限');
      if (Theme.of(context).platform == TargetPlatform.iOS) {
        // iOS 使用 photos 权限
        final permission = await Permission.photos.request();
        hasPermission = permission.isGranted;
        debugPrint('🔥 iOS photos权限: $hasPermission');

        if (!hasPermission) {
          // 如果权限被拒绝，尝试引导用户到设置
          if (permission.isPermanentlyDenied) {
            throw Exception('相册权限被永久拒绝，请到设置中手动开启权限');
          } else {
            throw Exception('需要相册权限才能保存图片');
          }
        }
      } else {
        // Android 权限处理 - 尝试多种权限
        debugPrint('🔥 检查Android权限');

        // 先尝试photos权限（Android 13+）
        var photosPermission = await Permission.photos.request();
        if (photosPermission.isGranted) {
          hasPermission = true;
          debugPrint('🔥 Android photos权限获取成功');
        } else {
          // 如果photos权限失败，尝试storage权限
          var storagePermission = await Permission.storage.request();
          hasPermission = storagePermission.isGranted;
          debugPrint('🔥 Android storage权限: $hasPermission');
        }

        if (!hasPermission) {
          throw Exception('需要存储权限才能保存图片，请在设置中开启权限');
        }
      }

      debugPrint('🔥 权限检查通过，开始创建卡片图片');

      debugPrint('🔥 开始创建真实卡片图片');
      // 使用内置绘图方法创建真实的卡片图片
      Uint8List imageBytes;

      try {
        // 创建真实的卡片图片，包含用户内容和模板样式
        final config = exportConfig ?? const ExportConfig();
        imageBytes = await _createCardImage(content, template, config);
        debugPrint('🔥 卡片图片创建成功');
      } catch (e) {
        debugPrint('🔥 卡片图片创建出错: $e');
        throw Exception('卡片图片创建失败：$e');
      }

      if (imageBytes.isEmpty) {
        throw Exception('截图失败：生成的图片为空');
      }

      debugPrint('🔥 截图成功，图片大小: ${imageBytes.length} bytes');

      debugPrint('🔥 开始保存到相册');
      // 保存到相册
      final config = exportConfig ?? const ExportConfig();
      final result = await ImageGallerySaver.saveImage(
        imageBytes,
        name: 'ContentPal_TextCard_${DateTime.now().millisecondsSinceEpoch}',
        quality: config.quality,
        isReturnImagePathOfIOS: true, // iOS返回图片路径
      );

      debugPrint('🔥 保存结果: $result');

      // 检查保存结果
      if (result != null) {
        // 不同平台的成功判断逻辑
        bool isSuccess = false;
        if (result is Map) {
          isSuccess = result['isSuccess'] == true;
        } else if (result is String && result.isNotEmpty) {
          // iOS可能直接返回路径字符串
          isSuccess = true;
        }

        if (isSuccess) {
          debugPrint('🔥 保存成功，显示成功提示');
          // 延迟显示成功消息，确保之前的 SnackBar 已经消失
          Future.delayed(const Duration(milliseconds: 500), () {
            if (context.mounted) {
              _showSuccessMessage(context, '图片已成功保存到相册');
            }
          });
          debugPrint('🔥 成功提示已安排显示');
        } else {
          final errorMsg =
              result is Map ? (result['errorMessage'] ?? '未知错误') : '保存失败';
          throw Exception('保存失败：$errorMsg');
        }
      } else {
        throw Exception('保存失败：返回结果为空');
      }
    } catch (e) {
      debugPrint('🔥 导出过程中出错: $e');
      String errorMessage = '导出失败：';
      if (e.toString().contains('permission')) {
        errorMessage += '权限不足，请在设置中开启相册权限';
      } else if (e.toString().contains('Invalid image dimensions')) {
        errorMessage += '图片尺寸无效，请重试';
      } else if (e.toString().contains('截图失败')) {
        errorMessage += '图片生成失败，请重试';
      } else {
        errorMessage += e.toString().replaceAll('Exception: ', '');
      }
      _showErrorMessage(context, errorMessage);
      rethrow; // 重新抛出异常以便上层捕获
    }
  }

  /// 构建用于导出的卡片widget
  static Widget buildExportCard(String content, EnhancedCardTemplate template) {
    // 使用标准的社交媒体卡片尺寸
    const double cardWidth = 300.0;
    const double cardHeight = 400.0;
    const double margin = 20.0; // 添加边距避免裁剪问题

    return Container(
      width: cardWidth + margin * 2,
      height: cardHeight + margin * 2,
      color: Colors.white, // 使用白色背景而不是透明
      child: Center(
        child: Container(
          width: cardWidth,
          height: cardHeight,
          decoration: BoxDecoration(
            gradient: template.backgroundGradient,
            borderRadius: BorderRadius.circular(template.borderRadius),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 10,
                offset: const Offset(0, 5),
              ),
            ],
          ),
          child: Padding(
            padding: EdgeInsets.all(template.padding),
            child: _buildContent(content, template),
          ),
        ),
      ),
    );
  }

  /// 构建内容
  static Widget _buildContent(String content, EnhancedCardTemplate template) {
    // 使用安全的字体样式，避免字体加载问题
    final safeTextStyle = TextStyle(
      fontSize: template.contentFontSize,
      fontWeight: template.contentFontWeight,
      color: template.textColor,
      height: template.lineHeight,
      fontFamily: 'System', // 使用系统字体，确保可用
    );

    final safeTitleStyle = TextStyle(
      fontSize: template.titleFontSize,
      fontWeight: template.titleFontWeight,
      color: template.titleColor,
      height: 1.2,
      fontFamily: 'System', // 使用系统字体，确保可用
    );

    final lines = content.split('\n');
    final title = lines.isNotEmpty ? lines.first : '';
    final body = lines.length > 1 ? lines.skip(1).join('\n').trim() : '';

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisAlignment: MainAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: [
        // 标题
        if (title.isNotEmpty) ...[
          Text(
            title,
            style: safeTitleStyle,
            maxLines: 3,
            overflow: TextOverflow.ellipsis,
          ),
          SizedBox(height: template.padding * 0.3),
        ],

        // 内容
        if (body.isNotEmpty) ...[
          Text(
            body,
            style: safeTextStyle,
            textAlign: template.textAlign,
            maxLines: 20,
            overflow: TextOverflow.ellipsis,
          ),
        ] else if (title.isEmpty) ...[
          // 如果既没有标题也没有内容，显示整个content作为内容
          Text(
            content,
            style: safeTextStyle,
            textAlign: template.textAlign,
            maxLines: 25,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ],
    );
  }

  /// 显示成功消息
  static void _showSuccessMessage(BuildContext context, String message) {
    debugPrint('🔥 准备显示成功消息: $message');

    // 直接显示 SnackBar，不使用 addPostFrameCallback
    if (context.mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Row(
            children: [
              Icon(Icons.check_circle, color: Colors.white, size: 20),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  message,
                  style: const TextStyle(fontWeight: FontWeight.w500),
                ),
              ),
            ],
          ),
          backgroundColor: Colors.green,
          behavior: SnackBarBehavior.floating,
          duration: const Duration(seconds: 3),
          shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
          margin: const EdgeInsets.all(16),
        ),
      );
      debugPrint('🔥 成功消息已添加到 SnackBar');
    } else {
      debugPrint('🔥 上下文已销毁，无法显示成功消息');
    }
  }

  /// 显示错误消息
  static void _showErrorMessage(BuildContext context, String message) {
    debugPrint('🔥 准备显示错误消息: $message');

    // 确保在主线程中显示 SnackBar
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                Icon(Icons.error_outline, color: Colors.white, size: 20),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    message,
                    style: const TextStyle(fontWeight: FontWeight.w500),
                  ),
                ),
              ],
            ),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
            duration: const Duration(seconds: 4),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            margin: const EdgeInsets.all(16),
          ),
        );
        debugPrint('🔥 错误消息已添加到 SnackBar');
      }
    });
  }

  /// 创建真实的卡片图片
  static Future<Uint8List> _createCardImage(
    String content,
    EnhancedCardTemplate template,
    ExportConfig config,
  ) async {
    // 创建画布
    final recorder = ui.PictureRecorder();
    final canvas = Canvas(recorder);

    // 从配置获取卡片尺寸
    final dimensions = config.getDimensions();
    final double cardWidth = dimensions.width;
    final double cardHeight = dimensions.height;
    final double margin = dimensions.margin;

    // 计算缩放比例 - 基于原始设计尺寸300x400
    final double scaleFactor = dimensions.scaleFactor;

    final size = Size(cardWidth + margin * 2, cardHeight + margin * 2);

    // 绘制外部背景（与预览底色一致）
    final bgPaint = Paint()..color = const Color(0xFFF8FAFC);
    canvas.drawRect(Rect.fromLTWH(0, 0, size.width, size.height), bgPaint);

    // 计算卡片区域
    final cardRect = Rect.fromLTWH(margin, margin, cardWidth, cardHeight);

    // 先绘制阴影，再绘制卡片背景（避免阴影覆盖卡片导致颜色偏暗）
    final shadowPaint =
        Paint()
          ..color = Colors.black.withValues(alpha: 0.15)
          ..maskFilter = MaskFilter.blur(BlurStyle.normal, 30 * scaleFactor);
    canvas.drawRRect(
      RRect.fromRectAndRadius(
        cardRect.translate(0, 15 * scaleFactor), // 增加阴影偏移
        Radius.circular(template.borderRadius * scaleFactor),
      ),
      shadowPaint,
    );

    // 绘制卡片背景渐变
    final gradientPaint =
        Paint()..shader = template.backgroundGradient.createShader(cardRect);
    canvas.drawRRect(
      RRect.fromRectAndRadius(
        cardRect,
        Radius.circular(template.borderRadius * scaleFactor),
      ),
      gradientPaint,
    );

    // 装饰元素（与预览一致）
    if (template.layoutStyle == 'classic_style') {
      // 右上角圆形点缀
      final double decoSize = 60 * scaleFactor;
      final Offset topRight = Offset(
        cardRect.right - 20 * scaleFactor - decoSize / 2,
        cardRect.top + 20 * scaleFactor + decoSize / 2,
      );
      final Paint decoPaint =
          Paint()..color = template.accentColor.withValues(alpha: 0.1);
      canvas.drawCircle(topRight, decoSize / 2, decoPaint);

      // 左下角装饰线条
      final double lineWidth = 40 * scaleFactor;
      final double lineHeight = 3 * scaleFactor;
      final Rect lineRect = Rect.fromLTWH(
        cardRect.left + 20 * scaleFactor,
        cardRect.bottom - 30 * scaleFactor,
        lineWidth,
        lineHeight,
      );
      final RRect lineRRect = RRect.fromRectAndRadius(
        lineRect,
        Radius.circular(2 * scaleFactor),
      );
      final Paint linePaint = Paint()..color = template.accentColor;
      canvas.drawRRect(lineRRect, linePaint);
    } else if (template.layoutStyle == 'gradient_style') {
      // 右上角柔光渐变
      final double haloSize = 120 * scaleFactor;
      final Rect haloRect = Rect.fromCircle(
        center: Offset(
          cardRect.right - 30 * scaleFactor,
          cardRect.top - 30 * scaleFactor,
        ),
        radius: haloSize / 2,
      );
      final Paint haloPaint =
          Paint()
            ..shader = ui.Gradient.radial(haloRect.center, haloSize / 2, [
              Colors.white.withValues(alpha: 0.2),
              Colors.transparent,
            ]);
      canvas.drawCircle(haloRect.center, haloSize / 2, haloPaint);
    } else if (template.layoutStyle == 'quote') {
      // 左上/右下引号（简化为半透明方块点缀，避免字体依赖）
      final Paint quotePaint =
          Paint()..color = template.accentColor.withValues(alpha: 0.08);
      canvas.drawRRect(
        RRect.fromRectAndRadius(
          Rect.fromLTWH(
            cardRect.left + 16 * scaleFactor,
            cardRect.top + 16 * scaleFactor,
            24 * scaleFactor,
            24 * scaleFactor,
          ),
          Radius.circular(6 * scaleFactor),
        ),
        quotePaint,
      );
      canvas.drawRRect(
        RRect.fromRectAndRadius(
          Rect.fromLTWH(
            cardRect.right - 40 * scaleFactor,
            cardRect.bottom - 40 * scaleFactor,
            24 * scaleFactor,
            24 * scaleFactor,
          ),
          Radius.circular(6 * scaleFactor),
        ),
        quotePaint,
      );
    }

    // 阴影已在背景之前绘制

    // 绘制内容 - 不分离标题，将所有内容作为正文显示
    final contentPainter = TextPainter(
      text: TextSpan(
        text: content,
        style: TextStyle(
          color: template.textColor,
          fontSize: template.contentFontSize * scaleFactor,
          fontWeight: template.contentFontWeight,
          height: template.lineHeight,
        ),
      ),
      textDirection: TextDirection.ltr,
      textAlign: template.textAlign,
    );

    // 计算可用的文本区域
    final double availableWidth =
        cardWidth - template.padding * scaleFactor * 2;
    final double availableHeight =
        cardHeight - template.padding * scaleFactor * 2;

    // 为水印预留空间
    final double watermarkSpace =
        config.includeWatermark ? 80 * scaleFactor : 0;
    final double textAreaHeight = availableHeight - watermarkSpace;

    contentPainter.layout(maxWidth: availableWidth);

    // 检查文本是否超出可用高度，如果超出则调整字体大小
    if (contentPainter.height > textAreaHeight) {
      // 计算需要缩小的比例
      final double heightRatio = textAreaHeight / contentPainter.height;
      final double adjustedFontSize =
          template.contentFontSize * scaleFactor * heightRatio * 0.95; // 留一点余量

      final adjustedContentPainter = TextPainter(
        text: TextSpan(
          text: content,
          style: TextStyle(
            color: template.textColor,
            fontSize: adjustedFontSize,
            fontWeight: template.contentFontWeight,
            height: template.lineHeight,
          ),
        ),
        textDirection: TextDirection.ltr,
        textAlign: template.textAlign,
      );

      adjustedContentPainter.layout(maxWidth: availableWidth);

      // 垂直居中绘制
      final double contentY =
          margin +
          template.padding * scaleFactor +
          (textAreaHeight - adjustedContentPainter.height) / 2;

      adjustedContentPainter.paint(
        canvas,
        Offset(margin + template.padding * scaleFactor, contentY),
      );
    } else {
      // 文本适合，垂直居中绘制
      final double contentY =
          margin +
          template.padding * scaleFactor +
          (textAreaHeight - contentPainter.height) / 2;

      contentPainter.paint(
        canvas,
        Offset(margin + template.padding * scaleFactor, contentY),
      );
    }

    // 绘制水印（如果配置中启用）
    if (config.includeWatermark) {
      final watermarkPainter = TextPainter(
        text: TextSpan(
          text: 'ContentPal',
          style: TextStyle(
            color: template.textColor.withValues(alpha: 0.3),
            fontSize: 32 * scaleFactor, // 应用缩放因子到水印字体大小
            fontWeight: FontWeight.w300,
          ),
        ),
        textDirection: TextDirection.ltr,
      );

      watermarkPainter.layout();
      watermarkPainter.paint(
        canvas,
        Offset(
          margin +
              cardWidth -
              watermarkPainter.width -
              48 * scaleFactor, // 应用缩放因子到边距
          margin +
              cardHeight -
              watermarkPainter.height -
              48 * scaleFactor, // 应用缩放因子到边距
        ),
      );
    }

    // 转换为图片
    final picture = recorder.endRecording();
    final image = await picture.toImage(
      size.width.toInt(),
      size.height.toInt(),
    );
    final byteData = await image.toByteData(format: ui.ImageByteFormat.png);

    return byteData!.buffer.asUint8List();
  }
}

/// 简单的卡片包装器，避免Material组件依赖
class SimpleCardWrapper extends StatelessWidget {
  final Widget child;

  const SimpleCardWrapper({super.key, required this.child});

  @override
  Widget build(BuildContext context) {
    return Directionality(
      textDirection: TextDirection.ltr,
      child: Container(
        width: 340,
        height: 440,
        color: Colors.white,
        child: Center(child: child),
      ),
    );
  }
}
