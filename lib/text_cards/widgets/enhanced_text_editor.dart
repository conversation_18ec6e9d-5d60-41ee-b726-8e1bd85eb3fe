import 'package:flutter/material.dart';

import '../models/font_manager.dart';

/// 增强的文本编辑器
/// 支持选中文本样式修改、实时预览等功能
class EnhancedTextEditor extends StatefulWidget {
  final String initialText;
  final Function(String) onTextChanged;
  final Function(String, TextStyle)? onStyleChanged;
  final TextStyle? baseStyle;

  const EnhancedTextEditor({
    super.key,
    required this.initialText,
    required this.onTextChanged,
    this.onStyleChanged,
    this.baseStyle,
  });

  @override
  State<EnhancedTextEditor> createState() => _EnhancedTextEditorState();
}

class _EnhancedTextEditorState extends State<EnhancedTextEditor>
    with SingleTickerProviderStateMixin {
  late TextEditingController _controller;
  late FocusNode _focusNode;
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _opacityAnimation;

  // 选中文本相关
  TextSelection? _currentSelection;
  OverlayEntry? _overlayEntry;

  // 样式状态
  bool _isBold = false;
  bool _isItalic = false;
  bool _isUnderline = false;
  Color _textColor = Colors.black;
  double _fontSize = 16.0;
  FontFamily _selectedFont = FontManager.getDefaultFont();

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.initialText);
    _focusNode = FocusNode();

    // 初始化动画控制器
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(begin: 0.8, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOutBack),
    );

    _opacityAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeOut),
    );

    _controller.addListener(_onTextChanged);
    _focusNode.addListener(_onFocusChanged);

    // 监听选择变化
    _controller.addListener(_onSelectionChanged);
  }

  @override
  void dispose() {
    _removeOverlay();
    _animationController.dispose();
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  void _onTextChanged() {
    widget.onTextChanged(_controller.text);
  }

  void _onSelectionChanged() {
    final selection = _controller.selection;
    if (selection != _currentSelection) {
      _currentSelection = selection;
      // 完全禁用自动显示工具栏，只有当选择取消时才隐藏工具栏
      if (!selection.isValid || selection.isCollapsed) {
        _hideStyleToolbar();
      }
    }
  }

  void _onFocusChanged() {
    if (!_focusNode.hasFocus) {
      _hideStyleToolbar();
    }
  }

  void _showStyleToolbarForSelection() {
    if (_currentSelection == null || _currentSelection!.isCollapsed) return;

    setState(() {});

    _removeOverlay();
    _overlayEntry = _createOverlayEntry();
    Overlay.of(context).insert(_overlayEntry!);

    // 启动显示动画
    _animationController.forward();
  }

  void _hideStyleToolbar() {
    setState(() {});

    // 启动隐藏动画，然后移除overlay
    _animationController.reverse().then((_) {
      _removeOverlay();
    });
  }

  void _removeOverlay() {
    _overlayEntry?.remove();
    _overlayEntry = null;
  }

  OverlayEntry _createOverlayEntry() {
    return OverlayEntry(
      builder:
          (context) => Stack(
            children: [
              // 半透明背景遮罩，点击可关闭工具栏
              Positioned.fill(
                child: GestureDetector(
                  onTap: _hideStyleToolbar,
                  child: Container(color: Colors.black.withValues(alpha: 0.3)),
                ),
              ),
              // 样式工具栏
              _buildPositionedToolbar(),
            ],
          ),
    );
  }

  Widget _buildPositionedToolbar() {
    // 获取屏幕尺寸
    final screenSize = MediaQuery.of(context).size;
    final toolbarWidth = 400.0;
    final toolbarHeight = 120.0;

    // 计算工具栏位置，确保不超出屏幕边界
    double left = (screenSize.width - toolbarWidth) / 2;
    double top = 150.0; // 默认位置

    // 确保不超出屏幕边界
    left = left.clamp(20.0, screenSize.width - toolbarWidth - 20.0);
    top = top.clamp(100.0, screenSize.height - toolbarHeight - 100.0);

    return Positioned(
      left: left,
      top: top,
      child: AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: Opacity(
              opacity: _opacityAnimation.value,
              child: Material(
                elevation: 8,
                borderRadius: BorderRadius.circular(12),
                child: _buildStyleToolbar(),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildStyleToolbar() {
    return Material(
      color: Theme.of(context).colorScheme.surface,
      borderRadius: BorderRadius.circular(12),
      elevation: 4,
      child: Container(
        constraints: const BoxConstraints(maxWidth: 400),
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 第一行：基本样式按钮
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  _buildStyleButton(
                    icon: Icons.format_bold,
                    isActive: _isBold,
                    onTap: _toggleBold,
                    tooltip: '加粗',
                  ),
                  const SizedBox(width: 8),
                  _buildStyleButton(
                    icon: Icons.format_italic,
                    isActive: _isItalic,
                    onTap: _toggleItalic,
                    tooltip: '斜体',
                  ),
                  const SizedBox(width: 8),
                  _buildStyleButton(
                    icon: Icons.format_underlined,
                    isActive: _isUnderline,
                    onTap: _toggleUnderline,
                    tooltip: '下划线',
                  ),
                  const SizedBox(width: 12),
                  _buildColorPicker(),
                  const SizedBox(width: 12),
                  _buildStyleButton(
                    icon: Icons.close,
                    isActive: false,
                    onTap: _hideStyleToolbar,
                    tooltip: '关闭',
                  ),
                ],
              ),
              const SizedBox(height: 12),
              // 第二行：字体大小和字体选择
              Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Text('字体大小:', style: TextStyle(fontSize: 12)),
                  const SizedBox(width: 8),
                  _buildFontSizeSlider(),
                  const SizedBox(width: 16),
                  const Text('字体:', style: TextStyle(fontSize: 12)),
                  const SizedBox(width: 8),
                  _buildFontSelector(),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStyleButton({
    required IconData icon,
    required bool isActive,
    required VoidCallback onTap,
    required String tooltip,
  }) {
    return Tooltip(
      message: tooltip,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(6),
        child: Container(
          width: 32,
          height: 32,
          decoration: BoxDecoration(
            color:
                isActive
                    ? Theme.of(context).colorScheme.primary
                    : Colors.transparent,
            borderRadius: BorderRadius.circular(6),
          ),
          child: Icon(
            icon,
            size: 18,
            color:
                isActive
                    ? Colors.white
                    : Theme.of(context).colorScheme.onSurface,
          ),
        ),
      ),
    );
  }

  Widget _buildColorPicker() {
    return PopupMenuButton<Color>(
      child: Container(
        width: 32,
        height: 32,
        decoration: BoxDecoration(
          color: _textColor,
          borderRadius: BorderRadius.circular(6),
          border: Border.all(
            color: Theme.of(context).colorScheme.outlineVariant,
          ),
        ),
      ),
      itemBuilder:
          (context) =>
              [
                    Colors.black,
                    Colors.red,
                    Colors.blue,
                    Colors.green,
                    Colors.orange,
                    Colors.purple,
                  ]
                  .map(
                    (color) => PopupMenuItem<Color>(
                      value: color,
                      child: Container(
                        width: 24,
                        height: 24,
                        decoration: BoxDecoration(
                          color: color,
                          shape: BoxShape.circle,
                        ),
                      ),
                    ),
                  )
                  .toList(),
      onSelected: (color) {
        setState(() {
          _textColor = color;
        });
        _applyStyleToSelection();
        // 不关闭样式工具栏，让用户可以继续调整其他样式
      },
    );
  }

  Widget _buildFontSelector() {
    return Container(
      constraints: const BoxConstraints(maxWidth: 120),
      child: PopupMenuButton<FontFamily>(
        child: Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            border: Border.all(
              color: Theme.of(context).colorScheme.outlineVariant,
            ),
            borderRadius: BorderRadius.circular(6),
          ),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Flexible(
                child: Text(
                  _selectedFont.displayName,
                  style: const TextStyle(fontSize: 12),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              const SizedBox(width: 4),
              const Icon(Icons.arrow_drop_down, size: 16),
            ],
          ),
        ),
        itemBuilder:
            (context) =>
                FontManager.availableFonts
                    .map(
                      (font) => PopupMenuItem<FontFamily>(
                        value: font,
                        child: Text(
                          font.displayName,
                          style: TextStyle(
                            fontFamily: font.fontFamily,
                            fontSize: 12,
                          ),
                        ),
                      ),
                    )
                    .toList(),
        onSelected: (font) {
          setState(() {
            _selectedFont = font;
          });
          _applyStyleToSelection();
          // 不关闭样式工具栏，让用户可以继续调整其他样式
        },
      ),
    );
  }

  Widget _buildFontSizeSlider() {
    return SizedBox(
      width: 100,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                '${_fontSize.round()}',
                style: const TextStyle(fontSize: 10),
              ),
              Expanded(
                child: Slider(
                  value: _fontSize,
                  min: 12,
                  max: 32,
                  divisions: 20,
                  activeColor: Theme.of(context).colorScheme.primary,
                  onChanged: (value) {
                    setState(() {
                      _fontSize = value;
                    });
                    _applyStyleToSelection();
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  void _toggleBold() {
    setState(() {
      _isBold = !_isBold;
    });
    _applyStyleToSelection();
  }

  void _toggleItalic() {
    setState(() {
      _isItalic = !_isItalic;
    });
    _applyStyleToSelection();
  }

  void _toggleUnderline() {
    setState(() {
      _isUnderline = !_isUnderline;
    });
    _applyStyleToSelection();
  }

  void _applyStyleToSelection() {
    if (_currentSelection == null || widget.onStyleChanged == null) return;

    final selectedText = _controller.text.substring(
      _currentSelection!.start,
      _currentSelection!.end,
    );

    final style = TextStyle(
      fontFamily: _selectedFont.fontFamily,
      fontWeight: _isBold ? FontWeight.bold : FontWeight.normal,
      fontStyle: _isItalic ? FontStyle.italic : FontStyle.normal,
      decoration: _isUnderline ? TextDecoration.underline : TextDecoration.none,
      color: _textColor,
      fontSize: _fontSize,
    );

    widget.onStyleChanged!(selectedText, style);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Theme.of(context).colorScheme.outlineVariant),
      ),
      child: TextField(
        controller: _controller,
        focusNode: _focusNode,
        maxLines: null,
        style:
            widget.baseStyle ??
            TextStyle(
              fontSize: 16,
              color: Theme.of(context).colorScheme.onSurface,
            ),
        decoration: InputDecoration(
          hintText: '在这里输入文本内容...',
          border: InputBorder.none,
          contentPadding: const EdgeInsets.all(16),
          hintStyle: TextStyle(
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
        ),
        contextMenuBuilder: _buildCustomContextMenu,
      ),
    );
  }

  /// 构建自定义上下文菜单
  Widget _buildCustomContextMenu(
    BuildContext context,
    EditableTextState editableTextState,
  ) {
    final List<ContextMenuButtonItem> buttonItems =
        editableTextState.contextMenuButtonItems;

    // 如果有选中文本，添加"修改样式"选项
    if (_controller.selection.isValid && !_controller.selection.isCollapsed) {
      buttonItems.add(
        ContextMenuButtonItem(
          label: '修改样式',
          onPressed: () {
            // 保存当前选择，因为关闭菜单可能会改变选择
            final currentSelection = _controller.selection;

            // 先关闭上下文菜单
            ContextMenuController.removeAny();

            // 延迟显示样式工具栏，确保上下文菜单完全关闭
            Future.delayed(const Duration(milliseconds: 200), () {
              // 恢复选择状态
              if (currentSelection.isValid && !currentSelection.isCollapsed) {
                _currentSelection = currentSelection;
                _controller.selection = currentSelection;
                _showStyleToolbarForSelection();
              }
            });
          },
        ),
      );
    }

    return AdaptiveTextSelectionToolbar.buttonItems(
      anchors: editableTextState.contextMenuAnchors,
      buttonItems: buttonItems,
    );
  }
}
