import 'package:flutter/material.dart';
import '../models/split_config.dart';

/// 拆分预览卡片组件
class SplitPreviewCard extends StatelessWidget {
  final SplitResultItem item;
  final int index;
  final bool isSelected;
  final VoidCallback? onTap;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;
  final VoidCallback? onMerge;

  const SplitPreviewCard({
    super.key,
    required this.item,
    required this.index,
    this.isSelected = false,
    this.onTap,
    this.onEdit,
    this.onDelete,
    this.onMerge,
  });

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final isDark = Theme.of(context).brightness == Brightness.dark;
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: colorScheme.surfaceContainerLow,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: isSelected ? colorScheme.primary : colorScheme.outlineVariant,
          width: isSelected ? 2 : 1,
        ),
        boxShadow: [
          BoxShadow(
            color:
                isDark
                    ? Colors.black.withValues(alpha: 0.25)
                    : Colors.black.withValues(alpha: 0.05),
            offset: const Offset(0, 2),
            blurRadius: 8,
            spreadRadius: 0,
          ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(16),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // 头部信息
                Row(
                  children: [
                    // 类型指示器
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: _getTypeColor().withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            _getTypeIcon(),
                            size: 12,
                            color: _getTypeColor(),
                          ),
                          const SizedBox(width: 4),
                          Text(
                            item.type.displayName,
                            style: TextStyle(
                              fontSize: 10,
                              fontWeight: FontWeight.w500,
                              color: _getTypeColor(),
                            ),
                          ),
                        ],
                      ),
                    ),

                    const SizedBox(width: 8),

                    // 索引
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 6,
                        vertical: 2,
                      ),
                      decoration: BoxDecoration(
                        color: colorScheme.surfaceContainerHigh,
                        borderRadius: BorderRadius.circular(6),
                        border: Border.all(color: colorScheme.outlineVariant),
                      ),
                      child: Text(
                        '#${index + 1}',
                        style: TextStyle(
                          fontSize: 10,
                          fontWeight: FontWeight.w500,
                          color: colorScheme.onSurfaceVariant,
                        ),
                      ),
                    ),

                    const Spacer(),

                    // 操作按钮
                    _buildActionButtons(colorScheme),
                  ],
                ),

                const SizedBox(height: 12),

                // 仅在标题项时显示标题
                if (_shouldShowTitle()) ...[
                  Text(
                    _getTitleText(),
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                      color: colorScheme.onSurface,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  const SizedBox(height: 8),
                ],

                // 内容预览（标题项不显示正文）
                if (item.type != SplitItemType.title)
                  Text(
                    _getPreviewContent(),
                    style: TextStyle(
                      fontSize: 12,
                      color: colorScheme.onSurfaceVariant,
                      height: 1.4,
                    ),
                    maxLines: 3,
                    overflow: TextOverflow.ellipsis,
                  ),

                const SizedBox(height: 8),

                // 统计信息
                Row(
                  children: [
                    Icon(
                      Icons.text_fields,
                      size: 12,
                      color: colorScheme.onSurfaceVariant,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      '${item.content.length} 字符',
                      style: TextStyle(
                        fontSize: 10,
                        color: colorScheme.onSurfaceVariant,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Icon(
                      Icons.format_list_numbered,
                      size: 12,
                      color: colorScheme.onSurfaceVariant,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      '${item.content.split('\n').length} 行',
                      style: TextStyle(
                        fontSize: 10,
                        color: colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 构建操作按钮
  Widget _buildActionButtons(ColorScheme colorScheme) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        if (onEdit != null)
          _buildActionButton(
            icon: Icons.edit_outlined,
            onTap: onEdit!,
            color: colorScheme.primary,
          ),
        if (onMerge != null)
          _buildActionButton(
            icon: Icons.merge_outlined,
            onTap: onMerge!,
            color: colorScheme.secondary,
          ),
        if (onDelete != null)
          _buildActionButton(
            icon: Icons.delete_outline,
            onTap: onDelete!,
            color: colorScheme.error,
          ),
      ],
    );
  }

  /// 构建单个操作按钮
  Widget _buildActionButton({
    required IconData icon,
    required VoidCallback onTap,
    required Color color,
  }) {
    return Container(
      margin: const EdgeInsets.only(left: 4),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(6),
          child: Container(
            padding: const EdgeInsets.all(4),
            child: Icon(icon, size: 14, color: color),
          ),
        ),
      ),
    );
  }

  /// 获取类型颜色
  Color _getTypeColor() {
    switch (item.type) {
      case SplitItemType.title:
        return const Color(0xFF6366F1);
      case SplitItemType.list:
        return const Color(0xFF10B981);
      case SplitItemType.quote:
        return const Color(0xFF8B5CF6);
      case SplitItemType.code:
        return const Color(0xFFEF4444);
      case SplitItemType.content:
        return const Color(0xFF64748B);
    }
  }

  /// 获取类型图标
  IconData _getTypeIcon() {
    switch (item.type) {
      case SplitItemType.title:
        return Icons.title;
      case SplitItemType.list:
        return Icons.list;
      case SplitItemType.quote:
        return Icons.format_quote;
      case SplitItemType.code:
        return Icons.code;
      case SplitItemType.content:
        return Icons.text_snippet;
    }
  }

  /// 获取预览内容
  String _getPreviewContent() {
    String content = item.content;

    // 如果有标题字段，认为第一行是标题，正文为其后部分
    if (item.title != null && item.title!.isNotEmpty) {
      final lines = content.split('\n');
      if (lines.length > 1) {
        content = lines.skip(1).join('\n').trim();
      }
    }

    return content.isEmpty ? '(空内容)' : content;
  }

  bool _shouldShowTitle() {
    // 仅当该项类型标注为标题 或 明确有标题字段但内容第一行不应再重复时显示
    if (item.type == SplitItemType.title) return true;
    return false;
  }

  String _getTitleText() {
    if (item.type == SplitItemType.title) {
      // 标题项：标题优先，其次从内容首行兜底
      if (item.title != null && item.title!.trim().isNotEmpty) {
        return item.title!;
      }
      final lines = item.content.split('\n');
      return lines.isNotEmpty ? lines.first : '';
    }
    return '';
  }
}
