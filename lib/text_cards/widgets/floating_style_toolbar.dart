// 浮动样式工具栏 - 类似阅读应用的文本选择工具栏
import 'package:flutter/material.dart';
import '../../generated/l10n/app_localizations.dart';
import '../models/content_models.dart';

class FloatingStyleToolbar extends StatefulWidget {
  final String selectedText;
  final Function(TextStyleConfig newStyle) onStyleChanged;
  final VoidCallback onClose;

  const FloatingStyleToolbar({
    super.key,
    required this.selectedText,
    required this.onStyleChanged,
    required this.onClose,
  });

  @override
  State<FloatingStyleToolbar> createState() => _FloatingStyleToolbarState();
}

class _FloatingStyleToolbarState extends State<FloatingStyleToolbar>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _opacityAnimation;

  // 当前样式状态
  TextStyleConfig _currentStyle = const TextStyleConfig();

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    _scaleAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.elasticOut),
    );

    _opacityAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(_animationController);

    _animationController.forward();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  void _updateStyle(TextStyleConfig Function(TextStyleConfig) update) {
    setState(() {
      _currentStyle = update(_currentStyle);
    });
    widget.onStyleChanged(_currentStyle);
  }

  @override
  Widget build(BuildContext context) {
    if (widget.selectedText.isEmpty) {
      return const SizedBox.shrink();
    }

    return Material(
      color: Colors.transparent,
      child: AnimatedBuilder(
        animation: _animationController,
        builder: (context, child) {
          return Transform.scale(
            scale: _scaleAnimation.value,
            child: Opacity(
              opacity: _opacityAnimation.value,
              child: _buildToolbar(),
            ),
          );
        },
      ),
    );
  }

  Widget _buildToolbar() {
    final colorScheme = Theme.of(context).colorScheme;
    return Container(
      height: 50,
      decoration: BoxDecoration(
        color: colorScheme.surfaceContainerHigh,
        borderRadius: BorderRadius.circular(25),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.3),
            offset: const Offset(0, 4),
            blurRadius: 10,
            spreadRadius: 1,
          ),
        ],
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          // 字体大小按钮
          _buildToolbarButton(
            icon: Icons.text_decrease,
            isActive: false,
            onTap:
                () => _updateStyle(
                  (style) => style.copyWith(
                    fontSize: (style.fontSize - 2).clamp(12, 32),
                  ),
                ),
          ),
          _buildToolbarButton(
            icon: Icons.text_increase,
            isActive: false,
            onTap:
                () => _updateStyle(
                  (style) => style.copyWith(
                    fontSize: (style.fontSize + 2).clamp(12, 32),
                  ),
                ),
          ),

          _buildDivider(),

          // 加粗按钮
          _buildToolbarButton(
            icon: Icons.format_bold,
            isActive: _currentStyle.fontWeight == FontWeight.bold,
            onTap:
                () => _updateStyle(
                  (style) => style.copyWith(
                    fontWeight:
                        style.fontWeight == FontWeight.bold
                            ? FontWeight.normal
                            : FontWeight.bold,
                  ),
                ),
          ),

          // 斜体按钮
          _buildToolbarButton(
            icon: Icons.format_italic,
            isActive: _currentStyle.italic,
            onTap:
                () => _updateStyle(
                  (style) => style.copyWith(italic: !style.italic),
                ),
          ),

          // 下划线按钮
          _buildToolbarButton(
            icon: Icons.format_underlined,
            isActive: _currentStyle.underline,
            onTap:
                () => _updateStyle(
                  (style) => style.copyWith(underline: !style.underline),
                ),
          ),

          _buildDivider(),

          // 颜色按钮
          _buildColorButton(),

          _buildDivider(),

          // 关闭按钮
          _buildToolbarButton(
            icon: Icons.close,
            isActive: false,
            onTap: widget.onClose,
          ),
        ],
      ),
    );
  }

  Widget _buildToolbarButton({
    required IconData icon,
    required bool isActive,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        width: 40,
        height: 40,
        margin: const EdgeInsets.symmetric(horizontal: 2),
        decoration: BoxDecoration(
          color:
              isActive
                  ? Theme.of(context).colorScheme.primary
                  : Colors.transparent,
          borderRadius: BorderRadius.circular(20),
        ),
        child: Icon(
          icon,
          color: Theme.of(context).colorScheme.onPrimary,
          size: 18,
        ),
      ),
    );
  }

  Widget _buildDivider() {
    return Container(
      width: 1,
      height: 20,
      margin: const EdgeInsets.symmetric(horizontal: 4),
      color: Theme.of(context).colorScheme.onSurface.withValues(alpha: 0.3),
    );
  }

  Widget _buildColorButton() {
    return GestureDetector(
      onTap: _showColorPicker,
      child: Container(
        width: 40,
        height: 40,
        margin: const EdgeInsets.symmetric(horizontal: 2),
        decoration: BoxDecoration(
          color: Colors.transparent,
          borderRadius: BorderRadius.circular(20),
        ),
        child: Stack(
          alignment: Alignment.center,
          children: [
            const Icon(Icons.format_color_text, color: Colors.white, size: 18),
            Positioned(
              bottom: 8,
              child: Container(
                width: 16,
                height: 3,
                decoration: BoxDecoration(
                  color: _currentStyle.color,
                  borderRadius: BorderRadius.circular(1.5),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showColorPicker() {
    final l10n = AppLocalizations.of(context);
    final colors = [
      Theme.of(context).colorScheme.onSurface,
      Colors.grey[700]!,
      Theme.of(context).colorScheme.primary,
      Theme.of(context).colorScheme.secondary,
      Colors.orange,
      Colors.red,
      Colors.purple,
      Colors.brown,
    ];

    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(l10n.textCardSelectColor),
            content: Wrap(
              spacing: 8,
              runSpacing: 8,
              children:
                  colors.map((color) {
                    final isSelected = _currentStyle.color == color;
                    return GestureDetector(
                      onTap: () {
                        Navigator.of(context).pop();
                        _updateStyle((style) => style.copyWith(color: color));
                      },
                      child: Container(
                        width: 40,
                        height: 40,
                        decoration: BoxDecoration(
                          color: color,
                          shape: BoxShape.circle,
                          border:
                              isSelected
                                  ? Border.all(color: Colors.blue, width: 3)
                                  : null,
                        ),
                        child:
                            isSelected
                                ? const Icon(
                                  Icons.check,
                                  color: Colors.white,
                                  size: 20,
                                )
                                : null,
                      ),
                    );
                  }).toList(),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: Text(l10n.cancel),
              ),
            ],
          ),
    );
  }
}
