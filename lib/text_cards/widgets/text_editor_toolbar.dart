import 'package:flutter/material.dart';

class TextEditorT<PERSON><PERSON> extends StatelessWidget {
  final VoidCallback? onBold;
  final VoidCallback? onItalic;
  final VoidCallback? onUnderline;
  final VoidCallback? onBulletList;
  final VoidCallback? onNumberList;
  final VoidCallback? onTable;
  final VoidCallback? onTemplate;

  const TextEditorToolbar({
    super.key,
    this.onBold,
    this.onItalic,
    this.onUnderline,
    this.onBulletList,
    this.onNumberList,
    this.onTable,
    this.onTemplate,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        _ToolbarButton(
          icon: Icons.format_bold,
          onPressed: onBold,
          tooltip: '加粗',
        ),
        _ToolbarButton(
          icon: Icons.format_italic,
          onPressed: onItalic,
          tooltip: '斜体',
        ),
        _ToolbarButton(
          icon: Icons.format_underlined,
          onPressed: onUnderline,
          tooltip: '下划线',
        ),
        const SizedBox(width: 8),
        Container(
          width: 1,
          height: 24,
          color: Theme.of(
            context,
          ).colorScheme.onSurfaceVariant.withValues(alpha: 0.3),
        ),
        const SizedBox(width: 8),
        _ToolbarButton(
          icon: Icons.format_list_bulleted,
          onPressed: onBulletList,
          tooltip: '无序列表',
        ),
        _ToolbarButton(
          icon: Icons.format_list_numbered,
          onPressed: onNumberList,
          tooltip: '有序列表',
        ),
        _ToolbarButton(
          icon: Icons.table_chart,
          onPressed: onTable,
          tooltip: '插入表格',
        ),
        const SizedBox(width: 8),
        Container(
          width: 1,
          height: 24,
          color: Theme.of(
            context,
          ).colorScheme.onSurfaceVariant.withValues(alpha: 0.3),
        ),
        const SizedBox(width: 8),
        _ToolbarButton(
          icon: Icons.palette,
          onPressed: onTemplate,
          tooltip: '更换模板',
        ),
      ],
    );
  }
}

class _ToolbarButton extends StatelessWidget {
  final IconData icon;
  final VoidCallback? onPressed;
  final String tooltip;

  const _ToolbarButton({
    required this.icon,
    this.onPressed,
    required this.tooltip,
  });

  @override
  Widget build(BuildContext context) {
    return Tooltip(
      message: tooltip,
      child: InkWell(
        onTap: onPressed,
        borderRadius: BorderRadius.circular(6),
        child: Container(
          width: 36,
          height: 36,
          decoration: BoxDecoration(borderRadius: BorderRadius.circular(6)),
          child: Icon(
            icon,
            size: 18,
            color:
                onPressed != null
                    ? Theme.of(context).colorScheme.onSurface
                    : Theme.of(context).colorScheme.onSurfaceVariant,
          ),
        ),
      ),
    );
  }
}
