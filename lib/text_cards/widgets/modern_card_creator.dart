import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../generated/l10n/app_localizations.dart';
import '../../services/service_locator.dart';
import '../models/enhanced_card_template.dart';
import '../models/export_config.dart';
import '../utils/card_export_helper.dart';
import 'advanced_text_editor.dart';
import 'card_preview_widget.dart';
import 'export_options_dialog.dart';
import 'dart:convert';
import '../../content/save_content_page.dart';
import '../../models/content_item.dart';

/// 现代风格的卡片创建器
/// 提供世界级的用户体验和交互设计
class ModernCardCreator extends StatefulWidget {
  final Function(String content, EnhancedCardTemplate template) onCardCreated;
  final EnhancedCardTemplate? initialTemplate;
  final String? initialText;
  final Map<String, dynamic>? initialCustomStyles;
  final int? initialStep; // 0: 编辑, 1: 模板, 2: 预览

  const ModernCardCreator({
    super.key,
    required this.onCardCreated,
    this.initialTemplate,
    this.initialText,
    this.initialCustomStyles,
    this.initialStep,
  });

  @override
  State<ModernCardCreator> createState() => _ModernCardCreatorState();
}

class _ModernCardCreatorState extends State<ModernCardCreator>
    with TickerProviderStateMixin {
  late AnimationController _slideController;
  late AnimationController _fadeController;
  late Animation<Offset> _slideAnimation;
  late Animation<double> _fadeAnimation;

  final TextEditingController _contentController = TextEditingController();
  late EnhancedCardTemplate _selectedTemplate;
  Map<String, dynamic> _customStyles = {};
  final ScrollController _templateScrollController = ScrollController();

  int _currentStep = 0; // 0: 编辑, 1: 模板, 2: 预览

  @override
  void initState() {
    super.initState();
    _initializeTemplate();
    _initializeAnimations();
    _setupDefaultContent();

    // 初始化自定义样式
    _customStyles = widget.initialCustomStyles ?? {};

    // 初始步骤优先级：指定的 initialStep > 有初始模板时默认模板页 > 默认编辑页
    if (widget.initialStep != null) {
      final s = widget.initialStep!;
      _currentStep = s < 0 ? 0 : (s > 2 ? 2 : s);
    } else if (widget.initialTemplate != null) {
      _currentStep = 1;
    }
  }

  void _initializeTemplate() {
    _selectedTemplate =
        widget.initialTemplate ??
        EnhancedCardTemplate.getModernTemplates().firstWhere(
          (t) => t.id == 'classic_style',
        );
  }

  void _initializeAnimations() {
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 1),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(parent: _slideController, curve: Curves.easeOutCubic),
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut),
    );

    _slideController.forward();
    _fadeController.forward();
  }

  void _setupDefaultContent() {
    // 如果有初始文本，使用初始文本，否则根据设置决定是否使用默认内容
    if (widget.initialText != null) {
      _contentController.text = widget.initialText!;
    } else {
      // 检查设置是否启用默认初始文本（文本卡片模块）
      final settingsService = ServiceLocator().settingsService;
      if (settingsService.settings.useDefaultInitialTextTextCards) {
        _contentController.text = '''今日分享

在这个快节奏的时代，我们总是忙于追赶时间，却忘记了停下来感受生活的美好。

有时候，最简单的快乐就藏在日常的小事里：
• 清晨的第一缕阳光
• 咖啡的香气
• 朋友的一个微笑

让我们学会在平凡中发现不平凡，在忙碌中找到内心的宁静。

#生活感悟 #正能量 #日常分享''';
      } else {
        _contentController.text = '';
      }
    }
  }

  @override
  void dispose() {
    _slideController.dispose();
    _fadeController.dispose();
    _contentController.dispose();
    _templateScrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return SlideTransition(
      position: _slideAnimation,
      child: Container(
        height: MediaQuery.of(context).size.height * 0.95,
        decoration: BoxDecoration(
          color: theme.colorScheme.surface,
          borderRadius: const BorderRadius.vertical(top: Radius.circular(24)),
        ),
        child: Column(
          children: [
            _buildHeader(),
            _buildTabBar(),
            Expanded(
              child: FadeTransition(
                opacity: _fadeAnimation,
                child: _buildCurrentView(),
              ),
            ),
            _buildBottomActions(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    final l10n = AppLocalizations.of(context);
    final theme = Theme.of(context);
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        children: [
          // 拖拽指示器
          Container(
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: theme.colorScheme.outlineVariant,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(height: 16),

          // 标题和关闭按钮
          Row(
            children: [
              Expanded(
                child: Text(
                  l10n.textCardCreateBeautifulCard,
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.w700,
                    color: theme.colorScheme.onSurface,
                  ),
                ),
              ),
              IconButton(
                onPressed: () => Navigator.pop(context),
                icon: Icon(
                  Icons.close,
                  color: theme.colorScheme.onSurfaceVariant,
                ),
                style: IconButton.styleFrom(
                  backgroundColor: theme.colorScheme.surfaceContainerHigh,
                  minimumSize: const Size(40, 40),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildTabBar() {
    final l10n = AppLocalizations.of(context);
    final theme = Theme.of(context);
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20),
      padding: const EdgeInsets.all(4),
      decoration: BoxDecoration(
        color: theme.colorScheme.surfaceContainerHigh,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          _buildTabItem(0, l10n.edit, Icons.edit_outlined),
          _buildTabItem(
            1,
            l10n.textCardsTemplateLibrary,
            Icons.palette_outlined,
          ),
          _buildTabItem(2, l10n.preview, Icons.visibility_outlined),
        ],
      ),
    );
  }

  Widget _buildTabItem(int index, String title, IconData icon) {
    final isActive = _currentStep == index;
    final theme = Theme.of(context);

    return Expanded(
      child: GestureDetector(
        onTap: () => _switchToStep(index),
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 200),
          padding: const EdgeInsets.symmetric(vertical: 12),
          decoration: BoxDecoration(
            color: isActive ? theme.colorScheme.surface : Colors.transparent,
            borderRadius: BorderRadius.circular(8),
            boxShadow:
                isActive && theme.brightness != Brightness.dark
                    ? [
                      BoxShadow(
                        color: Colors.black.withValues(alpha: 0.08),
                        offset: const Offset(0, 2),
                        blurRadius: 4,
                      ),
                    ]
                    : null,
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                size: 18,
                color:
                    isActive
                        ? theme.colorScheme.primary
                        : theme.colorScheme.onSurfaceVariant,
              ),
              const SizedBox(width: 8),
              Text(
                title,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: isActive ? FontWeight.w600 : FontWeight.w400,
                  color:
                      isActive
                          ? theme.colorScheme.primary
                          : theme.colorScheme.onSurfaceVariant,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildCurrentView() {
    switch (_currentStep) {
      case 0:
        return _buildEditView();
      case 1:
        return _buildTemplateView();
      case 2:
        return _buildPreviewView();
      default:
        return _buildEditView();
    }
  }

  Widget _buildEditView() {
    final l10n = AppLocalizations.of(context);
    final theme = Theme.of(context);
    return Padding(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            l10n.textCardsContentEditor,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: theme.colorScheme.onSurface,
            ),
          ),
          const SizedBox(height: 16),

          // 编辑器容器，适配深色模式背景/边框
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                color: theme.colorScheme.surfaceContainerLow,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: theme.colorScheme.outlineVariant),
              ),
              child: AdvancedTextEditor(
                initialText: _contentController.text,
                template: _selectedTemplate,
                onTextChanged: (text) {
                  _contentController.text = text;
                },
                onStyleChanged: (styles) {
                  setState(() {
                    _customStyles = styles;
                  });
                },
              ),
            ),
          ),

          const SizedBox(height: 16),
        ],
      ),
    );
  }

  Widget _buildTemplateView() {
    final l10n = AppLocalizations.of(context);
    final templates = EnhancedCardTemplate.getModernTemplates();

    // 如果有初始模板，延迟滚动到对应位置
    if (widget.initialTemplate != null) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        final selectedIndex = templates.indexWhere(
          (t) => t.id == _selectedTemplate.id,
        );
        if (selectedIndex != -1 && _templateScrollController.hasClients) {
          // 计算滚动位置（每行2个，所以行数是 index / 2）
          final row = selectedIndex ~/ 2;
          final itemHeight = 200.0; // 估算的每个模板卡片高度
          final spacing = 16.0;
          final scrollOffset = row * (itemHeight + spacing);

          _templateScrollController.animateTo(
            scrollOffset,
            duration: const Duration(milliseconds: 500),
            curve: Curves.easeInOut,
          );
        }
      });
    }

    return Padding(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            l10n.textCardsSelectTemplate,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Theme.of(context).colorScheme.onSurface,
            ),
          ),
          const SizedBox(height: 16),

          Expanded(
            child: GridView.builder(
              controller: _templateScrollController,
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                childAspectRatio: 0.8,
                crossAxisSpacing: 16,
                mainAxisSpacing: 16,
              ),
              itemCount: templates.length,
              itemBuilder: (context, index) {
                final template = templates[index];
                final isSelected = template.id == _selectedTemplate.id;

                return GestureDetector(
                  onTap: () {
                    setState(() {
                      _selectedTemplate = template;
                    });
                    HapticFeedback.selectionClick();
                  },
                  child: AnimatedContainer(
                    duration: const Duration(milliseconds: 200),
                    decoration: BoxDecoration(
                      gradient: template.backgroundGradient,
                      borderRadius: BorderRadius.circular(16),
                      border: Border.all(
                        color: isSelected ? Colors.white : Colors.transparent,
                        width: 3,
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: template.backgroundGradient.colors.first
                              .withValues(alpha: isSelected ? 0.4 : 0.2),
                          offset: const Offset(0, 8),
                          blurRadius: isSelected ? 20 : 12,
                        ),
                      ],
                    ),
                    child: Stack(
                      children: [
                        Padding(
                          padding: const EdgeInsets.all(16),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                template.name,
                                style: TextStyle(
                                  color: template.titleColor,
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                              const SizedBox(height: 8),
                              Text(
                                template.description,
                                style: TextStyle(
                                  color: template.textColor.withValues(
                                    alpha: 0.8,
                                  ),
                                  fontSize: 12,
                                ),
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                              ),
                              const Spacer(),
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 8,
                                  vertical: 4,
                                ),
                                decoration: BoxDecoration(
                                  color: template.accentColor.withValues(
                                    alpha: 0.2,
                                  ),
                                  borderRadius: BorderRadius.circular(6),
                                ),
                                child: Text(
                                  template.category,
                                  style: TextStyle(
                                    color: template.accentColor,
                                    fontSize: 10,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                        if (isSelected)
                          const Positioned(
                            top: 12,
                            right: 12,
                            child: Icon(
                              Icons.check_circle,
                              color: Colors.white,
                              size: 24,
                            ),
                          ),
                      ],
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPreviewView() {
    final l10n = AppLocalizations.of(context);
    final theme = Theme.of(context);
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                l10n.textCardsPreviewEffect,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.w600,
                  color: theme.colorScheme.onSurface,
                ),
              ),
              const Spacer(),
            ],
          ),
          const SizedBox(height: 16),

          Expanded(
            child: Center(
              child: CardPreviewWidget(
                content: _contentController.text,
                template: _selectedTemplate,
                customStyles: _customStyles,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBottomActions() {
    final theme = Theme.of(context);
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        border: Border(
          top: BorderSide(color: theme.colorScheme.outlineVariant, width: 1),
        ),
      ),
      child:
          _currentStep == 2
              ? _buildFinalStepActions()
              : _buildNormalStepActions(),
    );
  }

  Widget _buildNormalStepActions() {
    final l10n = AppLocalizations.of(context);
    final theme = Theme.of(context);
    return Row(
      children: [
        if (_currentStep > 0)
          Expanded(
            child: OutlinedButton(
              onPressed: _previousStep,
              style: OutlinedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                side: BorderSide(color: theme.colorScheme.outlineVariant),
              ),
              child: Text(l10n.textCardPreviousStep),
            ),
          ),

        if (_currentStep > 0) const SizedBox(width: 16),

        Expanded(
          flex: _currentStep > 0 ? 2 : 1,
          child: ElevatedButton(
            onPressed: _nextStepOrCreate,
            style: ElevatedButton.styleFrom(
              backgroundColor: theme.colorScheme.primary,
              foregroundColor: theme.colorScheme.onPrimary,
              padding: const EdgeInsets.symmetric(vertical: 16),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            child: Text(_getActionButtonText()),
          ),
        ),
      ],
    );
  }

  Widget _buildFinalStepActions() {
    final l10n = AppLocalizations.of(context);
    final theme = Theme.of(context);
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // 第一行：上一步 + 保存到内容库
        Row(
          children: [
            Expanded(
              child: OutlinedButton(
                onPressed: _previousStep,
                style: OutlinedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                  side: BorderSide(color: theme.colorScheme.outlineVariant),
                ),
                child: Text(l10n.textCardPreviousStep),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              flex: 2,
              child: ElevatedButton(
                onPressed: _createCard,
                style: ElevatedButton.styleFrom(
                  backgroundColor: theme.colorScheme.primary,
                  foregroundColor: theme.colorScheme.onPrimary,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: Text(l10n.textCardSaveToContentLibrary),
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        // 第二行：导出按钮（更显眼）
        SizedBox(
          width: double.infinity,
          child: ElevatedButton.icon(
            onPressed: _exportCard,
            icon: const Icon(Icons.download, size: 20),
            label: Text(
              l10n.textCardExportImage,
              style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
            ),
            style: ElevatedButton.styleFrom(
              backgroundColor: theme.colorScheme.secondary,
              foregroundColor: theme.colorScheme.onSecondary,
              padding: const EdgeInsets.symmetric(vertical: 18),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
              elevation: 2,
            ),
          ),
        ),
      ],
    );
  }

  void _switchToStep(int step) {
    if (step == _currentStep) return;

    setState(() {
      _currentStep = step;
    });

    _fadeController.reset();
    _fadeController.forward();
    HapticFeedback.selectionClick();
  }

  void _previousStep() {
    if (_currentStep > 0) {
      _switchToStep(_currentStep - 1);
    }
  }

  void _nextStepOrCreate() {
    if (_currentStep < 2) {
      _switchToStep(_currentStep + 1);
    } else {
      _createCard();
    }
  }

  Future<ExportConfig?> _showExportOptionsDialog() async {
    return showDialog<ExportConfig>(
      context: context,
      builder: (context) => ExportOptionsDialog(
        initialConfig: const ExportConfig(),
      ),
    );
  }

  void _exportCard() async {
    debugPrint('🔥 导出按钮被点击');

    try {
      HapticFeedback.lightImpact();

      // 显示导出选项对话框
      final exportConfig = await _showExportOptionsDialog();

      // 如果用户取消了对话框，直接返回
      if (exportConfig == null) {
        debugPrint('🔥 用户取消了导出');
        return;
      }

      debugPrint(
        '🔥 用户选择了导出配置: ${exportConfig.size.label} ${exportConfig.ratio.label}',
      );

      // 不再弹出开始导出的 SnackBar，避免与 Toast 重复

      debugPrint('🔥 开始调用导出助手');

      // 使用用户选择的配置调用导出助手
      await CardExportHelper.exportCard(
        context: context,
        content: _contentController.text,
        template: _selectedTemplate,
        customStyles: _customStyles,
        exportConfig: exportConfig,
      );

      debugPrint('🔥 导出完成，等待成功提示显示');

      // 导出成功反馈：Toast + 触觉
      if (mounted) {
        final l10n = AppLocalizations.of(context);
        final overlay = Overlay.of(context);
        {
          final entry = OverlayEntry(
            builder:
                (_) => Positioned(
                  bottom: 80,
                  left: 24,
                  right: 24,
                  child: _Toast(message: l10n.textCardExportSuccess),
                ),
          );
          overlay.insert(entry);
          Future.delayed(const Duration(seconds: 2), () => entry.remove());
        }
        HapticFeedback.mediumImpact();
      }
    } catch (e) {
      debugPrint('🔥 导出失败: $e');

      // CardExportHelper已经显示了错误消息，这里不需要重复显示
      // 只提供触觉反馈表示失败
      if (mounted) {
        HapticFeedback.heavyImpact();
      }
    }
  }

  void _createCard() {
    final l10n = AppLocalizations.of(context);
    final content = _contentController.text.trim();
    if (content.isEmpty) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text(l10n.textCardPleaseEnterContent)));
      return;
    }

    // 构造保存到内容库的JSON数据（含模板与自定义样式）
    final cardData = _buildTextCardJson(
      content,
      _selectedTemplate,
      _customStyles,
    );

    Navigator.push<ContentItem>(
      context,
      MaterialPageRoute(
        builder:
            (_) => SaveContentPage(
              initialTitle: _extractTitleFromContent(content),
              content: jsonEncode(cardData),
              contentType: ContentType.textCard,
              initialTags: [
                'text_card',
                _selectedTemplate.category,
                _selectedTemplate.name,
              ],
            ),
      ),
    ).then((saved) {
      if (saved != null) {
        // 回调给上层（兼容已有流程）
        widget.onCardCreated(content, _selectedTemplate);
        Navigator.maybePop(context);
      }
    });
  }

  String _getActionButtonText() {
    final l10n = AppLocalizations.of(context);
    switch (_currentStep) {
      case 0:
        return l10n.textCardsSelectTemplate;
      case 1:
        return l10n.textCardsPreviewEffect;
      case 2:
        return l10n.textCardSaveToContentLibrary;
      default:
        return l10n.next;
    }
  }

  Map<String, dynamic> _buildTextCardJson(
    String text,
    EnhancedCardTemplate template,
    Map<String, dynamic> customStyles,
  ) {
    Map<String, dynamic> gradientToMap(LinearGradient g) => {
      'colors': g.colors.map((c) => c.toARGB32()).toList(),
      'begin': g.begin.toString(),
      'end': g.end.toString(),
    };

    final templateMap = {
      'id': template.id,
      'name': template.name,
      'category': template.category,
      'description': template.description,
      'backgroundGradient': gradientToMap(template.backgroundGradient),
      'textColor': template.textColor.toARGB32(),
      'titleColor': template.titleColor.toARGB32(),
      'accentColor': template.accentColor.toARGB32(),
      'borderRadius': template.borderRadius,
      'titleFontSize': template.titleFontSize,
      'contentFontSize': template.contentFontSize,
      'titleFontWeight': template.titleFontWeight.index,
      'contentFontWeight': template.contentFontWeight.index,
      'lineHeight': template.lineHeight,
      'layoutStyle': template.layoutStyle,
      'fontFamily': template.fontFamily,
      'padding': template.padding,
      'textAlign': template.textAlign.index,
      'hasShadow': template.hasShadow,
      'hasDecorationElements': template.hasDecorationElements,
    };

    return {
      'type': 'text_card',
      'content': {
        'text': text,
        'template': templateMap,
        'customStyles': customStyles,
        'createdAt': DateTime.now().toIso8601String(),
        'version': '1.0',
      },
    };
  }

  String _extractTitleFromContent(String content) {
    final firstLine = content.split('\n').first.trim();
    return firstLine.isEmpty ? '文本卡片' : firstLine;
  }
}

class _Toast extends StatelessWidget {
  final String message;
  const _Toast({required this.message});

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          color: Colors.black.withValues(alpha: 0.8),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(Icons.check_circle, color: Colors.white, size: 18),
            const SizedBox(width: 8),
            Flexible(
              child: Text(
                message,
                style: const TextStyle(color: Colors.white, fontSize: 14),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
