import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../models/split_config.dart';
import '../models/enhanced_card_template.dart';
import '../models/export_config.dart';
import '../services/smart_split_service.dart';
import 'split_preview_card.dart';
import 'card_preview_widget.dart';
import '../utils/card_export_helper.dart';
import 'export_options_dialog.dart';
import '../../generated/l10n/app_localizations.dart';

/// 智能文本拆分器
class SmartTextSplitter extends StatefulWidget {
  final Function(
    List<String> contents,
    EnhancedCardTemplate template,
    bool fixedPreviewHeight,
  )
  onBatchCreate;

  // 新增：用于从内容库恢复
  final EnhancedCardTemplate? initialTemplate;
  final List<String>? initialContents; // 如果提供，直接填充拆分结果
  final int? initialStep; // 0..4，默认按流程；传 4 直接预览
  final SplitConfig? initialConfig;
  // 新增：恢复预览高度模式（固定高度 / 自适应高度）
  final bool? initialFixedPreviewHeight;

  const SmartTextSplitter({
    super.key,
    required this.onBatchCreate,
    this.initialTemplate,
    this.initialContents,
    this.initialStep,
    this.initialConfig,
    this.initialFixedPreviewHeight,
  });

  @override
  State<SmartTextSplitter> createState() => _SmartTextSplitterState();
}

class _SmartTextSplitterState extends State<SmartTextSplitter>
    with TickerProviderStateMixin {
  late AnimationController _slideController;
  late Animation<Offset> _slideAnimation;
  late AnimationController _fadeController;
  late Animation<double> _fadeAnimation;

  final TextEditingController _textController = TextEditingController();
  final TextEditingController _separatorController = TextEditingController();
  final PageController _pageController = PageController();

  SplitConfig _config = const SplitConfig();
  List<SplitResultItem> _splitResults = [];
  final Set<int> _selectedIndices = {};
  int _currentPage = 0; // 0: 输入, 1: 配置, 2: 拆分结果, 3: 模板, 4: 预览
  bool _isProcessing = false;
  late EnhancedCardTemplate _selectedTemplate;
  bool _fixedPreviewHeight = true; // 预览卡片高度：true 固定，false 自适应

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    _initializeDefaultText();
    _initializeTemplate();

    // 从内容库恢复：初始配置、内容、模板、步进
    if (widget.initialConfig != null) {
      _config = widget.initialConfig!;
    }
    if (widget.initialTemplate != null) {
      _selectedTemplate = widget.initialTemplate!;
    }
    if (widget.initialFixedPreviewHeight != null) {
      _fixedPreviewHeight = widget.initialFixedPreviewHeight!;
    }
    if (widget.initialContents != null && widget.initialContents!.isNotEmpty) {
      _splitResults = List.generate(
        widget.initialContents!.length,
        (i) => SplitResultItem(
          id: 'init_$i',
          content: widget.initialContents![i],
          originalIndex: i,
        ),
      );
    }
    if (widget.initialStep != null) {
      _currentPage = widget.initialStep!.clamp(0, 4);
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (_pageController.hasClients) {
          _pageController.jumpToPage(_currentPage);
        }
      });
    }
  }

  void _initializeAnimations() {
    _slideController = AnimationController(
      duration: const Duration(milliseconds: 400),
      vsync: this,
    );
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 1),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(parent: _slideController, curve: Curves.easeOutCubic),
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut),
    );

    _slideController.forward();
    _fadeController.forward();
  }

  void _initializeTemplate() {
    final templates = EnhancedCardTemplate.getModernTemplates();
    // 优先选择 classic_style，没有则取第一个
    _selectedTemplate = templates.firstWhere(
      (t) => t.id == 'classic_style',
      orElse: () => templates.first,
    );
  }

  void _initializeDefaultText() {
    _textController.text = '''什么是人工智能？

人工智能（Artificial Intelligence，AI）是计算机科学的一个分支，它企图了解智能的实质，并生产出一种新的能以人类智能相似的方式做出反应的智能机器。

---

机器学习的基本概念

机器学习是人工智能的一个重要分支，它使计算机能够在没有明确编程的情况下学习和改进。主要包括：

• 监督学习：使用标记数据训练模型
• 无监督学习：从未标记数据中发现模式
• 强化学习：通过试错来学习最优策略

---

深度学习的应用

深度学习是机器学习的一个子集，它模仿人脑神经网络的工作方式。在以下领域有广泛应用：

1. 图像识别和计算机视觉
2. 自然语言处理
3. 语音识别和合成
4. 推荐系统''';

    _separatorController.text = '---';
  }

  @override
  void dispose() {
    _slideController.dispose();
    _fadeController.dispose();
    _textController.dispose();
    _separatorController.dispose();
    _pageController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SlideTransition(
      position: _slideAnimation,
      child: Container(
        height: MediaQuery.of(context).size.height * 0.9,
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          borderRadius: const BorderRadius.vertical(top: Radius.circular(25)),
        ),
        child: Column(
          children: [
            _buildHeader(),
            _buildProgressIndicator(),
            Expanded(
              child: FadeTransition(
                opacity: _fadeAnimation,
                child: PageView(
                  controller: _pageController,
                  onPageChanged: (page) {
                    setState(() {
                      _currentPage = page;
                    });
                  },
                  children: [
                    _buildInputPage(),
                    _buildConfigPage(),
                    _buildSplitResultsPage(),
                    _buildTemplatePage(),
                    _buildPreviewPage(),
                  ],
                ),
              ),
            ),
            _buildBottomActions(),
          ],
        ),
      ),
    );
  }

  /// 构建头部
  Widget _buildHeader() {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(25)),
        boxShadow: [
          if (!isDark)
            const BoxShadow(
              color: Color(0x0A000000),
              offset: Offset(0, 1),
              blurRadius: 3,
            ),
        ],
      ),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              gradient: const LinearGradient(
                colors: [Color(0xFF8B5CF6), Color(0xFF6366F1)],
              ),
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Icon(
              Icons.auto_awesome,
              color: Colors.white,
              size: 20,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  AppLocalizations.of(context).smartTextSplitter,
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.w700,
                    color: theme.colorScheme.onSurface,
                  ),
                ),
                Text(
                  AppLocalizations.of(context).smartTextSplitterSubtitle,
                  style: TextStyle(
                    fontSize: 12,
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ),
          IconButton(
            onPressed: () => Navigator.pop(context),
            icon: Icon(Icons.close, color: theme.colorScheme.onSurfaceVariant),
          ),
        ],
      ),
    );
  }

  /// 构建进度指示器
  Widget _buildProgressIndicator() {
    final theme = Theme.of(context);
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 16),
      color: theme.colorScheme.surface,
      child: Row(
        children: [
          _buildStepIndicator(
            0,
            AppLocalizations.of(context).inputTextToSplit,
            Icons.edit_outlined,
          ),
          _buildStepConnector(),
          _buildStepIndicator(
            1,
            AppLocalizations.of(context).splitConfig,
            Icons.settings_outlined,
          ),
          _buildStepConnector(),
          _buildStepIndicator(
            2,
            AppLocalizations.of(context).splitPreview,
            Icons.preview_outlined,
          ),
          _buildStepConnector(),
          _buildStepIndicator(
            3,
            AppLocalizations.of(context).textCardsSelectTemplate,
            Icons.palette_outlined,
          ),
          _buildStepConnector(),
          _buildStepIndicator(
            4,
            AppLocalizations.of(context).preview,
            Icons.visibility_outlined,
          ),
        ],
      ),
    );
  }

  /// 构建步骤指示器
  Widget _buildStepIndicator(int step, String title, IconData icon) {
    final isActive = _currentPage == step;
    final isCompleted = _currentPage > step;

    return Expanded(
      child: Column(
        children: [
          Container(
            width: 32,
            height: 32,
            decoration: BoxDecoration(
              color:
                  isActive || isCompleted
                      ? const Color(0xFF6366F1)
                      : const Color(0xFFE2E8F0),
              borderRadius: BorderRadius.circular(16),
            ),
            child: Icon(
              isCompleted ? Icons.check : icon,
              size: 16,
              color:
                  isActive || isCompleted
                      ? Colors.white
                      : const Color(0xFF64748B),
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: TextStyle(
              fontSize: 10,
              fontWeight: FontWeight.w500,
              color:
                  isActive ? const Color(0xFF6366F1) : const Color(0xFF64748B),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建步骤连接器
  Widget _buildStepConnector() {
    return Container(
      width: 24,
      height: 2,
      color: const Color(0xFFE2E8F0),
      margin: const EdgeInsets.only(bottom: 16),
    );
  }

  /// 构建输入页面
  Widget _buildInputPage() {
    final l10n = AppLocalizations.of(context);
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            l10n.inputTextToSplit,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Theme.of(context).colorScheme.onSurface,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            l10n.pasteOrInputLongText,
            style: TextStyle(
              fontSize: 12,
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
          const SizedBox(height: 16),

          // 快速操作按钮
          Row(
            children: [
              _buildQuickActionButton(
                l10n.pasteClipboard,
                Icons.content_paste,
                _pasteFromClipboard,
              ),
              const SizedBox(width: 12),
              _buildQuickActionButton(
                l10n.clearContent,
                Icons.clear_all,
                _clearText,
              ),
            ],
          ),

          const SizedBox(height: 16),

          // 文本输入框
          Expanded(
            child: Container(
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surfaceContainerLow,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: Theme.of(context).colorScheme.outlineVariant,
                ),
              ),
              child: TextField(
                controller: _textController,
                decoration: InputDecoration(
                  hintText: AppLocalizations.of(context).inputTextHint,
                  border: InputBorder.none,
                  contentPadding: const EdgeInsets.all(16),
                  hintStyle: TextStyle(
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                ),
                maxLines: null,
                expands: true,
                textAlignVertical: TextAlignVertical.top,
                style: TextStyle(
                  fontSize: 14,
                  height: 1.5,
                  color: Theme.of(context).colorScheme.onSurface,
                ),
              ),
            ),
          ),

          const SizedBox(height: 16),

          // 统计信息
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Theme.of(
                context,
              ).colorScheme.primary.withValues(alpha: 0.06),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.info_outline,
                  size: 16,
                  color: Theme.of(context).colorScheme.primary,
                ),
                const SizedBox(width: 8),
                Text(
                  AppLocalizations.of(context).characterCount(
                    int.parse(_textController.text.length.toString()),
                  ),
                  style: TextStyle(
                    fontSize: 12,
                    color: Theme.of(context).colorScheme.primary,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建配置页面
  Widget _buildConfigPage() {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            AppLocalizations.of(context).splitConfig,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Theme.of(context).colorScheme.onSurface,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            AppLocalizations.of(context).splitConfigDescription,
            style: TextStyle(
              fontSize: 12,
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
          const SizedBox(height: 20),

          Expanded(
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // 拆分模式选择
                  _buildSectionTitle(AppLocalizations.of(context).splitMode),
                  const SizedBox(height: 12),
                  ...SplitMode.values.map((mode) => _buildModeOption(mode)),

                  const SizedBox(height: 24),

                  // 自定义分隔符（仅在自定义模式下显示）
                  if (_config.mode == SplitMode.custom) ...[
                    _buildSectionTitle(
                      AppLocalizations.of(context).customSeparator,
                    ),
                    const SizedBox(height: 12),
                    Container(
                      decoration: BoxDecoration(
                        color:
                            Theme.of(context).colorScheme.surfaceContainerLow,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(
                          color: Theme.of(context).colorScheme.outlineVariant,
                        ),
                      ),
                      child: TextField(
                        controller: _separatorController,
                        decoration: InputDecoration(
                          hintText:
                              AppLocalizations.of(context).customSeparatorHint,
                          border: InputBorder.none,
                          contentPadding: const EdgeInsets.all(12),
                          hintStyle: TextStyle(
                            color:
                                Theme.of(context).colorScheme.onSurfaceVariant,
                          ),
                        ),
                        style: TextStyle(
                          color: Theme.of(context).colorScheme.onSurface,
                        ),
                        onChanged: (value) {
                          setState(() {
                            _config = _config.copyWith(customSeparator: value);
                          });
                        },
                      ),
                    ),
                    const SizedBox(height: 24),
                  ],

                  // 高级选项
                  _buildSectionTitle(
                    AppLocalizations.of(context).advancedOptions,
                  ),
                  const SizedBox(height: 12),
                  _buildAdvancedOptions(),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建模板选择页面
  Widget _buildTemplatePage() {
    final l10n = AppLocalizations.of(context);
    final templates = EnhancedCardTemplate.getModernTemplates();

    return Padding(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            l10n.textCardsSelectTemplate,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: Color(0xFF1E293B),
            ),
          ),
          const SizedBox(height: 12),
          Expanded(
            child: GridView.builder(
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                childAspectRatio: 0.8,
                crossAxisSpacing: 16,
                mainAxisSpacing: 16,
              ),
              itemCount: templates.length,
              itemBuilder: (context, index) {
                final template = templates[index];
                final isSelected = template.id == _selectedTemplate.id;

                return GestureDetector(
                  onTap: () {
                    setState(() {
                      _selectedTemplate = template;
                    });
                    HapticFeedback.selectionClick();
                  },
                  child: AnimatedContainer(
                    duration: const Duration(milliseconds: 200),
                    decoration: BoxDecoration(
                      gradient: template.backgroundGradient,
                      borderRadius: BorderRadius.circular(16),
                      border: Border.all(
                        color: isSelected ? Colors.white : Colors.transparent,
                        width: 3,
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: template.backgroundGradient.colors.first
                              .withValues(alpha: isSelected ? 0.4 : 0.2),
                          offset: const Offset(0, 8),
                          blurRadius: isSelected ? 20 : 12,
                        ),
                      ],
                    ),
                    child: Stack(
                      children: [
                        Padding(
                          padding: const EdgeInsets.all(16),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                template.name,
                                style: TextStyle(
                                  color: template.titleColor,
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                              const SizedBox(height: 8),
                              Text(
                                template.description,
                                style: TextStyle(
                                  color: template.textColor.withValues(
                                    alpha: 0.8,
                                  ),
                                  fontSize: 12,
                                ),
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                              ),
                              const Spacer(),
                              Container(
                                padding: const EdgeInsets.symmetric(
                                  horizontal: 8,
                                  vertical: 4,
                                ),
                                decoration: BoxDecoration(
                                  color: template.accentColor.withValues(
                                    alpha: 0.2,
                                  ),
                                  borderRadius: BorderRadius.circular(6),
                                ),
                                child: Text(
                                  template.category,
                                  style: TextStyle(
                                    color: template.accentColor,
                                    fontSize: 10,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ),
                            ],
                          ),
                        ),
                        if (isSelected)
                          const Positioned(
                            top: 12,
                            right: 12,
                            child: Icon(
                              Icons.check_circle,
                              color: Colors.white,
                              size: 24,
                            ),
                          ),
                      ],
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  /// 构建预览页面（整体滚动预览）
  Widget _buildPreviewPage() {
    final l10n = AppLocalizations.of(context);
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                l10n.preview,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Theme.of(context).colorScheme.onSurface,
                ),
              ),
              const Spacer(),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Theme.of(
                    context,
                  ).colorScheme.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Text(
                  l10n.totalCards(_splitResults.length),
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            l10n.splitPreviewDescription,
            style: TextStyle(
              fontSize: 12,
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
          const SizedBox(height: 16),

          // 预览尺寸配置：固定高度 / 自适应高度
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.surfaceContainerLowest,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: Theme.of(context).colorScheme.outlineVariant,
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.straighten,
                  size: 16,
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
                const SizedBox(width: 8),
                Text(
                  '预览尺寸',
                  style: TextStyle(
                    fontSize: 12,
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                ),
                const Spacer(),
                _buildPreviewSizeChip('固定卡片高度', _fixedPreviewHeight, () {
                  setState(() => _fixedPreviewHeight = true);
                }),
                const SizedBox(width: 8),
                _buildPreviewSizeChip('高度自适应', !_fixedPreviewHeight, () {
                  setState(() => _fixedPreviewHeight = false);
                }),
              ],
            ),
          ),

          const SizedBox(height: 12),

          Expanded(
            child:
                _splitResults.isEmpty
                    ? _buildEmptyState()
                    : ListView.separated(
                      itemCount: _splitResults.length,
                      separatorBuilder: (_, __) => const SizedBox(height: 16),
                      itemBuilder: (context, index) {
                        final item = _splitResults[index];
                        final previewContent = item.content;

                        if (_fixedPreviewHeight) {
                          return AspectRatio(
                            aspectRatio: 4 / 5,
                            child: ClipRRect(
                              borderRadius: BorderRadius.circular(
                                _selectedTemplate.borderRadius,
                              ),
                              child: CardPreviewWidget(
                                content: previewContent,
                                template: _selectedTemplate,
                              ),
                            ),
                          );
                        } else {
                          final totalWidth = MediaQuery.of(context).size.width;
                          // 预览页整体左右有 20 padding
                          final availableWidth = totalWidth - 40;
                          final dynamicHeight = _calculatePreviewHeight(
                            previewContent,
                            availableWidth,
                            _selectedTemplate,
                          );
                          return SizedBox(
                            height: dynamicHeight,
                            child: ClipRRect(
                              borderRadius: BorderRadius.circular(
                                _selectedTemplate.borderRadius,
                              ),
                              child: CardPreviewWidget(
                                content: previewContent,
                                template: _selectedTemplate,
                              ),
                            ),
                          );
                        }
                      },
                    ),
          ),
        ],
      ),
    );
  }

  // 预览尺寸选择 Chip
  Widget _buildPreviewSizeChip(
    String label,
    bool selected,
    VoidCallback onTap,
  ) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(16),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 6),
        decoration: BoxDecoration(
          color:
              selected
                  ? Theme.of(context).colorScheme.primary
                  : Theme.of(context).colorScheme.surface,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color: Theme.of(context).colorScheme.outlineVariant,
          ),
        ),
        child: Text(
          label,
          style: TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w600,
            color:
                selected
                    ? Theme.of(context).colorScheme.onPrimary
                    : Theme.of(context).colorScheme.onSurfaceVariant,
          ),
        ),
      ),
    );
  }

  // 根据内容估算自适应预览高度
  double _calculatePreviewHeight(
    String content,
    double availableWidth,
    EnhancedCardTemplate template,
  ) {
    final padding = template.padding;
    final contentWidth = availableWidth - padding * 2;

    final lines = content.split('\n');
    final title = lines.isNotEmpty ? lines.first : '';
    final body = lines.length > 1 ? lines.skip(1).join('\n').trim() : '';

    double titleHeight = 0;
    if (title.isNotEmpty) {
      final titlePainter = TextPainter(
        text: TextSpan(
          text: title,
          style: TextStyle(
            fontSize: template.titleFontSize,
            fontWeight: template.titleFontWeight,
            height: 1.2,
            color: template.titleColor,
          ),
        ),
        textDirection: TextDirection.ltr,
        maxLines: 3,
      );
      titlePainter.layout(maxWidth: contentWidth);
      titleHeight = titlePainter.height + padding * 0.3;
    }

    double bodyHeight = 0;
    if (body.isNotEmpty) {
      final bodyPainter = TextPainter(
        text: TextSpan(
          text: body,
          style: TextStyle(
            fontSize: template.contentFontSize,
            fontWeight: template.contentFontWeight,
            height: template.lineHeight,
            color: template.textColor,
          ),
        ),
        textDirection: TextDirection.ltr,
      );
      bodyPainter.layout(maxWidth: contentWidth);
      bodyHeight = bodyPainter.height;
    }

    final total = padding * 2 + titleHeight + bodyHeight;
    // 设定合理范围
    return total.clamp(260.0, 720.0);
  }

  /// 构建拆分结果页（可编辑/批量操作）
  Widget _buildSplitResultsPage() {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                AppLocalizations.of(context).splitPreview,
                style: const TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: Color(0xFF1E293B),
                ),
              ),
              const Spacer(),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: const Color(0xFF10B981).withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Text(
                  AppLocalizations.of(context).totalCards(_splitResults.length),
                  style: const TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    color: Color(0xFF10B981),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            AppLocalizations.of(context).splitPreviewDescription,
            style: const TextStyle(fontSize: 12, color: Color(0xFF64748B)),
          ),
          const SizedBox(height: 16),

          // 批量操作
          Row(
            children: [
              _buildBatchActionButton(
                AppLocalizations.of(context).selectAll,
                Icons.select_all,
                _selectAll,
              ),
              const SizedBox(width: 8),
              _buildBatchActionButton(
                AppLocalizations.of(context).deselectAll,
                Icons.deselect,
                _deselectAll,
              ),
              const SizedBox(width: 8),
              _buildBatchActionButton(
                AppLocalizations.of(context).deleteSelected,
                Icons.delete_outline,
                _deleteSelected,
                color: const Color(0xFFEF4444),
              ),
            ],
          ),

          const SizedBox(height: 16),

          Expanded(
            child:
                _splitResults.isEmpty
                    ? _buildEmptyState()
                    : ListView.builder(
                      itemCount: _splitResults.length,
                      itemBuilder: (context, index) {
                        final item = _splitResults[index];
                        return SplitPreviewCard(
                          item: item,
                          index: index,
                          isSelected: _selectedIndices.contains(index),
                          onTap: () => _toggleSelection(index),
                          onEdit: () => _editItem(index),
                          onDelete: () => _deleteItem(index),
                          onMerge:
                              index < _splitResults.length - 1
                                  ? () => _mergeWithNext(index)
                                  : null,
                        );
                      },
                    ),
          ),
        ],
      ),
    );
  }

  /// 构建空状态
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.content_cut,
            size: 48,
            color: Colors.grey.withValues(alpha: 0.4),
          ),
          const SizedBox(height: 16),
          Text(
            AppLocalizations.of(context).noSplitResults,
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w500,
              color: Color(0xFF64748B),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            AppLocalizations.of(context).noSplitResultsDescription,
            style: const TextStyle(fontSize: 12, color: Color(0xFF94A3B8)),
          ),
        ],
      ),
    );
  }

  /// 构建底部操作按钮
  Widget _buildBottomActions() {
    // 最后一页（预览）展示“保存到内容库”和“全部保存到相册”
    if (_currentPage == 4) {
      final l10n = AppLocalizations.of(context);
      return Container(
        padding: const EdgeInsets.all(20),
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          boxShadow: [
            BoxShadow(
              color:
                  Theme.of(context).brightness == Brightness.dark
                      ? Colors.black.withValues(alpha: 0.3)
                      : const Color(0x0A000000),
              offset: const Offset(0, -1),
              blurRadius: 3,
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: _previousPage,
                    style: OutlinedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      side: BorderSide(
                        color: Theme.of(context).colorScheme.outlineVariant,
                      ),
                    ),
                    child: Text(
                      l10n.textCardPreviousStep,
                      style: TextStyle(
                        fontWeight: FontWeight.w600,
                        color: Theme.of(context).colorScheme.onSurface,
                      ),
                    ),
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  flex: 2,
                  child: ElevatedButton(
                    onPressed: _isProcessing ? null : _createCards,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Theme.of(context).colorScheme.primary,
                      foregroundColor: Theme.of(context).colorScheme.onPrimary,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      elevation: 0,
                    ),
                    child: Text(l10n.textCardSaveToContentLibrary),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _isProcessing ? null : _exportAllCards,
                icon: const Icon(Icons.download, size: 20),
                label: Text(
                  '${l10n.textCardSaveToAlbum}（${l10n.batchProcessing}）',
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: Theme.of(context).colorScheme.secondary,
                  foregroundColor: Theme.of(context).colorScheme.onSecondary,
                  padding: const EdgeInsets.symmetric(vertical: 18),
                  elevation: 2,
                ),
              ),
            ),
          ],
        ),
      );
    }

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surface,
        boxShadow: [
          BoxShadow(
            color:
                Theme.of(context).brightness == Brightness.dark
                    ? Colors.black.withValues(alpha: 0.3)
                    : const Color(0x0A000000),
            offset: const Offset(0, -1),
            blurRadius: 3,
          ),
        ],
      ),
      child: Row(
        children: [
          if (_currentPage > 0)
            Expanded(
              child: OutlinedButton(
                onPressed: _previousPage,
                style: OutlinedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  side: BorderSide(
                    color: Theme.of(context).colorScheme.outlineVariant,
                  ),
                ),
                child: Text(
                  AppLocalizations.of(context).previousStep,
                  style: TextStyle(
                    fontWeight: FontWeight.w600,
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                ),
              ),
            ),

          if (_currentPage > 0) const SizedBox(width: 12),

          Expanded(
            flex: _currentPage == 0 ? 1 : 2,
            child: ElevatedButton(
              onPressed: _getNextButtonAction(),
              style: ElevatedButton.styleFrom(
                backgroundColor: Theme.of(context).colorScheme.primary,
                foregroundColor: Theme.of(context).colorScheme.onPrimary,
                padding: const EdgeInsets.symmetric(vertical: 16),
                elevation: 0,
              ),
              child:
                  _isProcessing
                      ? const SizedBox(
                        width: 20,
                        height: 20,
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(
                            Colors.white,
                          ),
                        ),
                      )
                      : Text(
                        _getNextButtonText(),
                        style: const TextStyle(fontWeight: FontWeight.w600),
                      ),
            ),
          ),
        ],
      ),
    );
  }
  // ===== 辅助方法 =====

  /// 构建快速操作按钮
  Widget _buildQuickActionButton(
    String text,
    IconData icon,
    VoidCallback onTap,
  ) {
    return Expanded(
      child: Container(
        height: 40,
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: const Color(0xFFE2E8F0)),
        ),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            onTap: onTap,
            borderRadius: BorderRadius.circular(8),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(icon, size: 16, color: const Color(0xFF6366F1)),
                const SizedBox(width: 8),
                Text(
                  text,
                  style: const TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                    color: Color(0xFF6366F1),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 构建节标题
  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: TextStyle(
        fontSize: 14,
        fontWeight: FontWeight.w600,
        color: Theme.of(context).colorScheme.onSurface,
      ),
    );
  }

  /// 构建模式选项
  Widget _buildModeOption(SplitMode mode) {
    final isSelected = _config.mode == mode;

    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerLow,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color:
              isSelected
                  ? Theme.of(context).colorScheme.primary
                  : Theme.of(context).colorScheme.outlineVariant,
          width: isSelected ? 2 : 1,
        ),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () {
            setState(() {
              // 切换到自定义分隔符模式时，若未设置，自动使用输入框或默认 '---'
              if (mode == SplitMode.custom &&
                  _config.customSeparator.trim().isEmpty) {
                final fallback =
                    _separatorController.text.trim().isNotEmpty
                        ? _separatorController.text.trim()
                        : '---';
                _config = _config.copyWith(
                  mode: mode,
                  customSeparator: fallback,
                );
              } else {
                _config = _config.copyWith(mode: mode);
              }
            });
          },
          borderRadius: BorderRadius.circular(8),
          child: Padding(
            padding: const EdgeInsets.all(12),
            child: Row(
              children: [
                Container(
                  width: 20,
                  height: 20,
                  decoration: BoxDecoration(
                    shape: BoxShape.circle,
                    border: Border.all(
                      color:
                          isSelected
                              ? Theme.of(context).colorScheme.primary
                              : Theme.of(context).colorScheme.outlineVariant,
                      width: 2,
                    ),
                    color:
                        isSelected
                            ? Theme.of(context).colorScheme.primary
                            : Colors.transparent,
                  ),
                  child:
                      isSelected
                          ? const Icon(
                            Icons.check,
                            size: 12,
                            color: Colors.white,
                          )
                          : null,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        mode.displayName,
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color:
                              isSelected
                                  ? Theme.of(context).colorScheme.primary
                                  : Theme.of(context).colorScheme.onSurface,
                        ),
                      ),
                      const SizedBox(height: 2),
                      Text(
                        mode.description,
                        style: TextStyle(
                          fontSize: 12,
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// 构建高级选项
  Widget _buildAdvancedOptions() {
    return Column(
      children: [
        _buildSwitchOption(
          AppLocalizations.of(context).autoDetectTitles,
          AppLocalizations.of(context).autoDetectTitlesDescription,
          _config.autoDetectTitles,
          (value) {
            setState(() {
              _config = _config.copyWith(autoDetectTitles: value);
            });
          },
        ),
        const SizedBox(height: 12),
        _buildSwitchOption(
          AppLocalizations.of(context).preserveFormatting,
          AppLocalizations.of(context).preserveFormattingDescription,
          _config.preserveFormatting,
          (value) {
            setState(() {
              _config = _config.copyWith(preserveFormatting: value);
            });
          },
        ),
        const SizedBox(height: 12),
        _buildSwitchOption(
          AppLocalizations.of(context).smartMerge,
          AppLocalizations.of(context).smartMergeDescription,
          _config.smartMerge,
          (value) {
            setState(() {
              _config = _config.copyWith(smartMerge: value);
            });
          },
        ),
        const SizedBox(height: 16),
        _buildSliderOption(
          AppLocalizations.of(context).maxLength,
          AppLocalizations.of(context).maxLengthDescription,
          _config.maxLength.toDouble(),
          100,
          1000,
          (value) {
            setState(() {
              _config = _config.copyWith(maxLength: value.round());
            });
          },
        ),
      ],
    );
  }

  /// 构建开关选项
  Widget _buildSwitchOption(
    String title,
    String description,
    bool value,
    ValueChanged<bool> onChanged,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerLow,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Theme.of(context).colorScheme.outlineVariant),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                    color: Theme.of(context).colorScheme.onSurface,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  description,
                  style: TextStyle(
                    fontSize: 12,
                    color: Theme.of(context).colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
            ),
          ),
          Switch(
            value: value,
            onChanged: onChanged,
            activeColor: Theme.of(context).colorScheme.primary,
          ),
        ],
      ),
    );
  }

  /// 构建滑块选项
  Widget _buildSliderOption(
    String title,
    String description,
    double value,
    double min,
    double max,
    ValueChanged<double> onChanged,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerLow,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Theme.of(context).colorScheme.outlineVariant),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: TextStyle(
                        fontSize: 14,
                        fontWeight: FontWeight.w600,
                        color: Theme.of(context).colorScheme.onSurface,
                      ),
                    ),
                    const SizedBox(height: 2),
                    Text(
                      description,
                      style: TextStyle(
                        fontSize: 12,
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: Theme.of(
                    context,
                  ).colorScheme.primary.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(6),
                ),
                child: Text(
                  value.round().toString(),
                  style: TextStyle(
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: Theme.of(context).colorScheme.primary,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          SliderTheme(
            data: SliderTheme.of(context).copyWith(
              activeTrackColor: Theme.of(context).colorScheme.primary,
              inactiveTrackColor: Theme.of(context).colorScheme.outlineVariant,
              thumbColor: Theme.of(context).colorScheme.primary,
              overlayColor: Theme.of(
                context,
              ).colorScheme.primary.withValues(alpha: 0.1),
              trackHeight: 4,
            ),
            child: Slider(
              value: value,
              min: min,
              max: max,
              divisions: ((max - min) / 50).round(),
              onChanged: onChanged,
            ),
          ),
        ],
      ),
    );
  }

  // ===== 事件处理方法 =====

  /// 批量操作按钮
  Widget _buildBatchActionButton(
    String text,
    IconData icon,
    VoidCallback onTap, {
    Color color = const Color(0xFF6366F1),
  }) {
    return Container(
      height: 32,
      padding: const EdgeInsets.symmetric(horizontal: 12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(6),
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(6),
          child: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(icon, size: 14, color: color),
              const SizedBox(width: 6),
              Text(
                text,
                style: TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                  color: color,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // ===== 拆分结果交互 =====
  void _toggleSelection(int index) {
    setState(() {
      if (_selectedIndices.contains(index)) {
        _selectedIndices.remove(index);
      } else {
        _selectedIndices.add(index);
      }
    });
  }

  void _selectAll() {
    setState(() {
      _selectedIndices
        ..clear()
        ..addAll(List<int>.generate(_splitResults.length, (i) => i));
    });
  }

  void _deselectAll() {
    setState(() {
      _selectedIndices.clear();
    });
  }

  void _deleteSelected() {
    if (_selectedIndices.isEmpty) return;
    setState(() {
      final toDelete =
          _selectedIndices.toList()..sort((a, b) => b.compareTo(a));
      for (final idx in toDelete) {
        _splitResults.removeAt(idx);
      }
      _selectedIndices.clear();
    });
  }

  void _editItem(int index) {
    if (index >= _splitResults.length) return;
    final item = _splitResults[index];
    final titleController = TextEditingController(text: item.title ?? '');
    final contentController = TextEditingController(text: item.content);
    final l10n = AppLocalizations.of(context);
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(l10n.textCardEditCard),
            content: SizedBox(
              width: double.maxFinite,
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  TextField(
                    controller: titleController,
                    decoration: InputDecoration(
                      labelText: AppLocalizations.of(context).titleOptional,
                      border: const OutlineInputBorder(),
                    ),
                  ),
                  const SizedBox(height: 16),
                  TextField(
                    controller: contentController,
                    decoration: InputDecoration(
                      labelText: AppLocalizations.of(context).content,
                      border: const OutlineInputBorder(),
                    ),
                    maxLines: 5,
                  ),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text(l10n.cancel),
              ),
              ElevatedButton(
                onPressed: () {
                  setState(() {
                    _splitResults[index] = item.copyWith(
                      title:
                          titleController.text.trim().isEmpty
                              ? null
                              : titleController.text.trim(),
                      content: contentController.text,
                    );
                  });
                  Navigator.pop(context);
                },
                child: Text(l10n.save),
              ),
            ],
          ),
    );
  }

  void _deleteItem(int index) {
    if (index >= _splitResults.length) return;
    setState(() {
      _splitResults.removeAt(index);
      _selectedIndices.remove(index);
      final adjusted = <int>{};
      for (final s in _selectedIndices) {
        adjusted.add(s > index ? s - 1 : s);
      }
      _selectedIndices
        ..clear()
        ..addAll(adjusted);
    });
  }

  void _mergeWithNext(int index) {
    if (index >= _splitResults.length - 1) return;
    setState(() {
      _splitResults = SmartSplitService.mergeItems(
        _splitResults,
        index,
        index + 1,
      );
      _selectedIndices.clear();
    });
  }

  /// 从剪贴板粘贴
  Future<void> _pasteFromClipboard() async {
    try {
      final data = await Clipboard.getData(Clipboard.kTextPlain);
      if (data?.text != null) {
        setState(() {
          _textController.text = data!.text!;
        });
      }
    } catch (e) {
      // 忽略错误
    }
  }

  /// 清空文本
  void _clearText() {
    setState(() {
      _textController.clear();
    });
  }

  /// 上一页
  void _previousPage() {
    if (_currentPage > 0) {
      _pageController.previousPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  /// 下一页或执行操作
  VoidCallback? _getNextButtonAction() {
    if (_isProcessing) return null;

    switch (_currentPage) {
      case 0:
        return _textController.text.trim().isNotEmpty ? _nextPage : null;
      case 1:
        return _performSplit;
      case 2:
        return _splitResults.isNotEmpty ? _nextPage : null;
      case 3:
        return _splitResults.isNotEmpty ? _nextPage : null;
      case 4:
        return _splitResults.isNotEmpty ? _createCards : null;
    }
    return null;
  }

  /// 获取下一步按钮文本
  String _getNextButtonText() {
    switch (_currentPage) {
      case 0:
        return AppLocalizations.of(context).nextStep;
      case 1:
        return AppLocalizations.of(context).startSplitting;
      case 2:
        return AppLocalizations.of(context).textCardsSelectTemplate;
      case 3:
        return AppLocalizations.of(context).preview;
      case 4:
        return AppLocalizations.of(context).createCards;
      default:
        return AppLocalizations.of(context).nextStep;
    }
  }

  /// 下一页
  void _nextPage() {
    if (_currentPage < 4) {
      _pageController.nextPage(
        duration: const Duration(milliseconds: 300),
        curve: Curves.easeInOut,
      );
    }
  }

  /// 执行拆分
  Future<void> _performSplit() async {
    setState(() {
      _isProcessing = true;
    });

    try {
      await Future.delayed(const Duration(milliseconds: 500)); // 模拟处理时间

      final results = SmartSplitService.splitText(
        _textController.text,
        _config,
      );

      setState(() {
        _splitResults = results;
        _selectedIndices.clear();
        _isProcessing = false;
      });

      _nextPage();
    } catch (e) {
      setState(() {
        _isProcessing = false;
      });

      final l10n = AppLocalizations.of(context);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(l10n.textCardSplitFailed(e.toString()))),
      );
    }
  }

  /// 创建卡片
  Future<void> _createCards() async {
    if (_splitResults.isEmpty) return;

    setState(() {
      _isProcessing = true;
    });

    try {
      // 提取所有内容
      final contents = _splitResults.map((item) => item.content).toList();

      // 使用所选模板，携带当前预览高度模式
      widget.onBatchCreate(contents, _selectedTemplate, _fixedPreviewHeight);

      // 关闭弹窗
      Navigator.pop(context);
    } catch (e) {
      setState(() {
        _isProcessing = false;
      });

      final l10n = AppLocalizations.of(context);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(l10n.textCardCreateFailed(e.toString()))),
      );
    }
  }

  Future<ExportConfig?> _showExportOptionsDialog() async {
    return showDialog<ExportConfig>(
      context: context,
      builder: (context) => ExportOptionsDialog(
        initialConfig: const ExportConfig(),
      ),
    );
  }

  /// 批量导出所有卡片到相册（显示导出配置对话框）
  Future<void> _exportAllCards() async {
    if (_splitResults.isEmpty || _isProcessing) return;
    setState(() {
      _isProcessing = true;
    });

    try {
      // 1. 显示导出选项对话框（可复用单张导出配置）
      final exportConfig = await _showExportOptionsDialog();
      if (exportConfig == null) {
        setState(() {
          _isProcessing = false;
        });
        return; // 用户取消
      }

      // 2. 逐张导出保存到相册
      for (final item in _splitResults) {
        final previewContent = item.content;
        await CardExportHelper.exportCard(
          context: context,
          content: previewContent,
          template: _selectedTemplate,
          exportConfig: exportConfig,
        );
        await Future.delayed(const Duration(milliseconds: 200));
      }

      if (mounted) {
        final l10n = AppLocalizations.of(context);
        final overlay = Overlay.of(context);
        {
          final entry = OverlayEntry(
            builder:
                (_) => Positioned(
                  bottom: 80,
                  left: 24,
                  right: 24,
                  child: _Toast(message: l10n.textCardExportSuccess),
                ),
          );
          overlay.insert(entry);
          Future.delayed(const Duration(seconds: 2), () => entry.remove());
        }
      }
    } catch (e) {
      // 错误消息已由导出助手显示，这里仅重置状态
    } finally {
      if (mounted) {
        setState(() {
          _isProcessing = false;
        });
      }
    }
  }
}

class _Toast extends StatelessWidget {
  final String message;
  const _Toast({required this.message});

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          color: Colors.black.withValues(alpha: 0.8),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(Icons.check_circle, color: Colors.white, size: 18),
            const SizedBox(width: 8),
            Flexible(
              child: Text(
                message,
                style: const TextStyle(color: Colors.white, fontSize: 14),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
