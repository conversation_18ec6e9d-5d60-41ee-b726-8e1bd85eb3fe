import 'package:flutter/material.dart';
import '../../generated/l10n/app_localizations.dart';
import '../../config/app_theme.dart';
import '../models/enhanced_card_template.dart';
import 'modern_card_creator.dart';

/// 模板画廊
/// 展示所有可用的卡片模板，支持分类浏览和预览
class TemplateGallery extends StatefulWidget {
  final Function(EnhancedCardTemplate)? onTemplateSelected;

  const TemplateGallery({super.key, this.onTemplateSelected});

  @override
  State<TemplateGallery> createState() => _TemplateGalleryState();
}

class _TemplateGalleryState extends State<TemplateGallery>
    with TickerProviderStateMixin {
  late TabController _tabController;
  late AnimationController _slideController;
  late Animation<Offset> _slideAnimation;

  String _selectedCategory = 'all';
  List<EnhancedCardTemplate> _filteredTemplates = [];

  @override
  void initState() {
    super.initState();

    final categories = ['all', ...EnhancedCardTemplate.getAllCategories()];
    _tabController = TabController(length: categories.length, vsync: this);
    _tabController.addListener(_onTabChanged);

    _slideController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 1),
      end: Offset.zero,
    ).animate(
      CurvedAnimation(parent: _slideController, curve: Curves.easeOutCubic),
    );

    _slideController.forward();
    _filterTemplates();
  }

  @override
  void dispose() {
    _tabController.removeListener(_onTabChanged);
    _tabController.dispose();
    _slideController.dispose();
    super.dispose();
  }

  void _onTabChanged() {
    final categories = ['all', ...EnhancedCardTemplate.getAllCategories()];
    _selectedCategory = categories[_tabController.index];
    _filterTemplates();
  }

  void _filterTemplates() {
    setState(() {
      if (_selectedCategory == 'all') {
        _filteredTemplates = EnhancedCardTemplate.getModernTemplates();
      } else {
        _filteredTemplates = EnhancedCardTemplate.getTemplatesByCategory(
          _selectedCategory,
        );
      }
    });
  }

  void _filterByCategory(String category) {
    setState(() {
      _selectedCategory = category;
    });
    _filterTemplates();
  }

  @override
  Widget build(BuildContext context) {
    return SlideTransition(
      position: _slideAnimation,
      child: Container(
        height: MediaQuery.of(context).size.height * 0.9,
        decoration: BoxDecoration(
          color: Theme.of(context).colorScheme.surface,
          borderRadius: const BorderRadius.vertical(top: Radius.circular(25)),
        ),
        child: Column(
          children: [
            _buildHeader(),
            _buildCategoryTabs(),
            Expanded(child: _buildTemplateGrid()),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    final l10n = AppLocalizations.of(context);
    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        children: [
          // 拖拽指示器
          Container(
            width: 40,
            height: 4,
            decoration: BoxDecoration(
              color: Theme.of(
                context,
              ).colorScheme.onSurfaceVariant.withValues(alpha: 0.3),
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          const SizedBox(height: 16),

          // 标题和关闭按钮
          Row(
            children: [
              Expanded(
                child: Text(
                  l10n.textCardsTemplateGallery,
                  style: TextStyle(
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    color: Theme.of(context).colorScheme.onSurface,
                  ),
                ),
              ),
              IconButton(
                onPressed: () => Navigator.pop(context),
                icon: Icon(
                  Icons.close,
                  color: Theme.of(context).colorScheme.onSurfaceVariant,
                ),
              ),
            ],
          ),

          const SizedBox(height: 8),
          Text(
            '${l10n.textCardsBrowseTemplates} ${_filteredTemplates.length} ${l10n.textCardsBeautifulTemplates}',
            style: TextStyle(
              fontSize: 14,
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCategoryTabs() {
    final l10n = AppLocalizations.of(context);
    final categories = ['all', ...EnhancedCardTemplate.getAllCategories()];
    final categoryNames = {
      'all': l10n.textCardsAllCategories,
      'business': l10n.textCardsBusinessCategory,
      'academic': l10n.textCardsAcademicCategory,
      'creative': l10n.textCardsCreativeCategory,
      'minimal': l10n.textCardsMinimalCategory,
      'modern': l10n.textCardsModernCategory,
      'dark': l10n.textCardsDarkCategory,
      'nature': l10n.textCardsNatureCategory,
      'warm': l10n.textCardsWarmCategory,
      'tech': l10n.textCardsTechCategory,
      'elegant': l10n.textCardsElegantCategory,
      'vintage': l10n.textCardsVintageCategory,
    };

    return Container(
      height: 60,
      margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: categories.length,
        itemBuilder: (context, index) {
          final category = categories[index];
          final isSelected = category == _selectedCategory;

          return GestureDetector(
            onTap: () => _filterByCategory(category),
            child: Container(
              margin: const EdgeInsets.only(right: 12),
              padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
              decoration: BoxDecoration(
                color:
                    isSelected
                        ? Theme.of(context).colorScheme.primary
                        : Theme.of(context).colorScheme.surfaceContainerLowest,
                borderRadius: BorderRadius.circular(25),
                border: Border.all(
                  color:
                      isSelected
                          ? Theme.of(context).colorScheme.primary
                          : Theme.of(context).colorScheme.outlineVariant,
                  width: 1,
                ),
                boxShadow:
                    isSelected
                        ? [
                          BoxShadow(
                            color: Theme.of(
                              context,
                            ).colorScheme.primary.withValues(alpha: 0.3),
                            offset: const Offset(0, 2),
                            blurRadius: 8,
                          ),
                        ]
                        : null,
              ),
              child: Center(
                child: Text(
                  categoryNames[category] ?? category,
                  style: TextStyle(
                    color:
                        isSelected
                            ? Theme.of(context).colorScheme.onPrimary
                            : Theme.of(context).colorScheme.onSurface,
                    fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
                    fontSize: 14,
                  ),
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildTemplateGrid() {
    return Padding(
      padding: const EdgeInsets.all(20),
      child: GridView.builder(
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 2,
          childAspectRatio: 0.8,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
        ),
        itemCount: _filteredTemplates.length,
        itemBuilder: (context, index) {
          final template = _filteredTemplates[index];
          return _buildTemplateCard(template);
        },
      ),
    );
  }

  Widget _buildTemplateCard(EnhancedCardTemplate template) {
    debugPrint('构建模板卡片: ${template.name}');
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () {
          debugPrint('InkWell onTap 被触发: ${template.name}');
          _selectTemplate(template);
        },
        borderRadius: BorderRadius.circular(20),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(20),
            boxShadow: [
              BoxShadow(
                color: template.backgroundGradient.colors.first.withValues(
                  alpha: 0.2,
                ),
                offset: const Offset(0, 8),
                blurRadius: 20,
              ),
            ],
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(20),
            child: Stack(
              children: [
                // 模板预览
                Positioned.fill(
                  child: Container(
                    decoration: BoxDecoration(
                      gradient: template.backgroundGradient,
                    ),
                    padding: EdgeInsets.all(template.padding * 0.8),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // 示例标题
                        Text(
                          template.name,
                          style: TextStyle(
                            color: template.titleColor,
                            fontSize: template.titleFontSize * 0.8,
                            fontWeight: template.titleFontWeight,
                          ),
                        ),
                        SizedBox(height: template.padding * 0.3),

                        // 示例内容
                        Expanded(
                          child: Text(
                            template.description,
                            style: TextStyle(
                              color: template.textColor,
                              fontSize: template.contentFontSize * 0.7,
                              fontWeight: template.contentFontWeight,
                              height: template.lineHeight,
                            ),
                            maxLines: 3,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),

                        // 底部装饰
                        Row(
                          children: [
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 8,
                                vertical: 4,
                              ),
                              decoration: BoxDecoration(
                                color: template.accentColor.withValues(
                                  alpha: 0.2,
                                ),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              child: Text(
                                template.category,
                                style: TextStyle(
                                  color: template.accentColor,
                                  fontSize: 10,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                            const Spacer(),
                            if (template.hasDecorationElements)
                              Icon(
                                Icons.auto_awesome,
                                size: 16,
                                color: template.accentColor,
                              ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),

                // 悬浮操作按钮
                Positioned(
                  top: 12,
                  right: 12,
                  child: Material(
                    color: Colors.transparent,
                    child: InkWell(
                      onTap: () {
                        debugPrint('发送按钮被点击: ${template.name}');
                        _navigateToCardCreator(template);
                      },
                      borderRadius: BorderRadius.circular(18),
                      child: Container(
                        width: 36,
                        height: 36,
                        decoration: BoxDecoration(
                          color: Colors.white.withValues(alpha: 0.9),
                          shape: BoxShape.circle,
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withValues(alpha: 0.1),
                              offset: const Offset(0, 2),
                              blurRadius: 8,
                            ),
                          ],
                        ),
                        child: Icon(
                          Icons.send,
                          size: 20,
                          color: AppTheme.primaryColor,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _navigateToCardCreator(EnhancedCardTemplate template) {
    debugPrint('跳转到卡片创建器，使用模板: ${template.name}');

    // 关闭当前模板画廊
    Navigator.pop(context);

    // 显示现代卡片创建器，并传入选中的模板
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder:
          (context) => ModernCardCreator(
            initialTemplate: template,
            onCardCreated: (content, selectedTemplate) {
              // 这里可以处理卡片创建完成后的回调
              // 如果需要，可以调用父组件的回调
              if (widget.onTemplateSelected != null) {
                widget.onTemplateSelected!(selectedTemplate);
              }
            },
          ),
    );
  }

  void _selectTemplate(EnhancedCardTemplate template) {
    debugPrint('模板被选中: ${template.name}');
    debugPrint('onTemplateSelected 是否为空: ${widget.onTemplateSelected == null}');

    if (widget.onTemplateSelected != null) {
      debugPrint('调用 onTemplateSelected 回调');
      widget.onTemplateSelected!(template);
      Navigator.pop(context);
    } else {
      debugPrint('显示模板详情对话框');
      // 显示模板详情或其他操作
      _showTemplateDetails(template);
    }
  }

  void _showTemplateDetails(EnhancedCardTemplate template) {
    final l10n = AppLocalizations.of(context);
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(template.name),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('${l10n.textCardCategory}：${template.category}'),
                const SizedBox(height: 8),
                Text('${l10n.textCardDescription}：${template.description}'),
                const SizedBox(height: 16),
                Container(
                  height: 100,
                  decoration: BoxDecoration(
                    gradient: template.backgroundGradient,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Center(
                    child: Text(
                      l10n.textCardsPreviewEffect,
                      style: TextStyle(
                        color: template.textColor,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text(l10n.textCardClose),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                  _selectTemplate(template);
                },
                child: Text(l10n.textCardUseTemplate),
              ),
            ],
          ),
    );
  }
}
