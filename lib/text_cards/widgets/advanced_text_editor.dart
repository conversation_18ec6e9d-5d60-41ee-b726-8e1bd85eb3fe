import 'package:flutter/material.dart';

import '../models/enhanced_card_template.dart';
import '../models/font_manager.dart';

/// 高级文本编辑器
/// 支持选中文本的内联编辑、实时预览、手势控制等功能
class AdvancedTextEditor extends StatefulWidget {
  final String initialText;
  final EnhancedCardTemplate template;
  final Function(String) onTextChanged;
  final Function(Map<String, dynamic>)? onStyleChanged;
  final VoidCallback? onTemplateEdit;

  const AdvancedTextEditor({
    super.key,
    required this.initialText,
    required this.template,
    required this.onTextChanged,
    this.onStyleChanged,
    this.onTemplateEdit,
  });

  @override
  State<AdvancedTextEditor> createState() => _AdvancedTextEditorState();
}

class _AdvancedTextEditorState extends State<AdvancedTextEditor>
    with TickerProviderStateMixin {
  late TextEditingController _controller;
  late FocusNode _focusNode;
  late AnimationController _toolbarAnimationController;

  // 选中文本相关
  TextSelection? _currentSelection;
  OverlayEntry? _toolbarOverlay;
  bool _isToolbarVisible = false;

  // 当前样式状态
  FontFamily _selectedFont = FontManager.getDefaultFont();
  double _fontSize = 16.0;
  FontWeight _fontWeight = FontWeight.w400;
  TextAlign _textAlign = TextAlign.left;

  @override
  void initState() {
    super.initState();
    _initializeEditor();
    _initializeAnimations();
    _loadTemplateStyles();
  }

  void _initializeEditor() {
    _controller = TextEditingController(text: widget.initialText);
    _focusNode = FocusNode();

    _controller.addListener(_onTextChanged);
    _controller.addListener(_onSelectionChanged);
    _focusNode.addListener(_onFocusChanged);
  }

  void _initializeAnimations() {
    _toolbarAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
  }

  void _loadTemplateStyles() {
    _selectedFont =
        FontManager.getFontByName(widget.template.fontFamily) ??
        FontManager.getDefaultFont();
    _fontSize = widget.template.contentFontSize;
    _fontWeight = widget.template.contentFontWeight;
    _textAlign = widget.template.textAlign;
  }

  @override
  void dispose() {
    _removeToolbarOverlay();
    _toolbarAnimationController.dispose();
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  void _onTextChanged() {
    widget.onTextChanged(_controller.text);
  }

  void _onSelectionChanged() {
    final selection = _controller.selection;
    if (selection != _currentSelection) {
      _currentSelection = selection;
      _handleSelectionChange();
    }
  }

  void _onFocusChanged() {
    // 如果工具栏正在显示，不要因为焦点变化而隐藏工具栏
    // 用户可能正在与工具栏交互
    if (!_focusNode.hasFocus && !_isToolbarVisible) {
      _hideToolbar();
    }
  }

  void _handleSelectionChange() {
    final selection = _controller.selection;
    // 如果工具栏正在显示，不要因为选择变化而隐藏工具栏
    // 用户可能正在调整样式，这会导致选择状态变化
    if ((!selection.isValid || selection.isCollapsed) && !_isToolbarVisible) {
      _hideToolbar();
    }
    // 工具栏现在只通过上下文菜单的"修改样式"选项显示
  }

  void _hideToolbar() {
    if (!_isToolbarVisible) return;

    setState(() {
      _isToolbarVisible = false;
    });

    _toolbarAnimationController.reverse().then((_) {
      _removeToolbarOverlay();
    });
  }

  void _removeToolbarOverlay() {
    _toolbarOverlay?.remove();
    _toolbarOverlay = null;
  }

  @override
  Widget build(BuildContext context) {
    return TextField(
      controller: _controller,
      focusNode: _focusNode,
      expands: true,
      maxLines: null,
      style: FontManager.createTextStyle(
        fontFamily: _selectedFont,
        fontSize: _fontSize,
        fontWeight: _fontWeight,
        color: _getEditableTextColor(),
        height: widget.template.lineHeight,
      ),
      decoration: InputDecoration(
        hintText: '在这里输入文本内容...\n',
        hintStyle: TextStyle(color: _getHintTextColor(), fontSize: _fontSize),
        border: InputBorder.none,
        contentPadding: EdgeInsets.all(16),
      ),
      textAlign: _textAlign,
    );
  }

  /// 获取可编辑文本的颜色，确保在编辑模式下清晰可见
  Color _getEditableTextColor() {
    // 使用主题的 onSurface，适配明暗色模式
    return Theme.of(context).colorScheme.onSurface;
  }

  /// 获取提示文本的颜色
  Color _getHintTextColor() {
    // 使用主题的 onSurfaceVariant，适配明暗色模式
    return Theme.of(context).colorScheme.onSurfaceVariant;
  }

  // 已移除按模板背景动态判断深浅色的逻辑，编辑模式统一使用深色文字
}
