// 内容编辑器 - 专注于纯文本编辑和内容拆分
import 'package:flutter/material.dart';

import '../../generated/l10n/app_localizations.dart';
import 'models/content_models.dart';
import 'visual_renderer_page.dart';

class ContentEditorPage extends StatefulWidget {
  final String? existingContent;
  final String? title;

  const ContentEditorPage({super.key, this.existingContent, this.title});

  @override
  State<ContentEditorPage> createState() => _ContentEditorPageState();
}

class _ContentEditorPageState extends State<ContentEditorPage> {
  late TextEditingController _titleController;
  late TextEditingController _contentController;
  final List<String> _splitMarkers = [];
  bool _isPreviewMode = false;
  RichTextBlock? _previewBlock;
  late AppLocalizations _l10n;

  @override
  void initState() {
    super.initState();
    _titleController = TextEditingController(text: widget.title ?? '');
    _contentController = TextEditingController(
      text: widget.existingContent ?? '',
    );
    _updatePreview();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _l10n = AppLocalizations.of(context);
  }

  @override
  void dispose() {
    _titleController.dispose();
    _contentController.dispose();
    super.dispose();
  }

  void _updatePreview() {
    final content = _contentController.text;
    _previewBlock = ContentParser.parseTextToRichBlock(content);
    setState(() {});
  }

  List<String> _splitContentIntoSections() {
    final content = _contentController.text;
    if (_splitMarkers.isEmpty) {
      return [content];
    }

    final sections = <String>[];
    final lines = content.split('\n');
    List<String> currentSection = [];

    for (final line in lines) {
      if (_splitMarkers.contains(line.trim())) {
        if (currentSection.isNotEmpty) {
          sections.add(currentSection.join('\n'));
          currentSection = [];
        }
      } else {
        currentSection.add(line);
      }
    }

    if (currentSection.isNotEmpty) {
      sections.add(currentSection.join('\n'));
    }

    return sections.where((section) => section.trim().isNotEmpty).toList();
  }

  void _addSplitMarker() {
    showDialog(
      context: context,
      builder:
          (context) => _SplitMarkerDialog(
            onMarkerSelected: (marker) {
              final cursorPos = _contentController.selection.baseOffset;
              final text = _contentController.text;
              final newText =
                  '${text.substring(0, cursorPos)}\n$marker\n${text.substring(cursorPos)}';

              _contentController.text = newText;
              _contentController.selection = TextSelection.fromPosition(
                TextPosition(offset: cursorPos + marker.length + 2),
              );

              if (!_splitMarkers.contains(marker)) {
                _splitMarkers.add(marker);
              }

              _updatePreview();
            },
          ),
    );
  }

  void _proceedToRenderer() {
    final sections = _splitContentIntoSections();
    final title = _titleController.text.trim();

    if (sections.length == 1) {
      // 单个卡片
      Navigator.of(context).push(
        MaterialPageRoute(
          builder:
              (context) => VisualRendererPage(
                content: sections.first,
                title: title.isEmpty ? _l10n.textCardsUnnamedCard : title,
                mode: 'single',
              ),
        ),
      );
    } else {
      // 多个卡片
      Navigator.of(context).push(
        MaterialPageRoute(
          builder:
              (context) => VisualRendererPage(
                sections: sections,
                title: title.isEmpty ? _l10n.textCardsUnnamedDocument : title,
                mode: 'multiple',
              ),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    return Scaffold(
      appBar: AppBar(
        title: Text(_l10n.textCardsContentEditor),
        backgroundColor: colorScheme.surface,
        foregroundColor: colorScheme.onSurface,
        elevation: 0,
        actions: [
          IconButton(
            icon: Icon(
              _isPreviewMode ? Icons.edit : Icons.visibility,
              color: colorScheme.onSurface,
            ),
            onPressed: () {
              setState(() {
                _isPreviewMode = !_isPreviewMode;
                if (_isPreviewMode) {
                  _updatePreview();
                }
              });
            },
          ),
          IconButton(
            icon: Icon(Icons.cut, color: colorScheme.onSurface),
            onPressed: _addSplitMarker,
            tooltip: _l10n.textCardsAddSplitMarker,
          ),
          IconButton(
            icon: Icon(Icons.arrow_forward, color: colorScheme.onSurface),
            onPressed: _proceedToRenderer,
            tooltip: _l10n.textCardsEnterRenderer,
          ),
        ],
      ),
      body: Column(
        children: [
          // 标题输入
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: colorScheme.surfaceContainerLow,
              border: Border(
                bottom: BorderSide(color: colorScheme.outlineVariant),
              ),
            ),
            child: TextField(
              controller: _titleController,
              decoration: InputDecoration(
                hintText: _l10n.textCardsEnterTitle,
                border: InputBorder.none,
                hintStyle: TextStyle(color: colorScheme.onSurfaceVariant),
              ),
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w500,
                color: colorScheme.onSurface,
              ),
            ),
          ),

          // 分割线状态
          if (_splitMarkers.isNotEmpty)
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              color: colorScheme.primary.withValues(alpha: 0.08),
              child: Row(
                children: [
                  Icon(
                    Icons.info_outline,
                    size: 16,
                    color: colorScheme.primary,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    _l10n.textCardsSplitMarkerInfo(
                      _splitMarkers.length,
                      _splitContentIntoSections().length,
                    ),
                    style: TextStyle(color: colorScheme.primary, fontSize: 12),
                  ),
                ],
              ),
            ),

          // 主要内容区域
          Expanded(
            child: _isPreviewMode ? _buildPreviewArea() : _buildEditArea(),
          ),
        ],
      ),
    );
  }

  Widget _buildEditArea() {
    final colorScheme = Theme.of(context).colorScheme;
    return Container(
      padding: const EdgeInsets.all(16),
      child: TextField(
        controller: _contentController,
        maxLines: null,
        expands: true,
        textAlignVertical: TextAlignVertical.top,
        decoration: InputDecoration(
          hintText: _l10n.textCardsEnterContentHint,
          border: InputBorder.none,
          hintStyle: TextStyle(color: colorScheme.onSurfaceVariant),
        ),
        style: TextStyle(
          fontSize: 16,
          height: 1.5,
          color: colorScheme.onSurface,
        ),
        onChanged: (_) => _updatePreview(),
      ),
    );
  }

  Widget _buildPreviewArea() {
    final colorScheme = Theme.of(context).colorScheme;
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            _l10n.textCardsTextPreview,
            style: TextStyle(
              fontSize: 14,
              color: colorScheme.onSurfaceVariant,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 16),
          Expanded(
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                border: Border.all(color: colorScheme.outlineVariant),
                borderRadius: BorderRadius.circular(8),
                color: colorScheme.surface,
              ),
              child:
                  _previewBlock != null
                      ? RichText(
                        text: _previewBlock!.toTextSpan(),
                        textAlign: TextAlign.left,
                      )
                      : Text(
                        _l10n.textCardsPreviewWillAppear,
                        style: TextStyle(color: colorScheme.onSurfaceVariant),
                      ),
            ),
          ),
        ],
      ),
    );
  }
}

class _SplitMarkerDialog extends StatefulWidget {
  final Function(String) onMarkerSelected;

  const _SplitMarkerDialog({required this.onMarkerSelected});

  @override
  State<_SplitMarkerDialog> createState() => __SplitMarkerDialogState();
}

class __SplitMarkerDialogState extends State<_SplitMarkerDialog> {
  final TextEditingController _customController = TextEditingController();
  late AppLocalizations _l10n;

  @override
  void initState() {
    super.initState();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _l10n = AppLocalizations.of(context);
  }

  final List<String> _predefinedMarkers = [
    '--- 分割线 ---',
    '=== 新卡片 ===',
    '### 分段 ###',
    '>>> 下一部分 >>>',
    '*** 拆分点 ***',
  ];

  @override
  void dispose() {
    _customController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text(_l10n.textCardsSelectSplitMarker),
      content: SizedBox(
        width: double.maxFinite,
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              _l10n.textCardsPredefinedMarkers,
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
            const SizedBox(height: 8),
            ...(_predefinedMarkers.map(
              (marker) => ListTile(
                title: Text(marker),
                onTap: () {
                  Navigator.of(context).pop();
                  widget.onMarkerSelected(marker);
                },
                dense: true,
              ),
            )),
            const SizedBox(height: 16),
            Text(
              _l10n.textCardsCustomMarker,
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
            const SizedBox(height: 8),
            TextField(
              controller: _customController,
              decoration: InputDecoration(
                hintText: _l10n.textCardsEnterCustomMarker,
                border: const OutlineInputBorder(),
              ),
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: Text(_l10n.cancel),
        ),
        ElevatedButton(
          onPressed: () {
            final custom = _customController.text.trim();
            if (custom.isNotEmpty) {
              Navigator.of(context).pop();
              widget.onMarkerSelected(custom);
            }
          },
          child: Text(_l10n.textCardsUseCustom),
        ),
      ],
    );
  }
}
