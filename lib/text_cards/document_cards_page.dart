import 'package:flutter/material.dart';
import '../../generated/l10n/app_localizations.dart';
import 'models/document_model.dart';
import 'models/text_card_model.dart';
import 'widgets/text_card_widget.dart';
import 'widgets/card_template_selector.dart';
import 'export/card_export_page.dart';
import 'document_splitter_page.dart';

class DocumentCardsPage extends StatefulWidget {
  final DocumentModel document;

  const DocumentCardsPage({super.key, required this.document});

  @override
  State<DocumentCardsPage> createState() => _DocumentCardsPageState();
}

class _DocumentCardsPageState extends State<DocumentCardsPage> {
  late DocumentModel _document;
  bool _isGridView = true;
  late AppLocalizations _l10n;

  @override
  void initState() {
    super.initState();
    _document = widget.document;
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _l10n = AppLocalizations.of(context);
  }

  void _changeGlobalTemplate() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder:
          (context) => CardTemplateSelector(
            onTemplateSelected: (templateId) {
              setState(() {
                // 更新全局模板
                _document = _document.copyWith(
                  globalTemplateId: templateId,
                  updatedAt: DateTime.now(),
                );
                // 更新所有卡片的模板
                final updatedCards =
                    _document.cards
                        .map((card) => card.copyWith(templateId: templateId))
                        .toList();
                _document = _document.copyWith(cards: updatedCards);
              });
              Navigator.pop(context);
            },
          ),
    );
  }

  void _editDocument() async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => DocumentSplitterPage(document: _document),
      ),
    );

    if (result != null && result is DocumentModel) {
      setState(() {
        _document = result;
      });
    }
  }

  void _exportCards() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => CardExportPage(document: _document),
      ),
    );
  }

  void _exportSingleCard(TextCardModel card) {
    // 创建只包含单个卡片的临时文档
    final tempDocument = DocumentModel(
      id: 'temp_${card.id}',
      title: card.title,
      originalText: card.content,
      cards: [card],
      globalTemplateId: card.templateId,
      createdAt: DateTime.now(),
    );

    Navigator.push(
      context,
      MaterialPageRoute(
        builder:
            (context) =>
                CardExportPage(document: tempDocument, isSingleCard: true),
      ),
    );
  }

  void _showCardOptions(TextCardModel card) {
    final colorScheme = Theme.of(context).colorScheme;
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder:
          (context) => Container(
            padding: const EdgeInsets.all(20),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  width: 40,
                  height: 4,
                  decoration: BoxDecoration(
                    color: colorScheme.onSurfaceVariant.withValues(alpha: 0.3),
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
                const SizedBox(height: 16),
                Text(
                  card.title,
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: colorScheme.onSurface,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: 20),
                ListTile(
                  leading: Icon(Icons.image, color: colorScheme.primary),
                  title: Text(_l10n.textCardExportAsImage),
                  subtitle: Text(_l10n.textCardExportAsImageDesc),
                  onTap: () {
                    Navigator.pop(context);
                    _exportSingleCard(card);
                  },
                ),
                ListTile(
                  leading: Icon(Icons.edit, color: colorScheme.primary),
                  title: Text(_l10n.textCardEditCard),
                  subtitle: Text(_l10n.textCardEditCardDesc),
                  onTap: () {
                    Navigator.pop(context);
                    // TODO: 实现单卡片编辑
                  },
                ),
                ListTile(
                  leading: Icon(Icons.delete, color: colorScheme.error),
                  title: Text(_l10n.textCardDeleteCard),
                  subtitle: Text(_l10n.textCardDeleteCardDesc),
                  onTap: () {
                    Navigator.pop(context);
                    _deleteCard(card);
                  },
                ),
              ],
            ),
          ),
    );
  }

  void _deleteCard(TextCardModel card) {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(_l10n.textCardDeleteCard),
            content: Text(_l10n.textCardDeleteCardConfirm(card.title)),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text(_l10n.cancel),
              ),
              TextButton(
                onPressed: () {
                  setState(() {
                    final updatedCards =
                        _document.cards.where((c) => c.id != card.id).toList();
                    _document = _document.copyWith(
                      cards: updatedCards,
                      updatedAt: DateTime.now(),
                    );
                  });
                  Navigator.pop(context);
                },
                style: TextButton.styleFrom(foregroundColor: Colors.red),
                child: Text(_l10n.delete),
              ),
            ],
          ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    return Scaffold(
      backgroundColor: colorScheme.surface,
      appBar: AppBar(
        title: Text(
          _document.title,
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: colorScheme.onSurface,
          ),
        ),
        backgroundColor: colorScheme.surface,
        elevation: 0,
        actions: [
          IconButton(
            icon: Icon(
              _isGridView ? Icons.view_list : Icons.grid_view,
              color: colorScheme.onSurface,
            ),
            onPressed: () {
              setState(() {
                _isGridView = !_isGridView;
              });
            },
            tooltip:
                _isGridView ? _l10n.textCardListView : _l10n.textCardGridView,
          ),
          PopupMenuButton<String>(
            icon: Icon(Icons.more_vert, color: colorScheme.onSurface),
            onSelected: (value) {
              switch (value) {
                case 'edit':
                  _editDocument();
                  break;
                case 'template':
                  _changeGlobalTemplate();
                  break;
                case 'export':
                  _exportCards();
                  break;
              }
            },
            itemBuilder:
                (context) => [
                  PopupMenuItem(
                    value: 'edit',
                    child: Row(
                      children: [
                        const Icon(Icons.edit, size: 20),
                        const SizedBox(width: 8),
                        Text(_l10n.textCardEditDocument),
                      ],
                    ),
                  ),
                  PopupMenuItem(
                    value: 'template',
                    child: Row(
                      children: [
                        const Icon(Icons.palette, size: 20),
                        const SizedBox(width: 8),
                        Text(_l10n.textCardUniformStyle),
                      ],
                    ),
                  ),
                  PopupMenuItem(
                    value: 'export',
                    child: Row(
                      children: [
                        const Icon(Icons.image, size: 20),
                        const SizedBox(width: 8),
                        Text(_l10n.textCardExportImage),
                      ],
                    ),
                  ),
                ],
          ),
          const SizedBox(width: 8),
        ],
      ),
      body: Column(
        children: [
          // 文档信息栏
          Container(
            color: colorScheme.surface,
            padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
            child: Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        '${_l10n.textCardTotalCards} ${_document.cards.length} ${_l10n.textCardCards}',
                        style: TextStyle(
                          fontSize: 16,
                          fontWeight: FontWeight.w500,
                          color: colorScheme.onSurface,
                        ),
                      ),
                      const SizedBox(height: 2),
                      Text(
                        '${_l10n.textCardTemplate}：${CardTemplate.getBuiltInTemplates().firstWhere((t) => t.id == _document.globalTemplateId).name}',
                        style: TextStyle(
                          fontSize: 12,
                          color: colorScheme.onSurfaceVariant,
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    color: colorScheme.primary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: Text(
                    _formatTime(_document.updatedAt ?? _document.createdAt),
                    style: TextStyle(
                      fontSize: 11,
                      color: colorScheme.primary,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),

          const Divider(height: 1),

          // 卡片列表
          Expanded(
            child:
                _document.cards.isEmpty
                    ? _buildEmptyState()
                    : _isGridView
                    ? _buildGridView()
                    : _buildListView(),
          ),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        heroTag: 'document_cards_fab',
        backgroundColor: colorScheme.primary,
        onPressed: _exportCards,
        child: Icon(Icons.image, color: colorScheme.onPrimary),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.text_snippet_outlined,
            size: 80,
            color: Theme.of(context).colorScheme.onSurfaceVariant,
          ),
          const SizedBox(height: 16),
          Text(
            _l10n.textCardNoCardsInDocument,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: Theme.of(context).colorScheme.onSurface,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _l10n.textCardEditDocumentToAddCards,
            style: TextStyle(
              fontSize: 14,
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),
          const SizedBox(height: 24),
          ElevatedButton.icon(
            onPressed: _editDocument,
            icon: const Icon(Icons.edit),
            label: Text(_l10n.textCardEditDocument),
            style: ElevatedButton.styleFrom(
              backgroundColor: Theme.of(context).colorScheme.primary,
              foregroundColor: Theme.of(context).colorScheme.onPrimary,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildGridView() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: GridView.builder(
        gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
          crossAxisCount: 1,
          childAspectRatio: 1.5,
          mainAxisSpacing: 16,
        ),
        itemCount: _document.cards.length,
        itemBuilder: (context, index) {
          final card = _document.cards[index];
          return TextCardWidget(
            card: card,
            onTap: () => _showCardOptions(card),
            onDelete: () => _deleteCard(card),
          );
        },
      ),
    );
  }

  Widget _buildListView() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _document.cards.length,
      itemBuilder: (context, index) {
        final card = _document.cards[index];
        return Padding(
          padding: const EdgeInsets.only(bottom: 16),
          child: TextCardWidget(
            card: card,
            isCompact: true,
            onTap: () => _showCardOptions(card),
            onDelete: () => _deleteCard(card),
          ),
        );
      },
    );
  }

  String _formatTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);

    if (difference.inDays > 0) {
      return '${difference.inDays}${_l10n.textCardDaysAgo}';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}${_l10n.textCardHoursAgo}';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}${_l10n.textCardMinutesAgo}';
    } else {
      return _l10n.textCardJustNow;
    }
  }
}
