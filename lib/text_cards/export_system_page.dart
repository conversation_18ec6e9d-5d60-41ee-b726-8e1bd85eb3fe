// 导出系统 - 支持单个卡片和批量导出
import 'dart:io';
import 'dart:ui' as ui;

import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:path_provider/path_provider.dart';
import 'package:saver_gallery/saver_gallery.dart';
import 'package:share_plus/share_plus.dart';

import '../../generated/l10n/app_localizations.dart';
import 'models/content_models.dart';

class ExportSystemPage extends StatefulWidget {
  final List<RenderCard> cards;
  final String title;

  const ExportSystemPage({super.key, required this.cards, required this.title});

  @override
  State<ExportSystemPage> createState() => _ExportSystemPageState();
}

class _ExportSystemPageState extends State<ExportSystemPage> {
  final List<GlobalKey> _cardKeys = [];
  bool _isExporting = false;
  int _currentExportIndex = 0;

  @override
  void initState() {
    super.initState();
    _cardKeys.addAll(
      List.generate(widget.cards.length, (index) => GlobalKey()),
    );
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    final colorScheme = Theme.of(context).colorScheme;
    return Scaffold(
      appBar: AppBar(
        title: Text('${l10n.export} - ${widget.title}'),
        backgroundColor: colorScheme.surface,
        foregroundColor: colorScheme.onSurface,
        elevation: 0,
        actions: [
          IconButton(
            icon: Icon(Icons.share, color: colorScheme.onSurface),
            onPressed: _isExporting ? null : _exportAndShare,
            tooltip: l10n.textCardShare,
          ),
          IconButton(
            icon: Icon(Icons.save, color: colorScheme.onSurface),
            onPressed: _isExporting ? null : _saveToGallery,
            tooltip: l10n.textCardSaveToGallery,
          ),
        ],
      ),
      body: Column(
        children: [
          // 导出状态
          if (_isExporting) _buildExportProgress(),

          // 导出选项
          _buildExportOptions(),

          // 卡片预览列表
          Expanded(
            child: ListView.builder(
              padding: const EdgeInsets.all(16),
              itemCount: widget.cards.length,
              itemBuilder: (context, index) {
                return Padding(
                  padding: const EdgeInsets.only(bottom: 24),
                  child: _buildExportableCard(widget.cards[index], index),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildExportProgress() {
    final l10n = AppLocalizations.of(context);
    final colorScheme = Theme.of(context).colorScheme;
    final progress = _currentExportIndex / widget.cards.length;
    return Container(
      padding: const EdgeInsets.all(16),
      color: colorScheme.primary.withValues(alpha: 0.06),
      child: Column(
        children: [
          Row(
            children: [
              Icon(Icons.file_download, color: colorScheme.primary),
              const SizedBox(width: 8),
              Text(
                '${l10n.textCardExporting} $_currentExportIndex/${widget.cards.length}',
                style: TextStyle(
                  color: colorScheme.primary,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          LinearProgressIndicator(value: progress),
        ],
      ),
    );
  }

  Widget _buildExportOptions() {
    final l10n = AppLocalizations.of(context);
    final colorScheme = Theme.of(context).colorScheme;
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: colorScheme.surface,
        border: Border(bottom: BorderSide(color: colorScheme.outlineVariant)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            l10n.textCardExportOptions,
            style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: _isExporting ? null : _exportAndShare,
                  icon: const Icon(Icons.share),
                  label: Text(l10n.textCardShare),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: colorScheme.primary,
                    foregroundColor: colorScheme.onPrimary,
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: _isExporting ? null : _saveToGallery,
                  icon: const Icon(Icons.save),
                  label: Text(l10n.textCardSaveToGallery),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: colorScheme.secondary,
                    foregroundColor: colorScheme.onSecondary,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            widget.cards.length == 1
                ? l10n.textCardExportSingleCard
                : '${l10n.textCardExportMultipleCards} ${widget.cards.length} ${l10n.textCardImages}',
            style: TextStyle(fontSize: 12, color: colorScheme.onSurfaceVariant),
          ),
        ],
      ),
    );
  }

  Widget _buildExportableCard(RenderCard card, int index) {
    final l10n = AppLocalizations.of(context);
    final colorScheme = Theme.of(context).colorScheme;
    return RepaintBoundary(
      key: _cardKeys[index],
      child: Container(
        decoration: BoxDecoration(
          color: colorScheme.surface,
          border: Border.all(color: colorScheme.outlineVariant),
          borderRadius: BorderRadius.circular(8),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.06),
              spreadRadius: 1,
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          children: [
            // 卡片标题栏
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: colorScheme.surfaceContainerLowest,
                border: Border(
                  bottom: BorderSide(color: colorScheme.outlineVariant),
                ),
                borderRadius: const BorderRadius.only(
                  topLeft: Radius.circular(8),
                  topRight: Radius.circular(8),
                ),
              ),
              child: Row(
                children: [
                  Text(
                    '${l10n.textCardCard} ${index + 1} ${l10n.textCardCardNumber}',
                    style: TextStyle(
                      fontSize: 12,
                      color: colorScheme.onSurfaceVariant,
                    ),
                  ),
                  const Spacer(),
                  Text(
                    card.title,
                    style: const TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ),
            ),

            // 实际的卡片内容
            _buildCardContent(card),
          ],
        ),
      ),
    );
  }

  Widget _buildCardContent(RenderCard card) {
    return Container(
      width: double.infinity,
      margin: card.config.margin,
      padding: card.config.padding,
      decoration: BoxDecoration(
        color: card.config.backgroundColor,
        border: Border.all(
          color: card.config.borderColor,
          width: card.config.borderWidth,
        ),
        borderRadius: BorderRadius.circular(card.config.borderRadius),
        boxShadow: card.config.shadow != null ? [card.config.shadow!] : null,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 卡片标题
          if (card.title.isNotEmpty) ...[
            Text(
              card.title,
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.onSurface,
              ),
            ),
            const SizedBox(height: 16),
          ],

          // 富文本内容
          RichText(text: card.content.toTextSpan(), textAlign: TextAlign.left),
        ],
      ),
    );
  }

  Future<void> _exportAndShare() async {
    final l10n = AppLocalizations.of(context);
    setState(() {
      _isExporting = true;
      _currentExportIndex = 0;
    });

    try {
      final imageFiles = await _captureCards();

      if (imageFiles.isNotEmpty) {
        await SharePlus.instance.share(
          ShareParams(
            files: imageFiles.map((file) => XFile(file.path)).toList(),
            text: '${l10n.textCardShareFromApp} ${widget.title}',
          ),
        );
      }
    } catch (e) {
      _showError('${l10n.textCardShareFailed}: $e');
    } finally {
      setState(() {
        _isExporting = false;
        _currentExportIndex = 0;
      });
    }
  }

  Future<void> _saveToGallery() async {
    final l10n = AppLocalizations.of(context);
    setState(() {
      _isExporting = true;
      _currentExportIndex = 0;
    });

    try {
      final imageFiles = await _captureCards();

      int savedCount = 0;
      for (final file in imageFiles) {
        final result = await SaverGallery.saveFile(
          filePath: file.path,
          fileName:
              'contentpal_${DateTime.now().millisecondsSinceEpoch}_$savedCount.png',
          skipIfExists: false,
        );

        if (result.isSuccess) {
          savedCount++;
        }
      }

      if (savedCount > 0) {
        _showSuccess(
          '${l10n.textCardSavedToGallery} $savedCount ${l10n.textCardCardsToGallery}',
        );
      } else {
        _showError(l10n.textCardSaveFailed('unknown'));
      }
    } catch (e) {
      _showError(l10n.textCardSaveFailed(e));
    } finally {
      setState(() {
        _isExporting = false;
        _currentExportIndex = 0;
      });
    }
  }

  Future<List<File>> _captureCards() async {
    final tempDir = await getTemporaryDirectory();
    final files = <File>[];

    for (int i = 0; i < _cardKeys.length; i++) {
      setState(() {
        _currentExportIndex = i + 1;
      });

      try {
        final boundary =
            _cardKeys[i].currentContext?.findRenderObject()
                as RenderRepaintBoundary?;

        if (boundary != null) {
          final image = await boundary.toImage(pixelRatio: 3.0);
          final byteData = await image.toByteData(
            format: ui.ImageByteFormat.png,
          );
          final bytes = byteData!.buffer.asUint8List();

          final file = File(
            '${tempDir.path}/card_${i}_${DateTime.now().millisecondsSinceEpoch}.png',
          );
          await file.writeAsBytes(bytes);
          files.add(file);
        }
      } catch (e) {
        debugPrint('捕获卡片 $i 失败: $e');
      }
    }

    return files;
  }

  void _showSuccess(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(Icons.check_circle, color: Colors.white, size: 20),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                message,
                style: const TextStyle(fontWeight: FontWeight.w500),
              ),
            ),
          ],
        ),
        backgroundColor: Colors.green,
        behavior: SnackBarBehavior.floating,
        duration: const Duration(seconds: 3),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        margin: const EdgeInsets.all(16),
      ),
    );
  }

  void _showError(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(Icons.error_outline, color: Colors.white, size: 20),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                message,
                style: const TextStyle(fontWeight: FontWeight.w500),
              ),
            ),
          ],
        ),
        backgroundColor: Colors.red,
        behavior: SnackBarBehavior.floating,
        duration: const Duration(seconds: 4),
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(8)),
        margin: const EdgeInsets.all(16),
      ),
    );
  }
}
