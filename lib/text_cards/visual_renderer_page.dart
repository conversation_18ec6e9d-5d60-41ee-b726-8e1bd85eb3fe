// 可视化渲染器 - 真正的阅读应用式文本选择交互
import 'package:flutter/material.dart';
import 'package:uuid/uuid.dart';

import 'export_system_page.dart';
import '../../generated/l10n/app_localizations.dart';
import 'models/content_models.dart';
import 'widgets/floating_style_toolbar.dart';

class VisualRendererPage extends StatefulWidget {
  final String? content;
  final List<String>? sections;
  final String title;
  final String mode; // 'single' or 'multiple'

  const VisualRendererPage({
    super.key,
    this.content,
    this.sections,
    required this.title,
    required this.mode,
  });

  @override
  State<VisualRendererPage> createState() => _VisualRendererPageState();
}

class _VisualRendererPageState extends State<VisualRendererPage> {
  late List<RenderCard> _cards;
  int _currentCardIndex = 0;
  late AppLocalizations _l10n;

  // 文本选择相关
  TextSelection? _currentSelection;
  bool _isTextSelected = false;
  OverlayEntry? _toolbarOverlay;

  // 用于检测文本选择的控制器
  final TextEditingController _textController = TextEditingController();
  final FocusNode _focusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    _initializeCards();
    _setupTextSelection();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _l10n = AppLocalizations.of(context);
  }

  @override
  void dispose() {
    _hideToolbar();
    _textController.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  void _setupTextSelection() {
    _textController.addListener(() {
      // 监听文本选择变化
      final selection = _textController.selection;
      if (selection.start != selection.end) {
        // 有文本被选中
        setState(() {
          _currentSelection = selection;
          _isTextSelected = true;
        });
        _showToolbarForSelection(selection);
      } else {
        // 无文本选中
        setState(() {
          _currentSelection = null;
          _isTextSelected = false;
        });
        _hideToolbar();
      }
    });
  }

  void _initializeCards() {
    final uuid = const Uuid();
    final now = DateTime.now();

    if (widget.mode == 'single') {
      // 单个卡片模式
      final richTextBlock = ContentParser.parseTextToRichBlock(
        widget.content ?? '',
        id: 'main_content',
      );
      _cards = [
        RenderCard(
          id: uuid.v4(),
          title: widget.title,
          content: richTextBlock,
          config: const CardConfig(),
          createdAt: now,
          updatedAt: now,
        ),
      ];
    } else {
      // 多个卡片模式
      _cards =
          (widget.sections ?? []).asMap().entries.map((entry) {
            final index = entry.key;
            final section = entry.value;
            final richTextBlock = ContentParser.parseTextToRichBlock(
              section,
              id: 'section_$index',
            );

            return RenderCard(
              id: uuid.v4(),
              config: CardConfig(),
              content: richTextBlock,
              createdAt: now,
              title: '${widget.title} - Part ${index + 1}',
              updatedAt: now,
            );
          }).toList();
    }

    // 设置初始文本内容
    if (_cards.isNotEmpty) {
      _textController.text = _cards[_currentCardIndex].content.content;
    }
  }

  RenderCard get _currentCard => _cards[_currentCardIndex];

  void _showToolbarForSelection(TextSelection selection) {
    _hideToolbar(); // 隐藏之前的工具栏

    // 延迟显示工具栏，等待布局完成
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final RenderBox? renderBox = context.findRenderObject() as RenderBox?;
      if (renderBox != null) {
        // 计算选择区域的大概位置（这里简化处理）
        final position = Offset(
          MediaQuery.of(context).size.width / 2, // 居中显示
          200, // 固定高度
        );

        _toolbarOverlay = OverlayEntry(
          builder:
              (context) => Positioned(
                left: position.dx - 150, // 工具栏宽度的一半
                top: position.dy - 60, // 工具栏高度 + 间距
                child: FloatingStyleToolbar(
                  selectedText: _getSelectedText(),
                  onStyleChanged: _applyStyleToSelection,
                  onClose: _clearSelection,
                ),
              ),
        );

        Overlay.of(context).insert(_toolbarOverlay!);
      }
    });
  }

  void _hideToolbar() {
    if (_toolbarOverlay != null) {
      _toolbarOverlay!.remove();
      _toolbarOverlay = null;
    }
  }

  void _clearSelection() {
    _textController.selection = TextSelection.collapsed(
      offset: _textController.selection.end,
    );
    setState(() {
      _currentSelection = null;
      _isTextSelected = false;
    });
    _hideToolbar();
  }

  String _getSelectedText() {
    if (_currentSelection == null) return '';

    final text = _textController.text;
    return text.substring(_currentSelection!.start, _currentSelection!.end);
  }

  void _applyStyleToSelection(TextStyleConfig style) {
    if (_currentSelection == null) return;

    setState(() {
      final currentCard = _currentCard;
      final newRange = TextStyleRange(
        start: _currentSelection!.start,
        end: _currentSelection!.end,
        style: style,
      );

      final updatedContent = currentCard.content.addStyleRange(newRange);

      _cards[_currentCardIndex] = currentCard.copyWith(
        content: updatedContent,
        updatedAt: DateTime.now(),
      );
    });

    // 保持选择状态，让用户可以继续调整样式
  }

  void _switchCard(int newIndex) {
    if (newIndex == _currentCardIndex) return;

    _hideToolbar();
    _clearSelection();

    setState(() {
      _currentCardIndex = newIndex;
      _textController.text = _cards[_currentCardIndex].content.content;
    });
  }

  void _exportCurrentCard() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder:
            (context) => ExportSystemPage(
              cards: [_currentCard],
              title: _currentCard.title,
            ),
      ),
    );
  }

  void _exportAllCards() {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder:
            (context) => ExportSystemPage(cards: _cards, title: widget.title),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    return Scaffold(
      appBar: AppBar(
        title: Text(
          '${_l10n.textCardTextStyleCustomization} - ${_currentCard.title}',
        ),
        backgroundColor: colorScheme.surface,
        foregroundColor: colorScheme.onSurface,
        elevation: 0,
        actions: [
          if (_cards.length > 1) ...[
            IconButton(
              icon: Icon(Icons.navigate_before, color: colorScheme.onSurface),
              onPressed:
                  _currentCardIndex > 0
                      ? () => _switchCard(_currentCardIndex - 1)
                      : null,
            ),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              margin: const EdgeInsets.symmetric(vertical: 12),
              decoration: BoxDecoration(
                color: colorScheme.surfaceContainerHigh,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                '${_currentCardIndex + 1}/${_cards.length}',
                style: TextStyle(fontSize: 12, color: colorScheme.onSurface),
              ),
            ),
            IconButton(
              icon: Icon(Icons.navigate_next, color: colorScheme.onSurface),
              onPressed:
                  _currentCardIndex < _cards.length - 1
                      ? () => _switchCard(_currentCardIndex + 1)
                      : null,
            ),
          ],
          PopupMenuButton<String>(
            icon: Icon(Icons.more_vert, color: colorScheme.onSurface),
            onSelected: (value) {
              switch (value) {
                case 'export_current':
                  _exportCurrentCard();
                  break;
                case 'export_all':
                  _exportAllCards();
                  break;
                case 'reset_styles':
                  _resetStyles();
                  break;
                case 'clear_selection':
                  _clearSelection();
                  break;
              }
            },
            itemBuilder:
                (context) => [
                  PopupMenuItem(
                    value: 'export_current',
                    child: Row(
                      children: [
                        const Icon(Icons.share, size: 18),
                        const SizedBox(width: 8),
                        Text(_l10n.textCardExportCurrentCard),
                      ],
                    ),
                  ),
                  if (_cards.length > 1)
                    PopupMenuItem(
                      value: 'export_all',
                      child: Row(
                        children: [
                          const Icon(Icons.share_outlined, size: 18),
                          const SizedBox(width: 8),
                          Text(_l10n.textCardBatchExport),
                        ],
                      ),
                    ),
                  PopupMenuItem(
                    value: 'clear_selection',
                    child: Row(
                      children: [
                        Icon(Icons.clear_all, size: 18),
                        SizedBox(width: 8),
                        Text(_l10n.textCardClearSelection),
                      ],
                    ),
                  ),
                  PopupMenuItem(
                    value: 'reset_styles',
                    child: Row(
                      children: [
                        Icon(Icons.refresh, size: 18),
                        SizedBox(width: 8),
                        Text(_l10n.textCardResetStyle),
                      ],
                    ),
                  ),
                ],
          ),
        ],
      ),
      body: Container(
        padding: const EdgeInsets.all(24),
        color: colorScheme.surfaceContainerLowest,
        child: Center(
          child: Container(
            constraints: const BoxConstraints(maxWidth: 600),
            child: Column(
              children: [
                // 使用说明
                Container(
                  padding: const EdgeInsets.all(12),
                  margin: const EdgeInsets.only(bottom: 16),
                  decoration: BoxDecoration(
                    color: colorScheme.primary.withValues(alpha: 0.08),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: colorScheme.primary.withValues(alpha: 0.3),
                    ),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.info_outline,
                        color: colorScheme.primary,
                        size: 16,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          _l10n.textCardSelectionInstructions,
                          style: TextStyle(
                            color: colorScheme.primary,
                            fontSize: 14,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

                // 可选择的文本渲染区域
                Expanded(child: _buildSelectableTextCard()),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSelectableTextCard() {
    final card = _currentCard;

    return Container(
      width: double.infinity,
      padding: card.config.padding,
      decoration: BoxDecoration(
        color: card.config.backgroundColor,
        border: Border.all(
          color: card.config.borderColor,
          width: card.config.borderWidth,
        ),
        borderRadius: BorderRadius.circular(card.config.borderRadius),
        boxShadow: card.config.shadow != null ? [card.config.shadow!] : null,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 卡片标题
          if (card.title.isNotEmpty) ...[
            Text(
              card.title,
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: Theme.of(context).colorScheme.onSurface,
              ),
            ),
            const SizedBox(height: 16),
          ],

          // 可选择的富文本内容
          Expanded(
            child: Stack(
              children: [
                // 隐藏的TextField用于文本选择
                Positioned.fill(
                  child: TextField(
                    controller: _textController,
                    focusNode: _focusNode,
                    maxLines: null,
                    expands: true,
                    style: const TextStyle(color: Colors.transparent),
                    decoration: const InputDecoration(
                      border: InputBorder.none,
                      contentPadding: EdgeInsets.zero,
                    ),
                    cursorColor: Colors.transparent,
                    enableInteractiveSelection: true,
                    showCursor: false,
                  ),
                ),

                // 显示的富文本内容
                Positioned.fill(
                  child: IgnorePointer(
                    child: SingleChildScrollView(
                      child: RichText(
                        text: card.content.toTextSpan(),
                        textAlign: TextAlign.left,
                      ),
                    ),
                  ),
                ),

                // 选择高亮覆盖层
                if (_isTextSelected && _currentSelection != null)
                  Positioned.fill(
                    child: IgnorePointer(
                      child: CustomPaint(
                        painter: SelectionHighlightPainter(
                          selection: _currentSelection!,
                          text: _textController.text,
                          textStyle: card.content.defaultStyle.toTextStyle(),
                          highlightColor: Theme.of(
                            context,
                          ).colorScheme.primary.withValues(alpha: 0.3),
                        ),
                      ),
                    ),
                  ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _resetStyles() {
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(_l10n.textCardResetStyle),
            content: Text(_l10n.textCardResetStyleConfirm),
            actions: [
              TextButton(
                onPressed: () => Navigator.of(context).pop(),
                child: Text(_l10n.cancel),
              ),
              ElevatedButton(
                onPressed: () {
                  Navigator.of(context).pop();
                  setState(() {
                    final card = _currentCard;
                    final resetContent = card.content.copyWith(
                      styleRanges: [],
                      defaultStyle: const TextStyleConfig(),
                    );

                    _cards[_currentCardIndex] = card.copyWith(
                      content: resetContent,
                      updatedAt: DateTime.now(),
                    );
                  });
                  _clearSelection();
                },
                child: Text(_l10n.confirm),
              ),
            ],
          ),
    );
  }
}

/// 自定义绘制器用于高亮显示选中的文本
class SelectionHighlightPainter extends CustomPainter {
  final TextSelection selection;
  final String text;
  final TextStyle textStyle;
  final Color highlightColor;

  SelectionHighlightPainter({
    required this.selection,
    required this.text,
    required this.textStyle,
    required this.highlightColor,
  });

  @override
  void paint(Canvas canvas, Size size) {
    if (selection.start == selection.end) return;

    final textPainter = TextPainter(
      text: TextSpan(text: text, style: textStyle),
      textDirection: TextDirection.ltr,
    );

    textPainter.layout(maxWidth: size.width);

    final List<TextBox> boxes = textPainter.getBoxesForSelection(selection);

    final paint =
        Paint()
          ..color = highlightColor
          ..style = PaintingStyle.fill;

    for (final box in boxes) {
      canvas.drawRect(box.toRect(), paint);
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) {
    return oldDelegate is! SelectionHighlightPainter ||
        oldDelegate.selection != selection ||
        oldDelegate.text != text;
  }
}
