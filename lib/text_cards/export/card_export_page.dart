import 'package:flutter/material.dart';

import '../../generated/l10n/app_localizations.dart';
import '../models/document_model.dart';
import '../widgets/text_card_widget.dart';
import 'card_renderer.dart';

class CardExportPage extends StatefulWidget {
  final DocumentModel document;
  final bool isSingleCard;

  const CardExportPage({
    super.key,
    required this.document,
    this.isSingleCard = false,
  });

  @override
  State<CardExportPage> createState() => _CardExportPageState();
}

class _CardExportPageState extends State<CardExportPage> {
  ExportConfig _config = const ExportConfig(
    size: ExportSize.square1080,
    platform: ExportPlatform.general,
  );

  bool _isExporting = false;
  bool _showPreview = true;
  late AppLocalizations _l10n;

  void _updateConfig(ExportConfig newConfig) {
    setState(() {
      _config = newConfig;
    });
  }

  void _exportCards() async {
    setState(() => _isExporting = true);

    try {
      final renderer = CardRenderer();

      if (widget.isSingleCard) {
        // 导出单个卡片
        final card = widget.document.cards.first;
        await renderer.exportSingleCard(card, _config);
      } else {
        // 导出整个文档
        await renderer.exportDocument(widget.document, _config);
      }

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                Icon(Icons.check_circle, color: Colors.white, size: 20),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    _l10n.textCardExportSuccess,
                    style: const TextStyle(fontWeight: FontWeight.w500),
                  ),
                ),
              ],
            ),
            backgroundColor: Colors.green,
            behavior: SnackBarBehavior.floating,
            duration: const Duration(seconds: 3),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            margin: const EdgeInsets.all(16),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Row(
              children: [
                Icon(Icons.error_outline, color: Colors.white, size: 20),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    '${_l10n.textCardExportFailed}: $e',
                    style: const TextStyle(fontWeight: FontWeight.w500),
                  ),
                ),
              ],
            ),
            backgroundColor: Colors.red,
            behavior: SnackBarBehavior.floating,
            duration: const Duration(seconds: 4),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
            margin: const EdgeInsets.all(16),
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isExporting = false);
      }
    }
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _l10n = AppLocalizations.of(context);
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    return Scaffold(
      backgroundColor: colorScheme.surface,
      appBar: AppBar(
        title: Text(
          widget.isSingleCard
              ? _l10n.textCardExportCard
              : _l10n.textCardExportDocument,
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: colorScheme.onSurface,
          ),
        ),
        backgroundColor: colorScheme.surface,
        elevation: 0,
        actions: [
          IconButton(
            icon: Icon(
              _showPreview ? Icons.visibility_off : Icons.visibility,
              color: colorScheme.onSurface,
            ),
            onPressed: () {
              setState(() {
                _showPreview = !_showPreview;
              });
            },
          ),
          const SizedBox(width: 8),
        ],
      ),
      body: Column(
        children: [
          if (_showPreview) ...[
            // 预览区域
            Container(
              color: colorScheme.surface,
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    _l10n.textCardPreview,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: colorScheme.onSurface,
                    ),
                  ),
                  const SizedBox(height: 12),
                  _buildPreview(),
                ],
              ),
            ),
            const Divider(height: 1),
          ],

          // 配置区域
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  _buildSizeSection(),
                  const SizedBox(height: 24),
                  _buildPlatformSection(),
                  const SizedBox(height: 24),
                  _buildFormatSection(),
                  const SizedBox(height: 24),
                  _buildWatermarkSection(),
                  const SizedBox(height: 24),
                  _buildAdvancedSection(),
                ],
              ),
            ),
          ),

          // 导出按钮
          Container(
            color: colorScheme.surface,
            padding: const EdgeInsets.all(16),
            child: SizedBox(
              width: double.infinity,
              child: ElevatedButton.icon(
                onPressed: _isExporting ? null : _exportCards,
                icon:
                    _isExporting
                        ? const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(
                              Colors.white,
                            ),
                          ),
                        )
                        : const Icon(Icons.download),
                label: Text(
                  _isExporting
                      ? _l10n.textCardExporting
                      : _l10n.textCardStartExport,
                ),
                style: ElevatedButton.styleFrom(
                  backgroundColor: colorScheme.primary,
                  foregroundColor: colorScheme.onPrimary,
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  textStyle: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPreview() {
    final aspectRatio = _config.size.width / _config.size.height;

    return Container(
      width: double.infinity,
      height: 200,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerLowest,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Theme.of(context).colorScheme.outlineVariant),
      ),
      child: Center(
        child: AspectRatio(
          aspectRatio: aspectRatio,
          child: Container(
            decoration: BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.circular(4),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  offset: const Offset(0, 2),
                  blurRadius: 4,
                ),
              ],
            ),
            child:
                widget.isSingleCard
                    ? _buildSingleCardPreview()
                    : _buildDocumentPreview(),
          ),
        ),
      ),
    );
  }

  Widget _buildSingleCardPreview() {
    final card = widget.document.cards.first;
    return Padding(
      padding: const EdgeInsets.all(8),
      child: TextCardWidget(card: card),
    );
  }

  Widget _buildDocumentPreview() {
    return Padding(
      padding: const EdgeInsets.all(8),
      child: Column(
        children: [
          // 文档标题
          Text(
            widget.document.title,
            style: const TextStyle(fontSize: 10, fontWeight: FontWeight.bold),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
          const SizedBox(height: 4),
          // 卡片预览
          Expanded(
            child: ListView.builder(
              itemCount: widget.document.cards.length.clamp(0, 3),
              itemBuilder: (context, index) {
                final card = widget.document.cards[index];
                return Container(
                  height: 20,
                  margin: const EdgeInsets.only(bottom: 2),
                  decoration: BoxDecoration(
                    color: Theme.of(
                      context,
                    ).colorScheme.primary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(2),
                  ),
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 4),
                    child: Row(
                      children: [
                        Text(
                          card.title,
                          style: const TextStyle(fontSize: 6),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),
                );
              },
            ),
          ),
          if (widget.document.cards.length > 3)
            Text(
              '+ ${widget.document.cards.length - 3} ${_l10n.textCardMore}',
              style: TextStyle(
                fontSize: 6,
                color: Theme.of(context).colorScheme.onSurfaceVariant,
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildSizeSection() {
    return _ConfigSection(
      title: _l10n.textCardExportSize,
      child: Column(
        children:
            ExportSize.values.map((size) {
              return RadioListTile<ExportSize>(
                title: Text(size.description),
                subtitle:
                    size != ExportSize.custom
                        ? Text('${size.width} × ${size.height}')
                        : null,
                value: size,
                groupValue: _config.size,
                onChanged: (value) {
                  if (value != null) {
                    _updateConfig(_config.copyWith(size: value));
                  }
                },
              );
            }).toList(),
      ),
    );
  }

  Widget _buildPlatformSection() {
    return _ConfigSection(
      title: _l10n.textCardTargetPlatform,
      child: Column(
        children:
            ExportPlatform.values.map((platform) {
              return RadioListTile<ExportPlatform>(
                title: Text(platform.description),
                value: platform,
                groupValue: _config.platform,
                onChanged: (value) {
                  if (value != null) {
                    _updateConfig(_config.copyWith(platform: value));
                  }
                },
              );
            }).toList(),
      ),
    );
  }

  Widget _buildFormatSection() {
    return _ConfigSection(
      title: _l10n.textCardFileFormat,
      child: Column(
        children:
            ExportFormat.values.map((format) {
              return RadioListTile<ExportFormat>(
                title: Text(format.description),
                value: format,
                groupValue: _config.format,
                onChanged: (value) {
                  if (value != null) {
                    _updateConfig(_config.copyWith(format: value));
                  }
                },
              );
            }).toList(),
      ),
    );
  }

  Widget _buildWatermarkSection() {
    return _ConfigSection(
      title: _l10n.textCardWatermarkSettings,
      child: Column(
        children: [
          SwitchListTile(
            title: Text(_l10n.textCardAddWatermark),
            subtitle: Text(_l10n.textCardAddWatermarkSubtitle),
            value: _config.includeWatermark,
            onChanged: (value) {
              _updateConfig(_config.copyWith(includeWatermark: value));
            },
          ),
          if (_config.includeWatermark)
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: TextField(
                decoration: InputDecoration(
                  labelText: _l10n.textCardCustomWatermarkText,
                  hintText: _l10n.textCardCustomWatermarkHint,
                  border: const OutlineInputBorder(),
                ),
                onChanged: (value) {
                  _updateConfig(
                    _config.copyWith(
                      customWatermark: value.isEmpty ? null : value,
                    ),
                  );
                },
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildAdvancedSection() {
    return _ConfigSection(
      title: _l10n.textCardAdvancedOptions,
      child: Column(
        children: [
          SwitchListTile(
            title: Text(_l10n.textCardIncludeTitle),
            subtitle: Text(_l10n.textCardIncludeTitleSubtitle),
            value: _config.includeTitle,
            onChanged: (value) {
              _updateConfig(_config.copyWith(includeTitle: value));
            },
          ),
          SwitchListTile(
            title: Text(_l10n.textCardIncludeTimestamp),
            subtitle: Text(_l10n.textCardIncludeTimestampSubtitle),
            value: _config.includeTimestamp,
            onChanged: (value) {
              _updateConfig(_config.copyWith(includeTimestamp: value));
            },
          ),
          Padding(
            padding: const EdgeInsets.all(16),
            child: Row(
              children: [
                Expanded(
                  child: Text(
                    _l10n.textCardImageQuality,
                    style: const TextStyle(fontSize: 16),
                  ),
                ),
                Expanded(
                  flex: 2,
                  child: Slider(
                    value: _config.quality,
                    min: 0.1,
                    max: 1.0,
                    divisions: 9,
                    label: '${(_config.quality * 100).round()}%',
                    onChanged: (value) {
                      _updateConfig(_config.copyWith(quality: value));
                    },
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class _ConfigSection extends StatelessWidget {
  final String title;
  final Widget child;

  const _ConfigSection({required this.title, required this.child});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Theme.of(context).colorScheme.onSurface,
          ),
        ),
        const SizedBox(height: 12),
        Container(
          decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.surface,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: Theme.of(context).colorScheme.outlineVariant,
            ),
          ),
          child: child,
        ),
      ],
    );
  }
}
