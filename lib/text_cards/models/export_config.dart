/// 导出配置模型
class ExportConfig {
  final ExportSize size;
  final ExportRatio ratio;
  final int quality;
  final bool includeWatermark;
  final bool includeTitle;
  final bool includeTimestamp;
  final CustomDimensions? customDimensions;

  const ExportConfig({
    this.size = ExportSize.large,
    this.ratio = ExportRatio.ratio4x5,
    this.quality = 100,
    this.includeWatermark = true,
    this.includeTitle = true,
    this.includeTimestamp = false,
    this.customDimensions,
  });

  ExportConfig copyWith({
    ExportSize? size,
    ExportRatio? ratio,
    int? quality,
    bool? includeWatermark,
    bool? includeTitle,
    bool? includeTimestamp,
    CustomDimensions? customDimensions,
  }) {
    return ExportConfig(
      size: size ?? this.size,
      ratio: ratio ?? this.ratio,
      quality: quality ?? this.quality,
      includeWatermark: includeWatermark ?? this.includeWatermark,
      includeTitle: includeTitle ?? this.includeTitle,
      includeTimestamp: includeTimestamp ?? this.includeTimestamp,
      customDimensions: customDimensions ?? this.customDimensions,
    );
  }

  /// 获取实际的像素尺寸
  ExportDimensions getDimensions() {
    if (customDimensions != null) {
      return ExportDimensions(
        customDimensions!.width.toDouble(),
        customDimensions!.height.toDouble(),
      );
    }

    switch (size) {
      case ExportSize.small:
        return _getSmallDimensions();
      case ExportSize.medium:
        return _getMediumDimensions();
      case ExportSize.large:
        return _getLargeDimensions();
      case ExportSize.extraLarge:
        return _getExtraLargeDimensions();
    }
  }

  /// 估算文件大小（KB）
  double getEstimatedFileSize() {
    final dimensions = getDimensions();
    final pixels = dimensions.width * dimensions.height;
    final qualityFactor = quality / 100.0;

    // 基于经验公式估算文件大小
    // 高质量JPEG大约每像素0.1-0.3字节
    final bytesPerPixel = 0.2 * qualityFactor;
    final estimatedBytes = pixels * bytesPerPixel;

    return estimatedBytes / 1024.0; // 转换为KB
  }

  /// 获取适用场景描述
  String getUsageScenario() {
    final fileSize = getEstimatedFileSize();

    if (fileSize < 100) {
      return '适合快速分享，文件小';
    } else if (fileSize < 500) {
      return '适合社交媒体，清晰度好';
    } else if (fileSize < 1000) {
      return '适合高清分享，细节丰富';
    } else {
      return '适合专业用途，打印质量';
    }
  }

  ExportDimensions _getSmallDimensions() {
    switch (ratio) {
      case ExportRatio.ratio1x1:
        return const ExportDimensions(540, 540);
      case ExportRatio.ratio4x5:
        return const ExportDimensions(540, 675);
      case ExportRatio.ratio16x9:
        return const ExportDimensions(540, 304);
      case ExportRatio.ratio9x16:
        return const ExportDimensions(540, 960);
    }
  }

  ExportDimensions _getMediumDimensions() {
    switch (ratio) {
      case ExportRatio.ratio1x1:
        return const ExportDimensions(720, 720);
      case ExportRatio.ratio4x5:
        return const ExportDimensions(720, 900);
      case ExportRatio.ratio16x9:
        return const ExportDimensions(720, 405);
      case ExportRatio.ratio9x16:
        return const ExportDimensions(720, 1280);
    }
  }

  ExportDimensions _getLargeDimensions() {
    switch (ratio) {
      case ExportRatio.ratio1x1:
        return const ExportDimensions(1080, 1080);
      case ExportRatio.ratio4x5:
        return const ExportDimensions(1080, 1350);
      case ExportRatio.ratio16x9:
        return const ExportDimensions(1080, 608);
      case ExportRatio.ratio9x16:
        return const ExportDimensions(1080, 1920);
    }
  }

  ExportDimensions _getExtraLargeDimensions() {
    switch (ratio) {
      case ExportRatio.ratio1x1:
        return const ExportDimensions(1440, 1440);
      case ExportRatio.ratio4x5:
        return const ExportDimensions(1440, 1800);
      case ExportRatio.ratio16x9:
        return const ExportDimensions(1440, 810);
      case ExportRatio.ratio9x16:
        return const ExportDimensions(1440, 2560);
    }
  }
}

/// 自定义尺寸
class CustomDimensions {
  final int width;
  final int height;

  const CustomDimensions(this.width, this.height);

  double get aspectRatio => width / height;

  String get displayText => '$width×$height';
}

/// 导出尺寸枚举
enum ExportSize {
  small('小', '适合预览'),
  medium('中', '适合社交媒体'),
  large('大', '高清分享'),
  extraLarge('超大', '打印质量');

  const ExportSize(this.label, this.description);
  final String label;
  final String description;
}

/// 导出比例枚举
enum ExportRatio {
  ratio1x1('1:1', '正方形'),
  ratio4x5('4:5', 'Instagram 推荐'),
  ratio16x9('16:9', '横屏宽屏'),
  ratio9x16('9:16', '竖屏故事');

  const ExportRatio(this.label, this.description);
  final String label;
  final String description;
}

/// 导出尺寸数据
class ExportDimensions {
  final double width;
  final double height;

  const ExportDimensions(this.width, this.height);

  double get aspectRatio => width / height;

  /// 获取缩放因子（基于300px基准宽度）
  double get scaleFactor => width / 300.0;

  /// 获取边距
  double get margin => width * 0.05; // 5% 边距

  String get displayText => '${width.toInt()}×${height.toInt()}';
}
