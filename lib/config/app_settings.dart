import 'package:flutter/material.dart';

import '../markdown/models/markdown_watermark.dart';
import 'app_theme.dart';
import 'chinese_traditional_colors.dart';
import 'constants.dart';

class AppSettings {
  final ThemeMode themeMode;
  final AppThemeType themeType;
  final ChineseTraditionalColorTheme? chineseTraditionalColorTheme; // 中国传统色主题
  final double aiTemperature;
  final bool privacyEnhanced;
  final String? defaultModelId;
  final bool enableStreamingResponse;
  final int requestTimeout; // 请求超时时间（秒）
  final MarkdownWatermark watermark; // 添加水印设置
  final bool useDefaultInitialText; // 全局：是否使用默认初始文本（兼容旧版本）
  // 分模块默认初始文本控制（如未设置，默认回退到 useDefaultInitialText）
  final bool useDefaultInitialTextMarkdown;
  final bool useDefaultInitialTextTextCards;
  final bool useDefaultInitialTextSvg;
  final bool useDefaultInitialTextHtml;
  final bool useDefaultInitialTextTransformer;
  // SVG 初始数据控制
  final bool enableBuiltinSvg; // 是否在SVG首页注入内置示例
  final String svgPreset; // 内置示例预设名

  AppSettings({
    this.themeMode = ThemeMode.system,
    this.themeType = AppThemeType.materialYou,
    this.chineseTraditionalColorTheme,
    this.aiTemperature = AppConstants.defaultTemperature,
    this.privacyEnhanced = AppConstants.defaultPrivacyEnhanced,
    this.defaultModelId,
    this.enableStreamingResponse = AppConstants.defaultStreamingResponse,
    this.requestTimeout = AppConstants.defaultRequestTimeout,
    this.useDefaultInitialText = true, // 默认使用初始文本（全局兼容）
    bool? useDefaultInitialTextMarkdown,
    bool? useDefaultInitialTextTextCards,
    bool? useDefaultInitialTextSvg,
    bool? useDefaultInitialTextHtml,
    bool? useDefaultInitialTextTransformer,
    this.enableBuiltinSvg = true,
    this.svgPreset = 'minimal_logo',
    MarkdownWatermark? watermark,
  }) : useDefaultInitialTextMarkdown =
           useDefaultInitialTextMarkdown ?? useDefaultInitialText,
       useDefaultInitialTextTextCards =
           useDefaultInitialTextTextCards ?? useDefaultInitialText,
       useDefaultInitialTextSvg =
           useDefaultInitialTextSvg ?? useDefaultInitialText,
       useDefaultInitialTextHtml =
           useDefaultInitialTextHtml ?? useDefaultInitialText,
       useDefaultInitialTextTransformer =
           useDefaultInitialTextTransformer ?? useDefaultInitialText,
       watermark = watermark ?? MarkdownWatermark.defaultWatermark();

  factory AppSettings.fromJson(Map<String, dynamic> json) {
    MarkdownWatermark? watermark;
    if (json.containsKey('watermark')) {
      final watermarkJson = json['watermark'] as Map<String, dynamic>;
      watermark = MarkdownWatermark(
        text: watermarkJson['text'] ?? '',
        textColor: Color(watermarkJson['textColor'] ?? 0),
        fontSize: watermarkJson['fontSize']?.toDouble() ?? 12.0,
        fontFamily: watermarkJson['fontFamily'] ?? '',
        fontStyle:
            watermarkJson['fontStyle'] == 1
                ? FontStyle.italic
                : FontStyle.normal,
        fontWeight:
            watermarkJson['fontWeight'] == 1
                ? FontWeight.bold
                : FontWeight.normal,
        isVisible: watermarkJson['isVisible'] ?? false,
        position: WatermarkPosition.values[watermarkJson['position'] ?? 0],
        opacity: watermarkJson['opacity']?.toDouble() ?? 0.1,
      );
    }

    return AppSettings(
      themeMode: ThemeMode.values[json['themeMode'] ?? 2],
      themeType:
          json.containsKey('themeType')
              ? AppThemeType.values[json['themeType']]
              : AppThemeType.materialYou,
      chineseTraditionalColorTheme: ChineseTraditionalColors.fromString(
        json['chineseTraditionalColorTheme'],
      ),
      aiTemperature: json['aiTemperature'] ?? AppConstants.defaultTemperature,
      privacyEnhanced:
          json['privacyEnhanced'] ?? AppConstants.defaultPrivacyEnhanced,
      defaultModelId: json['defaultModelId'],
      enableStreamingResponse:
          json['enableStreamingResponse'] ??
          AppConstants.defaultStreamingResponse,
      requestTimeout:
          json['requestTimeout'] ?? AppConstants.defaultRequestTimeout,
      useDefaultInitialText: json['useDefaultInitialText'] ?? true,
      useDefaultInitialTextMarkdown:
          json['useDefaultInitialTextMarkdown'] ??
          (json['useDefaultInitialText'] ?? true),
      useDefaultInitialTextTextCards:
          json['useDefaultInitialTextTextCards'] ??
          (json['useDefaultInitialText'] ?? true),
      useDefaultInitialTextSvg:
          json['useDefaultInitialTextSvg'] ??
          (json['useDefaultInitialText'] ?? true),
      useDefaultInitialTextHtml:
          json['useDefaultInitialTextHtml'] ??
          (json['useDefaultInitialText'] ?? true),
      useDefaultInitialTextTransformer:
          json['useDefaultInitialTextTransformer'] ??
          (json['useDefaultInitialText'] ?? true),
      enableBuiltinSvg: json['enableBuiltinSvg'] ?? true,
      svgPreset: json['svgPreset'] ?? 'minimal_logo',
      watermark: watermark,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'themeMode': themeMode.index,
      'themeType': themeType.index,
      'chineseTraditionalColorTheme':
          chineseTraditionalColorTheme != null
              ? ChineseTraditionalColors.themeToString(
                chineseTraditionalColorTheme!,
              )
              : null,
      'aiTemperature': aiTemperature,
      'privacyEnhanced': privacyEnhanced,
      'defaultModelId': defaultModelId,
      'enableStreamingResponse': enableStreamingResponse,
      'requestTimeout': requestTimeout,
      // 兼容字段：仍然保存全局开关
      'useDefaultInitialText': useDefaultInitialText,
      // 新增分模块字段
      'useDefaultInitialTextMarkdown': useDefaultInitialTextMarkdown,
      'useDefaultInitialTextTextCards': useDefaultInitialTextTextCards,
      'useDefaultInitialTextSvg': useDefaultInitialTextSvg,
      'useDefaultInitialTextHtml': useDefaultInitialTextHtml,
      'useDefaultInitialTextTransformer': useDefaultInitialTextTransformer,
      'enableBuiltinSvg': enableBuiltinSvg,
      'svgPreset': svgPreset,
      'watermark': {
        'text': watermark.text,
        'textColor': watermark.textColor.toARGB32(),
        'fontSize': watermark.fontSize,
        'fontFamily': watermark.fontFamily,
        'fontStyle': watermark.fontStyle == FontStyle.italic ? 1 : 0,
        'fontWeight': watermark.fontWeight == FontWeight.bold ? 1 : 0,
        'isVisible': watermark.isVisible,
        'position': watermark.position.index,
        'opacity': watermark.opacity,
      },
    };
  }

  AppSettings copyWith({
    ThemeMode? themeMode,
    AppThemeType? themeType,
    ChineseTraditionalColorTheme? chineseTraditionalColorTheme,
    double? aiTemperature,
    bool? privacyEnhanced,
    String? defaultModelId,
    bool? enableStreamingResponse,
    int? requestTimeout,
    bool? useDefaultInitialText,
    bool? useDefaultInitialTextMarkdown,
    bool? useDefaultInitialTextTextCards,
    bool? useDefaultInitialTextSvg,
    bool? useDefaultInitialTextHtml,
    bool? useDefaultInitialTextTransformer,
    MarkdownWatermark? watermark,
    bool? enableBuiltinSvg,
    String? svgPreset,
  }) {
    return AppSettings(
      themeMode: themeMode ?? this.themeMode,
      themeType: themeType ?? this.themeType,
      chineseTraditionalColorTheme:
          chineseTraditionalColorTheme ?? this.chineseTraditionalColorTheme,
      aiTemperature: aiTemperature ?? this.aiTemperature,
      privacyEnhanced: privacyEnhanced ?? this.privacyEnhanced,
      defaultModelId: defaultModelId ?? this.defaultModelId,
      enableStreamingResponse:
          enableStreamingResponse ?? this.enableStreamingResponse,
      requestTimeout: requestTimeout ?? this.requestTimeout,
      useDefaultInitialText:
          useDefaultInitialText ?? this.useDefaultInitialText,
      useDefaultInitialTextMarkdown:
          useDefaultInitialTextMarkdown ?? this.useDefaultInitialTextMarkdown,
      useDefaultInitialTextTextCards:
          useDefaultInitialTextTextCards ?? this.useDefaultInitialTextTextCards,
      useDefaultInitialTextSvg:
          useDefaultInitialTextSvg ?? this.useDefaultInitialTextSvg,
      useDefaultInitialTextHtml:
          useDefaultInitialTextHtml ?? this.useDefaultInitialTextHtml,
      useDefaultInitialTextTransformer:
          useDefaultInitialTextTransformer ??
          this.useDefaultInitialTextTransformer,
      watermark: watermark ?? this.watermark,
      enableBuiltinSvg: enableBuiltinSvg ?? this.enableBuiltinSvg,
      svgPreset: svgPreset ?? this.svgPreset,
    );
  }
}
