import 'package:flutter/material.dart';

import '../config/app_theme.dart';
import '../generated/l10n/app_localizations.dart';
import 'models/pdf_document.dart';
import 'pdf_service.dart';
import 'pdf_viewer_screen.dart';
import 'pdf_security_screen.dart';

/// PDF管理器屏幕
class PdfManagerScreen extends StatefulWidget {
  const PdfManagerScreen({super.key});

  @override
  State<PdfManagerScreen> createState() => _PdfManagerScreenState();
}

class _PdfManagerScreenState extends State<PdfManagerScreen> {
  /// PDF服务
  final _pdfService = PdfService();

  /// PDF文档列表
  List<PdfDocument> _documents = [];

  /// 是否正在加载
  bool _isLoading = true;

  /// 搜索控制器
  final _searchController = TextEditingController();

  /// 搜索文本
  String _searchText = '';

  /// 选中的文档列表（用于批量操作）
  final List<PdfDocument> _selectedDocuments = [];

  /// 是否处于选择模式
  bool get _isSelectionMode => _selectedDocuments.isNotEmpty;

  /// 本地化
  late AppLocalizations _l10n;

  @override
  void initState() {
    super.initState();
    _l10n = AppLocalizations.of(context);
    _initializeService();
  }

  /// 初始化服务并加载文档
  Future<void> _initializeService() async {
    setState(() {
      _isLoading = true;
    });

    await _pdfService.initialize();
    await _loadDocuments();

    setState(() {
      _isLoading = false;
    });
  }

  /// 加载文档列表
  Future<void> _loadDocuments() async {
    final documents = await _pdfService.getAllDocuments();
    setState(() {
      _documents = documents;
    });
  }

  /// 导入PDF
  Future<void> _importPdf() async {
    final document = await _pdfService.importPdf();
    if (document != null) {
      await _loadDocuments();
    }
  }

  /// 删除PDF
  Future<void> _deletePdf(PdfDocument document) async {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: Text(_l10n.pdfConfirmDelete),
          content: Text(_l10n.pdfDeleteConfirm(document.fileName)),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text(_l10n.pdfCancel),
            ),
            TextButton(
              onPressed: () async {
                Navigator.pop(context);
                await _pdfService.deleteDocument(document.id);
                await _loadDocuments();
              },
              style: TextButton.styleFrom(foregroundColor: Colors.red),
              child: Text(_l10n.pdfDelete),
            ),
          ],
        );
      },
    );
  }

  /// 批量删除PDF
  Future<void> _deleteSelectedPdfs() async {
    if (_selectedDocuments.isEmpty) return;

    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: Text(_l10n.pdfConfirmDelete),
          content: Text(_l10n.pdfBatchDeleteConfirm(_selectedDocuments.length)),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text(_l10n.pdfCancel),
            ),
            TextButton(
              onPressed: () async {
                Navigator.pop(context);

                for (final doc in _selectedDocuments) {
                  await _pdfService.deleteDocument(doc.id);
                }

                setState(() {
                  _selectedDocuments.clear();
                });

                await _loadDocuments();
              },
              style: TextButton.styleFrom(foregroundColor: Colors.red),
              child: Text(_l10n.pdfDelete),
            ),
          ],
        );
      },
    );
  }

  /// 批量合并PDF
  Future<void> _mergeSelectedPdfs() async {
    if (_selectedDocuments.length < 2) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text(_l10n.pdfSelectAtLeastTwo)));
      return;
    }

    setState(() {
      _isLoading = true;
    });

    final mergedDoc = await _pdfService.mergePdfs(_selectedDocuments);

    setState(() {
      _isLoading = false;
      _selectedDocuments.clear();
    });

    if (mergedDoc != null) {
      await _loadDocuments();
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text(_l10n.pdfMergeSuccess)));
    } else {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text(_l10n.pdfMergeFailed)));
    }
  }

  /// 切换文档选择状态
  void _toggleDocumentSelection(PdfDocument document) {
    setState(() {
      if (_selectedDocuments.contains(document)) {
        _selectedDocuments.remove(document);
      } else {
        _selectedDocuments.add(document);
      }
    });
  }

  /// 清除所有选择
  void _clearSelection() {
    setState(() {
      _selectedDocuments.clear();
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(),
      body: _buildBody(),
      floatingActionButton: _buildFloatingActionButton(),
      bottomNavigationBar: _isSelectionMode ? _buildSelectionBar() : null,
    );
  }

  /// 构建应用栏
  PreferredSizeWidget _buildAppBar() {
    return AppBar(
      title:
          _searchController.text.isEmpty
              ? Text(_l10n.pdfManagerTitle)
              : TextField(
                controller: _searchController,
                decoration: InputDecoration(
                  hintText: _l10n.pdfSearch,
                  border: InputBorder.none,
                  hintStyle: TextStyle(color: AppTheme.textLightColor),
                ),
                style: TextStyle(color: AppTheme.textDarkColor),
                onChanged: (value) {
                  setState(() {
                    _searchText = value;
                  });
                },
              ),
      leading:
          _isSelectionMode
              ? IconButton(icon: Icon(Icons.close), onPressed: _clearSelection)
              : null,
      actions: [
        if (_searchController.text.isEmpty)
          IconButton(
            icon: Icon(Icons.search),
            onPressed: () {
              setState(() {
                // 激活搜索框
                _searchController.text = '';
                FocusScope.of(context).requestFocus(FocusNode());
              });
            },
          )
        else
          IconButton(
            icon: Icon(Icons.clear),
            onPressed: () {
              setState(() {
                _searchController.clear();
                _searchText = '';
              });
            },
          ),
        if (_isSelectionMode)
          IconButton(
            icon: Icon(Icons.select_all),
            onPressed: () {
              setState(() {
                if (_selectedDocuments.length == _documents.length) {
                  // 如果已全选，则取消全选
                  _selectedDocuments.clear();
                } else {
                  // 否则全选
                  _selectedDocuments.clear();
                  _selectedDocuments.addAll(_documents);
                }
              });
            },
          ),
      ],
    );
  }

  /// 构建主体
  Widget _buildBody() {
    if (_isLoading) {
      return Center(child: CircularProgressIndicator());
    }

    if (_documents.isEmpty) {
      return _buildEmptyState();
    }

    // 根据搜索文本过滤文档
    final filteredDocuments =
        _searchText.isEmpty
            ? _documents
            : _documents
                .where(
                  (doc) => doc.fileName.toLowerCase().contains(
                    _searchText.toLowerCase(),
                  ),
                )
                .toList();

    if (filteredDocuments.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.search_off, size: 80, color: AppTheme.textLightColor),
            SizedBox(height: 16),
            Text(
              _l10n.pdfNoDocuments,
              style: TextStyle(fontSize: 18, color: AppTheme.textMediumColor),
            ),
            SizedBox(height: 8),
            Text(
              _l10n.pdfTryDifferentSearch,
              style: TextStyle(color: AppTheme.textLightColor),
            ),
          ],
        ),
      );
    }

    return ListView.builder(
      padding: EdgeInsets.all(8),
      itemCount: filteredDocuments.length,
      itemBuilder: (context, index) {
        final document = filteredDocuments[index];
        return _buildDocumentCard(document);
      },
    );
  }

  /// 构建空状态页面 - 炫酷的首页展示
  Widget _buildEmptyState() {
    return SingleChildScrollView(
      padding: EdgeInsets.all(24),
      child: Column(
        children: [
          SizedBox(height: 20),
          // 主标题区域
          _buildHeroSection(),
          SizedBox(height: 32),
          // 统计数据展示
          _buildStatsSection(),
          SizedBox(height: 32),
          // 核心功能展示
          _buildFeatureShowcase(),
          SizedBox(height: 32),
          // 使用技巧
          _buildTipsSection(),
          SizedBox(height: 32),
          // 快速开始按钮
          _buildQuickStartSection(),
          SizedBox(height: 24),
        ],
      ),
    );
  }

  /// 构建主标题区域
  Widget _buildHeroSection() {
    return Container(
      width: double.infinity,
      padding: EdgeInsets.all(28),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Color(0xFF667eea),
            Color(0xFF764ba2),
          ],
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Color(0xFF667eea).withValues(alpha: 0.4),
            offset: Offset(0, 12),
            blurRadius: 32,
            spreadRadius: 0,
          ),
        ],
      ),
      child: Column(
        children: [
          // 动态PDF图标
          Container(
            width: 72,
            height: 72,
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.15),
              borderRadius: BorderRadius.circular(18),
              border: Border.all(
                color: Colors.white.withValues(alpha: 0.2),
                width: 1,
              ),
            ),
            child: Icon(Icons.picture_as_pdf, size: 40, color: Colors.white),
          ),
          SizedBox(height: 20),
          Text(
            _l10n.pdfIntelligentCenter,
            style: TextStyle(
              fontSize: 26,
              fontWeight: FontWeight.w700,
              color: Colors.white,
              letterSpacing: 0.5,
            ),
          ),
          SizedBox(height: 8),
          Text(
            _l10n.pdfCenterSubtitle,
            style: TextStyle(
              fontSize: 15,
              color: Colors.white.withValues(alpha: 0.85),
              height: 1.4,
            ),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 20),
          // 版本信息
          Container(
            padding: EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.15),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Text(
              _l10n.pdfVersion,
              style: TextStyle(
                fontSize: 12,
                color: Colors.white.withValues(alpha: 0.9),
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建功能展示区域
  Widget _buildFeatureShowcase() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(Icons.star, color: AppTheme.primaryColor, size: 20),
            SizedBox(width: 8),
            Text(
              _l10n.pdfCoreFeatures,
              style: TextStyle(
                fontSize: 20,
                fontWeight: FontWeight.bold,
                color: AppTheme.textDarkColor,
              ),
            ),
            Spacer(),
            Text(
              _l10n.pdfProfessional,
              style: TextStyle(
                fontSize: 12,
                color: AppTheme.primaryColor,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        SizedBox(height: 16),
        // 功能卡片网格
        GridView.count(
          shrinkWrap: true,
          physics: NeverScrollableScrollPhysics(),
          crossAxisCount: 2,
          crossAxisSpacing: 12,
          mainAxisSpacing: 12,
          childAspectRatio: 1.15,
          children: [
            _buildFeatureCard(
              icon: Icons.security,
              title: _l10n.pdfSecurityEncryption,
              description: '${_l10n.pdfPasswordProtection}\n${_l10n.pdfPermissionControl}',
              gradient: LinearGradient(
                colors: [Color(0xFF667eea), Color(0xFF764ba2)],
              ),
              onTap: _showSecurityFeatures,
            ),
            _buildFeatureCard(
              icon: Icons.edit_note,
              title: _l10n.pdfSmartAnnotations,
              description: '${_l10n.pdfHighlight}\n${_l10n.pdfTextAnnotations}',
              gradient: LinearGradient(
                colors: [Color(0xFF11998e), Color(0xFF38ef7d)],
              ),
              onTap: _showAnnotationFeatures,
            ),
            _buildFeatureCard(
              icon: Icons.search,
              title: _l10n.pdfFastSearch,
              description: '${_l10n.pdfFullTextSearch}\n${_l10n.pdfPreciseLocation}',
              gradient: LinearGradient(
                colors: [Color(0xFFf093fb), Color(0xFFf5576c)],
              ),
              onTap: _showSearchFeatures,
            ),
            _buildFeatureCard(
              icon: Icons.merge_type,
              title: _l10n.pdfDocumentMerge,
              description: '${_l10n.pdfMultiFileMerge}\n批量处理',
              gradient: LinearGradient(
                colors: [Color(0xFFffecd2), Color(0xFFfcb69f)],
              ),
              onTap: _showMergeFeatures,
            ),
            _buildFeatureCard(
              icon: Icons.share,
              title: _l10n.pdfEasySharing,
              description: '多种格式\n${_l10n.pdfOneClickShare}',
              gradient: LinearGradient(
                colors: [Color(0xFF4facfe), Color(0xFF00f2fe)],
              ),
              onTap: _showSharingFeatures,
            ),
            _buildFeatureCard(
              icon: Icons.cloud_sync,
              title: _l10n.pdfCloudSync,
              description: '多设备同步\n${_l10n.pdfAutoSync}',
              gradient: LinearGradient(
                colors: [Color(0xFFa8edea), Color(0xFFfed6e3)],
              ),
              onTap: _showCloudFeatures,
            ),
          ],
        ),
      ],
    );
  }

  /// 构建功能卡片
  Widget _buildFeatureCard({
    required IconData icon,
    required String title,
    required String description,
    required LinearGradient gradient,
    VoidCallback? onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(16),
      child: Container(
        padding: EdgeInsets.all(20),
        decoration: BoxDecoration(
          gradient: gradient,
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: gradient.colors.first.withValues(alpha: 0.3),
              offset: Offset(0, 4),
              blurRadius: 12,
              spreadRadius: 0,
            ),
          ],
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, size: 32, color: Colors.white),
            SizedBox(height: 12),
            Text(
              title,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            SizedBox(height: 8),
            Text(
              description,
              style: TextStyle(
                fontSize: 12,
                color: Colors.white.withValues(alpha: 0.9),
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  /// 构建快速开始区域
  Widget _buildQuickStartSection() {
    return Container(
      padding: EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            offset: Offset(0, 4),
            blurRadius: 16,
            spreadRadius: 0,
          ),
        ],
      ),
      child: Column(
        children: [
          Row(
            children: [
              Icon(Icons.rocket_launch, color: AppTheme.primaryColor, size: 20),
              SizedBox(width: 8),
              Text(
                _l10n.pdfQuickStart,
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.textDarkColor,
                ),
              ),
            ],
          ),
          SizedBox(height: 20),
          // 主要操作按钮
          SizedBox(
            width: double.infinity,
            child: ElevatedButton.icon(
              onPressed: _importPdf,
              icon: Icon(Icons.upload_file, size: 22),
              label: Text(
                _l10n.pdfImportDocument,
                style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
              ),
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryColor,
                foregroundColor: Colors.white,
                padding: EdgeInsets.symmetric(vertical: 16, horizontal: 24),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
                elevation: 2,
                shadowColor: AppTheme.primaryColor.withValues(alpha: 0.3),
              ),
            ),
          ),
          SizedBox(height: 12),
          // 次要操作按钮
          Row(
            children: [
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: () => _showDemoContent(),
                  icon: Icon(Icons.play_circle_outline, size: 18),
                  label: Text(_l10n.pdfViewDemo),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: AppTheme.primaryColor,
                    side: BorderSide(color: AppTheme.primaryColor),
                    padding: EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                ),
              ),
              SizedBox(width: 12),
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: () => _showHelp(),
                  icon: Icon(Icons.help_outline, size: 18),
                  label: Text(_l10n.pdfHelp),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: AppTheme.textMediumColor,
                    side: BorderSide(color: AppTheme.textMediumColor),
                    padding: EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                ),
              ),
            ],
          ),
          SizedBox(height: 16),
          // 支持格式说明
          Container(
            padding: EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: AppTheme.bgIndigo50,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                Icon(Icons.info_outline, 
                     size: 16, 
                     color: AppTheme.textMediumColor),
                SizedBox(width: 8),
                Expanded(
                  child: Text(
                    _l10n.pdfSupportInfo,
                    style: TextStyle(
                      fontSize: 13,
                      color: AppTheme.textMediumColor,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 构建文档卡片
  Widget _buildDocumentCard(PdfDocument document) {
    final isSelected = _selectedDocuments.contains(document);

    return Card(
      margin: EdgeInsets.only(bottom: 8),
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side:
            isSelected
                ? BorderSide(color: AppTheme.primaryColor, width: 2)
                : BorderSide.none,
      ),
      child: InkWell(
        onTap:
            _isSelectionMode
                ? () => _toggleDocumentSelection(document)
                : () => _openPdf(document),
        onLongPress: () => _toggleDocumentSelection(document),
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: EdgeInsets.all(12),
          child: Row(
            children: [
              // 缩略图容器
              Container(
                width: 60,
                height: 80,
                decoration: BoxDecoration(
                  color: AppTheme.bgIndigo50,
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(color: AppTheme.borderColor),
                ),
                child: Stack(
                  alignment: Alignment.center,
                  children: [
                    Icon(Icons.picture_as_pdf, color: Colors.red, size: 36),
                    if (isSelected)
                      Positioned(
                        right: 0,
                        top: 0,
                        child: Container(
                          decoration: BoxDecoration(
                            color: AppTheme.primaryColor,
                            shape: BoxShape.circle,
                          ),
                          child: Icon(
                            Icons.check,
                            color: Colors.white,
                            size: 16,
                          ),
                        ),
                      ),
                  ],
                ),
              ),
              SizedBox(width: 12),
              // 文档信息
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      document.fileName,
                      style: TextStyle(
                        fontWeight: FontWeight.bold,
                        fontSize: 16,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    SizedBox(height: 4),
                    Row(
                      children: [
                        _buildInfoChip(
                          '${document.pageCount ?? "?"} 页',
                          Icons.content_copy,
                        ),
                        SizedBox(width: 8),
                        _buildInfoChip(document.formattedSize, Icons.storage),
                        SizedBox(width: 8),
                        _buildSecurityChip(document),
                      ],
                    ),
                    SizedBox(height: 4),
                    Text(
                      _l10n.pdfModified(_formatDate(document.modifiedAt)),
                      style: TextStyle(
                        color: AppTheme.textLightColor,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
              // 操作按钮
              if (!_isSelectionMode)
                PopupMenuButton<String>(
                  onSelected: (value) {
                    switch (value) {
                      case 'security':
                        _openSecuritySettings(document);
                        break;
                      case 'delete':
                        _deletePdf(document);
                        break;
                      case 'select':
                        _toggleDocumentSelection(document);
                        break;
                    }
                  },
                  itemBuilder:
                      (context) => [
                        PopupMenuItem(
                          value: 'security',
                          child: Row(
                            children: [
                              Icon(
                                Icons.security,
                                color: AppTheme.textDarkColor,
                              ),
                              SizedBox(width: 8),
                              Text(_l10n.pdfSecuritySettings),
                            ],
                          ),
                        ),
                        PopupMenuItem(
                          value: 'select',
                          child: Row(
                            children: [
                              Icon(
                                Icons.check_circle_outline,
                                color: AppTheme.textDarkColor,
                              ),
                              SizedBox(width: 8),
                              Text(_l10n.pdfSelect),
                            ],
                          ),
                        ),
                        PopupMenuItem(
                          value: 'delete',
                          child: Row(
                            children: [
                              Icon(Icons.delete_outline, color: Colors.red),
                              SizedBox(width: 8),
                              Text(_l10n.pdfDelete, style: TextStyle(color: Colors.red)),
                            ],
                          ),
                        ),
                      ],
                ),
            ],
          ),
        ),
      ),
    );
  }

  /// 构建信息标签
  Widget _buildInfoChip(String text, IconData icon) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: AppTheme.bgIndigo50,
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(icon, size: 12, color: AppTheme.textMediumColor),
          SizedBox(width: 4),
          Text(
            text,
            style: TextStyle(fontSize: 12, color: AppTheme.textMediumColor),
          ),
        ],
      ),
    );
  }

  /// 构建安全状态标签
  Widget _buildSecurityChip(PdfDocument document) {
    Color chipColor;
    IconData chipIcon;

    if (document.isEncrypted) {
      chipColor = Colors.orange;
      chipIcon = Icons.lock;
    } else if (!document.allowPrint ||
        !document.allowCopy ||
        !document.allowEdit) {
      chipColor = Colors.blue;
      chipIcon = Icons.lock_outline;
    } else {
      chipColor = Colors.green;
      chipIcon = Icons.lock_open;
    }

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: chipColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(chipIcon, size: 12, color: chipColor),
          SizedBox(width: 4),
          Text(
            document.securityStatus,
            style: TextStyle(fontSize: 12, color: chipColor),
          ),
        ],
      ),
    );
  }

  /// 构建浮动按钮
  Widget _buildFloatingActionButton() {
    return FloatingActionButton(
      onPressed: _importPdf,
      tooltip: _l10n.pdfImportTooltip,
      child: Icon(Icons.add),
    );
  }

  /// 构建选择模式底部栏
  Widget _buildSelectionBar() {
    return BottomAppBar(
      height: 56,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          _buildActionButton(
            label: _l10n.pdfMerge,
            icon: Icons.merge_type,
            onPressed: _mergeSelectedPdfs,
          ),
          _buildActionButton(
            label: _l10n.pdfDelete,
            icon: Icons.delete,
            color: Colors.red,
            onPressed: _deleteSelectedPdfs,
          ),
        ],
      ),
    );
  }

  /// 构建操作按钮
  Widget _buildActionButton({
    required String label,
    required IconData icon,
    required VoidCallback onPressed,
    Color? color,
  }) {
    return InkWell(
      onTap: onPressed,
      borderRadius: BorderRadius.circular(8),
      child: Padding(
        padding: EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, color: color ?? AppTheme.primaryColor),
            SizedBox(height: 2),
            Text(
              label,
              style: TextStyle(color: color ?? AppTheme.primaryColor),
            ),
          ],
        ),
      ),
    );
  }

  /// 打开PDF
  void _openPdf(PdfDocument document) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => PdfViewerScreen(document: document),
      ),
    ).then((_) {
      // 返回时刷新文档列表
      _loadDocuments();
    });
  }

  /// 打开安全设置
  void _openSecuritySettings(PdfDocument document) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => PdfSecurityScreen(document: document),
      ),
    ).then((result) {
      // 如果有更改，刷新文档列表
      if (result == true) {
        _loadDocuments();
      }
    });
  }

  /// 格式化日期
  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      if (difference.inHours == 0) {
        if (difference.inMinutes == 0) {
          return _l10n.pdfJustNow;
        }
        return _l10n.pdfMinutesAgo(difference.inMinutes);
      }
      return _l10n.pdfHoursAgo(difference.inHours);
    } else if (difference.inDays < 7) {
      return _l10n.pdfDaysAgo(difference.inDays);
    } else {
      return '${date.year}-${date.month.toString().padLeft(2, '0')}-${date.day.toString().padLeft(2, '0')}';
    }
  }

  /// 显示安全功能介绍
  void _showSecurityFeatures() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder:
          (context) => DraggableScrollableSheet(
            initialChildSize: 0.7,
            maxChildSize: 0.9,
            minChildSize: 0.5,
            builder:
                (context, scrollController) => Container(
                  padding: EdgeInsets.all(24),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Container(
                            padding: EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              gradient: AppTheme.blueGradient,
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Icon(
                              Icons.security,
                              color: Colors.white,
                              size: 24,
                            ),
                          ),
                          SizedBox(width: 16),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  _l10n.pdfSecurityEncryption,
                                  style: TextStyle(
                                    fontSize: 24,
                                    fontWeight: FontWeight.bold,
                                    color: AppTheme.textDarkColor,
                                  ),
                                ),
                                Text(
                                  _l10n.pdfProtectYourDocuments,
                                  style: TextStyle(
                                    fontSize: 16,
                                    color: AppTheme.textMediumColor,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 24),
                      Expanded(
                        child: ListView(
                          controller: scrollController,
                          children: [
                            _buildFeatureItem(
                              icon: Icons.lock,
                              title: '密码保护',
                              description: '为PDF文档设置用户密码和所有者密码，确保文档安全',
                              color: AppTheme.blueGradient.colors.first,
                            ),
                            SizedBox(height: 16),
                            _buildFeatureItem(
                              icon: Icons.admin_panel_settings,
                              title: '权限控制',
                              description: '精细控制文档的打印、复制、编辑等权限',
                              color: AppTheme.blueGradient.colors.first,
                            ),
                            SizedBox(height: 16),
                            _buildFeatureItem(
                              icon: Icons.verified_user,
                              title: '加密算法',
                              description: '采用业界标准的AES加密算法，保障文档安全',
                              color: AppTheme.blueGradient.colors.first,
                            ),
                            SizedBox(height: 24),
                            SizedBox(
                              width: double.infinity,
                              child: ElevatedButton.icon(
                                onPressed: () {
                                  Navigator.pop(context);
                                  _importPdf();
                                },
                                icon: Icon(Icons.add),
                                label: Text('导入PDF开始使用'),
                                style: ElevatedButton.styleFrom(
                                  backgroundColor:
                                      AppTheme.blueGradient.colors.first,
                                  foregroundColor: Colors.white,
                                  padding: EdgeInsets.symmetric(vertical: 16),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
          ),
    );
  }

  /// 构建统计数据区域
  Widget _buildStatsSection() {
    return Container(
      padding: EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            offset: Offset(0, 4),
            blurRadius: 16,
            spreadRadius: 0,
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.analytics, color: AppTheme.primaryColor, size: 20),
              SizedBox(width: 8),
              Text(
                '使用统计',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.textDarkColor,
                ),
              ),
            ],
          ),
          SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  title: '文档总数',
                  value: '0',
                  icon: Icons.description,
                  color: AppTheme.blueGradient.colors.first,
                ),
              ),
              SizedBox(width: 12),
              Expanded(
                child: _buildStatCard(
                  title: '今日处理',
                  value: '0',
                  icon: Icons.today,
                  color: AppTheme.greenGradient.colors.first,
                ),
              ),
              SizedBox(width: 12),
              Expanded(
                child: _buildStatCard(
                  title: '存储空间',
                  value: '0MB',
                  icon: Icons.storage,
                  color: AppTheme.orangeGradient.colors.first,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 构建统计卡片
  Widget _buildStatCard({
    required String title,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: color.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          SizedBox(height: 4),
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              color: AppTheme.textMediumColor,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  /// 构建使用技巧区域
  Widget _buildTipsSection() {
    return Container(
      padding: EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Color(0xFFF093FB).withValues(alpha: 0.1),
            Color(0xFFF5576C).withValues(alpha: 0.1),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: Color(0xFFF093FB).withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: EdgeInsets.all(8),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [Color(0xFFF093FB), Color(0xFFF5576C)],
                  ),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(Icons.lightbulb, color: Colors.white, size: 16),
              ),
              SizedBox(width: 12),
              Text(
                '使用技巧',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: AppTheme.textDarkColor,
                ),
              ),
            ],
          ),
          SizedBox(height: 16),
          _buildTipItem(
            icon: Icons.touch_app,
            title: '长按选择',
            description: '长按文档卡片可进入多选模式，批量操作更高效',
          ),
          SizedBox(height: 12),
          _buildTipItem(
            icon: Icons.security,
            title: '安全加密',
            description: '为重要文档设置密码保护，确保信息安全',
          ),
          SizedBox(height: 12),
          _buildTipItem(
            icon: Icons.merge_type,
            title: '文档合并',
            description: '选择多个PDF文档，一键合并成单个文件',
          ),
        ],
      ),
    );
  }

  /// 构建技巧项
  Widget _buildTipItem({
    required IconData icon,
    required String title,
    required String description,
  }) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: EdgeInsets.all(6),
          decoration: BoxDecoration(
            color: AppTheme.primaryColor.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(6),
          ),
          child: Icon(icon, size: 16, color: AppTheme.primaryColor),
        ),
        SizedBox(width: 12),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                title,
                style: TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: AppTheme.textDarkColor,
                ),
              ),
              SizedBox(height: 2),
              Text(
                description,
                style: TextStyle(
                  fontSize: 13,
                  color: AppTheme.textMediumColor,
                  height: 1.3,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  /// 构建功能项
  Widget _buildFeatureItem({
    required IconData icon,
    required String title,
    required String description,
    required Color color,
  }) {
    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.2)),
      ),
      child: Row(
        children: [
          Container(
            padding: EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(icon, color: color, size: 20),
          ),
          SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.textDarkColor,
                  ),
                ),
                SizedBox(height: 4),
                Text(
                  description,
                  style: TextStyle(
                    fontSize: 14,
                    color: AppTheme.textMediumColor,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 显示注释功能介绍
  void _showAnnotationFeatures() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder:
          (context) => DraggableScrollableSheet(
            initialChildSize: 0.7,
            maxChildSize: 0.9,
            minChildSize: 0.5,
            builder:
                (context, scrollController) => Container(
                  padding: EdgeInsets.all(24),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Container(
                            padding: EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              gradient: AppTheme.greenGradient,
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Icon(
                              Icons.edit_note,
                              color: Colors.white,
                              size: 24,
                            ),
                          ),
                          SizedBox(width: 16),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'PDF 智能注释',
                                  style: TextStyle(
                                    fontSize: 24,
                                    fontWeight: FontWeight.bold,
                                    color: AppTheme.textDarkColor,
                                  ),
                                ),
                                Text(
                                  '高效标记和批注工具',
                                  style: TextStyle(
                                    fontSize: 16,
                                    color: AppTheme.textMediumColor,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 24),
                      Expanded(
                        child: ListView(
                          controller: scrollController,
                          children: [
                            _buildFeatureItem(
                              icon: Icons.highlight,
                              title: '文本高亮',
                              description: '选择重要文本进行高亮标记，支持多种颜色',
                              color: AppTheme.greenGradient.colors.first,
                            ),
                            SizedBox(height: 16),
                            _buildFeatureItem(
                              icon: Icons.note_add,
                              title: '文字批注',
                              description: '在任意位置添加文字注释，记录想法和备注',
                              color: AppTheme.greenGradient.colors.first,
                            ),
                            SizedBox(height: 16),
                            _buildFeatureItem(
                              icon: Icons.bookmark,
                              title: '书签管理',
                              description: '创建书签快速定位到重要页面和章节',
                              color: AppTheme.greenGradient.colors.first,
                            ),
                            SizedBox(height: 24),
                            SizedBox(
                              width: double.infinity,
                              child: ElevatedButton.icon(
                                onPressed: () {
                                  Navigator.pop(context);
                                  _importPdf();
                                },
                                icon: Icon(Icons.add),
                                label: Text('导入PDF开始使用'),
                                style: ElevatedButton.styleFrom(
                                  backgroundColor:
                                      AppTheme.greenGradient.colors.first,
                                  foregroundColor: Colors.white,
                                  padding: EdgeInsets.symmetric(vertical: 16),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
          ),
    );
  }

  /// 显示搜索功能介绍
  void _showSearchFeatures() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder:
          (context) => DraggableScrollableSheet(
            initialChildSize: 0.7,
            maxChildSize: 0.9,
            minChildSize: 0.5,
            builder:
                (context, scrollController) => Container(
                  padding: EdgeInsets.all(24),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Container(
                            padding: EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              gradient: AppTheme.purpleGradient,
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Icon(
                              Icons.search,
                              color: Colors.white,
                              size: 24,
                            ),
                          ),
                          SizedBox(width: 16),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'PDF 快速搜索',
                                  style: TextStyle(
                                    fontSize: 24,
                                    fontWeight: FontWeight.bold,
                                    color: AppTheme.textDarkColor,
                                  ),
                                ),
                                Text(
                                  '强大的内容检索能力',
                                  style: TextStyle(
                                    fontSize: 16,
                                    color: AppTheme.textMediumColor,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 24),
                      Expanded(
                        child: ListView(
                          controller: scrollController,
                          children: [
                            _buildFeatureItem(
                              icon: Icons.search,
                              title: '全文检索',
                              description: '在整个PDF文档中快速搜索关键词和短语',
                              color: AppTheme.purpleGradient.colors.first,
                            ),
                            SizedBox(height: 16),
                            _buildFeatureItem(
                              icon: Icons.location_on,
                              title: '精确定位',
                              description: '自动跳转到搜索结果所在的页面和位置',
                              color: AppTheme.purpleGradient.colors.first,
                            ),
                            SizedBox(height: 16),
                            _buildFeatureItem(
                              icon: Icons.filter_list,
                              title: '智能过滤',
                              description: '按文档名称、创建时间等条件筛选PDF文件',
                              color: AppTheme.purpleGradient.colors.first,
                            ),
                            SizedBox(height: 24),
                            SizedBox(
                              width: double.infinity,
                              child: ElevatedButton.icon(
                                onPressed: () {
                                  Navigator.pop(context);
                                  _importPdf();
                                },
                                icon: Icon(Icons.add),
                                label: Text('导入PDF开始使用'),
                                style: ElevatedButton.styleFrom(
                                  backgroundColor:
                                      AppTheme.purpleGradient.colors.first,
                                  foregroundColor: Colors.white,
                                  padding: EdgeInsets.symmetric(vertical: 16),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
          ),
    );
  }

  /// 显示分享功能介绍
  void _showSharingFeatures() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder:
          (context) => DraggableScrollableSheet(
            initialChildSize: 0.7,
            maxChildSize: 0.9,
            minChildSize: 0.5,
            builder:
                (context, scrollController) => Container(
                  padding: EdgeInsets.all(24),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Container(
                            padding: EdgeInsets.all(12),
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                colors: [Color(0xFF4facfe), Color(0xFF00f2fe)],
                              ),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Icon(
                              Icons.share,
                              color: Colors.white,
                              size: 24,
                            ),
                          ),
                          SizedBox(width: 16),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'PDF 便捷分享',
                                  style: TextStyle(
                                    fontSize: 24,
                                    fontWeight: FontWeight.bold,
                                    color: AppTheme.textDarkColor,
                                  ),
                                ),
                                Text(
                                  '多种方式分享您的文档',
                                  style: TextStyle(
                                    fontSize: 16,
                                    color: AppTheme.textMediumColor,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                      SizedBox(height: 24),
                      Expanded(
                        child: ListView(
                          controller: scrollController,
                          children: [
                            _buildFeatureItem(
                              icon: Icons.share,
                              title: '一键分享',
                              description: '通过邮件、消息等方式快速分享PDF文档',
                              color: Color(0xFF4facfe),
                            ),
                            SizedBox(height: 16),
                            _buildFeatureItem(
                              icon: Icons.file_download,
                              title: '格式导出',
                              description: '将PDF转换为图片、文本等多种格式导出',
                              color: Color(0xFF4facfe),
                            ),
                            SizedBox(height: 16),
                            _buildFeatureItem(
                              icon: Icons.cloud_upload,
                              title: '云端同步',
                              description: '上传到云端存储，随时随地访问您的文档',
                              color: Color(0xFF4facfe),
                            ),
                            SizedBox(height: 24),
                            SizedBox(
                              width: double.infinity,
                              child: ElevatedButton.icon(
                                onPressed: () {
                                  Navigator.pop(context);
                                  _importPdf();
                                },
                                icon: Icon(Icons.add),
                                label: Text('导入PDF开始使用'),
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: Color(0xFF4facfe),
                                  foregroundColor: Colors.white,
                                  padding: EdgeInsets.symmetric(vertical: 16),
                                  shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
          ),
    );
  }

  /// 显示合并功能介绍
  void _showMergeFeatures() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.7,
        maxChildSize: 0.9,
        minChildSize: 0.5,
        builder: (context, scrollController) => Container(
          padding: EdgeInsets.all(24),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [Color(0xFFffecd2), Color(0xFFfcb69f)],
                      ),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Icon(
                      Icons.merge_type,
                      color: Colors.white,
                      size: 24,
                    ),
                  ),
                  SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'PDF 文档合并',
                          style: TextStyle(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                            color: AppTheme.textDarkColor,
                          ),
                        ),
                        Text(
                          '高效的批量处理工具',
                          style: TextStyle(
                            fontSize: 16,
                            color: AppTheme.textMediumColor,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              SizedBox(height: 24),
              Expanded(
                child: ListView(
                  controller: scrollController,
                  children: [
                    _buildFeatureItem(
                      icon: Icons.merge_type,
                      title: '多文件合并',
                      description: '选择多个PDF文档，按顺序合并成单个文件',
                      color: Color(0xFFfcb69f),
                    ),
                    SizedBox(height: 16),
                    _buildFeatureItem(
                      icon: Icons.sort,
                      title: '自定义顺序',
                      description: '拖拽调整文档顺序，灵活控制合并结果',
                      color: Color(0xFFfcb69f),
                    ),
                    SizedBox(height: 16),
                    _buildFeatureItem(
                      icon: Icons.speed,
                      title: '快速处理',
                      description: '高效的合并算法，大文件也能快速完成',
                      color: Color(0xFFfcb69f),
                    ),
                    SizedBox(height: 24),
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton.icon(
                        onPressed: () {
                          Navigator.pop(context);
                          _importPdf();
                        },
                        icon: Icon(Icons.add),
                        label: Text('导入PDF开始使用'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Color(0xFFfcb69f),
                          foregroundColor: Colors.white,
                          padding: EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 显示云端功能介绍
  void _showCloudFeatures() {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
      ),
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.7,
        maxChildSize: 0.9,
        minChildSize: 0.5,
        builder: (context, scrollController) => Container(
          padding: EdgeInsets.all(24),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [Color(0xFFa8edea), Color(0xFFfed6e3)],
                      ),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Icon(
                      Icons.cloud_sync,
                      color: Colors.white,
                      size: 24,
                    ),
                  ),
                  SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'PDF 云端同步',
                          style: TextStyle(
                            fontSize: 24,
                            fontWeight: FontWeight.bold,
                            color: AppTheme.textDarkColor,
                          ),
                        ),
                        Text(
                          '多设备无缝同步体验',
                          style: TextStyle(
                            fontSize: 16,
                            color: AppTheme.textMediumColor,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
              SizedBox(height: 24),
              Expanded(
                child: ListView(
                  controller: scrollController,
                  children: [
                    _buildFeatureItem(
                      icon: Icons.sync,
                      title: '自动同步',
                      description: '文档自动备份到云端，多设备实时同步',
                      color: Color(0xFFa8edea),
                    ),
                    SizedBox(height: 16),
                    _buildFeatureItem(
                      icon: Icons.backup,
                      title: '安全备份',
                      description: '重要文档云端备份，永不丢失',
                      color: Color(0xFFa8edea),
                    ),
                    SizedBox(height: 16),
                    _buildFeatureItem(
                      icon: Icons.devices,
                      title: '跨设备访问',
                      description: '手机、平板、电脑随时随地访问您的文档',
                      color: Color(0xFFa8edea),
                    ),
                    SizedBox(height: 24),
                    SizedBox(
                      width: double.infinity,
                      child: ElevatedButton.icon(
                        onPressed: () {
                          Navigator.pop(context);
                          _importPdf();
                        },
                        icon: Icon(Icons.add),
                        label: Text('导入PDF开始使用'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Color(0xFFa8edea),
                          foregroundColor: Colors.white,
                          padding: EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// 显示演示内容
  void _showDemoContent() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: Row(
          children: [
            Icon(Icons.play_circle_outline, color: AppTheme.primaryColor),
            SizedBox(width: 8),
            Text('功能演示'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '体验PDF专业工具的强大功能：',
              style: TextStyle(fontWeight: FontWeight.w600),
            ),
            SizedBox(height: 12),
            _buildDemoItem('📄', '文档管理', '导入、组织、搜索PDF文件'),
            _buildDemoItem('🔒', '安全加密', '密码保护和权限控制'),
            _buildDemoItem('✏️', '智能注释', '高亮、批注、书签功能'),
            _buildDemoItem('🔄', '文档合并', '多文件批量处理'),
            _buildDemoItem('☁️', '云端同步', '多设备无缝同步'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('了解了'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _importPdf();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.primaryColor,
              foregroundColor: Colors.white,
            ),
            child: Text('立即体验'),
          ),
        ],
      ),
    );
  }

  /// 构建演示项
  Widget _buildDemoItem(String emoji, String title, String description) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(emoji, style: TextStyle(fontSize: 16)),
          SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontWeight: FontWeight.w600,
                    fontSize: 14,
                  ),
                ),
                Text(
                  description,
                  style: TextStyle(
                    fontSize: 12,
                    color: AppTheme.textMediumColor,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// 显示帮助信息
  void _showHelp() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: Row(
          children: [
            Icon(Icons.help_outline, color: AppTheme.primaryColor),
            SizedBox(width: 8),
            Text('使用帮助'),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '快速上手指南：',
              style: TextStyle(fontWeight: FontWeight.w600),
            ),
            SizedBox(height: 12),
            _buildHelpItem('1', '点击"导入PDF文档"按钮选择文件'),
            _buildHelpItem('2', '长按文档卡片进入多选模式'),
            _buildHelpItem('3', '点击文档卡片打开PDF阅读器'),
            _buildHelpItem('4', '使用右上角菜单进行更多操作'),
            SizedBox(height: 12),
            Container(
              padding: EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: AppTheme.bgIndigo50,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  Icon(Icons.info_outline, 
                       size: 16, 
                       color: AppTheme.primaryColor),
                  SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      '支持PDF格式，最大文件100MB',
                      style: TextStyle(
                        fontSize: 12,
                        color: AppTheme.textMediumColor,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('知道了'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _importPdf();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.primaryColor,
              foregroundColor: Colors.white,
            ),
            child: Text('开始使用'),
          ),
        ],
      ),
    );
  }

  /// 构建帮助项
  Widget _buildHelpItem(String step, String description) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 20,
            height: 20,
            decoration: BoxDecoration(
              color: AppTheme.primaryColor,
              shape: BoxShape.circle,
            ),
            child: Center(
              child: Text(
                step,
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
          SizedBox(width: 12),
          Expanded(
            child: Text(
              description,
              style: TextStyle(fontSize: 14),
            ),
          ),
        ],
      ),
    );
  }
}
