import 'dart:io';
import 'package:flutter/material.dart';
import 'package:syncfusion_flutter_pdfviewer/pdfviewer.dart'
    hide HighlightAnnotation;
import 'package:share_plus/share_plus.dart';

import '../config/app_theme.dart';
import '../generated/l10n/app_localizations.dart';
import 'models/pdf_document.dart' as app_model;
import 'models/pdf_annotation.dart';
import 'pdf_service.dart';

/// PDF查看器屏幕
class PdfViewerScreen extends StatefulWidget {
  /// PDF文档
  final app_model.PdfDocument document;

  const PdfViewerScreen({super.key, required this.document});

  @override
  State<PdfViewerScreen> createState() => _PdfViewerScreenState();
}

class _PdfViewerScreenState extends State<PdfViewerScreen> {
  /// PDF查看器控制器
  final PdfViewerController _pdfViewerController = PdfViewerController();

  /// 当前页码
  int _currentPage = 1;

  /// 总页数
  int _totalPages = 0;

  /// 是否显示工具栏
  bool _showToolbar = true;

  /// 是否显示注释
  bool _showAnnotations = true;

  /// 注释列表
  List<PdfAnnotation> _annotations = [];

  /// PDF服务
  final _pdfService = PdfService();

  /// 搜索控制器
  final _searchController = TextEditingController();

  /// 是否正在搜索
  bool _isSearching = false;

  /// 搜索结果
  PdfTextSearchResult _searchResult = PdfTextSearchResult();

  /// 国际化
  late AppLocalizations _l10n;

  @override
  void initState() {
    super.initState();
    _loadAnnotations();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _l10n = AppLocalizations.of(context);
  }

  /// 加载注释
  Future<void> _loadAnnotations() async {
    final annotations = await _pdfService.getAnnotations(widget.document.id);
    setState(() {
      _annotations = annotations;
    });
  }

  @override
  void dispose() {
    _pdfViewerController.dispose();
    _searchController.dispose();
    _searchResult.clear();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: _buildAppBar(),
      body: _buildBody(),
      bottomNavigationBar: _showToolbar ? _buildBottomBar() : null,
    );
  }

  /// 构建应用栏
  PreferredSizeWidget _buildAppBar() {
    if (_isSearching) {
      return AppBar(
        title: TextField(
          controller: _searchController,
          decoration: InputDecoration(
            hintText: _l10n.pdfSearch,
            border: InputBorder.none,
            hintStyle: TextStyle(color: AppTheme.textLightColor),
          ),
          style: TextStyle(color: AppTheme.textDarkColor),
          autofocus: true,
          onSubmitted: (value) {
            if (value.isNotEmpty) {
              _searchText(value);
            }
          },
        ),
        leading: IconButton(
          icon: Icon(Icons.arrow_back),
          onPressed: () {
            setState(() {
              _isSearching = false;
              _searchResult.clear();
            });
          },
        ),
        actions: [
          IconButton(
            icon: Icon(Icons.search),
            onPressed: () {
              if (_searchController.text.isNotEmpty) {
                _searchText(_searchController.text);
              }
            },
          ),
          IconButton(
            icon: Icon(Icons.clear),
            onPressed: () {
              _searchController.clear();
              _searchResult.clear();
            },
          ),
        ],
      );
    }

    return AppBar(
      title: Text(
        widget.document.fileName,
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      ),
      actions: [
        IconButton(
          icon: Icon(Icons.search),
          onPressed: () {
            setState(() {
              _isSearching = true;
            });
          },
        ),
        IconButton(icon: Icon(Icons.share), onPressed: _sharePdf),
        PopupMenuButton<String>(
          onSelected: (value) {
            switch (value) {
              case 'annotations':
                setState(() {
                  _showAnnotations = !_showAnnotations;
                });
                break;
              case 'info':
                _showDocumentInfo();
                break;
            }
          },
          itemBuilder:
              (context) => [
                PopupMenuItem(
                  value: 'annotations',
                  child: Row(
                    children: [
                      Icon(
                        _showAnnotations
                            ? Icons.visibility
                            : Icons.visibility_off,
                        color: AppTheme.textDarkColor,
                      ),
                      SizedBox(width: 8),
                      Text(_showAnnotations ? (_l10n.pdfHideAnnotations) : (_l10n.pdfShowAnnotations)),
                    ],
                  ),
                ),
                PopupMenuItem(
                  value: 'info',
                  child: Row(
                    children: [
                      Icon(Icons.info_outline, color: AppTheme.textDarkColor),
                      SizedBox(width: 8),
                      Text(_l10n.pdfDocumentInfo),
                    ],
                  ),
                ),
              ],
        ),
      ],
    );
  }

  /// 搜索文本
  void _searchText(String text) {
    _searchResult = _pdfViewerController.searchText(text);
    _searchResult.addListener(() {
      if (mounted) {
        setState(() {});
      }
    });
  }

  /// 构建主体
  Widget _buildBody() {
    return Stack(
      children: [
        // PDF查看器
        SfPdfViewer.file(
          File(widget.document.filePath),
          controller: _pdfViewerController,
          onDocumentLoaded: (details) {
            setState(() {
              _totalPages = details.document.pages.count;
            });
          },
          onPageChanged: (details) {
            setState(() {
              _currentPage = details.newPageNumber;
            });
          },
          onTap: (details) {
            // 点击切换工具栏显示状态
            setState(() {
              _showToolbar = !_showToolbar;
            });
          },
          enableDoubleTapZooming: true,
          canShowScrollHead: true,
          canShowScrollStatus: true,
          canShowPaginationDialog: true,
          enableTextSelection: true,
          pageSpacing: 8.0,
        ),

        if (_showAnnotations) ..._buildAnnotationOverlays(),
      ],
    );
  }

  /// 构建底部栏
  Widget _buildBottomBar() {
    return BottomAppBar(
      height: 56,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          IconButton(
            icon: Icon(Icons.first_page),
            onPressed: () {
              _pdfViewerController.firstPage();
            },
          ),
          IconButton(
            icon: Icon(Icons.navigate_before),
            onPressed: () {
              _pdfViewerController.previousPage();
            },
          ),
          Text(
            '$_currentPage / $_totalPages',
            style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
          ),
          IconButton(
            icon: Icon(Icons.navigate_next),
            onPressed: () {
              _pdfViewerController.nextPage();
            },
          ),
          IconButton(
            icon: Icon(Icons.last_page),
            onPressed: () {
              _pdfViewerController.lastPage();
            },
          ),
        ],
      ),
    );
  }

  /// 构建注释覆盖层
  List<Widget> _buildAnnotationOverlays() {
    // 筛选当前页的注释
    final currentPageAnnotations =
        _annotations
            .where((annotation) => annotation.pageNumber == _currentPage)
            .toList();

    // 转换为Widget
    return currentPageAnnotations.map((annotation) {
      // 根据注释类型渲染不同的UI
      switch (annotation.type) {
        case PdfAnnotationType.text:
          final textAnnotation = annotation as TextAnnotation;
          return Positioned(
            left: textAnnotation.bounds.left,
            top: textAnnotation.bounds.top,
            child: GestureDetector(
              onTap: () => _showAnnotationDetails(textAnnotation),
              child: Container(
                width: textAnnotation.bounds.width,
                padding: EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: textAnnotation.color.withValues(alpha: 0.7),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  textAnnotation.content,
                  style: TextStyle(
                    fontSize: textAnnotation.fontSize,
                    fontWeight:
                        textAnnotation.isBold
                            ? FontWeight.bold
                            : FontWeight.normal,
                    fontStyle:
                        textAnnotation.isItalic
                            ? FontStyle.italic
                            : FontStyle.normal,
                    color: Colors.white,
                  ),
                ),
              ),
            ),
          );

        case PdfAnnotationType.highlight:
          final highlightAnnotation = annotation as HighlightAnnotation;
          return Positioned(
            left: highlightAnnotation.bounds.left,
            top: highlightAnnotation.bounds.top,
            child: GestureDetector(
              onTap: () => _showAnnotationDetails(highlightAnnotation),
              child: Container(
                width: highlightAnnotation.bounds.width,
                height: highlightAnnotation.bounds.height,
                decoration: BoxDecoration(
                  color: highlightAnnotation.color.withValues(alpha: 0.3),
                  border: Border.all(
                    color: highlightAnnotation.color,
                    width: 1,
                  ),
                ),
              ),
            ),
          );

        default:
          return SizedBox.shrink();
      }
    }).toList();
  }

  /// 显示注释详情
  void _showAnnotationDetails(PdfAnnotation annotation) {
    showModalBottomSheet(
      context: context,
      builder: (context) {
        return Padding(
          padding: EdgeInsets.all(16),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    _l10n.pdfAnnotationDetails,
                    style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                  ),
                  IconButton(
                    icon: Icon(Icons.delete),
                    onPressed: () {
                      Navigator.pop(context);
                      _deleteAnnotation(annotation);
                    },
                  ),
                ],
              ),
              SizedBox(height: 8),
              Text(_l10n.pdfAuthor(annotation.author)),
              Text(_l10n.pdfCreatedAt(annotation.createdAt, annotation.createdAt.toString().substring(0, 16))),
              SizedBox(height: 16),
              if (annotation is TextAnnotation)
                Text(
                  _l10n.pdfContent(annotation.content),
                  style: TextStyle(fontSize: 16),
                )
              else if (annotation is HighlightAnnotation)
                Text(
                  _l10n.pdfHighlightedText(annotation.text),
                  style: TextStyle(fontSize: 16),
                ),
            ],
          ),
        );
      },
    );
  }

  /// 删除注释
  Future<void> _deleteAnnotation(PdfAnnotation annotation) async {
    await _pdfService.deleteAnnotation(widget.document.id, annotation.id);
    await _loadAnnotations();
  }

  /// 分享PDF
  void _sharePdf() {
    SharePlus.instance.share(
      ShareParams(files: [XFile(widget.document.filePath)], text: widget.document.fileName),
    );
  }

  /// 显示文档信息
  void _showDocumentInfo() {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          title: Text(_l10n.pdfDocumentInfo),
          content: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildInfoRow(_l10n.pdfFileName, widget.document.fileName),
              _buildInfoRow(_l10n.pdfFileSize, widget.document.formattedSize),
              _buildInfoRow(_l10n.pdfPageCount, '${widget.document.pageCount ?? "未知"}'),
              _buildInfoRow(
                _l10n.pdfCreatedTime,
                widget.document.createdAt.toString().substring(0, 16),
              ),
              _buildInfoRow(
                _l10n.pdfModifiedTime,
                widget.document.modifiedAt.toString().substring(0, 16),
              ),
              _buildInfoRow(_l10n.pdfAnnotationCount, '${widget.document.annotationCount}'),
            ],
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text(_l10n.pdfClose),
            ),
          ],
        );
      },
    );
  }

  /// 构建信息行
  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 80,
            child: Text(
              '$label:',
              style: TextStyle(fontWeight: FontWeight.bold),
            ),
          ),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }
}
