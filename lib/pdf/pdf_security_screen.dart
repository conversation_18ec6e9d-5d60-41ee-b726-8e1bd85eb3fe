import 'package:flutter/material.dart';
import '../config/app_theme.dart';
import '../generated/l10n/app_localizations.dart';
import 'models/pdf_document.dart';
import 'models/pdf_security.dart';
import 'pdf_service.dart';

/// PDF安全设置界面
class PdfSecurityScreen extends StatefulWidget {
  final PdfDocument document;

  const PdfSecurityScreen({super.key, required this.document});

  @override
  State<PdfSecurityScreen> createState() => _PdfSecurityScreenState();
}

class _PdfSecurityScreenState extends State<PdfSecurityScreen> {
  /// PDF服务
  final _pdfService = PdfService();

  /// 用户密码控制器
  final _userPasswordController = TextEditingController();

  /// 所有者密码控制器
  final _ownerPasswordController = TextEditingController();

  /// 当前密码控制器（用于解密）
  final _currentPasswordController = TextEditingController();

  /// 是否正在加载
  bool _isLoading = false;

  /// 是否显示用户密码
  bool _showUserPassword = false;

  /// 是否显示所有者密码
  bool _showOwnerPassword = false;

  /// 是否显示当前密码
  bool _showCurrentPassword = false;

  /// 权限设置
  late PdfPermissions _permissions;

  /// 是否加密
  bool _isEncrypted = false;

  /// 国际化
  late AppLocalizations _l10n;

  @override
  void initState() {
    super.initState();
    _initializeData();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _l10n = AppLocalizations.of(context);
  }

  /// 初始化数据
  void _initializeData() {
    _permissions = widget.document.security.permissions.copyWith();
    _isEncrypted = widget.document.security.isEncrypted;
  }

  /// 执行加密
  Future<void> _encryptPdf() async {
    if (_userPasswordController.text.isEmpty) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text(_l10n.pdfEnterUserPassword)));
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final success = await _pdfService.encryptPdf(
        widget.document.id,
        _userPasswordController.text,
        _ownerPasswordController.text.isNotEmpty
            ? _ownerPasswordController.text
            : null,
        _permissions,
      );

      if (success) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text(_l10n.pdfEncryptSuccess)));
        Navigator.pop(context, true);
      } else {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text(_l10n.pdfEncryptFailed)));
      }
    } catch (e) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text(_l10n.pdfEncryptionFailed(e.toString()))));
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// 执行解密
  Future<void> _decryptPdf() async {
    if (_currentPasswordController.text.isEmpty) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text(_l10n.pdfEnterCurrentPassword)));
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final success = await _pdfService.decryptPdf(
        widget.document.id,
        _currentPasswordController.text,
      );

      if (success) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text(_l10n.pdfDecryptSuccess)));
        Navigator.pop(context, true);
      } else {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text(_l10n.pdfDecryptFailed)));
      }
    } catch (e) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text(_l10n.pdfDecryptionFailed(e.toString()))));
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  /// 设置权限
  Future<void> _setPermissions() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final success = await _pdfService.setPdfPermissions(
        widget.document.id,
        _permissions,
        _ownerPasswordController.text.isNotEmpty
            ? _ownerPasswordController.text
            : null,
      );

      if (success) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text(_l10n.pdfPermissionsSuccess)));
        Navigator.pop(context, true);
      } else {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text(_l10n.pdfPermissionsFailed)));
      }
    } catch (e) {
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text(_l10n.pdfSettingsFailed(e.toString()))));
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  @override
  void dispose() {
    _userPasswordController.dispose();
    _ownerPasswordController.dispose();
    _currentPasswordController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(_l10n.pdfSecuritySettings),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
      ),
      body:
          _isLoading
              ? const Center(child: CircularProgressIndicator())
              : _buildBody(),
    );
  }

  /// 构建主体内容
  Widget _buildBody() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildDocumentInfo(),
          const SizedBox(height: 24),
          if (_isEncrypted) ...[
            _buildDecryptSection(),
            const SizedBox(height: 24),
          ] else ...[
            _buildEncryptSection(),
            const SizedBox(height: 24),
            _buildPermissionsSection(),
            const SizedBox(height: 24),
            _buildPresetPermissions(),
            const SizedBox(height: 24),
            _buildActionButtons(),
          ],
        ],
      ),
    );
  }

  /// 构建文档信息
  Widget _buildDocumentInfo() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(_l10n.pdfDocumentInformation, style: Theme.of(context).textTheme.titleLarge),
            const SizedBox(height: 8),
            Row(
              children: [
                const Icon(Icons.picture_as_pdf, size: 20),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    widget.document.fileName,
                    style: Theme.of(context).textTheme.bodyMedium,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                const Icon(Icons.security, size: 20),
                const SizedBox(width: 8),
                Text(
                  _l10n.pdfStatus(widget.document.securityStatus),
                  style: Theme.of(context).textTheme.bodyMedium,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// 构建解密部分
  Widget _buildDecryptSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(_l10n.pdfDecryptPdf, style: Theme.of(context).textTheme.titleLarge),
            const SizedBox(height: 16),
            Text(
              _l10n.pdfEncryptedMessage,
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _currentPasswordController,
              obscureText: !_showCurrentPassword,
              decoration: InputDecoration(
                labelText: _l10n.pdfCurrentPassword,
                hintText: _l10n.pdfEnterCurrentPassword,
                border: const OutlineInputBorder(),
                suffixIcon: IconButton(
                  icon: Icon(
                    _showCurrentPassword
                        ? Icons.visibility_off
                        : Icons.visibility,
                  ),
                  onPressed: () {
                    setState(() {
                      _showCurrentPassword = !_showCurrentPassword;
                    });
                  },
                ),
              ),
            ),
            const SizedBox(height: 16),
            SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: _decryptPdf,
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.primaryColor,
                  foregroundColor: Colors.white,
                ),
                child: Text(_l10n.pdfDecryptButton),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建加密部分
  Widget _buildEncryptSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(_l10n.pdfEncryptionSettings, style: Theme.of(context).textTheme.titleLarge),
            const SizedBox(height: 16),
            TextFormField(
              controller: _userPasswordController,
              obscureText: !_showUserPassword,
              decoration: InputDecoration(
                labelText: _l10n.pdfUserPassword,
                hintText: _l10n.pdfUserPasswordHint,
                border: const OutlineInputBorder(),
                suffixIcon: IconButton(
                  icon: Icon(
                    _showUserPassword ? Icons.visibility_off : Icons.visibility,
                  ),
                  onPressed: () {
                    setState(() {
                      _showUserPassword = !_showUserPassword;
                    });
                  },
                ),
              ),
            ),
            const SizedBox(height: 16),
            TextFormField(
              controller: _ownerPasswordController,
              obscureText: !_showOwnerPassword,
              decoration: InputDecoration(
                labelText: _l10n.pdfOwnerPassword,
                hintText: _l10n.pdfOwnerPasswordHint,
                border: const OutlineInputBorder(),
                suffixIcon: IconButton(
                  icon: Icon(
                    _showOwnerPassword
                        ? Icons.visibility_off
                        : Icons.visibility,
                  ),
                  onPressed: () {
                    setState(() {
                      _showOwnerPassword = !_showOwnerPassword;
                    });
                  },
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// 构建权限设置部分
  Widget _buildPermissionsSection() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(_l10n.pdfPermissionSettings, style: Theme.of(context).textTheme.titleLarge),
            const SizedBox(height: 16),
            _buildPermissionSwitch(
              _l10n.pdfAllowPrint,
              _l10n.pdfAllowPrintDesc,
              _permissions.allowPrint,
              (value) {
                setState(() {
                  _permissions.allowPrint = value;
                });
              },
            ),
            _buildPermissionSwitch(
              _l10n.pdfAllowCopy,
              _l10n.pdfAllowCopyDesc,
              _permissions.allowCopy,
              (value) {
                setState(() {
                  _permissions.allowCopy = value;
                });
              },
            ),
            _buildPermissionSwitch(
              _l10n.pdfAllowEdit,
              _l10n.pdfAllowEditDesc,
              _permissions.allowEdit,
              (value) {
                setState(() {
                  _permissions.allowEdit = value;
                });
              },
            ),
            _buildPermissionSwitch(
              _l10n.pdfAllowEditAnnotations,
              _l10n.pdfAllowEditAnnotationsDesc,
              _permissions.allowEditAnnotations,
              (value) {
                setState(() {
                  _permissions.allowEditAnnotations = value;
                });
              },
            ),
            _buildPermissionSwitch(
              _l10n.pdfAllowFillForms,
              _l10n.pdfAllowFillFormsDesc,
              _permissions.allowFillForms,
              (value) {
                setState(() {
                  _permissions.allowFillForms = value;
                });
              },
            ),
            _buildPermissionSwitch(
              _l10n.pdfAllowExtractPages,
              _l10n.pdfAllowExtractPagesDesc,
              _permissions.allowExtractPages,
              (value) {
                setState(() {
                  _permissions.allowExtractPages = value;
                });
              },
            ),
            _buildPermissionSwitch(
              _l10n.pdfAllowAssembleDocument,
              _l10n.pdfAllowAssembleDocumentDesc,
              _permissions.allowAssembleDocument,
              (value) {
                setState(() {
                  _permissions.allowAssembleDocument = value;
                });
              },
            ),
            _buildPermissionSwitch(
              _l10n.pdfAllowHighQualityPrint,
              _l10n.pdfAllowHighQualityPrintDesc,
              _permissions.allowHighQualityPrint,
              (value) {
                setState(() {
                  _permissions.allowHighQualityPrint = value;
                });
              },
            ),
          ],
        ),
      ),
    );
  }

  /// 构建权限开关
  Widget _buildPermissionSwitch(
    String title,
    String subtitle,
    bool value,
    ValueChanged<bool> onChanged,
  ) {
    return SwitchListTile(
      title: Text(title),
      subtitle: Text(subtitle),
      value: value,
      onChanged: onChanged,
      activeColor: AppTheme.primaryColor,
    );
  }

  /// 构建预设权限
  Widget _buildPresetPermissions() {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(_l10n.pdfPresetPermissions, style: Theme.of(context).textTheme.titleLarge),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _buildPresetButton(_l10n.pdfAllPermissions, Icons.lock_open, () {
                  setState(() {
                    _permissions = PdfPermissions.all;
                  });
                }),
                _buildPresetButton(_l10n.pdfBasicPermissions, Icons.lock_outline, () {
                  setState(() {
                    _permissions = PdfPermissions.basic;
                  });
                }),
                _buildPresetButton(_l10n.pdfReadOnly, Icons.lock, () {
                  setState(() {
                    _permissions = PdfPermissions.readOnly;
                  });
                }),
              ],
            ),
          ],
        ),
      ),
    );
  }

  /// 构建预设按钮
  Widget _buildPresetButton(
    String label,
    IconData icon,
    VoidCallback onPressed,
  ) {
    return ElevatedButton(
      onPressed: onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: AppTheme.primaryColor.withValues(alpha: 0.1),
        foregroundColor: AppTheme.primaryColor,
      ),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [Icon(icon), const SizedBox(height: 4), Text(label)],
      ),
    );
  }

  /// 构建操作按钮
  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: ElevatedButton(
            onPressed: _setPermissions,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.primaryColor.withValues(alpha: 0.1),
              foregroundColor: AppTheme.primaryColor,
            ),
            child: Text(_l10n.pdfSetPermissionsOnly),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: ElevatedButton(
            onPressed: _encryptPdf,
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.primaryColor,
              foregroundColor: Colors.white,
            ),
            child: Text(_l10n.pdfEncryptAndSetPermissions),
          ),
        ),
      ],
    );
  }
}
