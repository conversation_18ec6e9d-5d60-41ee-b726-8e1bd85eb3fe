import 'dart:io';
import 'dart:math' as math;
import 'dart:convert';
import 'package:flutter/foundation.dart' show kIsWeb;

import 'package:flutter/material.dart';
import 'package:path/path.dart' as path;

import '../html/html_manager_screen.dart';
import '../markdown/markdown_render_screen.dart';
import '../svg/svg_manager_screen.dart';
import '../text_cards/models/text_card_model.dart';
import '../text_cards/text_card_editor_page.dart';
import 'localization_service.dart';

enum FileType { markdown, text, svg, html, unknown }

class FileIntentService {
  static final FileIntentService _instance = FileIntentService._internal();
  factory FileIntentService() => _instance;
  FileIntentService._internal();

  /// 获取文件类型
  FileType getFileType(String filePath) {
    final extension = path.extension(filePath).toLowerCase();

    switch (extension) {
      case '.md':
      case '.markdown':
        return FileType.markdown;
      case '.txt':
      case '.text':
        return FileType.text;
      case '.svg':
        return FileType.svg;
      case '.html':
      case '.htm':
        return FileType.html;
      default:
        return FileType.unknown;
    }
  }

  /// 根据文件类型导航到对应的编辑器
  Future<void> navigateToEditor(
    BuildContext context,
    String filePath,
    FileType fileType,
  ) async {
    // 读取文件内容
    String? fileContent;
    try {
      final file = File(filePath);
      
      // 添加详细日志
      debugPrint('=== 文件读取调试信息 ===');
      debugPrint('文件路径: $filePath');
      debugPrint('文件是否存在: ${file.existsSync()}');
      
      if (file.existsSync()) {
        final fileSize = file.lengthSync();
        debugPrint('文件大小: $fileSize 字节');
        
        // 检查文件权限
        try {
          final canRead = file.openRead().first.then((bytes) => bytes.isNotEmpty).timeout(
            const Duration(seconds: 2),
            onTimeout: () => false,
          );
          debugPrint('文件可读性检查: $canRead');
        } catch (e) {
          debugPrint('文件可读性检查失败: $e');
        }
        
        // iOS特定处理
      if (!kIsWeb && Platform.isIOS) {
        debugPrint('iOS平台，使用特殊文件处理');
        // 首先尝试直接读取
        try {
          fileContent = await file.readAsString(encoding: utf8);
        } catch (e) {
          debugPrint('直接读取失败，尝试使用备用方法: $e');
          
          // 如果直接读取失败，尝试通过iOS原生方法读取
          try {
            // 检查文件是否在安全作用域内
            final parentDir = file.parent.path;
            if (parentDir.startsWith('/private/var/mobile/') || 
                parentDir.startsWith('/var/mobile/')) {
              debugPrint('检测到iOS安全作用域文件，需要特殊处理');
              
              // 读取文件字节
              final bytes = await file.readAsBytes();
              fileContent = utf8.decode(bytes, allowMalformed: true);
            } else {
              fileContent = await file.readAsString(encoding: latin1);
            }
          } catch (e2) {
            debugPrint('所有读取方法都失败: $e2');
            fileContent = '';
          }
        }
      } else {
        // 非iOS平台，尝试多种编码
        try {
          fileContent = await file.readAsString(encoding: utf8);
        } catch (e) {
          debugPrint('UTF-8编码读取失败，尝试使用系统默认编码: $e');
          try {
            fileContent = await file.readAsString(encoding: latin1);
          } catch (e2) {
            debugPrint('Latin1编码读取失败，尝试使用系统默认编码: $e2');
            fileContent = await file.readAsString();
          }
        }
      }
      
      debugPrint('读取到的内容长度: ${fileContent.length}');
      debugPrint('内容前100个字符: ${fileContent.substring(0, math.min(100, fileContent.length))}');

        // 验证文件内容
        if (fileContent.isEmpty) {
          debugPrint('错误: 文件内容为空');
          _showErrorDialog(
            context,
            LocalizationService().getLocalizedErrorMessage('errors.file.empty'),
            LocalizationService().getLocalizedErrorMessage('errors.file.empty'),
          );
          return;
        }

        // 检查文件大小（限制为10MB）
        if (fileSize > 10 * 1024 * 1024) {
          debugPrint('错误: 文件过大');
          _showErrorDialog(
            context,
            LocalizationService().getLocalizedErrorMessage(
              'errors.file.tooLarge',
            ),
            LocalizationService().getLocalizedErrorMessage(
              'errors.file.tooLarge',
            ),
          );
          return;
        }

        // 清理内容
        fileContent = _cleanFileContent(fileContent);
        debugPrint('清理后的内容长度: ${fileContent.length}');
      } else {
        debugPrint('错误: 文件不存在');
        _showErrorDialog(
          context,
          '文件不存在',
          '指定的文件路径不存在: $filePath',
        );
        return;
      }
    } catch (e) {
      debugPrint('读取文件失败: $e');
      debugPrint('错误类型: ${e.runtimeType}');
      _showErrorDialog(
        context,
        LocalizationService().getLocalizedErrorMessage(
          'errors.file.readFailed',
        ),
        '${LocalizationService().getLocalizedErrorMessage('errors.file.readFailed')}: $e',
      );
      return;
    }

    if (fileContent.trim().isEmpty) {
      debugPrint('错误: 文件内容在清理后为空');
      _showErrorDialog(
        context,
        LocalizationService().getLocalizedErrorMessage('errors.file.empty'),
        LocalizationService().getLocalizedErrorMessage('errors.file.empty'),
      );
      return;
    }

    final fileName = path.basenameWithoutExtension(filePath);

    switch (fileType) {
      case FileType.markdown:
        Navigator.push(
          context,
          MaterialPageRoute(
            builder:
                (context) => MarkdownRenderScreen(initialMarkdown: fileContent),
          ),
        );
        break;

      case FileType.text:
        Navigator.push(
          context,
          MaterialPageRoute(
            builder:
                (context) => TextCardEditorPage(
                  existingCard:
                      fileContent != null
                          ? TextCardModel(
                            id:
                                DateTime.now().millisecondsSinceEpoch
                                    .toString(),
                            title: fileName,
                            content: fileContent,
                            templateId: 'card_shadow',
                            createdAt: DateTime.now(),
                            updatedAt: DateTime.now(),
                          )
                          : null,
                ),
          ),
        );
        break;

      case FileType.svg:
        // 跳转到 SVG 管理页并直接预览该 SVG
        Navigator.push(
          context,
          MaterialPageRoute(
            builder:
                (context) => SvgManagerScreen(
                  initialSvgContent: fileContent,
                  initialTitle: fileName,
                ),
          ),
        );
        break;

      case FileType.html:
        // 跳转到 HTML 管理页并直接预览该 HTML
        debugPrint('正在导航到 HTML 编辑器...');
        debugPrint('文件名: $fileName');
        debugPrint('内容长度: ${fileContent.length}');
        
        try {
          final route = MaterialPageRoute(
            builder:
                (context) => HtmlManagerScreen(
                  initialHtmlContent: fileContent,
                  initialTitle: fileName,
                ),
          );
          
          Navigator.push(
            context,
            route,
          ).then((_) {
            debugPrint('HTML 编辑器页面已关闭');
          }).catchError((e) {
            debugPrint('导航到 HTML 编辑器时发生错误: $e');
          });
          
          debugPrint('HTML 导航命令已执行');
        } catch (e) {
          debugPrint('创建 HTML 路由时发生错误: $e');
          _showErrorDialog(
            context,
            '导航失败',
            '无法打开 HTML 编辑器: $e',
          );
        }
        break;

      case FileType.unknown:
        _showUnsupportedFileDialog(context, filePath);
        break;
    }
  }

  /// 清理文件内容
  String _cleanFileContent(String content) {
    // 移除BOM字符
    if (content.startsWith('\uFEFF')) {
      content = content.substring(1);
    }

    // 统一换行符
    content = content.replaceAll('\r\n', '\n').replaceAll('\r', '\n');

    // 移除null字符和其他控制字符
    content = content
        .replaceAll('\u0000', '')
        .replaceAll(RegExp(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]'), '');

    return content.trim();
  }

  /// 处理文件打开请求
  Future<void> handleFileOpen(BuildContext context, String filePath) async {
    final fileType = getFileType(filePath);
    await navigateToEditor(context, filePath, fileType);
  }

  /// 处理文本内容
  void handleTextContent(
    BuildContext context,
    String content, {
    String? title,
  }) {
    final textCard = TextCardModel(
      id: DateTime.now().millisecondsSinceEpoch.toString(),
      title: title ?? LocalizationService().getLocalizedUILabel('ui.newText'),
      content: content,
      templateId: 'card_shadow',
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => TextCardEditorPage(existingCard: textCard),
      ),
    );
  }

  /// 显示不支持的文件类型对话框
  void _showUnsupportedFileDialog(BuildContext context, String filePath) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(
            LocalizationService().getLocalizedErrorMessage(
              'errors.file.unsupportedType',
            ),
          ),
          content: Text(
            '${LocalizationService().getLocalizedErrorMessage('errors.file.unsupportedType')}\n\n${LocalizationService().getLocalizedUILabel('ui.supportedFileTypes')}',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(
                LocalizationService().getLocalizedUILabel('ui.confirm'),
              ),
            ),
          ],
        );
      },
    );
  }

  /// 显示错误对话框
  void _showErrorDialog(BuildContext context, String title, String message) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: Text(title),
          content: Text(message),
          actions: [
            TextButton(
              onPressed: () => Navigator.of(context).pop(),
              child: Text(
                LocalizationService().getLocalizedUILabel('ui.confirm'),
              ),
            ),
          ],
        );
      },
    );
  }

  /// 清理资源
  void dispose() {
    // 清理临时文件
    _cleanupTempFiles();
  }
  
  /// 清理临时文件
  Future<void> _cleanupTempFiles() async {
    if (!kIsWeb && Platform.isIOS) {
      try {
        final tempDir = Directory.systemTemp;
        if (await tempDir.exists()) {
          final files = await tempDir.list().where((entity) => 
            entity is File && 
            (entity.path.contains('contentpal_') || entity.path.contains('temp_'))
          ).cast<File>().toList();
          
          for (final file in files) {
            try {
              await file.delete();
              debugPrint('已删除临时文件: ${file.path}');
            } catch (e) {
              debugPrint('删除临时文件失败: $e');
            }
          }
        }
      } catch (e) {
        debugPrint('清理临时文件失败: $e');
      }
    }
  }
}
