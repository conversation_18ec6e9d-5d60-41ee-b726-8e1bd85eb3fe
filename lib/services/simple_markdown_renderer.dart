import 'package:flutter/material.dart';
import 'dart:math' as math;

/// 简单的Markdown渲染器
/// 当flutter_markdown包出现问题时的备用方案
class SimpleMarkdownRenderer extends StatelessWidget {
  final String markdownText;
  final TextStyle? baseTextStyle;
  final bool selectable;
  final bool isBlockMode;

  const SimpleMarkdownRenderer({
    super.key,
    required this.markdownText,
    this.baseTextStyle,
    this.selectable = false,
    this.isBlockMode = false,
  });

  @override
  Widget build(BuildContext context) {
    final TextStyle defaultStyle =
        baseTextStyle ??
        const TextStyle(fontSize: 16, color: Colors.black87, height: 1.6);

    final widgets = _parseMarkdownToWidgets(markdownText, defaultStyle);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      mainAxisSize: MainAxisSize.min,
      children: widgets,
    );
  }

  /// 解析Markdown为Widget列表，支持表格和分割线
  List<Widget> _parseMarkdownToWidgets(String text, TextStyle baseStyle) {
    final List<Widget> widgets = [];
    final lines = text.split('\n');

    for (int i = 0; i < lines.length; i++) {
      final line = lines[i];

      // 检查是否是表格
      if (_isTableRow(line)) {
        final tableLines = <String>[];
        int j = i;

        // 收集连续的表格行
        while (j < lines.length && _isTableRow(lines[j])) {
          tableLines.add(lines[j]);
          j++;
        }

        if (tableLines.isNotEmpty) {
          widgets.add(_buildTable(tableLines, baseStyle));
          i = j - 1; // 跳过已处理的行
          continue;
        }
      }

      // 检查是否是分割线（非分块模式）
      if (!isBlockMode && _isHorizontalRule(line)) {
        widgets.add(_buildHorizontalRule());
        continue;
      }

      // 处理其他类型的行
      final widget = _parseLineToWidget(line, baseStyle);
      if (widget != null) {
        widgets.add(widget);
      }
    }

    return widgets;
  }



  List<TextSpan> _parseLine(String line, TextStyle baseStyle) {
    final List<TextSpan> spans = [];

    // 处理标题
    if (line.startsWith('#')) {
      final headerLevel = line.indexOf(' ');
      if (headerLevel > 0) {
        final headerText = line.substring(headerLevel + 1);
        final fontSize = math.max(20.0, 28.0 - (headerLevel * 2));
        final headerStyle = baseStyle.copyWith(
          fontSize: fontSize,
          fontWeight: FontWeight.bold,
        );

        // 解析标题中的内联样式（加粗、斜体等）
        spans.addAll(_parseInlineMarkdown(headerText, headerStyle));
        return spans;
      }
    }

    // 处理列表项
    if (line.trim().startsWith('- ') || line.trim().startsWith('* ')) {
      final listText = line.trim().substring(2);
      spans.add(TextSpan(text: '• ', style: baseStyle));
      spans.addAll(_parseInlineMarkdown(listText, baseStyle));
      return spans;
    }

    // 处理有序列表
    final orderedListMatch = RegExp(r'^\s*\d+\.\s+').firstMatch(line);
    if (orderedListMatch != null) {
      final number = orderedListMatch.group(0)!;
      final listText = line.substring(orderedListMatch.end);
      spans.add(TextSpan(text: number, style: baseStyle));
      spans.addAll(_parseInlineMarkdown(listText, baseStyle));
      return spans;
    }

    // 处理引用
    if (line.trim().startsWith('> ')) {
      final quoteText = line.trim().substring(2);
      spans.add(
        TextSpan(
          text: quoteText,
          style: baseStyle.copyWith(
            fontStyle: FontStyle.italic,
            color: baseStyle.color?.withValues(alpha: 0.8),
          ),
        ),
      );
      return spans;
    }

    // 处理代码块
    if (line.trim().startsWith('```')) {
      spans.add(
        TextSpan(
          text: line,
          style: baseStyle.copyWith(
            fontFamily: 'monospace',
            backgroundColor: Colors.grey[100],
          ),
        ),
      );
      return spans;
    }

    // 处理普通文本
    spans.addAll(_parseInlineMarkdown(line, baseStyle));
    return spans;
  }

  List<TextSpan> _parseInlineMarkdown(String text, TextStyle baseStyle) {
    final List<TextSpan> spans = [];
    final RegExp markdownPattern = RegExp(
      r'(\*\*[^*]+\*\*)|(\*[^*]+\*)|(__[^_]+__)|(_[^_]+_)|(`[^`]+`)|([^*_`]+)',
    );

    final matches = markdownPattern.allMatches(text);

    for (final match in matches) {
      final matchText = match.group(0)!;

      if (matchText.startsWith('**') && matchText.endsWith('**')) {
        // 粗体
        spans.add(
          TextSpan(
            text: matchText.substring(2, matchText.length - 2),
            style: baseStyle.copyWith(fontWeight: FontWeight.bold),
          ),
        );
      } else if (matchText.startsWith('__') && matchText.endsWith('__')) {
        // 下划线粗体
        spans.add(
          TextSpan(
            text: matchText.substring(2, matchText.length - 2),
            style: baseStyle.copyWith(
              fontWeight: FontWeight.bold,
              decoration: TextDecoration.underline,
            ),
          ),
        );
      } else if (matchText.startsWith('*') && matchText.endsWith('*')) {
        // 斜体
        spans.add(
          TextSpan(
            text: matchText.substring(1, matchText.length - 1),
            style: baseStyle.copyWith(fontStyle: FontStyle.italic),
          ),
        );
      } else if (matchText.startsWith('_') && matchText.endsWith('_')) {
        // 下划线斜体
        spans.add(
          TextSpan(
            text: matchText.substring(1, matchText.length - 1),
            style: baseStyle.copyWith(
              fontStyle: FontStyle.italic,
              decoration: TextDecoration.underline,
            ),
          ),
        );
      } else if (matchText.startsWith('`') && matchText.endsWith('`')) {
        // 行内代码
        spans.add(
          TextSpan(
            text: matchText.substring(1, matchText.length - 1),
            style: baseStyle.copyWith(
              fontFamily: 'monospace',
              backgroundColor: Colors.grey[200],
            ),
          ),
        );
      } else {
        // 普通文本
        spans.add(TextSpan(text: matchText, style: baseStyle));
      }
    }

    return spans;
  }

  /// 检查是否是表格行
  bool _isTableRow(String line) {
    final trimmed = line.trim();
    return trimmed.contains('|') && trimmed.length > 1;
  }

  /// 检查是否是分割线
  bool _isHorizontalRule(String line) {
    final trimmed = line.trim();
    return trimmed == '---' ||
        trimmed == '***' ||
        trimmed == '___' ||
        RegExp(r'^-{3,}$').hasMatch(trimmed) ||
        RegExp(r'^\*{3,}$').hasMatch(trimmed) ||
        RegExp(r'^_{3,}$').hasMatch(trimmed);
  }

  /// 构建分割线
  Widget _buildHorizontalRule() {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: 16),
      height: 1,
      color: Colors.grey[300],
    );
  }

  /// 解析单行为Widget
  Widget? _parseLineToWidget(String line, TextStyle baseStyle) {
    if (line.trim().isEmpty) {
      return const SizedBox(height: 8);
    }

    final spans = _parseLine(line, baseStyle);
    if (spans.isEmpty) return null;

    return Padding(
      padding: const EdgeInsets.only(bottom: 4),
      child:
          selectable
              ? SelectableText.rich(TextSpan(children: spans), style: baseStyle)
              : RichText(text: TextSpan(children: spans, style: baseStyle)),
    );
  }

  /// 构建表格
  Widget _buildTable(List<String> tableLines, TextStyle baseStyle) {
    if (tableLines.isEmpty) return const SizedBox.shrink();

    final rows = <List<String>>[];
    bool hasHeaderSeparator = false;

    // 解析表格行
    for (int i = 0; i < tableLines.length; i++) {
      final line = tableLines[i].trim();
      if (line.isEmpty) continue;

      // 检查是否是分隔符行（如 |---|---|）
      if (line.contains('-') &&
          line
              .split('|')
              .every(
                (cell) =>
                    cell.trim().isEmpty ||
                    cell
                        .trim()
                        .replaceAll('-', '')
                        .replaceAll(':', '')
                        .trim()
                        .isEmpty,
              )) {
        hasHeaderSeparator = true;
        continue;
      }

      // 解析表格单元格
      final cells =
          line
              .split('|')
              .map((cell) => cell.trim())
              .where((cell) => cell.isNotEmpty)
              .toList();

      if (cells.isNotEmpty) {
        rows.add(cells);
      }
    }

    if (rows.isEmpty) return const SizedBox.shrink();

    // 确定列数
    final maxColumns = rows
        .map((row) => row.length)
        .reduce((a, b) => a > b ? a : b);

    return Container(
      margin: const EdgeInsets.symmetric(vertical: 8),
      decoration: BoxDecoration(
        border: Border.all(color: Colors.grey[300]!),
        borderRadius: BorderRadius.circular(4),
      ),
      child: Table(
        border: TableBorder.all(color: Colors.grey[300]!),
        columnWidths: Map.fromIterable(
          List.generate(maxColumns, (index) => index),
          value: (_) => const FlexColumnWidth(),
        ),
        children:
            rows.asMap().entries.map((entry) {
              final rowIndex = entry.key;
              final row = entry.value;
              final isHeader = hasHeaderSeparator && rowIndex == 0;

              return TableRow(
                decoration:
                    isHeader ? BoxDecoration(color: Colors.grey[100]) : null,
                children: List.generate(maxColumns, (colIndex) {
                  final cellText = colIndex < row.length ? row[colIndex] : '';
                  final cellSpans = _parseInlineMarkdown(cellText, baseStyle);

                  return Padding(
                    padding: const EdgeInsets.all(8),
                    child: RichText(
                      text: TextSpan(
                        children: cellSpans,
                        style:
                            isHeader
                                ? baseStyle.copyWith(
                                  fontWeight: FontWeight.bold,
                                )
                                : baseStyle,
                      ),
                    ),
                  );
                }),
              );
            }).toList(),
      ),
    );
  }
}
