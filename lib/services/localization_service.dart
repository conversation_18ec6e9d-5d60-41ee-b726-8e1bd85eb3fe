import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// 本地化服务，管理应用的语言设置
class LocalizationService extends ChangeNotifier {
  static const String _localeKey = 'app_locale';
  
  // 支持的语言列表
  static const List<Locale> supportedLocales = [
    Locale('en', ''), // English
    Locale('zh', ''), // Chinese (Simplified)
    Locale('ja', ''), // Japanese
  ];

  // 语言显示名称映射
  static const Map<String, String> localeNames = {
    'en': 'English',
    'zh': '中文',
    'ja': '日本語',
  };

  Locale? _currentLocale;
  
  /// 获取当前语言设置
  Locale? get currentLocale => _currentLocale;
  
  /// 获取当前语言的显示名称
  String get currentLocaleName {
    if (_currentLocale == null) return 'System';
    return localeNames[_currentLocale!.languageCode] ?? 'Unknown';
  }

  /// 初始化本地化服务
  Future<void> initialize() async {
    final prefs = await SharedPreferences.getInstance();
    final localeCode = prefs.getString(_localeKey);

    if (localeCode != null) {
      _currentLocale = Locale(localeCode);
    } else {
      // 如果没有保存的语言设置，设置为null表示跟随系统
      _currentLocale = null;
    }

    notifyListeners();
  }

  /// 设置语言
  Future<void> setLocale(Locale? locale) async {
    if (_currentLocale == locale) return;
    
    _currentLocale = locale;
    
    final prefs = await SharedPreferences.getInstance();
    if (locale != null) {
      await prefs.setString(_localeKey, locale.languageCode);
    } else {
      await prefs.remove(_localeKey);
    }
    
    notifyListeners();
  }

  
  /// 检查是否支持指定语言
  bool isLocaleSupported(Locale locale) {
    return supportedLocales.any(
      (supportedLocale) => supportedLocale.languageCode == locale.languageCode,
    );
  }

  /// 获取语言选择列表（包含"跟随系统"选项）
  List<LocaleOption> getLocaleOptions() {
    final options = <LocaleOption>[
      LocaleOption(null, 'System', getLocalizedUILabel('ui.followSystem')),
    ];
    
    for (final locale in supportedLocales) {
      final name = localeNames[locale.languageCode] ?? locale.languageCode;
      options.add(LocaleOption(locale, name, name));
    }
    
    return options;
  }

  /// 根据语言代码获取Locale
  Locale? getLocaleByCode(String code) {
    if (code == 'system') return null;
    
    for (final locale in supportedLocales) {
      if (locale.languageCode == code) {
        return locale;
      }
    }
    
    return null;
  }

  /// 获取本地化的错误消息
  String getLocalizedErrorMessage(String key, {Map<String, dynamic>? args}) {
    // 根据当前语言返回对应的错误消息
    final currentLang = _currentLocale?.languageCode ?? 'zh';
    
    final errorMessages = <String, Map<String, String>>{
      'errors.content.loadFailed': {
        'zh': '加载内容项失败',
        'en': 'Failed to load content item',
        'ja': 'コンテンツ項目の読み込みに失敗しました',
      },
      'errors.content.mustBeTextType': {
        'zh': '必须是文本类型内容',
        'en': 'Must be text type content',
        'ja': 'テキストタイプのコンテンツである必要があります',
      },
      'errors.content.mustBeImageType': {
        'zh': '必须是图像类型内容',
        'en': 'Must be image type content',
        'ja': '画像タイプのコンテンツである必要があります',
      },
      'errors.content.itemNotFound': {
        'zh': '内容项不存在',
        'en': 'Content item not found',
        'ja': 'コンテンツ項目が見つかりません',
      },
      'errors.content.notInitialized': {
        'zh': 'ContentService 未初始化',
        'en': 'ContentService not initialized',
        'ja': 'ContentServiceが初期化されていません',
      },
      'errors.content.renderFailed': {
        'zh': '渲染失败',
        'en': 'Render failed',
        'ja': 'レンダリングに失敗しました',
      },
      'errors.audio.fileNotFound': {
        'zh': '录音文件不存在',
        'en': 'Audio file not found',
        'ja': '音声ファイルが見つかりません',
      },
      'errors.audio.invalidFileSize': {
        'zh': '录音文件大小异常 ({fileSize} 字节)，请重试录音',
        'en': 'Invalid audio file size ({fileSize} bytes), please retry recording',
        'ja': '音声ファイルのサイズが無効です（{fileSize} バイト）、再録音してください',
      },
      'errors.transcription.audioFileNotFound': {
        'zh': '音频文件不存在',
        'en': 'Audio file not found',
        'ja': '音声ファイルが見つかりません',
      },
      'errors.transcription.failed': {
        'zh': '转录失败',
        'en': 'Transcription failed',
        'ja': '文字起こしに失敗しました',
      },
      'errors.transcription.initFailed': {
        'zh': '语音识别服务初始化失败',
        'en': 'Speech recognition service initialization failed',
        'ja': '音声認識サービスの初期化に失敗しました',
      },
      'errors.transcription.startFailed': {
        'zh': '启动转录失败',
        'en': 'Failed to start transcription',
        'ja': '文字起こしの開始に失敗しました',
      },
      'errors.transcription.error': {
        'zh': '语音识别错误',
        'en': 'Speech recognition error',
        'ja': '音声認識エラー',
      },
      'errors.file.empty': {
        'zh': '文件为空',
        'en': 'File is empty',
        'ja': 'ファイルが空です',
      },
      'errors.file.tooLarge': {
        'zh': '文件大小超过10MB，无法打开',
        'en': 'File size exceeds 10MB, cannot open',
        'ja': 'ファイルサイズが10MBを超えているため開けません',
      },
      'errors.file.readFailed': {
        'zh': '读取文件失败',
        'en': 'Failed to read file',
        'ja': 'ファイルの読み込みに失敗しました',
      },
      'errors.file.unsupportedType': {
        'zh': '不支持的文件类型',
        'en': 'Unsupported file type',
        'ja': 'サポートされていないファイルタイプです',
      },
      'errors.recording.noPermission': {
        'zh': '没有获得麦克风权限，请在设置中允许访问麦克风',
        'en': 'Microphone permission not granted, please allow microphone access in settings',
        'ja': 'マイクの権限が許可されていません。設定でマイクアクセスを許可してください',
      },
      'errors.recording.directoryFailed': {
        'zh': '音频文件目录创建失败',
        'en': 'Failed to create audio file directory',
        'ja': '音声ファイルディレクトリの作成に失敗しました',
      },
      'errors.recording.startFailed': {
        'zh': '录音启动失败，录音机状态异常',
        'en': 'Failed to start recording, recorder status abnormal',
        'ja': '録音の開始に失敗しました。レコーダーの状態が異常です',
      },
      'ui.supportedFileTypes': {
        'zh': '支持的文件类型：\n• Markdown (.md, .markdown)\n• 文本文件 (.txt)\n• SVG图像 (.svg)\n• HTML文件 (.html, .htm)',
        'en': 'Supported file types:\n• Markdown (.md, .markdown)\n• Text files (.txt)\n• SVG images (.svg)\n• HTML files (.html, .htm)',
        'ja': 'サポートされているファイルタイプ：\n• Markdown (.md, .markdown)\n• テキストファイル (.txt)\n• SVG画像 (.svg)\n• HTMLファイル (.html, .htm)',
      },
      'ui.newText': {
        'zh': '新建文本',
        'en': 'New Text',
        'ja': '新規テキスト',
      },
    };

    final messageMap = errorMessages[key];
    if (messageMap == null) {
      return key; // 如果找不到对应的key，返回key本身
    }

    var message = messageMap[currentLang] ?? messageMap['zh'] ?? key;
    
    // 处理参数替换
    if (args != null) {
      args.forEach((key, value) {
        message = message.replaceAll('{$key}', value.toString());
      });
    }
    
    return message;
  }

  /// 获取本地化的UI标签
  String getLocalizedUILabel(String key) {
    final currentLang = _currentLocale?.languageCode ?? 'zh';
    
    final uiLabels = <String, Map<String, String>>{
      'ui.defaultTheme': {
        'zh': '默认主题',
        'en': 'Default Theme',
        'ja': 'デフォルトテーマ',
      },
      'ui.followSystem': {
        'zh': '跟随系统',
        'en': 'Follow System',
        'ja': 'システムに従う',
      },
      'ui.confirm': {
        'zh': '确定',
        'en': 'Confirm',
        'ja': '確認',
      },
      'ui.voiceRecording.defaultTitle': {
        'zh': '录音',
        'en': 'Voice Recording',
        'ja': '音声録音',
      },
    };

    final labelMap = uiLabels[key];
    if (labelMap == null) {
      return key;
    }

    return labelMap[currentLang] ?? labelMap['zh'] ?? key;
  }

  /// 获取本地化的转录相关文本
  String getLocalizedTranscriptionText(String key, {Map<String, dynamic>? args}) {
    final currentLang = _currentLocale?.languageCode ?? 'zh';
    
    final transcriptionTexts = <String, Map<String, String>>{
      'transcription.simulatedContent': {
        'zh': '这是从音频文件 "{fileName}" 转录出的文本内容。\n\n转录时间: {timestamp}\n文件路径: {filePath}\n\n转录设置:\n- 语言: {currentTaskLanguage}\n- 启用标点符号: {enablePunctuation}\n- 说话人检测: {enableSpeakerDetection}\n- 置信度阈值: {confidenceThreshold}\n\n转录结果会根据音频质量和语音清晰度有所不同。在实际应用中，这里会显示真实的转录内容。',
        'en': 'This is the transcribed text content from audio file "{fileName}".\n\nTranscription time: {timestamp}\nFile path: {filePath}\n\nTranscription settings:\n- Language: {currentTaskLanguage}\n- Enable punctuation: {enablePunctuation}\n- Speaker detection: {enableSpeakerDetection}\n- Confidence threshold: {confidenceThreshold}\n\nTranscription results may vary based on audio quality and speech clarity. In actual applications, real transcribed content would be displayed here.',
        'ja': 'これは音声ファイル"{fileName}"から文字起こしされたテキストコンテンツです。\n\n文字起こし時刻: {timestamp}\nファイルパス: {filePath}\n\n文字起こし設定:\n- 言語: {currentTaskLanguage}\n- 句読点を有効にする: {enablePunctuation}\n- 話者検出: {enableSpeakerDetection}\n- 信頼度しきい値: {confidenceThreshold}\n\n文字起こし結果は音質と音声の明瞭度によって異なります。実際のアプリケーションでは、ここに実際の文字起こしコンテンツが表示されます。',
      },
    };

    final textMap = transcriptionTexts[key];
    if (textMap == null) {
      return key;
    }

    var text = textMap[currentLang] ?? textMap['zh'] ?? key;
    
    // 处理参数替换
    if (args != null) {
      args.forEach((key, value) {
        text = text.replaceAll('{$key}', value.toString());
      });
    }
    
    return text;
  }
}

/// 语言选项数据类
class LocaleOption {
  final Locale? locale;
  final String name;
  final String displayName;
  
  const LocaleOption(this.locale, this.name, this.displayName);
  
  String get code => locale?.languageCode ?? 'system';
  
  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is LocaleOption && other.locale == locale;
  }
  
  @override
  int get hashCode => locale.hashCode;
}
