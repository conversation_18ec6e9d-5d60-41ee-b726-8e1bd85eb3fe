import 'dart:io';

import 'package:flutter/material.dart';
import 'package:permission_handler/permission_handler.dart';

/// Permission helper error messages
class PermissionHelperErrors {
  static const String requestPhotosFailed = '请求相册权限失败';
  static const String requestMultipleFailed = '请求多个权限失败';
  static const String iosPermissionCheckFailed = '启动时权限状态检查失败';
}

/// 权限帮助工具
class PermissionHelper {
  /// 请求相册权限
  static Future<bool> requestPhotosPermission() async {
    try {
      // 检查是否已有权限
      if (await Permission.photos.isGranted) {
        return true;
      }

      // 请求权限
      PermissionStatus status = await Permission.photos.request();
      return status.isGranted;
    } catch (e) {
      debugPrint('${PermissionHelperErrors.requestPhotosFailed}: $e');
      return false;
    }
  }

  /// 请求多个权限
  static Future<Map<Permission, PermissionStatus>> requestMultiplePermissions(
    List<Permission> permissions,
  ) async {
    try {
      return await permissions.request();
    } catch (e) {
      debugPrint('${PermissionHelperErrors.requestMultipleFailed}: $e');
      return {
        for (var permission in permissions) permission: PermissionStatus.denied,
      };
    }
  }

  static Future<void> checkIosPermissions() async {
    if (Platform.isIOS) {
      try {
        debugPrint("===== 应用启动时检查iOS权限状态 =====");

        // 检查照片权限状态
        final photosStatus = await Permission.photos.status;
        debugPrint("iOS照片权限状态: $photosStatus");

        // 注意：这里仅记录权限状态，不主动请求，确保应用首次运行时能正确识别权限
      } catch (e) {
        debugPrint("${PermissionHelperErrors.iosPermissionCheckFailed}: $e");
      }
    }
  }

}
