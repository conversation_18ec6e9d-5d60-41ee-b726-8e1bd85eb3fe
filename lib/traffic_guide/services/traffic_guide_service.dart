import 'dart:convert';
import 'dart:io';
import 'dart:math' as math;
import 'dart:ui' as ui;

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:path_provider/path_provider.dart';

import '../models/traffic_guide_models.dart';

class TrafficGuideService {
  static final TrafficGuideService _instance = TrafficGuideService._internal();
  factory TrafficGuideService() => _instance;
  TrafficGuideService._internal();

  // 隐形水印编码哨兵与位映射
  // 使用三连字符作为起止哨兵，避免与普通文本/其他不可见字符冲突
  static const String _wmStart = '\u200D\u200D\u200D'; // ZERO WIDTH JOINER x3
  static const String _wmEnd = '\u2060\u2060\u2060'; // WORD JOINER x3
  static const String _bit0 = '\u200B'; // ZERO WIDTH SPACE 表示位0
  static const String _bit1 = '\u200C'; // ZERO WIDTH NON-JOINER 表示位1

  // 不可见字符映射
  static const Map<String, String> _invisibleChars = {
    '\u200B': '零宽空格',
    '\u200C': '零宽非连接符',
    '\u200D': '零宽连接符',
    '\u2060': '词连接符',
    '\uFEFF': '零宽不换行空格',
    '\u2061': '函数应用',
    '\u2062': '不可见乘号',
    '\u2063': '不可见分隔符',
    '\u2064': '不可见加号',
  };

  // 扩展的装饰字符映射
  static const Map<String, String> _decorativeDiacritics = {
    '\u0300': '重音符 (̀)',
    '\u0301': '锐音符 (́)',
    '\u0302': '抑扬符 (̂)',
    '\u0303': '波浪符 (̃)',
    '\u0304': '长音符 (̄)',
    '\u0305': '上划线 (̅)',
    '\u0306': '短音符 (̆)',
    '\u0307': '上点 (̇)',
    '\u0308': '分音符 (̈)',
    '\u0309': '钩音符 (̉)',
    '\u030A': '上圆圈 (̊)',
    '\u030B': '双锐音符 (̋)',
    '\u030C': '抑音符 (̌)',
    '\u030D': '垂直重音符 (̍)',
    '\u030E': '双垂直重音符 (̎)',
    '\u030F': '双抑扬符 (̏)',
    '\u0310': '抑扬符 (̐)',
    '\u0311': '抑音符 (̑)',
    '\u0312': '钩音符 (̒)',
    '\u0313': '锐音符 (̓)',
    '\u0314': '重音符 (̔)',
    '\u0315': '抑音符 (̕)',
    '\u0316': '抑扬符 (̖)',
    '\u0317': '抑音符 (̗)',
    '\u0318': '抑扬符 (̘)',
    '\u0319': '抑音符 (̙)',
    '\u031A': '抑扬符 (̚)',
    '\u031B': '抑音符 (̛)',
    '\u031C': '抑扬符 (̜)',
    '\u031D': '抑音符 (̝)',
    '\u031E': '抑扬符 (̞)',
    '\u031F': '抑音符 (̟)',
    '\u0320': '抑扬符 (̠)',
    '\u0321': '抑音符 (̡)',
    '\u0322': '抑扬符 (̢)',
    '\u0323': '下点 (̣)',
    '\u0324': '双下点 (̤)',
    '\u0325': '下圆圈 (̥)',
    '\u0326': '下钩 (̦)',
    '\u0327': '下钩 (̧)',
    '\u0328': '下钩 (̨)',
    '\u0329': '下钩 (̩)',
    '\u032A': '下钩 (̪)',
    '\u032B': '下钩 (̫)',
    '\u032C': '下钩 (̬)',
    '\u032D': '下钩 (̭)',
    '\u032E': '下钩 (̮)',
    '\u032F': '下钩 (̯)',
    '\u0330': '下划线 (̰)',
    '\u0331': '下划线 (̱)',
    '\u0332': '下划线 (̲)',
    '\u0333': '下划线 (̳)',
    '\u0334': '下划线 (̴)',
    '\u0335': '下划线 (̵)',
    '\u0336': '下划线 (̶)',
    '\u0337': '下划线 (̷)',
    '\u0338': '下划线 (̸)',
    '\u0339': '下划线 (̹)',
    '\u033A': '下划线 (̺)',
    '\u033B': '下划线 (̻)',
    '\u033C': '下划线 (̼)',
    '\u033D': '下划线 (̽)',
    '\u033E': '下划线 (̾)',
    '\u033F': '下划线 (̿)',
    '\u0340': '下划线 (̀)',
    '\u0341': '下划线 (́)',
    '\u0342': '下划线 (͂)',
    '\u0343': '下划线 (̓)',
    '\u0344': '下划线 (̈́)',
    '\u0345': '下划线 (ͅ)',
    '\u0346': '下划线 (͆)',
    '\u0347': '下划线 (͇)',
    '\u0348': '下划线 (͈)',
    '\u0349': '下划线 (͉)',
    '\u034A': '下划线 (͊)',
    '\u034B': '下划线 (͋)',
    '\u034C': '下划线 (͌)',
    '\u034D': '下划线 (͍)',
    '\u034E': '下划线 (͎)',
    '\u034F': '下划线 (͏)',
    '\u0350': '下划线 (͐)',
    '\u0351': '下划线 (͑)',
    '\u0352': '下划线 (͒)',
    '\u0353': '下划线 (͓)',
    '\u0354': '下划线 (͔)',
    '\u0355': '下划线 (͕)',
    '\u0356': '下划线 (͖)',
    '\u0357': '下划线 (͗)',
    '\u0358': '下划线 (͘)',
    '\u0359': '下划线 (͙)',
    '\u035A': '下划线 (͚)',
    '\u035B': '下划线 (͛)',
    '\u035C': '下划线 (͜)',
    '\u035D': '下划线 (͝)',
    '\u035E': '下划线 (͞)',
    '\u035F': '下划线 (͟)',
    '\u0360': '下划线 (͠)',
    '\u0361': '下划线 (͡)',
    '\u0362': '下划线 (͢)',
    '\u0363': '下划线 (ͣ)',
    '\u0364': '下划线 (ͤ)',
    '\u0365': '下划线 (ͥ)',
    '\u0366': '下划线 (ͦ)',
    '\u0367': '下划线 (ͧ)',
    '\u0368': '下划线 (ͨ)',
    '\u0369': '下划线 (ͩ)',
    '\u036A': '下划线 (ͪ)',
    '\u036B': '下划线 (ͫ)',
    '\u036C': '下划线 (ͬ)',
    '\u036D': '下划线 (ͭ)',
    '\u036E': '下划线 (ͮ)',
    '\u036F': '下划线 (ͯ)',
  };

  // 数字到emoji映射
  static const Map<String, String> _numberToEmoji = {
    '0': '⓪',
    '1': '①',
    '2': '②',
    '3': '③',
    '4': '④',
    '5': '⑤',
    '6': '⑥',
    '7': '⑦',
    '8': '⑧',
    '9': '⑨',
  };

  // 字母到特殊Unicode映射
  static const Map<String, String> _letterToUnicode = {
    'a': 'ᴀ',
    'b': 'ʙ',
    'c': 'ᴄ',
    'd': 'ᴅ',
    'e': 'ᴇ',
    'f': 'ꜰ',
    'g': 'ɢ',
    'h': 'ʜ',
    'i': 'ɪ',
    'j': 'ᴊ',
    'k': 'ᴋ',
    'l': 'ʟ',
    'm': 'ᴍ',
    'n': 'ɴ',
    'o': 'ᴏ',
    'p': 'ᴘ',
    'q': 'ǫ',
    'r': 'ʀ',
    's': 'ꜱ',
    't': 'ᴛ',
    'u': 'ᴜ',
    'v': 'ᴠ',
    'w': 'ᴡ',
    'x': 'x',
    'y': 'ʏ',
    'z': 'ᴢ',
  };

  // 添加更多有趣的字符映射
  static const Map<String, String> _fancyLetters = {
    'a': '𝒶',
    'b': '𝒷',
    'c': '𝒸',
    'd': '𝒹',
    'e': '𝑒',
    'f': '𝒻',
    'g': '𝑔',
    'h': '𝒽',
    'i': '𝒾',
    'j': '𝒿',
    'k': '𝓀',
    'l': '𝓁',
    'm': '𝓂',
    'n': '𝓃',
    'o': '𝑜',
    'p': '𝓅',
    'q': '𝓆',
    'r': '𝓇',
    's': '𝓈',
    't': '𝓉',
    'u': '𝓊',
    'v': '𝓋',
    'w': '𝓌',
    'x': '𝓍',
    'y': '𝓎',
    'z': '𝓏',
  };

  // 粗体字母映射
  static const Map<String, String> _boldLetters = {
    'a': '𝐚',
    'b': '𝐛',
    'c': '𝐜',
    'd': '𝐝',
    'e': '𝐞',
    'f': '𝐟',
    'g': '𝐠',
    'h': '𝐡',
    'i': '𝐢',
    'j': '𝐣',
    'k': '𝐤',
    'l': '𝐥',
    'm': '𝐦',
    'n': '𝐧',
    'o': '𝐨',
    'p': '𝐩',
    'q': '𝐪',
    'r': '𝐫',
    's': '𝐬',
    't': '𝐭',
    'u': '𝐮',
    'v': '𝐯',
    'w': '𝐰',
    'x': '𝐱',
    'y': '𝐲',
    'z': '𝐳',
  };

  // 装饰性符号
  static const List<String> _decorativeSymbols = [
    '✨',
    '🌟',
    '💫',
    '⭐',
    '🎀',
    '🎈',
    '🎉',
    '🎊',
    '🎋',
    '🎍',
    '🎎',
    '🎏',
    '🎐',
    '🎑',
    '🎒',
    '🎓',
    '🎖️',
    '🎗️',
    '🎘',
    '🎙️',
    '🎚️',
    '🎛️',
    '🎜️',
    '🎝️',
    '🎞️',
    '🎟️',
    '🎠',
    '🎡',
    '🎢',
    '🎣',
    '🎤',
    '🎥',
    '🎦',
    '🎧',
    '🎨',
    '🎩',
    '🎪',
    '🎫',
    '🎬',
    '🎭',
  ];

  /// 生成引流图片
  Future<Uint8List> generateTrafficImage(TrafficImageConfig config) async {
    final recorder = ui.PictureRecorder();
    final canvas = Canvas(recorder);
    final size = Size(800, 600);

    // 绘制高级渐变背景
    _drawAdvancedBackground(canvas, size, config);

    // 添加装饰图层
    _addDecorationLayer(canvas, size, config);

    // 添加高级干扰元素
    _addAdvancedNoise(canvas, size, config.noiseLevel);

    // 绘制主文本
    _drawStylizedText(canvas, size, config);

    // 添加高级扭曲效果
    if (config.distortionLevel > 0) {
      _applyAdvancedDistortion(canvas, size, config.distortionLevel);
    }

    // 添加图层混合效果
    _applyLayerBlending(canvas, size, config);

    // 添加水印
    if (config.addWatermark && config.watermarkText.isNotEmpty) {
      _addStylizedWatermark(canvas, size, config);
    }

    final picture = recorder.endRecording();
    final image = await picture.toImage(
      size.width.toInt(),
      size.height.toInt(),
    );
    final byteData = await image.toByteData(format: ui.ImageByteFormat.png);

    return byteData!.buffer.asUint8List();
  }

  /// 转换文本（添加不可见字符、emoji转换等）
  String transformText(String text, TextTransformConfig config) {
    String result = text;

    if (config.enableEmojiConversion) {
      result = _convertToEmoji(result);
    }

    if (config.enableUnicodeVariation) {
      result = _applyUnicodeVariation(result);
    }

    if (config.enableInvisibleChars) {
      result = _addInvisibleChars(result);
    }

    if (config.enableSensitiveWordMasking) {
      result = _maskSensitiveWords(result, config.sensitiveWords);
    }

    // 应用自定义emoji映射
    for (final entry in config.customEmojiMap.entries) {
      result = result.replaceAll(entry.key, entry.value);
    }

    return result;
  }

  /// 转换文本（增强版，支持多种转换模式）
  String transformTextEnhanced(String text, String mode) {
    try {
      switch (mode) {
        case 'emoji':
          return _convertToEmojiEnhanced(text);
        case 'fancy':
          return _convertToFancy(text);
        case 'bold':
          return _convertToBold(text);
        case 'decorative':
          return _convertToDecorative(text);
        case 'mixed':
          return _convertToMixed(text);
        case 'invisible':
          return _addInvisibleChars(text);
        case 'unicode':
          return _applyUnicodeVariationEnhanced(text);
        default:
          return text;
      }
    } catch (e) {
      debugPrint('转换失败: $e');
      return text; // 返回原文本而不是抛出异常
    }
  }

  String _convertToEmojiEnhanced(String text) {
    String result = text;

    // 转换数字为圆圈数字
    for (final entry in _numberToEmoji.entries) {
      result = result.replaceAll(entry.key, entry.value);
    }

    // 转换字母为小型大写字母
    for (final entry in _letterToUnicode.entries) {
      result = result.replaceAll(entry.key, entry.value);
      result = result.replaceAll(entry.key.toUpperCase(), entry.value);
    }

    return result;
  }

  String _convertToFancy(String text) {
    String result = text;

    // 转换为花体字母
    for (final entry in _fancyLetters.entries) {
      result = result.replaceAll(entry.key, entry.value);
      result = result.replaceAll(entry.key.toUpperCase(), entry.value);
    }

    // 转换数字
    for (final entry in _numberToEmoji.entries) {
      result = result.replaceAll(entry.key, entry.value);
    }

    return result;
  }

  String _convertToBold(String text) {
    String result = text;

    // 转换为粗体字母
    for (final entry in _boldLetters.entries) {
      result = result.replaceAll(entry.key, entry.value);
      result = result.replaceAll(entry.key.toUpperCase(), entry.value);
    }

    return result;
  }

  String _convertToDecorative(String text) {
    final random = math.Random();
    final buffer = StringBuffer();

    for (int i = 0; i < text.length; i++) {
      final char = text[i];

      // 随机添加装饰性符号
      if (random.nextDouble() < 0.3) {
        final symbol =
            _decorativeSymbols[random.nextInt(_decorativeSymbols.length)];
        buffer.write(symbol);
      }

      buffer.write(char);

      // 在字符后随机添加装饰
      if (random.nextDouble() < 0.2) {
        final symbol =
            _decorativeSymbols[random.nextInt(_decorativeSymbols.length)];
        buffer.write(symbol);
      }
    }

    return buffer.toString();
  }

  String _convertToMixed(String text) {
    String result = text;
    final random = math.Random();

    // 随机选择转换方式
    final conversions = [
      _convertToEmojiEnhanced,
      _convertToFancy,
      _convertToBold,
    ];

    // 应用2-3种转换
    final numConversions = 2 + random.nextInt(2);
    for (int i = 0; i < numConversions; i++) {
      final conversion = conversions[random.nextInt(conversions.length)];
      result = conversion(result);
    }

    // 添加一些装饰性符号
    if (random.nextDouble() < 0.5) {
      result = _convertToDecorative(result);
    }

    return result;
  }

  /// 添加水印到文本
  String addTextWatermark(String text, WatermarkConfig config) {
    if (config.invisible) {
      // 使用标准哨兵与二进制位编码添加隐形水印
      if (config.text.isEmpty) return text;
      final watermark = encodeInvisibleWatermark(config.text);
      return text + watermark;
    } else {
      // 可见水印
      return '$text\n${config.text}';
    }
  }

  /// 移除文本水印
  String removeTextWatermark(String text, WatermarkConfig config) {
    if (config.invisible) {
      // 移除所有以哨兵包裹的隐形水印片段
      final pattern = RegExp('$_wmStart[$_bit0$_bit1]+$_wmEnd');
      return text.replaceAll(pattern, '');
    } else {
      // 移除可见水印
      final lines = text.split('\n');
      if (lines.isNotEmpty && lines.last == config.text) {
        lines.removeLast();
        return lines.join('\n');
      }
      return text;
    }
  }

  /// 保存项目
  Future<void> saveProject(TrafficGuideProject project) async {
    final dir = await getApplicationDocumentsDirectory();
    final file = File('${dir.path}/traffic_guide_${project.id}.json');
    await file.writeAsString(jsonEncode(project.toJson()));
  }

  /// 加载项目
  Future<TrafficGuideProject?> loadProject(String id) async {
    try {
      final dir = await getApplicationDocumentsDirectory();
      final file = File('${dir.path}/traffic_guide_$id.json');
      if (await file.exists()) {
        final content = await file.readAsString();
        return TrafficGuideProject.fromJson(jsonDecode(content));
      }
    } catch (e) {
      debugPrint('加载项目失败: $e');
    }
    return null;
  }

  /// 获取所有项目
  Future<List<TrafficGuideProject>> getAllProjects() async {
    final List<TrafficGuideProject> projects = [];
    try {
      final dir = await getApplicationDocumentsDirectory();
      final files = dir.listSync().where(
        (file) =>
            file.path.contains('traffic_guide_') && file.path.endsWith('.json'),
      );

      for (final file in files) {
        final content = await File(file.path).readAsString();
        projects.add(TrafficGuideProject.fromJson(jsonDecode(content)));
      }
    } catch (e) {
      debugPrint('获取项目列表失败: $e');
    }
    return projects;
  }

  // 私有方法

  Color _parseColor(String colorString) {
    if (colorString.startsWith('#')) {
      final hex = colorString.substring(1);
      return Color(int.parse('FF$hex', radix: 16));
    }
    return Colors.black;
  }

  void _drawStylizedText(Canvas canvas, Size size, TrafficImageConfig config) {
    // 将文本分解为单个字符
    final characters = config.text.split('');
    final random = math.Random();

    // 计算基础字符间距，让文字充分利用整个图片宽度
    final baseCharWidth = config.fontSize * 0.8;
    final totalWidth = characters.length * baseCharWidth;

    // 如果文字总宽度超过图片宽度，则调整字符间距
    final availableWidth = size.width * 0.9; // 留10%边距
    final adjustedCharWidth =
        totalWidth > availableWidth
            ? availableWidth / characters.length
            : baseCharWidth;

    final startX = (size.width - (characters.length * adjustedCharWidth)) / 2;
    final centerY = size.height / 2;

    // 为每个字符创建不规则间距
    double currentX = startX;
    final charPositions = <int, Offset>{};

    for (int i = 0; i < characters.length; i++) {
      // 添加不规则的字符间距
      final spacingVariation =
          (random.nextDouble() - 0.5) * adjustedCharWidth * 0.6;
      currentX += spacingVariation;

      // 添加垂直偏移，让文字可以在整个高度范围内分布
      final verticalRange = size.height * 0.2; // 使用20%的图片高度
      final verticalOffset = (random.nextDouble() - 0.5) * verticalRange;

      charPositions[i] = Offset(currentX, centerY + verticalOffset);
      currentX += adjustedCharWidth;
    }

    // 绘制每个字符
    for (int i = 0; i < characters.length; i++) {
      final char = characters[i];
      final position = charPositions[i]!;

      // 为每个字符添加轻微的旋转
      final rotation = (random.nextDouble() - 0.5) * 0.4; // ±0.2弧度

      // 为每个字符添加轻微的缩放
      final scale = 0.85 + random.nextDouble() * 0.3; // 0.85-1.15倍

      // 为每个字符添加轻微的透明度变化
      final opacity = 0.75 + random.nextDouble() * 0.25; // 0.75-1.0

      canvas.save();
      canvas.translate(position.dx, position.dy);
      canvas.rotate(rotation);
      canvas.scale(scale);

      // 绘制字符阴影
      final shadowStyle = TextStyle(
        fontSize: config.fontSize,
        color: Colors.black.withValues(alpha: 0.3 * opacity),
        fontFamily: config.fontFamily,
        fontWeight: FontWeight.bold,
      );

      final shadowSpan = TextSpan(text: char, style: shadowStyle);
      final shadowPainter = TextPainter(
        text: shadowSpan,
        textDirection: TextDirection.ltr,
      );

      shadowPainter.layout();
      shadowPainter.paint(
        canvas,
        Offset(2, 2) -
            Offset(shadowPainter.width / 2, shadowPainter.height / 2),
      );

      // 绘制主字符
      final charStyle = TextStyle(
        fontSize: config.fontSize,
        color: _parseColor(config.textColor).withValues(alpha: opacity),
        fontFamily: config.fontFamily,
        fontWeight: FontWeight.bold,
        shadows: [
          Shadow(
            offset: const Offset(1, 1),
            blurRadius: 2,
            color: Colors.white.withValues(alpha: 0.3),
          ),
        ],
      );

      final charSpan = TextSpan(text: char, style: charStyle);
      final charPainter = TextPainter(
        text: charSpan,
        textDirection: TextDirection.ltr,
      );

      charPainter.layout();
      charPainter.paint(
        canvas,
        Offset(-charPainter.width / 2, -charPainter.height / 2),
      );

      canvas.restore();
    }

    // 添加文字装饰效果
    _addTextDecoration(canvas, charPositions, config);
  }

  void _addTextDecoration(
    Canvas canvas,
    Map<int, Offset> charPositions,
    TrafficImageConfig config,
  ) {
    if (charPositions.isEmpty) return;

    final paint =
        Paint()
          ..color = _parseColor(config.textColor).withValues(alpha: 0.1)
          ..style = PaintingStyle.stroke
          ..strokeWidth = 1.0;

    // 计算文字区域
    final positions = charPositions.values.toList();
    final minX = positions.map((p) => p.dx).reduce(math.min);
    final maxX = positions.map((p) => p.dx).reduce(math.max);
    final minY = positions.map((p) => p.dy).reduce(math.min);
    final maxY = positions.map((p) => p.dy).reduce(math.max);

    final textRect = Rect.fromLTWH(
      minX - 20,
      minY - 20,
      maxX - minX + 40,
      maxY - minY + 40,
    );

    // 绘制装饰框
    canvas.drawRRect(
      RRect.fromRectAndRadius(textRect, const Radius.circular(8)),
      paint,
    );

    // 添加连接线（连接相邻字符）
    final positionsList =
        charPositions.entries.toList()..sort((a, b) => a.key.compareTo(b.key));

    for (int i = 0; i < positionsList.length - 1; i++) {
      final current = positionsList[i].value;
      final next = positionsList[i + 1].value;

      // 随机决定是否绘制连接线
      if (math.Random().nextDouble() > 0.7) {
        final linePaint =
            Paint()
              ..color = _parseColor(config.textColor).withValues(alpha: 0.05)
              ..strokeWidth = 0.5
              ..style = PaintingStyle.stroke;

        canvas.drawLine(current, next, linePaint);
      }
    }
  }

  String _convertToEmoji(String text) {
    String result = text;

    // 转换数字
    for (final entry in _numberToEmoji.entries) {
      result = result.replaceAll(entry.key, entry.value);
    }

    // 转换字母
    for (final entry in _letterToUnicode.entries) {
      result = result.replaceAll(entry.key, entry.value);
      result = result.replaceAll(entry.key.toUpperCase(), entry.value);
    }

    return result;
  }

  String _applyUnicodeVariation(String text) {
    String result = text;
    final random = math.Random();

    // 为每个字符随机添加装饰效果
    for (int i = 0; i < text.length; i++) {
      final char = text[i];

      // 跳过空格和标点符号
      if (char == ' ' ||
          char == '\n' ||
          char == '\t' ||
          RegExp(r'[^\w\s]').hasMatch(char)) {
        continue;
      }

      // 随机决定是否添加装饰
      if (random.nextDouble() < 0.4) {
        // 40%概率添加装饰
        // 随机选择1-3个装饰字符
        final numDecorations = 1 + random.nextInt(3);
        String decorations = '';

        for (int j = 0; j < numDecorations; j++) {
          final decorationKey = _decorativeDiacritics.keys.elementAt(
            random.nextInt(_decorativeDiacritics.length),
          );
          decorations += decorationKey;
        }

        // 将装饰字符添加到原字符后面
        result =
            result.substring(0, i + 1) + decorations + result.substring(i + 1);

        // 更新索引，因为添加了装饰字符
        i += decorations.length;
      }
    }

    return result;
  }

  /// 增强的Unicode变体转换，支持多种装饰效果
  String _applyUnicodeVariationEnhanced(String text) {
    String result = text;
    final random = math.Random();

    // 为每个字符随机添加装饰效果
    for (int i = 0; i < result.length; i++) {
      final char = result[i];

      // 跳过空格、换行符、制表符和标点符号
      if (char == ' ' ||
          char == '\n' ||
          char == '\t' ||
          RegExp(r'[^\w\s]').hasMatch(char)) {
        continue;
      }

      // 随机决定是否添加装饰
      if (random.nextDouble() < 0.5) {
        // 50%概率添加装饰
        // 随机选择装饰类型
        final decorationType = random.nextInt(4);
        String decorations = '';

        switch (decorationType) {
          case 0: // 上装饰（重音符、锐音符等）
            final upperDecorations = [
              '\u0300',
              '\u0301',
              '\u0302',
              '\u0303',
              '\u0304',
              '\u0305',
              '\u0306',
              '\u0307',
              '\u0308',
              '\u0309',
              '\u030A',
              '\u030B',
              '\u030C',
              '\u030D',
              '\u030E',
              '\u030F',
            ];
            decorations =
                upperDecorations[random.nextInt(upperDecorations.length)];
            break;

          case 1: // 下装饰（下点、下划线等）
            final lowerDecorations = [
              '\u0323',
              '\u0324',
              '\u0325',
              '\u0326',
              '\u0327',
              '\u0328',
              '\u0329',
              '\u032A',
              '\u032B',
              '\u032C',
              '\u032D',
              '\u032E',
              '\u032F',
              '\u0330',
              '\u0331',
              '\u0332',
              '\u0333',
              '\u0334',
              '\u0335',
              '\u0336',
              '\u0337',
              '\u0338',
              '\u0339',
              '\u033A',
              '\u033B',
              '\u033C',
              '\u033D',
              '\u033E',
              '\u033F',
            ];
            decorations =
                lowerDecorations[random.nextInt(lowerDecorations.length)];
            break;

          case 2: // 组合装饰（上下都有）
            final upperDecorations = [
              '\u0300',
              '\u0301',
              '\u0302',
              '\u0303',
              '\u0304',
              '\u0305',
              '\u0306',
              '\u0307',
              '\u0308',
              '\u0309',
              '\u030A',
              '\u030B',
              '\u030C',
              '\u030D',
              '\u030E',
              '\u030F',
            ];
            final lowerDecorations = [
              '\u0323',
              '\u0324',
              '\u0325',
              '\u0326',
              '\u0327',
              '\u0328',
              '\u0329',
              '\u032A',
              '\u032B',
              '\u032C',
              '\u032D',
              '\u032E',
              '\u032F',
              '\u0330',
              '\u0331',
              '\u0332',
              '\u0333',
              '\u0334',
              '\u0335',
              '\u0336',
              '\u0337',
              '\u0338',
              '\u0339',
              '\u033A',
              '\u033B',
              '\u033C',
              '\u033D',
              '\u033E',
              '\u033F',
            ];
            decorations =
                upperDecorations[random.nextInt(upperDecorations.length)] +
                lowerDecorations[random.nextInt(lowerDecorations.length)];
            break;

          case 3: // 多重装饰（多个装饰字符）
            final allDecorations = [
              '\u0300',
              '\u0301',
              '\u0302',
              '\u0303',
              '\u0304',
              '\u0305',
              '\u0306',
              '\u0307',
              '\u0308',
              '\u0309',
              '\u030A',
              '\u030B',
              '\u030C',
              '\u030D',
              '\u030E',
              '\u030F',
              '\u0323',
              '\u0324',
              '\u0325',
              '\u0326',
              '\u0327',
              '\u0328',
              '\u0329',
              '\u032A',
              '\u032B',
              '\u032C',
              '\u032D',
              '\u032E',
              '\u032F',
              '\u0330',
              '\u0331',
              '\u0332',
              '\u0333',
              '\u0334',
              '\u0335',
              '\u0336',
              '\u0337',
              '\u0338',
              '\u0339',
              '\u033A',
              '\u033B',
              '\u033C',
              '\u033D',
              '\u033E',
              '\u033F',
            ];
            final numDecorations = 2 + random.nextInt(3); // 2-4个装饰字符
            for (int j = 0; j < numDecorations; j++) {
              decorations +=
                  allDecorations[random.nextInt(allDecorations.length)];
            }
            break;
        }

        // 将装饰字符添加到原字符后面
        result =
            result.substring(0, i + 1) + decorations + result.substring(i + 1);

        // 更新索引，因为添加了装饰字符
        i += decorations.length;
      }
    }

    return result;
  }

  String _addInvisibleChars(String text) {
    String result = text;
    final random = math.Random();

    // 在字符间随机添加不可见字符
    for (int i = 0; i < text.length - 1; i++) {
      if (random.nextDouble() < 0.2) {
        // 20%概率添加不可见字符
        final invisibleChar = _invisibleChars.keys.elementAt(
          random.nextInt(_invisibleChars.length),
        );
        result =
            result.substring(0, i + 1) +
            invisibleChar +
            result.substring(i + 1);
      }
    }

    return result;
  }

  String _maskSensitiveWords(String text, List<String> sensitiveWords) {
    String result = text;
    final random = math.Random();

    for (final word in sensitiveWords) {
      if (result.contains(word)) {
        // 在敏感词中间插入不可见字符
        final invisibleChar = _invisibleChars.keys.elementAt(
          random.nextInt(_invisibleChars.length),
        );
        final maskedWord = word.split('').join(invisibleChar);
        result = result.replaceAll(word, maskedWord);
      }
    }

    return result;
  }

  /// 编码不可见水印
  String encodeInvisibleWatermark(String watermark) {
    // 将水印文本以UTF-8编码为字节，再转二进制位串，0/1映射为零宽字符，首尾加哨兵
    final bytes = utf8.encode(watermark);
    final buffer = StringBuffer();
    buffer.write(_wmStart);
    for (final b in bytes) {
      final bits = b.toRadixString(2).padLeft(8, '0');
      for (final bit in bits.split('')) {
        buffer.write(bit == '0' ? _bit0 : _bit1);
      }
    }
    buffer.write(_wmEnd);
    return buffer.toString();
  }

  /// 检测文本中是否包含隐形水印
  bool hasInvisibleWatermark(String text) {
    // 检测标准哨兵是否存在
    final startIndex = text.indexOf(_wmStart);
    if (startIndex == -1) return false;
    final endIndex = text.indexOf(_wmEnd, startIndex + _wmStart.length);
    return endIndex != -1 && endIndex > startIndex;
  }

  /// 获取隐形水印内容
  String getInvisibleWatermark(String text) {
    return _decodeInvisibleWatermark(text);
  }

  String _decodeInvisibleWatermark(String text) {
    // 提取哨兵之间的位串并解码为UTF-8字符串
    final startIndex = text.indexOf(_wmStart);
    if (startIndex == -1) return '';
    final endIndex = text.indexOf(_wmEnd, startIndex + _wmStart.length);
    if (endIndex == -1 || endIndex <= startIndex) return '';

    final payload = text.substring(startIndex + _wmStart.length, endIndex);

    if (payload.isEmpty) return '';

    final bits = <String>[];
    for (int i = 0; i < payload.length; i++) {
      final ch = payload[i];
      if (ch == _bit0) {
        bits.add('0');
      } else if (ch == _bit1) {
        bits.add('1');
      }
    }

    if (bits.isEmpty) return '';

    // 按8位组装为字节
    final bytes = <int>[];
    for (int i = 0; i < bits.length; i += 8) {
      final chunk = bits.sublist(
        i,
        (i + 8) > bits.length ? bits.length : i + 8,
      );
      if (chunk.length < 8) break; // 非完整字节丢弃
      final byte = int.parse(chunk.join(), radix: 2);
      bytes.add(byte);
    }

    try {
      return utf8.decode(bytes, allowMalformed: true);
    } catch (_) {
      return '';
    }
  }

  void _drawAdvancedBackground(
    Canvas canvas,
    Size size,
    TrafficImageConfig config,
  ) {
    final rect = Rect.fromLTWH(0, 0, size.width, size.height);

    // 创建复杂的渐变背景
    final List<Color> gradientColors = [
      _parseColor(config.backgroundColor),
      _parseColor(config.backgroundColor).withValues(alpha: 0.8),
      _parseColor(config.backgroundColor).withValues(alpha: 0.6),
      _parseColor(config.backgroundColor).withValues(alpha: 0.9),
      _parseColor(config.backgroundColor),
    ];

    // 主渐变
    final mainGradient = LinearGradient(
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
      colors: gradientColors,
      stops: const [0.0, 0.3, 0.5, 0.7, 1.0],
    );

    // 绘制主渐变
    canvas.drawRect(rect, Paint()..shader = mainGradient.createShader(rect));

    // 添加径向渐变叠加
    final radialGradient = RadialGradient(
      center: Alignment.center,
      radius: 1.2,
      colors: [
        Colors.white.withValues(alpha: 0.15),
        Colors.white.withValues(alpha: 0.05),
        Colors.transparent,
      ],
      stops: const [0.0, 0.5, 1.0],
    );

    canvas.drawRect(rect, Paint()..shader = radialGradient.createShader(rect));

    // 添加纹理效果
    _addTextureEffect(canvas, size, config);
  }

  void _addTextureEffect(Canvas canvas, Size size, TrafficImageConfig config) {
    final random = math.Random();
    final paint =
        Paint()
          ..color = Colors.white.withValues(alpha: 0.03)
          ..strokeWidth = 0.5;

    // 添加细线纹理
    for (int i = 0; i < 100; i++) {
      final x = random.nextDouble() * size.width;
      final y = random.nextDouble() * size.height;
      canvas.drawLine(
        Offset(x, y),
        Offset(x + random.nextDouble() * 20, y + random.nextDouble() * 20),
        paint,
      );
    }

    // 添加点状纹理
    for (int i = 0; i < 200; i++) {
      final x = random.nextDouble() * size.width;
      final y = random.nextDouble() * size.height;
      canvas.drawCircle(Offset(x, y), 0.5, paint);
    }
  }

  void _addAdvancedNoise(Canvas canvas, Size size, double level) {
    final random = math.Random();

    // 添加多层次噪点
    _addNoiseLayer(canvas, size, level, 0.05, 300); // 小噪点
    _addNoiseLayer(canvas, size, level, 0.08, 200); // 中等噪点
    _addNoiseLayer(canvas, size, level, 0.12, 100); // 大噪点

    // 添加扭曲线条
    for (int i = 0; i < 15 * level; i++) {
      final paint =
          Paint()
            ..color = Colors.white.withValues(alpha: 0.1 * level)
            ..strokeWidth = random.nextDouble() * 2 + 0.5
            ..style = PaintingStyle.stroke;

      final path = Path();
      var x = random.nextDouble() * size.width;
      var y = random.nextDouble() * size.height;
      path.moveTo(x, y);

      // 创建贝塞尔曲线
      for (int j = 0; j < 3; j++) {
        final cp1x = x + (random.nextDouble() - 0.5) * 100;
        final cp1y = y + (random.nextDouble() - 0.5) * 100;
        final cp2x = x + (random.nextDouble() - 0.5) * 100;
        final cp2y = y + (random.nextDouble() - 0.5) * 100;
        x += (random.nextDouble() - 0.5) * 100;
        y += (random.nextDouble() - 0.5) * 100;
        path.cubicTo(cp1x, cp1y, cp2x, cp2y, x, y);
      }

      canvas.drawPath(path, paint);
    }

    // 添加干扰形状
    _addInterferenceShapes(canvas, size, level);
  }

  void _addNoiseLayer(
    Canvas canvas,
    Size size,
    double level,
    double opacity,
    int count,
  ) {
    final random = math.Random();
    final paint =
        Paint()
          ..color = Colors.white.withValues(alpha: opacity * level)
          ..style = PaintingStyle.fill;

    for (int i = 0; i < count; i++) {
      final x = random.nextDouble() * size.width;
      final y = random.nextDouble() * size.height;
      final radius = random.nextDouble() * 2 + 0.5;
      canvas.drawCircle(Offset(x, y), radius, paint);
    }
  }

  void _addInterferenceShapes(Canvas canvas, Size size, double level) {
    final random = math.Random();
    final paint =
        Paint()
          ..color = Colors.white.withValues(alpha: 0.05 * level)
          ..style = PaintingStyle.stroke
          ..strokeWidth = 1.0;

    for (int i = 0; i < 10 * level; i++) {
      final path = Path();
      final centerX = random.nextDouble() * size.width;
      final centerY = random.nextDouble() * size.height;
      final radius = random.nextDouble() * 30 + 10;

      // 创建不规则多边形
      path.moveTo(
        centerX + radius * math.cos(0),
        centerY + radius * math.sin(0),
      );

      for (int j = 1; j <= 6; j++) {
        final angle = j * math.pi / 3;
        final jitter = random.nextDouble() * 10 - 5;
        path.lineTo(
          centerX + (radius + jitter) * math.cos(angle),
          centerY + (radius + jitter) * math.sin(angle),
        );
      }

      path.close();
      canvas.drawPath(path, paint);
    }
  }

  void _applyAdvancedDistortion(Canvas canvas, Size size, double level) {
    final random = math.Random();
    canvas.save();

    // 应用多层扭曲效果
    for (int i = 0; i < 3; i++) {
      final distortionLevel = level * (1 - i * 0.2); // 逐层减小扭曲程度

      // 旋转扭曲
      final rotation = (random.nextDouble() - 0.5) * distortionLevel * 0.1;
      canvas.translate(size.width / 2, size.height / 2);
      canvas.rotate(rotation);
      canvas.translate(-size.width / 2, -size.height / 2);

      // 缩放扭曲
      final scaleX = 1.0 + (random.nextDouble() - 0.5) * distortionLevel * 0.08;
      final scaleY = 1.0 + (random.nextDouble() - 0.5) * distortionLevel * 0.08;
      canvas.scale(scaleX, scaleY);

      // 错切扭曲
      final skewX = (random.nextDouble() - 0.5) * distortionLevel * 0.05;
      final skewY = (random.nextDouble() - 0.5) * distortionLevel * 0.05;
      canvas.skew(skewX, skewY);
    }

    canvas.restore();
  }

  void _applyLayerBlending(
    Canvas canvas,
    Size size,
    TrafficImageConfig config,
  ) {
    // 添加高光层
    final highlightPaint =
        Paint()
          ..color = Colors.white.withValues(alpha: 0.1)
          ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 20);

    canvas.drawRect(
      Rect.fromLTWH(0, 0, size.width, size.height),
      highlightPaint,
    );

    // 添加暗部层
    final shadowPaint =
        Paint()
          ..color = Colors.black.withValues(alpha: 0.1)
          ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 15);

    canvas.drawRect(Rect.fromLTWH(0, 0, size.width, size.height), shadowPaint);
  }

  void _addStylizedWatermark(
    Canvas canvas,
    Size size,
    TrafficImageConfig config,
  ) {
    final watermarkStyle = TextStyle(
      fontSize: 14,
      color: Colors.white.withValues(alpha: 0.3),
      fontWeight: FontWeight.w500,
      letterSpacing: 2,
    );

    final textSpan = TextSpan(
      text: config.watermarkText,
      style: watermarkStyle,
    );
    final textPainter = TextPainter(
      text: textSpan,
      textDirection: TextDirection.ltr,
    );

    textPainter.layout();

    // 在四个角落添加水印
    final positions = [
      Offset(10, 10), // 左上
      Offset(size.width - textPainter.width - 10, 10), // 右上
      Offset(10, size.height - textPainter.height - 10), // 左下
      Offset(
        size.width - textPainter.width - 10,
        size.height - textPainter.height - 10,
      ), // 右下
    ];

    for (final position in positions) {
      canvas.save();
      canvas.translate(position.dx, position.dy);
      canvas.rotate(math.pi / 6); // 倾斜水印
      textPainter.paint(canvas, Offset.zero);
      canvas.restore();
    }
  }

  void _addDecorationLayer(
    Canvas canvas,
    Size size,
    TrafficImageConfig config,
  ) {
    final random = math.Random();

    // 添加装饰性几何图形
    for (int i = 0; i < 8; i++) {
      final paint =
          Paint()
            ..color = Colors.white.withValues(alpha: 0.05)
            ..style = PaintingStyle.fill
            ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 5);

      final x = random.nextDouble() * size.width;
      final y = random.nextDouble() * size.height;
      final radius = random.nextDouble() * 60 + 30;

      // 随机选择形状类型
      final shapeType = random.nextInt(3);
      switch (shapeType) {
        case 0: // 圆形
          canvas.drawCircle(Offset(x, y), radius, paint);
          break;
        case 1: // 矩形
          canvas.drawRect(
            Rect.fromCenter(
              center: Offset(x, y),
              width: radius * 2,
              height: radius * 1.5,
            ),
            paint,
          );
          break;
        case 2: // 多边形
          final path = Path();
          final sides = random.nextInt(3) + 5; // 5-7边形
          path.moveTo(x + radius * math.cos(0), y + radius * math.sin(0));

          for (int j = 1; j <= sides; j++) {
            final angle = j * 2 * math.pi / sides;
            path.lineTo(
              x + radius * math.cos(angle),
              y + radius * math.sin(angle),
            );
          }

          path.close();
          canvas.drawPath(path, paint);
          break;
      }
    }

    // 添加装饰性线条
    for (int i = 0; i < 5; i++) {
      final paint =
          Paint()
            ..color = Colors.white.withValues(alpha: 0.03)
            ..strokeWidth = random.nextDouble() * 3 + 1
            ..style = PaintingStyle.stroke
            ..strokeCap = StrokeCap.round;

      final path = Path();
      var x = random.nextDouble() * size.width;
      var y = random.nextDouble() * size.height;
      path.moveTo(x, y);

      // 创建平滑曲线
      for (int j = 0; j < 4; j++) {
        x += (random.nextDouble() - 0.5) * 200;
        y += (random.nextDouble() - 0.5) * 200;
        final cp1x = x - 50 + random.nextDouble() * 100;
        final cp1y = y - 50 + random.nextDouble() * 100;
        final cp2x = x - 50 + random.nextDouble() * 100;
        final cp2y = y - 50 + random.nextDouble() * 100;
        path.cubicTo(cp1x, cp1y, cp2x, cp2y, x, y);
      }

      canvas.drawPath(path, paint);
    }

    // 添加光效
    final gradient = RadialGradient(
      center: Alignment(
        random.nextDouble() * 2 - 1,
        random.nextDouble() * 2 - 1,
      ),
      radius: 0.7,
      colors: [
        Colors.white.withValues(alpha: 0.1),
        Colors.white.withValues(alpha: 0.05),
        Colors.transparent,
      ],
      stops: const [0.0, 0.5, 1.0],
    );

    canvas.drawRect(
      Rect.fromLTWH(0, 0, size.width, size.height),
      Paint()
        ..shader = gradient.createShader(
          Rect.fromLTWH(0, 0, size.width, size.height),
        ),
    );
  }
}
