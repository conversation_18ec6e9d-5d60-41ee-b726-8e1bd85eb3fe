import 'dart:math' as math;
import 'dart:ui' as ui;
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../models/image_template.dart';
import '../models/traffic_guide_models.dart';
import '../widgets/export_options.dart';
import '../utils/image_dimension_calculator.dart';

class AdvancedImageService {
  static final AdvancedImageService _instance =
      AdvancedImageService._internal();
  factory AdvancedImageService() => _instance;
  AdvancedImageService._internal();

  /// 使用模板生成高质量引流图片
  Future<Uint8List> generateImageWithTemplate(
    TrafficImageConfig config,
    ImageTemplate template, {
    ExportConfig? exportConfig,
  }) async {
    final size = _calculateOptimalSize(config, exportConfig);
    final recorder = ui.PictureRecorder();
    final canvas = Canvas(recorder);

    // 1. 绘制背景
    _drawBackground(canvas, size, config, template);

    // 2. 绘制文字内容
    await _drawText(canvas, size, config, template);

    // 3. 应用视觉效果
    _applyEffects(canvas, size, config);

    // 4. 添加水印（如果需要）
    if (config.addWatermark && config.watermarkText.isNotEmpty) {
      _addWatermark(canvas, size, config);
    }

    final picture = recorder.endRecording();
    final image = await picture.toImage(
      size.width.toInt(),
      size.height.toInt(),
    );

    final format =
        exportConfig?.format == ExportFormat.jpg
            ? ui.ImageByteFormat.rawRgba
            : ui.ImageByteFormat.png;

    final byteData = await image.toByteData(format: format);
    return byteData!.buffer.asUint8List();
  }

  /// 计算最优图片尺寸
  Size _calculateOptimalSize(
    TrafficImageConfig config,
    ExportConfig? exportConfig,
  ) {
    // 使用共享的尺寸计算工具确保与预览一致
    return ImageDimensionCalculator.calculateOptimalSize(
      config,
      exportSize: exportConfig?.size,
    );
  }

  /// 绘制背景
  void _drawBackground(
    Canvas canvas,
    Size size,
    TrafficImageConfig config,
    ImageTemplate template,
  ) {
    final rect = Rect.fromLTWH(0, 0, size.width, size.height);
    final backgroundColor = _parseColor(config.backgroundColor);

    final gradient = LinearGradient(
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
      colors: [backgroundColor, backgroundColor.withValues(alpha: 0.8)],
    );

    canvas.drawRect(rect, Paint()..shader = gradient.createShader(rect));
  }

  /// 绘制文字
  Future<void> _drawText(
    Canvas canvas,
    Size size,
    TrafficImageConfig config,
    ImageTemplate template,
  ) async {
    if (config.text.isEmpty) return;

    final style = TextStyle(
      fontSize: config.fontSize,
      fontWeight: template.fontWeight,
      color: _parseColor(config.textColor),
      fontFamily: config.fontFamily,
      shadows: [
        Shadow(
          offset: const Offset(2, 2),
          blurRadius: 4,
          color: Colors.black.withValues(alpha: 0.3),
        ),
      ],
    );

    final textSpan = TextSpan(text: config.text, style: style);
    final textPainter = TextPainter(
      text: textSpan,
      textDirection: TextDirection.ltr,
      textAlign: TextAlign.center,
    );

    textPainter.layout(maxWidth: size.width * 0.9);

    final offset = Offset(
      (size.width - textPainter.width) / 2,
      (size.height - textPainter.height) / 2,
    );

    textPainter.paint(canvas, offset);
  }

  /// 应用视觉效果
  void _applyEffects(Canvas canvas, Size size, TrafficImageConfig config) {
    // 添加干扰效果
    if (config.noiseLevel > 0) {
      _addNoise(canvas, size, config.noiseLevel);
    }

    // 添加扭曲效果
    if (config.distortionLevel > 0) {
      _addDistortion(canvas, size, config.distortionLevel);
    }
  }

  /// 添加水印
  void _addWatermark(Canvas canvas, Size size, TrafficImageConfig config) {
    final style = TextStyle(
      fontSize: 12,
      color: Colors.white.withValues(alpha: 0.6),
      fontWeight: FontWeight.w300,
    );

    final textSpan = TextSpan(text: config.watermarkText, style: style);
    final textPainter = TextPainter(
      text: textSpan,
      textDirection: TextDirection.ltr,
    );

    textPainter.layout();

    final offset = Offset(
      size.width - textPainter.width - 20,
      size.height - textPainter.height - 20,
    );

    textPainter.paint(canvas, offset);
  }

  /// 添加增强的干扰效果
  void _addNoise(Canvas canvas, Size size, double level) {
    final random = math.Random(42);

    // 增强的点状干扰
    final pointPaint =
        Paint()
          ..color = Colors.white.withValues(
            alpha: (0.15 + 0.25 * level).clamp(0.0, 0.8),
          )
          ..style = PaintingStyle.fill;

    final pointCount = (size.width * size.height * level * 0.005).toInt().clamp(
      50,
      2000,
    );
    for (int i = 0; i < pointCount; i++) {
      final x = random.nextDouble() * size.width;
      final y = random.nextDouble() * size.height;
      final radius = random.nextDouble() * 3 + 0.5;
      canvas.drawCircle(Offset(x, y), radius, pointPaint);
    }

    // 增强的线条干扰
    final linePaint =
        Paint()
          ..color = Colors.white.withValues(
            alpha: (0.1 + 0.2 * level).clamp(0.0, 0.6),
          )
          ..strokeWidth = 1 + level * 2;

    final lineCount = (15 + 25 * level).toInt();
    for (int i = 0; i < lineCount; i++) {
      final startX = random.nextDouble() * size.width;
      final startY = random.nextDouble() * size.height;
      final length = 30 + random.nextDouble() * 80 * level;
      final angle = random.nextDouble() * 2 * math.pi;
      final endX = startX + math.cos(angle) * length;
      final endY = startY + math.sin(angle) * length;

      canvas.drawLine(Offset(startX, startY), Offset(endX, endY), linePaint);
    }

    // 随机形状干扰
    final shapePaint =
        Paint()
          ..color = Colors.white.withValues(
            alpha: (0.08 + 0.15 * level).clamp(0.0, 0.5),
          )
          ..style = PaintingStyle.fill;

    final shapeCount = (5 + 10 * level).toInt();
    for (int i = 0; i < shapeCount; i++) {
      final centerX = random.nextDouble() * size.width;
      final centerY = random.nextDouble() * size.height;
      final shapeSize = 5 + random.nextDouble() * 15 * level;

      if (random.nextBool()) {
        canvas.drawRect(
          Rect.fromCenter(
            center: Offset(centerX, centerY),
            width: shapeSize,
            height: shapeSize,
          ),
          shapePaint,
        );
      } else {
        canvas.drawCircle(Offset(centerX, centerY), shapeSize / 2, shapePaint);
      }
    }
  }

  /// 添加增强的扭曲效果
  void _addDistortion(Canvas canvas, Size size, double level) {
    final random = math.Random(123);

    // 增强的波浪扭曲线条
    final wavePaint =
        Paint()
          ..color = Colors.white.withValues(
            alpha: (0.1 + 0.3 * level).clamp(0.0, 0.7),
          )
          ..style = PaintingStyle.stroke
          ..strokeWidth = 1 + level * 3;

    final waveCount = (25 + 35 * level).toInt();
    for (int i = 0; i < waveCount; i++) {
      final path = Path();
      final startX = random.nextDouble() * size.width;
      final startY = random.nextDouble() * size.height;

      path.moveTo(startX, startY);

      final segments = 6 + (4 * level).toInt();
      for (int j = 1; j <= segments; j++) {
        final amplitude = 20 + 60 * level;
        final frequency = 0.5 + level;
        final x = startX + j * 15 + math.sin(j * frequency) * amplitude;
        final y = startY + (random.nextDouble() - 0.5) * amplitude;
        path.lineTo(x, y);
      }

      canvas.drawPath(path, wavePaint);
    }

    // 螺旋扭曲效果
    final spiralPaint =
        Paint()
          ..color = Colors.white.withValues(
            alpha: (0.08 + 0.2 * level).clamp(0.0, 0.6),
          )
          ..style = PaintingStyle.stroke
          ..strokeWidth = 1 + level * 2;

    final spiralCount = (8 + 12 * level).toInt();
    for (int i = 0; i < spiralCount; i++) {
      final centerX = random.nextDouble() * size.width;
      final centerY = random.nextDouble() * size.height;
      final maxRadius = 20 + 40 * level;

      final path = Path();
      for (double angle = 0; angle < 4 * math.pi; angle += 0.2) {
        final radius = (angle / (4 * math.pi)) * maxRadius;
        final x = centerX + math.cos(angle) * radius;
        final y = centerY + math.sin(angle) * radius;

        if (angle == 0) {
          path.moveTo(x, y);
        } else {
          path.lineTo(x, y);
        }
      }
      canvas.drawPath(path, spiralPaint);
    }

    // 几何扭曲形状
    final shapePaint =
        Paint()
          ..color = Colors.white.withValues(
            alpha: (0.06 + 0.15 * level).clamp(0.0, 0.5),
          )
          ..style = PaintingStyle.fill;

    final shapeCount = (8 + 15 * level).toInt();
    for (int i = 0; i < shapeCount; i++) {
      final centerX = random.nextDouble() * size.width;
      final centerY = random.nextDouble() * size.height;
      final baseSize = 15 + random.nextDouble() * 30 * level;

      final path = Path();
      final sides = 3 + random.nextInt(5);
      for (int j = 0; j < sides; j++) {
        final angle = (j / sides) * 2 * math.pi;
        final radiusVariation = 0.7 + random.nextDouble() * 0.6;
        final radius = baseSize * radiusVariation;
        final x = centerX + math.cos(angle) * radius;
        final y = centerY + math.sin(angle) * radius;

        if (j == 0) {
          path.moveTo(x, y);
        } else {
          path.lineTo(x, y);
        }
      }
      path.close();
      canvas.drawPath(path, shapePaint);
    }

    // 网格扭曲效果
    if (level > 0.3) {
      final gridPaint =
          Paint()
            ..color = Colors.white.withValues(
              alpha: (0.05 + 0.1 * level).clamp(0.0, 0.4),
            )
            ..style = PaintingStyle.stroke
            ..strokeWidth = 0.5 + level;

      final gridSpacing = 40 - 20 * level;
      for (double x = 0; x < size.width; x += gridSpacing) {
        final path = Path();
        for (double y = 0; y < size.height; y += 5) {
          final distortedX = x + math.sin(y * 0.02) * 10 * level;
          if (y == 0) {
            path.moveTo(distortedX, y);
          } else {
            path.lineTo(distortedX, y);
          }
        }
        canvas.drawPath(path, gridPaint);
      }
    }
  }

  /// 解析颜色字符串
  Color _parseColor(String colorString) {
    if (colorString.startsWith('#')) {
      final hex = colorString.substring(1);
      return Color(int.parse('FF$hex', radix: 16));
    }
    return Colors.black;
  }




}
