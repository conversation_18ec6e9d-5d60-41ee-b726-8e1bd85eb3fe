import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'dart:math' as math;
import '../models/image_template.dart';
import '../models/traffic_guide_models.dart';
import '../utils/image_dimension_calculator.dart';

class RealTimePreview extends StatefulWidget {
  final TrafficImageConfig config;
  final ImageTemplate? template;
  final Function(Uint8List)? onImageGenerated;
  final bool isGenerating;

  const RealTimePreview({
    super.key,
    required this.config,
    this.template,
    this.onImageGenerated,
    this.isGenerating = false,
  });

  @override
  State<RealTimePreview> createState() => _RealTimePreviewState();
}

class _RealTimePreviewState extends State<RealTimePreview>
    with TickerProviderStateMixin {
  late AnimationController _pulseController;
  late AnimationController _shimmerController;
  late Animation<double> _pulseAnimation;
  late Animation<double> _shimmerAnimation;

  @override
  void initState() {
    super.initState();

    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    )..repeat(reverse: true);

    _shimmerController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    )..repeat();

    _pulseAnimation = Tween<double>(begin: 0.8, end: 1.0).animate(
      CurvedAnimation(parent: _pulseController, curve: Curves.easeInOut),
    );

    _shimmerAnimation = Tween<double>(begin: -1.0, end: 2.0).animate(
      CurvedAnimation(parent: _shimmerController, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _pulseController.dispose();
    _shimmerController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: Column(
        children: [
          // 标题栏
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
              borderRadius: const BorderRadius.vertical(
                top: Radius.circular(16),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.visibility,
                  color: Theme.of(context).primaryColor,
                  size: 20,
                ),
                const SizedBox(width: 8),
                const Expanded(
                  child: Text(
                    '实时预览',
                    style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                  ),
                ),
                if (widget.isGenerating)
                  SizedBox(
                    width: 16,
                    height: 16,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(
                        Theme.of(context).primaryColor,
                      ),
                    ),
                  ),
              ],
            ),
          ),

          // 预览区域
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: _buildPreviewContent(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPreviewContent() {
    if (widget.config.text.isEmpty) {
      return _buildEmptyState();
    }

    // 使用新的尺寸计算工具获取最优宽高比
    final aspectRatio = ImageDimensionCalculator.getOptimalAspectRatio(
      widget.config,
    );

    return Center(
      child: AspectRatio(
        aspectRatio: aspectRatio,
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: Colors.grey.withValues(alpha: 0.3),
              width: 1,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 10,
                offset: const Offset(0, 5),
              ),
            ],
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(12),
            child: LayoutBuilder(
              builder: (context, constraints) {
                final previewSize = Size(
                  constraints.maxWidth,
                  constraints.maxHeight,
                );
                return Stack(
                  children: [
                    // 背景
                    _buildBackground(),

                    // 文字内容 - 使用响应式渲染
                    _buildResponsiveTextContent(previewSize),

                    // 加载遮罩
                    if (widget.isGenerating) _buildLoadingOverlay(),
                  ],
                );
              },
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildBackground() {
    final template = widget.template;
    final backgroundColor = _parseColor(widget.config.backgroundColor);

    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [backgroundColor, backgroundColor.withValues(alpha: 0.8)],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
      ),
      child: Stack(
        children: [
          // 模板背景效果（如果有模板）
          if (template != null) _buildBackgroundEffects(template),

          // 扭曲效果
          if (widget.config.distortionLevel > 0) _buildDistortionEffect(),
        ],
      ),
    );
  }

  Widget _buildBackgroundEffects(ImageTemplate template) {
    return Stack(
      children: [
        // 纹理效果
        if (template.backgroundType == BackgroundType.texture)
          _buildTextureEffect(),

        // 几何图案
        if (template.backgroundType == BackgroundType.geometric)
          _buildGeometricPattern(),

        // 装饰元素
        ...template.decorations.map(
          (decoration) => _buildDecorationElement(decoration),
        ),
      ],
    );
  }

  Widget _buildTextureEffect() {
    return AnimatedBuilder(
      animation: _shimmerAnimation,
      builder: (context, child) {
        return Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment(_shimmerAnimation.value - 1, 0),
              end: Alignment(_shimmerAnimation.value, 0),
              colors: [
                Colors.transparent,
                Colors.white.withValues(alpha: 0.1),
                Colors.transparent,
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildGeometricPattern() {
    return CustomPaint(painter: GeometricPatternPainter(), size: Size.infinite);
  }

  Widget _buildDecorationElement(DecorationElement decoration) {
    return Positioned(
      left: decoration.position.dx,
      top: decoration.position.dy,
      child: Transform.rotate(
        angle: decoration.rotation,
        child: Container(
          width: decoration.size.width,
          height: decoration.size.height,
          decoration: BoxDecoration(
            color: decoration.color.withValues(alpha: decoration.opacity),
            shape:
                decoration.type == 'circle'
                    ? BoxShape.circle
                    : BoxShape.rectangle,
            borderRadius:
                decoration.type == 'rectangle'
                    ? BorderRadius.circular(4)
                    : null,
          ),
        ),
      ),
    );
  }

  Widget _buildResponsiveTextContent(Size previewSize) {
    final template = widget.template;
    final optimalSize = ImageDimensionCalculator.calculateOptimalSize(
      widget.config,
    );

    // 计算响应式字体大小和内边距
    final responsiveFontSize =
        ImageDimensionCalculator.calculatePreviewFontSize(
          widget.config,
          previewSize,
          template: template,
        );

    final responsivePadding = ImageDimensionCalculator.calculatePreviewPadding(
      previewSize,
      optimalSize,
    );

    return AnimatedBuilder(
      animation: _pulseAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: widget.isGenerating ? _pulseAnimation.value : 1.0,
          child: Stack(
            children: [
              // 背景效果
              if (template != null) _buildBackgroundEffects(template),

              // 文字内容
              Positioned.fill(
                child: Padding(
                  padding: EdgeInsets.all(responsivePadding),
                  child: Center(
                    child: ConstrainedBox(
                      constraints: BoxConstraints(
                        maxWidth: previewSize.width * 0.9, // 与导出服务保持一致的90%宽度约束
                      ),
                      child: Text(
                        widget.config.text,
                        style: TextStyle(
                          fontSize: responsiveFontSize,
                          fontWeight: template?.fontWeight ?? FontWeight.bold,
                          color: _parseColor(widget.config.textColor),
                          fontFamily: widget.config.fontFamily,
                          shadows: [
                            Shadow(
                              offset: const Offset(1, 1),
                              blurRadius: 2,
                              color: Colors.black.withValues(alpha: 0.3),
                            ),
                          ],
                        ),
                        textAlign: _getTextAlign(template?.textLayout),
                        maxLines: null,
                        softWrap: true,
                        overflow: TextOverflow.visible, // 确保文本不会被截断
                      ),
                    ),
                  ),
                ),
              ),

              // 干扰效果
              if (widget.config.noiseLevel > 0) _buildNoiseEffect(),

              // 水印
              if (widget.config.addWatermark &&
                  widget.config.watermarkText.isNotEmpty)
                _buildWatermarkOverlay(),
            ],
          ),
        );
      },
    );
  }

  Widget _buildDistortionEffect() {
    return Positioned.fill(
      child: CustomPaint(
        painter: DistortionPainter(widget.config.distortionLevel),
      ),
    );
  }

  Widget _buildNoiseEffect() {
    return Positioned.fill(
      child: CustomPaint(painter: NoisePainter(widget.config.noiseLevel)),
    );
  }

  Widget _buildWatermarkOverlay() {
    return Positioned(
      bottom: 8,
      right: 8,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
        decoration: BoxDecoration(
          color: Colors.black.withValues(alpha: 0.3),
          borderRadius: BorderRadius.circular(4),
        ),
        child: Text(
          widget.config.watermarkText,
          style: TextStyle(
            color: Colors.white.withValues(alpha: 0.8),
            fontSize: 10,
            fontWeight: FontWeight.w300,
          ),
        ),
      ),
    );
  }

  Widget _buildLoadingOverlay() {
    return AnimatedBuilder(
      animation: _shimmerAnimation,
      builder: (context, child) {
        return Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment(_shimmerAnimation.value - 1, -1),
              end: Alignment(_shimmerAnimation.value, 1),
              colors: [
                Colors.transparent,
                Colors.white.withValues(alpha: 0.3),
                Colors.transparent,
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(Icons.image_outlined, size: 48, color: Colors.grey[400]),
          const SizedBox(height: 12),
          Text(
            '输入文字开始预览',
            style: TextStyle(fontSize: 14, color: Colors.grey[600]),
          ),
          const SizedBox(height: 6),
          Text(
            '选择模板和配置参数\n实时查看效果',
            style: TextStyle(fontSize: 12, color: Colors.grey[500]),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  
  
  Color _parseColor(String colorString) {
    if (colorString.startsWith('#')) {
      final hex = colorString.substring(1);
      return Color(int.parse('FF$hex', radix: 16));
    }
    return Colors.black;
  }

  TextAlign _getTextAlign(TextLayout? layout) {
    switch (layout) {
      case TextLayout.left:
        return TextAlign.left;
      case TextLayout.right:
        return TextAlign.right;
      case TextLayout.center:
      default:
        return TextAlign.center;
    }
  }
}

class GeometricPatternPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint =
        Paint()
          ..color = Colors.white.withValues(alpha: 0.1)
          ..style = PaintingStyle.stroke
          ..strokeWidth = 1;

    // 绘制几何图案
    for (int i = 0; i < 5; i++) {
      final radius = (i + 1) * 20.0;
      canvas.drawCircle(
        Offset(size.width * 0.2, size.height * 0.3),
        radius,
        paint,
      );
    }

    for (int i = 0; i < 3; i++) {
      final size1 = (i + 1) * 15.0;
      canvas.drawRect(
        Rect.fromCenter(
          center: Offset(size.width * 0.8, size.height * 0.7),
          width: size1,
          height: size1,
        ),
        paint,
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

class NoisePainter extends CustomPainter {
  final double noiseLevel;

  NoisePainter(this.noiseLevel);

  @override
  void paint(Canvas canvas, Size size) {
    if (noiseLevel <= 0) return;

    final random = math.Random(42); // 固定种子，确保一致性

    // 增强的点状干扰 - 更多数量，更明显的效果
    final pointPaint =
        Paint()
          ..color = Colors.white.withValues(
            alpha: (0.15 + 0.25 * noiseLevel).clamp(0.0, 0.8),
          )
          ..style = PaintingStyle.fill;

    final pointCount = (size.width * size.height * noiseLevel * 0.005)
        .toInt()
        .clamp(50, 2000);

    for (int i = 0; i < pointCount; i++) {
      final x = random.nextDouble() * size.width;
      final y = random.nextDouble() * size.height;
      final radius = random.nextDouble() * 3 + 0.5;

      canvas.drawCircle(Offset(x, y), radius, pointPaint);
    }

    // 增强的线条干扰 - 更粗更明显
    final linePaint =
        Paint()
          ..color = Colors.white.withValues(
            alpha: (0.1 + 0.2 * noiseLevel).clamp(0.0, 0.6),
          )
          ..strokeWidth = 1 + noiseLevel * 2;

    final lineCount = (15 + 25 * noiseLevel).toInt();
    for (int i = 0; i < lineCount; i++) {
      final startX = random.nextDouble() * size.width;
      final startY = random.nextDouble() * size.height;
      final length = 30 + random.nextDouble() * 80 * noiseLevel;
      final angle = random.nextDouble() * 2 * math.pi;
      final endX = startX + math.cos(angle) * length;
      final endY = startY + math.sin(angle) * length;

      canvas.drawLine(Offset(startX, startY), Offset(endX, endY), linePaint);
    }

    // 新增：随机形状干扰
    final shapePaint =
        Paint()
          ..color = Colors.white.withValues(
            alpha: (0.08 + 0.15 * noiseLevel).clamp(0.0, 0.5),
          )
          ..style = PaintingStyle.fill;

    final shapeCount = (5 + 10 * noiseLevel).toInt();
    for (int i = 0; i < shapeCount; i++) {
      final centerX = random.nextDouble() * size.width;
      final centerY = random.nextDouble() * size.height;
      final shapeSize = 5 + random.nextDouble() * 15 * noiseLevel;

      if (random.nextBool()) {
        // 绘制矩形
        canvas.drawRect(
          Rect.fromCenter(
            center: Offset(centerX, centerY),
            width: shapeSize,
            height: shapeSize,
          ),
          shapePaint,
        );
      } else {
        // 绘制圆形
        canvas.drawCircle(Offset(centerX, centerY), shapeSize / 2, shapePaint);
      }
    }
  }

  @override
  bool shouldRepaint(NoisePainter oldDelegate) {
    return oldDelegate.noiseLevel != noiseLevel;
  }
}

class DistortionPainter extends CustomPainter {
  final double distortionLevel;

  DistortionPainter(this.distortionLevel);

  @override
  void paint(Canvas canvas, Size size) {
    if (distortionLevel <= 0) return;

    final random = math.Random(123); // 固定种子

    // 增强的波浪扭曲线条 - 更明显的效果
    final wavePaint =
        Paint()
          ..color = Colors.white.withValues(
            alpha: (0.1 + 0.3 * distortionLevel).clamp(0.0, 0.7),
          )
          ..style = PaintingStyle.stroke
          ..strokeWidth = 1 + distortionLevel * 3;

    final waveCount = (25 + 35 * distortionLevel).toInt();
    for (int i = 0; i < waveCount; i++) {
      final path = Path();
      final startX = random.nextDouble() * size.width;
      final startY = random.nextDouble() * size.height;

      path.moveTo(startX, startY);

      // 创建更复杂的波浪形路径
      final segments = 6 + (4 * distortionLevel).toInt();
      for (int j = 1; j <= segments; j++) {
        final amplitude = 20 + 60 * distortionLevel;
        final frequency = 0.5 + distortionLevel;
        final x = startX + j * 15 + math.sin(j * frequency) * amplitude;
        final y = startY + (random.nextDouble() - 0.5) * amplitude;
        path.lineTo(x, y);
      }

      canvas.drawPath(path, wavePaint);
    }

    // 增强的螺旋扭曲效果
    final spiralPaint =
        Paint()
          ..color = Colors.white.withValues(
            alpha: (0.08 + 0.2 * distortionLevel).clamp(0.0, 0.6),
          )
          ..style = PaintingStyle.stroke
          ..strokeWidth = 1 + distortionLevel * 2;

    final spiralCount = (8 + 12 * distortionLevel).toInt();
    for (int i = 0; i < spiralCount; i++) {
      final centerX = random.nextDouble() * size.width;
      final centerY = random.nextDouble() * size.height;
      final maxRadius = 20 + 40 * distortionLevel;

      final path = Path();
      for (double angle = 0; angle < 4 * math.pi; angle += 0.2) {
        final radius = (angle / (4 * math.pi)) * maxRadius;
        final x = centerX + math.cos(angle) * radius;
        final y = centerY + math.sin(angle) * radius;

        if (angle == 0) {
          path.moveTo(x, y);
        } else {
          path.lineTo(x, y);
        }
      }
      canvas.drawPath(path, spiralPaint);
    }

    // 增强的几何扭曲形状
    final shapePaint =
        Paint()
          ..color = Colors.white.withValues(
            alpha: (0.06 + 0.15 * distortionLevel).clamp(0.0, 0.5),
          )
          ..style = PaintingStyle.fill;

    final shapeCount = (8 + 15 * distortionLevel).toInt();
    for (int i = 0; i < shapeCount; i++) {
      final centerX = random.nextDouble() * size.width;
      final centerY = random.nextDouble() * size.height;
      final baseSize = 15 + random.nextDouble() * 30 * distortionLevel;

      // 创建不规则多边形
      final path = Path();
      final sides = 3 + random.nextInt(5);
      for (int j = 0; j < sides; j++) {
        final angle = (j / sides) * 2 * math.pi;
        final radiusVariation = 0.7 + random.nextDouble() * 0.6;
        final radius = baseSize * radiusVariation;
        final x = centerX + math.cos(angle) * radius;
        final y = centerY + math.sin(angle) * radius;

        if (j == 0) {
          path.moveTo(x, y);
        } else {
          path.lineTo(x, y);
        }
      }
      path.close();
      canvas.drawPath(path, shapePaint);
    }

    // 新增：网格扭曲效果
    final gridPaint =
        Paint()
          ..color = Colors.white.withValues(
            alpha: (0.05 + 0.1 * distortionLevel).clamp(0.0, 0.4),
          )
          ..style = PaintingStyle.stroke
          ..strokeWidth = 0.5 + distortionLevel;

    if (distortionLevel > 0.3) {
      final gridSpacing = 40 - 20 * distortionLevel;
      for (double x = 0; x < size.width; x += gridSpacing) {
        final path = Path();
        for (double y = 0; y < size.height; y += 5) {
          final distortedX = x + math.sin(y * 0.02) * 10 * distortionLevel;
          if (y == 0) {
            path.moveTo(distortedX, y);
          } else {
            path.lineTo(distortedX, y);
          }
        }
        canvas.drawPath(path, gridPaint);
      }
    }
  }

  @override
  bool shouldRepaint(DistortionPainter oldDelegate) {
    return oldDelegate.distortionLevel != distortionLevel;
  }
}
