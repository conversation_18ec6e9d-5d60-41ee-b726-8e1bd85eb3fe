import 'package:flutter/material.dart';
import '../../generated/l10n/app_localizations.dart';
import '../models/traffic_guide_models.dart';
import '../services/traffic_guide_service.dart';

class ProjectEditorScreen extends StatefulWidget {
  final TrafficGuideProject project;

  const ProjectEditorScreen({super.key, required this.project});

  @override
  State<ProjectEditorScreen> createState() => _ProjectEditorScreenState();
}

class _ProjectEditorScreenState extends State<ProjectEditorScreen> {
  final TrafficGuideService _service = TrafficGuideService();
  final _formKey = GlobalKey<FormState>();

  late TrafficGuideProject _project;
  bool _isSaving = false;

  @override
  void initState() {
    super.initState();
    _project = widget.project;
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    return Scaffold(
      appBar: AppBar(
        title: Text(l10n.trafficGuideProjectEditor),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
        actions: [
          TextButton(
            onPressed: _isSaving ? null : _saveProject,
            child:
                _isSaving
                    ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        color: Colors.white,
                      ),
                    )
                    : Text(l10n.save),
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // 基本信息
              _buildBasicInfoSection(),
              const SizedBox(height: 24),

              // 图片配置
              _buildImageConfigSection(),
              const SizedBox(height: 24),

              // 文本转换配置
              _buildTextTransformSection(),
              const SizedBox(height: 24),

              // 水印配置
              _buildWatermarkSection(),
              const SizedBox(height: 24),

              // 保存按钮
              ElevatedButton.icon(
                onPressed: _isSaving ? null : _saveProject,
                icon:
                    _isSaving
                        ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                        : const Icon(Icons.save),
                label: Text(_isSaving ? l10n.trafficGuideSaving : l10n.trafficGuideSaveProject),
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  backgroundColor: Theme.of(context).primaryColor,
                  foregroundColor: Colors.white,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBasicInfoSection() {
    final l10n = AppLocalizations.of(context);
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              l10n.trafficGuideBasicInfo,
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),

            TextFormField(
              decoration: InputDecoration(
                labelText: l10n.trafficGuideProjectName,
                hintText: l10n.trafficGuideProjectNameHint,
                border: OutlineInputBorder(),
              ),
              initialValue: _project.name,
              onChanged: (value) {
                _project = _project.copyWith(name: value);
              },
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return l10n.trafficGuideProjectNameRequired;
                }
                return null;
              },
            ),
            const SizedBox(height: 16),

            TextFormField(
              decoration: InputDecoration(
                labelText: l10n.trafficGuideProjectDescription,
                hintText: l10n.trafficGuideProjectDescriptionHint,
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
              initialValue: _project.description,
              onChanged: (value) {
                _project = _project.copyWith(description: value);
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildImageConfigSection() {
    final l10n = AppLocalizations.of(context);
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              l10n.trafficGuideImageConfiguration,
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),

            TextFormField(
              decoration: InputDecoration(
                labelText: l10n.trafficGuideDefaultText,
                hintText: l10n.trafficGuideDefaultTextHint,
                border: OutlineInputBorder(),
              ),
              initialValue: _project.imageConfig.text,
              onChanged: (value) {
                _project = _project.copyWith(
                  imageConfig: _project.imageConfig.copyWith(text: value),
                );
              },
            ),
            const SizedBox(height: 16),

            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    decoration: InputDecoration(
                      labelText: l10n.trafficGuideFontSize,
                      border: OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.number,
                    initialValue: _project.imageConfig.fontSize.toString(),
                    onChanged: (value) {
                      final size = double.tryParse(value);
                      if (size != null) {
                        _project = _project.copyWith(
                          imageConfig: _project.imageConfig.copyWith(
                            fontSize: size,
                          ),
                        );
                      }
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: TextFormField(
                    decoration: InputDecoration(
                      labelText: l10n.trafficGuideFontFamily,
                      border: OutlineInputBorder(),
                    ),
                    initialValue: _project.imageConfig.fontFamily,
                    onChanged: (value) {
                      _project = _project.copyWith(
                        imageConfig: _project.imageConfig.copyWith(
                          fontFamily: value,
                        ),
                      );
                    },
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    decoration: InputDecoration(
                      labelText: l10n.trafficGuideBackgroundColor,
                      border: OutlineInputBorder(),
                    ),
                    initialValue: _project.imageConfig.backgroundColor,
                    onChanged: (value) {
                      _project = _project.copyWith(
                        imageConfig: _project.imageConfig.copyWith(
                          backgroundColor: value,
                        ),
                      );
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: TextFormField(
                    decoration: InputDecoration(
                      labelText: l10n.trafficGuideTextColor,
                      border: OutlineInputBorder(),
                    ),
                    initialValue: _project.imageConfig.textColor,
                    onChanged: (value) {
                      _project = _project.copyWith(
                        imageConfig: _project.imageConfig.copyWith(
                          textColor: value,
                        ),
                      );
                    },
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            _buildSlider(
              label: l10n.trafficGuideInterferenceLevel,
              value: _project.imageConfig.noiseLevel,
              onChanged: (value) {
                _project = _project.copyWith(
                  imageConfig: _project.imageConfig.copyWith(noiseLevel: value),
                );
              },
            ),

            _buildSlider(
              label: l10n.trafficGuideDistortionLevel,
              value: _project.imageConfig.distortionLevel,
              onChanged: (value) {
                _project = _project.copyWith(
                  imageConfig: _project.imageConfig.copyWith(
                    distortionLevel: value,
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTextTransformSection() {
    final l10n = AppLocalizations.of(context);
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              l10n.trafficGuideTextTransformConfig,
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),

            _buildSwitchTile(
              title: l10n.trafficGuideEmojiConversion,
              subtitle: l10n.trafficGuideEmojiConversionSubtitle,
              value: _project.textConfig.enableEmojiConversion,
              onChanged: (value) {
                _project = _project.copyWith(
                  textConfig: _project.textConfig.copyWith(
                    enableEmojiConversion: value,
                  ),
                );
              },
            ),

            _buildSwitchTile(
              title: l10n.trafficGuideUnicodeVariation,
              subtitle: l10n.trafficGuideUnicodeVariationSubtitle,
              value: _project.textConfig.enableUnicodeVariation,
              onChanged: (value) {
                _project = _project.copyWith(
                  textConfig: _project.textConfig.copyWith(
                    enableUnicodeVariation: value,
                  ),
                );
              },
            ),

            _buildSwitchTile(
              title: l10n.trafficGuideInvisibleChars,
              subtitle: l10n.trafficGuideInvisibleCharsSubtitle,
              value: _project.textConfig.enableInvisibleChars,
              onChanged: (value) {
                _project = _project.copyWith(
                  textConfig: _project.textConfig.copyWith(
                    enableInvisibleChars: value,
                  ),
                );
              },
            ),

            _buildSwitchTile(
              title: l10n.trafficGuideSensitiveWordMasking,
              subtitle: l10n.trafficGuideSensitiveWordMaskingSubtitle,
              value: _project.textConfig.enableSensitiveWordMasking,
              onChanged: (value) {
                _project = _project.copyWith(
                  textConfig: _project.textConfig.copyWith(
                    enableSensitiveWordMasking: value,
                  ),
                );
              },
            ),

            const SizedBox(height: 16),

            TextFormField(
              decoration: InputDecoration(
                labelText: l10n.trafficGuideSensitiveWordsList,
                hintText: l10n.trafficGuideSensitiveWordsListHint,
                border: OutlineInputBorder(),
              ),
              initialValue: _project.textConfig.sensitiveWords.join(', '),
              onChanged: (value) {
                final words =
                    value
                        .split(',')
                        .map((e) => e.trim())
                        .where((e) => e.isNotEmpty)
                        .toList();
                _project = _project.copyWith(
                  textConfig: _project.textConfig.copyWith(
                    sensitiveWords: words,
                  ),
                );
              },
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildWatermarkSection() {
    final l10n = AppLocalizations.of(context);
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              l10n.trafficGuideWatermarkSettings,
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),

            TextFormField(
              decoration: InputDecoration(
                labelText: l10n.trafficGuideWatermarkContent,
                hintText: l10n.trafficGuideWatermarkContentHint,
                border: OutlineInputBorder(),
              ),
              initialValue: _project.watermarkConfig.text,
              onChanged: (value) {
                _project = _project.copyWith(
                  watermarkConfig: _project.watermarkConfig.copyWith(
                    text: value,
                  ),
                );
              },
            ),
            const SizedBox(height: 16),

            Row(
              children: [
                Checkbox(
                  value: _project.watermarkConfig.invisible,
                  onChanged: (value) {
                    _project = _project.copyWith(
                      watermarkConfig: _project.watermarkConfig.copyWith(
                        invisible: value ?? false,
                      ),
                    );
                    setState(() {});
                  },
                ),
                Text(l10n.trafficGuideInvisibleWatermark),
              ],
            ),

            if (!_project.watermarkConfig.invisible) ...[
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: TextFormField(
                      decoration: InputDecoration(
                        labelText: l10n.trafficGuideOpacity,
                        border: OutlineInputBorder(),
                      ),
                      keyboardType: TextInputType.number,
                      initialValue:
                          (_project.watermarkConfig.opacity * 100).toString(),
                      onChanged: (value) {
                        final opacity = double.tryParse(value);
                        if (opacity != null) {
                          _project = _project.copyWith(
                            watermarkConfig: _project.watermarkConfig.copyWith(
                              opacity: opacity / 100,
                            ),
                          );
                        }
                      },
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: TextFormField(
                      decoration: InputDecoration(
                        labelText: l10n.trafficGuideFontSize,
                        border: OutlineInputBorder(),
                      ),
                      keyboardType: TextInputType.number,
                      initialValue:
                          _project.watermarkConfig.fontSize.toString(),
                      onChanged: (value) {
                        final size = double.tryParse(value);
                        if (size != null) {
                          _project = _project.copyWith(
                            watermarkConfig: _project.watermarkConfig.copyWith(
                              fontSize: size,
                            ),
                          );
                        }
                      },
                    ),
                  ),
                ],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildSwitchTile({
    required String title,
    required String subtitle,
    required bool value,
    required Function(bool) onChanged,
  }) {
    return SwitchListTile(
      title: Text(title),
      subtitle: Text(subtitle),
      value: value,
      onChanged: onChanged,
      contentPadding: EdgeInsets.zero,
    );
  }

  Widget _buildSlider({
    required String label,
    required double value,
    required Function(double) onChanged,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [Text(label), Text('${(value * 100).toInt()}%')],
        ),
        Slider(
          value: value,
          min: 0.0,
          max: 1.0,
          divisions: 10,
          onChanged: onChanged,
        ),
      ],
    );
  }

  Future<void> _saveProject() async {
    final l10n = AppLocalizations.of(context);
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isSaving = true);

    try {
      final updatedProject = _project.copyWith(updatedAt: DateTime.now());
      await _service.saveProject(updatedProject);

      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text(l10n.trafficGuideProjectSaved)));
        Navigator.pop(context);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text(l10n.trafficGuideSaveFailed(e.toString()))));
      }
    } finally {
      if (mounted) {
        setState(() => _isSaving = false);
      }
    }
  }
}
