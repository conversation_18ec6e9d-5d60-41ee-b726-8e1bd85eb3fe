import 'package:flutter/material.dart';
import 'package:flutter/services.dart';

import '../../generated/l10n/app_localizations.dart';
import '../models/traffic_guide_models.dart';
import '../services/traffic_guide_service.dart';

class ImageGeneratorScreen extends StatefulWidget {
  const ImageGeneratorScreen({super.key});

  @override
  State<ImageGeneratorScreen> createState() => _ImageGeneratorScreenState();
}

class _ImageGeneratorScreenState extends State<ImageGeneratorScreen> {
  final TrafficGuideService _service = TrafficGuideService();
  final _formKey = GlobalKey<FormState>();

  late TrafficImageConfig _config;
  Uint8List? _generatedImage;
  bool _isGenerating = false;

  @override
  void initState() {
    super.initState();
    _config = TrafficImageConfig(text: '');
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    return Scaffold(
      appBar: AppBar(
        title: Text(l10n.trafficGuideImageGeneratorTitle),
        backgroundColor: Theme.of(context).primaryColor,
        foregroundColor: Colors.white,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Form(
          key: _formKey,
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // 配置表单
              _buildConfigForm(),
              const SizedBox(height: 24),

              // 生成按钮
              ElevatedButton.icon(
                onPressed: _isGenerating ? null : _generateImage,
                icon:
                    _isGenerating
                        ? const SizedBox(
                          width: 20,
                          height: 20,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                        : const Icon(Icons.image),
                label: Text(_isGenerating ? l10n.trafficGuideGenerating : l10n.trafficGuideGenerateImage),
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  backgroundColor: Theme.of(context).primaryColor,
                  foregroundColor: Colors.white,
                ),
              ),
              const SizedBox(height: 24),

              // 预览区域
              if (_generatedImage != null) _buildPreviewSection(),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildConfigForm() {
    final l10n = AppLocalizations.of(context);
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              l10n.trafficGuideImageConfiguration,
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),

            // 文本输入
            TextFormField(
              decoration: InputDecoration(
                labelText: l10n.trafficGuideTextContent,
                hintText: l10n.trafficGuideTextHint,
                border: OutlineInputBorder(),
              ),
              maxLines: 3,
              onChanged: (value) => _config = _config.copyWith(text: value),
              validator: (value) {
                if (value == null || value.isEmpty) {
                  return l10n.trafficGuideTextRequired;
                }
                return null;
              },
            ),
            const SizedBox(height: 16),

            // 字体大小
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    decoration: InputDecoration(
                      labelText: l10n.trafficGuideFontSize,
                      border: OutlineInputBorder(),
                    ),
                    keyboardType: TextInputType.number,
                    initialValue: _config.fontSize.toString(),
                    onChanged: (value) {
                      final size = double.tryParse(value);
                      if (size != null) {
                        _config = _config.copyWith(fontSize: size);
                      }
                    },
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: TextFormField(
                    decoration: InputDecoration(
                      labelText: l10n.trafficGuideFontFamily,
                      border: OutlineInputBorder(),
                    ),
                    initialValue: _config.fontFamily,
                    onChanged:
                        (value) =>
                            _config = _config.copyWith(fontFamily: value),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // 颜色选择
            Row(
              children: [
                Expanded(
                  child: _buildColorPicker(
                    label: l10n.trafficGuideBackgroundColor,
                    initialColor: _config.backgroundColor,
                    onChanged:
                        (color) =>
                            _config = _config.copyWith(backgroundColor: color),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildColorPicker(
                    label: l10n.trafficGuideTextColor,
                    initialColor: _config.textColor,
                    onChanged:
                        (color) => _config = _config.copyWith(textColor: color),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // 干扰设置
            Text(
              l10n.trafficGuideInterferenceSettings,
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),

            _buildSlider(
              label: l10n.trafficGuideInterferenceLevel,
              value: _config.noiseLevel,
              onChanged: (value) {
                setState(() {
                  _config = _config.copyWith(noiseLevel: value);
                });
              },
            ),

            _buildSlider(
              label: l10n.trafficGuideDistortionLevel,
              value: _config.distortionLevel,
              onChanged: (value) {
                setState(() {
                  _config = _config.copyWith(distortionLevel: value);
                });
              },
            ),
            const SizedBox(height: 16),

            // 水印设置
            Text(
              l10n.trafficGuideWatermarkSettings,
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                Checkbox(
                  value: _config.addWatermark,
                  onChanged:
                      (value) =>
                          _config = _config.copyWith(
                            addWatermark: value ?? false,
                          ),
                ),
                Text(l10n.trafficGuideAddWatermark),
              ],
            ),
            if (_config.addWatermark) ...[
              const SizedBox(height: 8),
              TextFormField(
                decoration: InputDecoration(
                  labelText: l10n.trafficGuideWatermarkContent,
                  hintText: l10n.trafficGuideWatermarkContentHint,
                  border: OutlineInputBorder(),
                ),
                onChanged:
                    (value) => _config = _config.copyWith(watermarkText: value),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildColorPicker({
    required String label,
    required String initialColor,
    required Function(String) onChanged,
  }) {
    final l10n = AppLocalizations.of(context);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(label),
        const SizedBox(height: 8),
        GestureDetector(
          onTap: () => _showColorPicker(initialColor, onChanged),
          child: Container(
            height: 50,
            decoration: BoxDecoration(
              color: _parseColor(initialColor),
              border: Border.all(color: Colors.grey),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Center(
              child: Text(
                l10n.trafficGuideSelectColor,
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildSlider({
    required String label,
    required double value,
    required Function(double) onChanged,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [Text(label), Text('${(value * 100).toInt()}%')],
        ),
        Slider(
          value: value,
          min: 0.0,
          max: 1.0,
          divisions: 10,
          onChanged: onChanged,
        ),
      ],
    );
  }

  Widget _buildPreviewSection() {
    final l10n = AppLocalizations.of(context);
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  l10n.trafficGuidePreview,
                  style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                Row(
                  children: [
                    IconButton(
                      onPressed: _saveImage,
                      icon: const Icon(Icons.save),
                      tooltip: l10n.trafficGuideSaveToAlbum,
                    ),
                    IconButton(
                      onPressed: _shareImage,
                      icon: const Icon(Icons.share),
                      tooltip: l10n.trafficGuideShare,
                    ),
                  ],
                ),
              ],
            ),
            const SizedBox(height: 16),
            Center(
              child: Container(
                decoration: BoxDecoration(
                  border: Border.all(color: Colors.grey),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: Image.memory(
                    _generatedImage!,
                    width: 300,
                    height: 225,
                    fit: BoxFit.contain,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Color _parseColor(String colorString) {
    if (colorString.startsWith('#')) {
      final hex = colorString.substring(1);
      return Color(int.parse('FF$hex', radix: 16));
    }
    return Colors.black;
  }

  void _showColorPicker(String initialColor, Function(String) onChanged) {
    final l10n = AppLocalizations.of(context);
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(l10n.trafficGuideSelectColor),
            content: SingleChildScrollView(
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  _buildColorOption('#000000', l10n.trafficGuideBlack, onChanged),
                  _buildColorOption('#FFFFFF', l10n.trafficGuideWhite, onChanged),
                  _buildColorOption('#FF0000', l10n.trafficGuideRed, onChanged),
                  _buildColorOption('#00FF00', l10n.trafficGuideGreen, onChanged),
                  _buildColorOption('#0000FF', l10n.trafficGuideBlue, onChanged),
                  _buildColorOption('#FFFF00', l10n.trafficGuideYellow, onChanged),
                  _buildColorOption('#FF00FF', l10n.trafficGuidePurple, onChanged),
                  _buildColorOption('#00FFFF', l10n.trafficGuideCyan, onChanged),
                ],
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text(l10n.cancel),
              ),
            ],
          ),
    );
  }

  Widget _buildColorOption(
    String color,
    String name,
    Function(String) onChanged,
  ) {
    return ListTile(
      leading: Container(
        width: 30,
        height: 30,
        decoration: BoxDecoration(
          color: _parseColor(color),
          border: Border.all(color: Colors.grey),
          borderRadius: BorderRadius.circular(4),
        ),
      ),
      title: Text(name),
      onTap: () {
        onChanged(color);
        Navigator.pop(context);
      },
    );
  }

  Future<void> _generateImage() async {
    final l10n = AppLocalizations.of(context);
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isGenerating = true);

    try {
      final image = await _service.generateTrafficImage(_config);
      setState(() {
        _generatedImage = image;
        _isGenerating = false;
      });
    } catch (e) {
      setState(() => _isGenerating = false);
      ScaffoldMessenger.of(
        context,
      ).showSnackBar(SnackBar(content: Text(l10n.trafficGuideImageGenerationFailed(e.toString()))));
    }
  }

  Future<void> _saveImage() async {
    final l10n = AppLocalizations.of(context);
    if (_generatedImage == null) return;

    try {
      // 显示保存提示
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(l10n.trafficGuideLongPressToSave)),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(l10n.trafficGuideSaveFailed(e.toString()))),
      );
    }
  }

  void _shareImage() {
    final l10n = AppLocalizations.of(context);
    // 这里可以实现分享功能
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(SnackBar(content: Text(l10n.trafficGuideShareFeatureInProgress)));
  }
}
