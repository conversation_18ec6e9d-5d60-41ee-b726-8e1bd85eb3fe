import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:image_gallery_saver/image_gallery_saver.dart';

import '../../generated/l10n/app_localizations.dart';
import '../models/image_template.dart';
import '../models/traffic_guide_models.dart';
import '../services/advanced_image_service.dart';
import '../widgets/template_selector.dart';
import '../widgets/advanced_color_picker.dart';
import '../widgets/real_time_preview.dart';
// Removed export options since we export directly without a sheet
import '../../services/service_locator.dart';
import '../../subscription/subscription_screen.dart';

/// 引流图片生成器
class AdvancedImageGeneratorScreen extends StatefulWidget {
  const AdvancedImageGeneratorScreen({super.key});

  @override
  State<AdvancedImageGeneratorScreen> createState() =>
      _AdvancedImageGeneratorScreenState();
}

class _AdvancedImageGeneratorScreenState
    extends State<AdvancedImageGeneratorScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;

  final AdvancedImageService _imageService = AdvancedImageService();
  final TextEditingController _textController = TextEditingController();
  final TextEditingController _watermarkController = TextEditingController();

  // 状态变量
  ImageTemplate? _selectedTemplate;
  TrafficImageConfig _config = TrafficImageConfig(text: '');
  bool _isGenerating = false;

  @override
  void initState() {
    super.initState();

    _tabController = TabController(length: 3, vsync: this);

    // 设置默认模板
    _selectedTemplate = TemplatePresets.templates.first;
    _updateConfigFromTemplate();

    // 初始化时同步控制器状态
    _syncControllersWithConfig();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _textController.dispose();
    _watermarkController.dispose();
    super.dispose();
  }

  void _updateConfigFromTemplate() {
    if (_selectedTemplate != null) {
      setState(() {
        _config = _config.copyWith(
          fontSize: _selectedTemplate!.baseFontSize,
          fontFamily: _selectedTemplate!.recommendedFonts.first,
          backgroundColor:
              '#${_selectedTemplate!.primaryColors.first.toARGB32().toRadixString(16).substring(2)}',
          textColor:
              '#${_selectedTemplate!.secondaryColors.first.toARGB32().toRadixString(16).substring(2)}',
          noiseLevel: _selectedTemplate!.noiseLevel,
          distortionLevel: _selectedTemplate!.distortionLevel,
        );
        _syncControllersWithConfig();
      });
    }
  }

  /// 同步控制器与配置对象的状态
  void _syncControllersWithConfig() {
    // 同步主文本控制器
    if (_textController.text != _config.text) {
      _textController.text = _config.text;
    }

    // 同步水印文本控制器
    if (_watermarkController.text != _config.watermarkText) {
      _watermarkController.text = _config.watermarkText;
    }
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    return Scaffold(
      backgroundColor: theme.colorScheme.surface,
      body: Column(
        children: [_buildAppBar(), Expanded(child: _buildMobileLayout())],
      ),
    );
  }

  Widget _buildMobileLayout() {
    final size = MediaQuery.of(context).size;
    final isCompact = size.height < 700;
    final previewHeight = isCompact ? 220.0 : 300.0;

    return Column(
      children: [
        // 预览区域 - 放在顶部
        Container(
          height: previewHeight,
          margin: const EdgeInsets.all(16),
          child: _buildPreviewPanel(),
        ),

        // 配置面板 - 放在底部，可滚动
        Expanded(
          child: Container(
            margin: const EdgeInsets.fromLTRB(16, 0, 16, 16),
            child: _buildConfigPanel(),
          ),
        ),
      ],
    );
  }

  Widget _buildAppBar() {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    final statusBarHeight = MediaQuery.of(context).padding.top;
    return Container(
      padding: EdgeInsets.only(
        left: 16,
        right: 16,
        top: 12 + statusBarHeight,
        bottom: 12,
      ),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: isDark
              ? [
                  theme.colorScheme.surface,
                  theme.colorScheme.surfaceContainerLow,
                ]
              : [
                  theme.colorScheme.surface,
                  theme.colorScheme.surfaceContainerHighest,
                ],
        ),
        border: Border(
          bottom: BorderSide(
            color: theme.colorScheme.outlineVariant.withValues(alpha: isDark ? 0.3 : 0.5),
            width: 1,
          ),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: isDark ? 0.2 : 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          IconButton(
            onPressed: () => Navigator.pop(context),
            icon: Icon(
              Icons.arrow_back_ios,
              color: theme.colorScheme.onSurface,
            ),
            style: IconButton.styleFrom(
              backgroundColor: theme.colorScheme.surfaceContainerHigh,
              foregroundColor: theme.colorScheme.onSurface,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            tooltip: AppLocalizations.of(context).back,
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  AppLocalizations.of(context).trafficGuideImageGenerator,
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                    color: theme.colorScheme.onSurface,
                  ),
                ),
                Text(
                  AppLocalizations.of(
                    context,
                  ).trafficGuideImageGeneratorSubtitle,
                  style: TextStyle(
                    fontSize: 12,
                    color: theme.colorScheme.onSurfaceVariant,
                    letterSpacing: 0.2,
                  ),
                ),
              ],
            ),
          ),
          _buildMobileActionButtons(),
        ],
      ),
    );
  }

  Widget _buildMobileActionButtons() {
    final theme = Theme.of(context);
    return ElevatedButton(
      onPressed:
          _config.text.isNotEmpty && !_isGenerating ? _directExport : null,
      style: ElevatedButton.styleFrom(
        padding: const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
        backgroundColor: theme.colorScheme.primary,
        foregroundColor: theme.colorScheme.onPrimary,
        shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
        minimumSize: const Size(80, 40),
        elevation: 0,
      ),
      child:
          _isGenerating
              ? const SizedBox(
                width: 16,
                height: 16,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              )
              : Text(
                AppLocalizations.of(context).trafficGuideExport,
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                ),
              ),
    );
  }

  Widget _buildConfigPanel() {
    final theme = Theme.of(context);
    return Container(
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color:
                theme.brightness == Brightness.dark
                    ? Colors.black.withValues(alpha: 0.25)
                    : Colors.black.withValues(alpha: 0.05),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: Column(
        children: [
          // 标签栏
          Container(
            decoration: BoxDecoration(
              color: theme.colorScheme.surfaceContainerHigh,
              borderRadius: const BorderRadius.vertical(
                top: Radius.circular(16),
              ),
            ),
            child: TabBar(
              controller: _tabController,
              tabs: [
                Tab(
                  text: AppLocalizations.of(context).trafficGuideTabText,
                  icon: Icon(
                    Icons.text_fields,
                    size: 18,
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                ),
                Tab(
                  text: AppLocalizations.of(context).trafficGuideTabTemplate,
                  icon: Icon(
                    Icons.palette,
                    size: 18,
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                ),
                Tab(
                  text: AppLocalizations.of(context).trafficGuideTabEffects,
                  icon: Icon(
                    Icons.auto_fix_high,
                    size: 18,
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                ),
              ],
              isScrollable: false,
              labelColor: theme.colorScheme.onPrimary,
              unselectedLabelColor: theme.colorScheme.onSurfaceVariant,
              indicator: BoxDecoration(
                color: theme.colorScheme.primary,
                borderRadius: BorderRadius.circular(10),
              ),
              indicatorSize: TabBarIndicatorSize.tab,
              labelStyle: const TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w600,
              ),
              unselectedLabelStyle: const TextStyle(fontSize: 12),
            ),
          ),

          // 内容区域
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildTextTab(),
                _buildTemplateTab(),
                _buildEffectsTab(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTemplateTab() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: TemplateSelector(
        templates: TemplatePresets.templates,
        selectedTemplate: _selectedTemplate,
        onTemplateSelected: (template) {
          setState(() {
            _selectedTemplate = template;
            _updateConfigFromTemplate();
          });
        },
      ),
    );
  }

  Widget _buildTextTab() {
    final theme = Theme.of(context);
    return Padding(
      padding: const EdgeInsets.all(16),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              AppLocalizations.of(context).trafficGuideTextContent,
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: theme.colorScheme.onSurface,
              ),
            ),
            const SizedBox(height: 16),

            // 文字输入框
            Container(
              decoration: BoxDecoration(
                color: theme.colorScheme.surfaceContainerLow,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: theme.colorScheme.outlineVariant),
              ),
              child: TextField(
                controller: _textController,
                maxLines: 4,
                decoration: InputDecoration(
                  hintText: AppLocalizations.of(context).trafficGuideTextHint,
                  border: InputBorder.none,
                  contentPadding: const EdgeInsets.all(16),
                  hintStyle: TextStyle(
                    color: theme.colorScheme.onSurfaceVariant,
                  ),
                ),
                style: TextStyle(
                  fontSize: 16,
                  color: theme.colorScheme.onSurface,
                ),
                onChanged: (value) {
                  setState(() {
                    _config = _config.copyWith(text: value);
                  });
                },
              ),
            ),

            const SizedBox(height: 24),

            // 字体设置
            _buildFontSettings(),

            const SizedBox(height: 24),

            // 颜色设置
            _buildColorSettings(),
          ],
        ),
      ),
    );
  }

  Widget _buildEffectsTab() {
    final theme = Theme.of(context);
    return Padding(
      padding: const EdgeInsets.all(16),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              AppLocalizations.of(context).trafficGuideVisualEffects,
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
                color: theme.colorScheme.onSurface,
              ),
            ),
            const SizedBox(height: 16),

            _buildEffectSlider(
              AppLocalizations.of(context).trafficGuideNoiseLevel,
              _config.noiseLevel,
              (value) =>
                  setState(() => _config = _config.copyWith(noiseLevel: value)),
            ),

            _buildEffectSlider(
              AppLocalizations.of(context).trafficGuideDistortionLevel,
              _config.distortionLevel,
              (value) => setState(
                () => _config = _config.copyWith(distortionLevel: value),
              ),
            ),

            const SizedBox(height: 24),

            // 水印设置
            _buildWatermarkSettings(),
          ],
        ),
      ),
    );
  }

  Widget _buildFontSettings() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          AppLocalizations.of(context).trafficGuideFontSettings,
          style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
        ),
        const SizedBox(height: 12),

        Row(
          children: [
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(AppLocalizations.of(context).trafficGuideFontSize),
                  const SizedBox(height: 8),
                  SliderTheme(
                    data: SliderTheme.of(context).copyWith(
                      activeTrackColor: const Color(0xFF667EEA),
                      thumbColor: const Color(0xFF667EEA),
                      overlayColor: const Color(
                        0xFF667EEA,
                      ).withValues(alpha: 0.2),
                    ),
                    child: Slider(
                      value: _config.fontSize,
                      min: 20,
                      max: 80,
                      divisions: 12,
                      label: _config.fontSize.toInt().toString(),
                      onChanged: (value) {
                        setState(() {
                          _config = _config.copyWith(fontSize: value);
                        });
                      },
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),

        const SizedBox(height: 12),
        _buildFontFamilyDropdown(),
      ],
    );
  }

  Widget _buildColorSettings() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          AppLocalizations.of(context).trafficGuideColorSettings,
          style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
        ),
        const SizedBox(height: 12),

        Row(
          children: [
            Expanded(
              child: GestureDetector(
                onTap: () => _showColorPickerDialog(true),
                child: Container(
                  height: 50,
                  decoration: BoxDecoration(
                    color: _parseColor(_config.textColor),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: Colors.grey.withValues(alpha: 0.3),
                    ),
                  ),
                  child: Center(
                    child: Text(
                      AppLocalizations.of(context).trafficGuideTextColor,
                      style: TextStyle(
                        color: _contrastTextColor(
                          _parseColor(_config.textColor),
                        ),
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: GestureDetector(
                onTap: () => _showColorPickerDialog(false),
                child: Container(
                  height: 50,
                  decoration: BoxDecoration(
                    color: _parseColor(_config.backgroundColor),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: Colors.grey.withValues(alpha: 0.3),
                    ),
                  ),
                  child: Center(
                    child: Text(
                      AppLocalizations.of(context).trafficGuideBackgroundColor,
                      style: TextStyle(
                        color: _contrastTextColor(
                          _parseColor(_config.backgroundColor),
                        ),
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),

        const SizedBox(height: 8),
        Row(
          children: [
            Expanded(
              child: Text(
                _config.textColor,
                style: TextStyle(fontSize: 12, color: Colors.grey[600]),
              ),
            ),
            Expanded(
              child: Text(
                _config.backgroundColor,
                textAlign: TextAlign.right,
                style: TextStyle(fontSize: 12, color: Colors.grey[600]),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildEffectSlider(
    String label,
    double value,
    Function(double) onChanged,
  ) {
    final theme = Theme.of(context);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              label,
              style: TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.w500,
                color: theme.colorScheme.onSurface,
              ),
            ),
            Text(
              '${(value * 100).toInt()}%',
              style: TextStyle(
                fontSize: 14,
                color: theme.colorScheme.onSurfaceVariant,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        SliderTheme(
          data: SliderTheme.of(context).copyWith(
            activeTrackColor: theme.colorScheme.primary,
            thumbColor: theme.colorScheme.primary,
            overlayColor: theme.colorScheme.primary.withValues(alpha: 0.2),
          ),
          child: Slider(value: value, onChanged: onChanged),
        ),
        const SizedBox(height: 16),
      ],
    );
  }

  Widget _buildWatermarkSettings() {
    final theme = Theme.of(context);
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Checkbox(
              value: _config.addWatermark,
              onChanged: (value) {
                setState(() {
                  _config = _config.copyWith(addWatermark: value ?? false);
                  // 如果禁用水印，清空水印文本
                  if (!(value ?? false)) {
                    _watermarkController.clear();
                    _config = _config.copyWith(watermarkText: '');
                  }
                });
              },
            ),
            Text(
              AppLocalizations.of(context).trafficGuideAddWatermark,
              style: TextStyle(color: theme.colorScheme.onSurface),
            ),
          ],
        ),

        if (_config.addWatermark) ...[
          const SizedBox(height: 12),
          TextField(
            controller: _watermarkController,
            decoration: InputDecoration(
              labelText: AppLocalizations.of(context).trafficGuideWatermarkText,
              border: const OutlineInputBorder(),
              hintText: AppLocalizations.of(context).trafficGuideWatermarkHint,
              hintStyle: TextStyle(color: theme.colorScheme.onSurfaceVariant),
            ),
            style: TextStyle(color: theme.colorScheme.onSurface),
            onChanged: (value) {
              setState(() {
                _config = _config.copyWith(watermarkText: value);
              });
            },
          ),
        ],
      ],
    );
  }

  Widget _buildPreviewPanel() {
    final theme = Theme.of(context);
    return Container(
      decoration: BoxDecoration(
        color: theme.colorScheme.surface,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color:
                theme.brightness == Brightness.dark
                    ? Colors.black.withValues(alpha: 0.25)
                    : Colors.black.withValues(alpha: 0.06),
            blurRadius: 20,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      padding: const EdgeInsets.all(12),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(12),
        child: RealTimePreview(
          config: _config,
          template: _selectedTemplate,
          isGenerating: _isGenerating,
        ),
      ),
    );
  }

  void _showColorPickerDialog(bool isTextColor) {
    showDialog(
      context: context,
      builder:
          (context) => Dialog(
            child: SizedBox(
              width: 400,
              child: AdvancedColorPicker(
                initialColor: _parseColor(
                  isTextColor ? _config.textColor : _config.backgroundColor,
                ),
                onColorChanged: (color) {
                  setState(() {
                    if (isTextColor) {
                      _config = _config.copyWith(
                        textColor:
                            '#${color.toARGB32().toRadixString(16).substring(2)}',
                      );
                    } else {
                      _config = _config.copyWith(
                        backgroundColor:
                            '#${color.toARGB32().toRadixString(16).substring(2)}',
                      );
                    }
                  });
                },
                presetColors: _selectedTemplate?.primaryColors ?? [],
              ),
            ),
          ),
    );
  }

  // 直接导出保存到相册（不弹出导出选项）
  void _directExport() async {
    // 订阅校验：仅付费用户可导出/保存到相册
    final subscriptionService = ServiceLocator().subscriptionService;
    final canUse = subscriptionService.canAccessFeature(
      'advanced_image_export',
    );
    if (!canUse) {
      if (mounted) {
        Navigator.of(context).push(
          MaterialPageRoute(builder: (context) => const SubscriptionScreen()),
        );
      }
      return;
    }

    if (_selectedTemplate == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            AppLocalizations.of(context).trafficGuideSelectTemplateFirst,
          ),
        ),
      );
      return;
    }

    setState(() => _isGenerating = true);

    try {
      final imageBytes = await _imageService.generateImageWithTemplate(
        _config,
        _selectedTemplate!,
      );

      await _saveToGallery(imageBytes);

      setState(() {
        _isGenerating = false;
      });

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            AppLocalizations.of(context).trafficGuideImageSavedSuccess,
          ),
          backgroundColor: Colors.green,
        ),
      );
    } catch (e) {
      setState(() => _isGenerating = false);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            AppLocalizations.of(context).trafficGuideSaveFailed('$e'),
          ),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  // 旧的带弹窗导出逻辑已移除

  Future<void> _saveToGallery(Uint8List imageBytes) async {
    // 请求权限
    bool hasPermission = false;

    if (Theme.of(context).platform == TargetPlatform.iOS) {
      // iOS 使用 photos 权限
      final permission = await Permission.photos.request();
      hasPermission = permission.isGranted;

      if (!hasPermission && permission.isPermanentlyDenied) {
        throw Exception(
          AppLocalizations.of(context).trafficGuidePermissionPermanentlyDenied,
        );
      }
    } else {
      // Android 尝试多种权限
      var photosPermission = await Permission.photos.request();
      if (photosPermission.isGranted) {
        hasPermission = true;
      } else {
        var storagePermission = await Permission.storage.request();
        hasPermission = storagePermission.isGranted;
      }
    }

    if (!hasPermission) {
      throw Exception(
        AppLocalizations.of(context).trafficGuidePermissionRequired,
      );
    }

    // 保存到相册
    final result = await ImageGallerySaver.saveImage(
      imageBytes,
      name: 'ContentPal_TrafficImage_${DateTime.now().millisecondsSinceEpoch}',
      quality: 100,
      isReturnImagePathOfIOS: true, // iOS返回图片路径
    );

    // 检查保存结果
    if (result != null) {
      bool isSuccess = false;
      if (result is Map) {
        isSuccess = result['isSuccess'] == true;
      } else if (result is String && result.isNotEmpty) {
        // iOS可能直接返回路径字符串
        isSuccess = true;
      }

      if (!isSuccess) {
        final errorMsg =
            result is Map ? (result['errorMessage'] ?? '未知错误') : '保存失败';
        throw Exception(
          AppLocalizations.of(
            context,
          ).trafficGuideSaveFailedWithMessage(errorMsg),
        );
      }
    } else {
      throw Exception(
        AppLocalizations.of(context).trafficGuideSaveFailedEmptyResult,
      );
    }
  }

  Color _contrastTextColor(Color background) {
    // 使用感知亮度选择黑或白的对比文字颜色
    return background.computeLuminance() > 0.5
        ? const Color(0xFF2C3E50)
        : Colors.white;
  }

  Widget _buildFontFamilyDropdown() {
    final fonts = _selectedTemplate?.recommendedFonts ?? <String>[];
    final current =
        fonts.contains(_config.fontFamily)
            ? _config.fontFamily
            : (fonts.isNotEmpty ? fonts.first : null);

    return DropdownButtonFormField<String>(
      value: current,
      decoration: InputDecoration(
        labelText: AppLocalizations.of(context).trafficGuideFontFamily,
        border: OutlineInputBorder(borderRadius: BorderRadius.circular(8)),
        contentPadding: const EdgeInsets.symmetric(
          horizontal: 12,
          vertical: 10,
        ),
      ),
      items:
          fonts
              .map(
                (f) => DropdownMenuItem<String>(
                  value: f,
                  child: Text(f, overflow: TextOverflow.ellipsis),
                ),
              )
              .toList(),
      onChanged: (value) {
        if (value == null) return;
        setState(() {
          _config = _config.copyWith(fontFamily: value);
        });
      },
    );
  }

  Color _parseColor(String colorString) {
    if (colorString.startsWith('#')) {
      final hex = colorString.substring(1);
      return Color(int.parse('FF$hex', radix: 16));
    }
    return Colors.black;
  }
}
