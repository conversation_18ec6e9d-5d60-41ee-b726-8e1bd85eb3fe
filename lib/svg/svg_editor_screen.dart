import 'dart:io';

import 'package:file_picker/file_picker.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../../services/service_locator.dart';
import '../config/app_theme.dart';
import '../generated/l10n/app_localizations.dart';
import 'svg_document.dart';

/// SVG编辑器屏幕
class SvgEditorScreen extends StatefulWidget {
  /// 初始SVG内容
  final String? initialSvgContent;

  /// 初始标题
  final String? initialTitle;

  /// 文档ID（如果是编辑现有文档）
  final String? documentId;

  /// 构造函数
  const SvgEditorScreen({
    super.key,
    this.initialSvgContent,
    this.initialTitle,
    this.documentId,
  });

  @override
  State<SvgEditorScreen> createState() => _SvgEditorScreenState();
}

class _SvgEditorScreenState extends State<SvgEditorScreen>
    with SingleTickerProviderStateMixin {
  /// SVG服务
  final _svgService = ServiceLocator().svgService;

  /// 编辑控制器
  late TextEditingController _svgController;

  /// 标题控制器
  late TextEditingController _titleController;

  /// 当前文档
  SvgDocument? _document;

  /// 是否正在处理
  bool _isProcessing = false;

  /// 是否有错误
  bool _hasError = false;

  /// 错误消息
  String _errorMessage = '';

  /// 是否已修改
  bool _isDirty = false;

  /// Tab控制器
  late TabController _tabController;

  /// 本地化
  late AppLocalizations _l10n;

  @override
  void initState() {
    super.initState();

    _svgController = TextEditingController(
      text: widget.initialSvgContent ?? '',
    );
    _titleController = TextEditingController(
      text: widget.initialTitle ?? 'Untitled',
    );

    _svgController.addListener(_onSvgChanged);

    // 初始化Tab控制器
    _tabController = TabController(length: 2, vsync: this);

    // 如果有文档ID，加载文档
    if (widget.documentId != null) {
      _loadDocument(widget.documentId!);
    } else if (widget.initialSvgContent != null) {
      // 如果有初始内容，创建新文档
      _createNewDocument();
    }
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    // 初始化本地化
    _l10n = AppLocalizations.of(context);
    // Update title controller with localized text if needed
    if (_titleController.text == 'Untitled') {
      _titleController.text = _l10n.svgUntitled;
    }
  }

  @override
  void dispose() {
    _svgController.removeListener(_onSvgChanged);
    _svgController.dispose();
    _titleController.dispose();
    _tabController.dispose();
    super.dispose();
  }

  /// 加载文档
  Future<void> _loadDocument(String id) async {
    setState(() {
      _isProcessing = true;
    });

    try {
      final document = _svgService.getDocument(id);

      if (document != null) {
        setState(() {
          _document = document;
          _svgController.text = document.content;
          _titleController.text = document.title;
          _isProcessing = false;
          _hasError = false;
        });
      } else {
        setState(() {
          _isProcessing = false;
          _hasError = true;
          _errorMessage = _l10n.svgDocumentNotFound;
        });
      }
    } catch (e) {
      setState(() {
        _isProcessing = false;
        _hasError = true;
        _errorMessage = _l10n.svgLoadDocumentFailed(e.toString());
      });
    }
  }

  /// 创建新文档
  Future<void> _createNewDocument() async {
    setState(() {
      _isProcessing = true;
    });

    try {
      final document = await _svgService.createFromString(
        _svgController.text,
        title: _titleController.text,
      );

      setState(() {
        _document = document;
        _isProcessing = false;
        _hasError = false;
        _isDirty = false;
      });
    } catch (e) {
      setState(() {
        _isProcessing = false;
        _hasError = true;
        _errorMessage = _l10n.svgCreateDocumentFailed(e.toString());
      });
    }
  }

  /// 保存文档
  Future<void> _saveDocument() async {
    // 如果是新文档或需要重命名，显示输入对话框
    if (_document == null || _document!.title == _l10n.svgUntitled) {
      final newTitle = await _showTitleInputDialog();
      if (newTitle == null) {
        // 用户取消了输入
        return;
      }
      _titleController.text = newTitle;
    }

    if (_document == null) {
      await _createNewDocument();
      return;
    }

    setState(() {
      _isProcessing = true;
    });

    try {
      _document!.updateContent(_svgController.text);
      _document!.updateTitle(_titleController.text);

      await _svgService.updateDocument(_document!);

      setState(() {
        _isProcessing = false;
        _hasError = false;
        _isDirty = false;
      });

      _showSnackBar(_l10n.svgSaveSuccess);
    } catch (e) {
      setState(() {
        _isProcessing = false;
        _hasError = true;
        _errorMessage = _l10n.svgSaveDocumentFailed(e.toString());
      });
    }
  }

  /// 显示标题输入对话框
  Future<String?> _showTitleInputDialog() async {
    return showDialog<String>(
      context: context,
      builder: (context) {
        final textController = TextEditingController(
          text: _titleController.text,
        );
        return AlertDialog(
          title: Text(_l10n.svgEnterFileName),
          content: TextField(
            controller: textController,
            decoration: InputDecoration(
              labelText: _l10n.svgFileName,
              hintText: _l10n.svgFileNameHint,
            ),
            autofocus: true,
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text(_l10n.cancel),
            ),
            TextButton(
              onPressed: () {
                if (textController.text.trim().isNotEmpty) {
                  Navigator.pop(context, textController.text.trim());
                } else {
                  _showSnackBar(_l10n.svgFileNameRequired);
                }
              },
              child: Text(_l10n.ok),
            ),
          ],
        );
      },
    );
  }

  /// 导入SVG文件
  Future<void> _importSvgFile() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['svg'],
      );

      if (result != null && result.files.isNotEmpty) {
        final file = File(result.files.first.path!);
        final document = await SvgDocument.fromFile(file);

        setState(() {
          _svgController.text = document.content;
          _titleController.text = document.title;
          _hasError = false;
          _isDirty = true;
        });
      }
    } catch (e) {
      setState(() {
        _hasError = true;
        _errorMessage = _l10n.svgImportFailed(e.toString());
      });
    }
  }

  /// 导出为PNG
  Future<void> _exportAsPng() async {
    if (_document == null) {
      await _saveDocument();
    }

    if (_document == null) {
      _showSnackBar(_l10n.svgSaveFirst);
      return;
    }

    setState(() {
      _isProcessing = true;
    });

    try {
      final file = await _svgService.saveAsPng(_document!, context);

      setState(() {
        _isProcessing = false;
      });

      if (file != null) {
        _showSnackBar(_l10n.svgExportPngSuccess(file.path));
      } else {
        _showSnackBar(_l10n.svgExportPngFailed);
      }
    } catch (e) {
      setState(() {
        _isProcessing = false;
        _hasError = true;
        _errorMessage = _l10n.svgExportPngFailed;
      });
    }
  }

  /// 分享SVG
  Future<void> _shareSvg() async {
    if (_document == null) {
      await _saveDocument();
    }

    if (_document == null) {
      _showSnackBar(_l10n.svgSaveFirst);
      return;
    }

    setState(() {
      _isProcessing = true;
    });

    try {
      await _document!.share();

      setState(() {
        _isProcessing = false;
      });
    } catch (e) {
      setState(() {
        _isProcessing = false;
        _hasError = true;
        _errorMessage = _l10n.svgShareSvgFailed(e.toString());
      });
    }
  }

  /// 分享为PNG
  Future<void> _shareAsPng() async {
    if (_document == null) {
      await _saveDocument();
    }

    if (_document == null) {
      _showSnackBar(_l10n.svgSaveFirst);
      return;
    }

    setState(() {
      _isProcessing = true;
    });

    try {
      await _svgService.shareAsPng(_document!, context);

      setState(() {
        _isProcessing = false;
      });
    } catch (e) {
      setState(() {
        _isProcessing = false;
        _hasError = true;
        _errorMessage = _l10n.svgSharePngFailed(e.toString());
      });
    }
  }

  /// SVG内容变更处理
  void _onSvgChanged() {
    setState(() {
      _isDirty = true;
      _hasError = false;
    });

    // 尝试解析SVG，检查是否有错误
    try {
      if (_svgController.text.isNotEmpty) {
        SvgPicture.string(_svgController.text);
      }
    } catch (e) {
      setState(() {
        _hasError = true;
        _errorMessage = _l10n.svgInvalidSvg(e.toString());
      });
    }
  }

  /// 显示提示消息
  void _showSnackBar(String message) {
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(SnackBar(content: Text(message)));
  }

  /// 显示iOS风格的菜单
  void _showIosStyleMenu(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    showCupertinoModalPopup<void>(
      context: context,
      builder: (BuildContext context) {
        return CupertinoActionSheet(
          title: Text(
            _l10n.svgMoreActions,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.w600,
              color: isDarkMode ? CupertinoColors.white : CupertinoColors.black,
            ),
          ),
          actions: [
            CupertinoActionSheetAction(
              onPressed: () {
                Navigator.pop(context);
                _importSvgFile();
              },
              child: Row(
                children: [
                  Container(
                    width: 30,
                    height: 30,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8),
                      color: theme.primaryColor.withValues(alpha: 0.1),
                    ),
                    child: Icon(
                      CupertinoIcons.folder_open,
                      size: 18,
                      color: theme.primaryColor,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Text(
                      _l10n.svgImportFile,
                      style: TextStyle(
                        fontSize: 16,
                        color:
                            isDarkMode
                                ? CupertinoColors.white
                                : CupertinoColors.black,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            CupertinoActionSheetAction(
              onPressed: () {
                Navigator.pop(context);
                _saveDocument();
              },
              child: Row(
                children: [
                  Container(
                    width: 30,
                    height: 30,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8),
                      color: theme.primaryColor.withValues(alpha: 0.1),
                    ),
                    child: Icon(
                      CupertinoIcons.square_arrow_down,
                      size: 18,
                      color: theme.primaryColor,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Text(
                      _l10n.svgSave,
                      style: TextStyle(
                        fontSize: 16,
                        color:
                            isDarkMode
                                ? CupertinoColors.white
                                : CupertinoColors.black,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            CupertinoActionSheetAction(
              onPressed: () {
                Navigator.pop(context);
                _exportAsPng();
              },
              child: Row(
                children: [
                  Container(
                    width: 30,
                    height: 30,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8),
                      color: theme.primaryColor.withValues(alpha: 0.1),
                    ),
                    child: Icon(
                      CupertinoIcons.photo,
                      size: 18,
                      color: theme.primaryColor,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Text(
                      _l10n.svgExportPng,
                      style: TextStyle(
                        fontSize: 16,
                        color:
                            isDarkMode
                                ? CupertinoColors.white
                                : CupertinoColors.black,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            CupertinoActionSheetAction(
              onPressed: () {
                Navigator.pop(context);
                _shareAsPng();
              },
              child: Row(
                children: [
                  Container(
                    width: 30,
                    height: 30,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8),
                      color: theme.primaryColor.withValues(alpha: 0.1),
                    ),
                    child: Icon(
                      CupertinoIcons.share,
                      size: 18,
                      color: theme.primaryColor,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Text(
                      _l10n.svgSharePng,
                      style: TextStyle(
                        fontSize: 16,
                        color:
                            isDarkMode
                                ? CupertinoColors.white
                                : CupertinoColors.black,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            CupertinoActionSheetAction(
              onPressed: () {
                Navigator.pop(context);
                _shareSvg();
              },
              child: Row(
                children: [
                  Container(
                    width: 30,
                    height: 30,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8),
                      color: theme.primaryColor.withValues(alpha: 0.1),
                    ),
                    child: Icon(
                      CupertinoIcons.doc_on_clipboard,
                      size: 18,
                      color: theme.primaryColor,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Text(
                      _l10n.svgShareSvg,
                      style: TextStyle(
                        fontSize: 16,
                        color:
                            isDarkMode
                                ? CupertinoColors.white
                                : CupertinoColors.black,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            CupertinoActionSheetAction(
              onPressed: () {
                Navigator.pop(context);
                _showTitleInputDialog().then((newTitle) {
                  if (newTitle != null) {
                    setState(() {
                      _titleController.text = newTitle;
                      _isDirty = true;
                    });
                  }
                });
              },
              child: Row(
                children: [
                  Container(
                    width: 30,
                    height: 30,
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8),
                      color: theme.primaryColor.withValues(alpha: 0.1),
                    ),
                    child: Icon(
                      CupertinoIcons.pencil_ellipsis_rectangle,
                      size: 18,
                      color: theme.primaryColor,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Text(
                      _l10n.svgRename,
                      style: TextStyle(
                        fontSize: 16,
                        color:
                            isDarkMode
                                ? CupertinoColors.white
                                : CupertinoColors.black,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
          cancelButton: CupertinoActionSheetAction(
            isDestructiveAction: true,
            onPressed: () => Navigator.pop(context),
            child: Text(
              _l10n.cancel,
              style: const TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
            ),
          ),
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      appBar: AppBar(
        elevation: 0,
        scrolledUnderElevation: 2,
        title: Text(_l10n.svgEditorTitle),
        bottom: TabBar(
          controller: _tabController,
          tabs: [
            Tab(icon: const Icon(Icons.code), text: _l10n.svgEditTab),
            Tab(icon: const Icon(Icons.visibility), text: _l10n.svgPreviewTab),
          ],
        ),
        actions: [
          // iOS风格的菜单按钮
          IconButton(
            icon: Icon(
              CupertinoIcons.ellipsis_circle,
              color: theme.primaryColor,
              size: 24,
            ),
            onPressed: () => _showIosStyleMenu(context),
            tooltip: _l10n.svgMoreActions,
          ),
        ],
      ),
      body:
          _isProcessing
              ? Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const CircularProgressIndicator(),
                    const SizedBox(height: 16),
                    Text(
                      _l10n.svgProcessing,
                      style: theme.textTheme.bodyMedium,
                    ),
                  ],
                ),
              )
              : Column(
                children: [
                  if (_hasError)
                    Container(
                      padding: const EdgeInsets.all(12),
                      margin: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: AppTheme.redLight,
                        borderRadius: BorderRadius.circular(
                          AppTheme.borderRadiusMD,
                        ),
                      ),
                      width: double.infinity,
                      child: Row(
                        children: [
                          Icon(Icons.error_outline, color: AppTheme.redDark),
                          const SizedBox(width: 8),
                          Expanded(
                            child: Text(
                              _errorMessage,
                              style: TextStyle(color: AppTheme.redDark),
                            ),
                          ),
                        ],
                      ),
                    ),
                  Expanded(
                    child: TabBarView(
                      controller: _tabController,
                      children: [
                        // 编辑区域
                        _buildEditorTab(isDarkMode),

                        // 预览区域
                        _buildPreviewTab(isDarkMode),
                      ],
                    ),
                  ),
                ],
              ),
      floatingActionButton:
          _isDirty && !_hasError
              ? FloatingActionButton.extended(
                onPressed: _saveDocument,
                icon: const Icon(Icons.save),
                label: Text(_l10n.svgSaveChanges),
                backgroundColor: AppTheme.primaryColor,
                foregroundColor: Colors.white,
              )
              : null,
    );
  }

  /// 构建编辑器Tab
  Widget _buildEditorTab(bool isDarkMode) {
    return Card(
      margin: const EdgeInsets.all(8),
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusMD),
        side: BorderSide(
          color: isDarkMode ? AppTheme.darkBorderColor : AppTheme.borderColor,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(8),
        child: TextField(
          controller: _svgController,
          maxLines: null,
          expands: true,
          decoration: InputDecoration(
            hintText: _l10n.svgEnterSvgCode,
            border: InputBorder.none,
            fillColor:
                isDarkMode ? AppTheme.darkBgColor : AppTheme.bgLightColor,
            filled: true,
            contentPadding: const EdgeInsets.all(12),
          ),
          style: TextStyle(
            fontFamily: 'monospace',
            fontSize: 14,
            color: isDarkMode ? AppTheme.darkTextColor : AppTheme.textDarkColor,
          ),
        ),
      ),
    );
  }

  /// 构建预览Tab
  Widget _buildPreviewTab(bool isDarkMode) {
    return Card(
      margin: const EdgeInsets.all(8),
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppTheme.borderRadiusMD),
        side: BorderSide(
          color: isDarkMode ? AppTheme.darkBorderColor : AppTheme.borderColor,
        ),
      ),
      child: Container(
        padding: const EdgeInsets.all(16),
        color: isDarkMode ? AppTheme.darkBgColor : AppTheme.bgLightColor,
        child:
            _svgController.text.isEmpty
                ? Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.image_not_supported_outlined,
                        size: 80,
                        color:
                            isDarkMode
                                ? AppTheme.darkTextLightColor
                                : AppTheme.textLightColor,
                      ),
                      const SizedBox(height: 16),
                      Text(
                        _l10n.svgNoContent,
                        style: TextStyle(
                          fontSize: 18,
                          color:
                              isDarkMode
                                  ? AppTheme.darkTextLightColor
                                  : AppTheme.textLightColor,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        _l10n.svgEnterCodeInEditor,
                        style: TextStyle(
                          color:
                              isDarkMode
                                  ? AppTheme.darkTextLightColor
                                  : AppTheme.textLightColor,
                        ),
                      ),
                    ],
                  ),
                )
                : _buildInteractiveSvgPreview(),
      ),
    );
  }

  /// 构建可交互的SVG预览
  Widget _buildInteractiveSvgPreview() {
    final sanitized = _svgService.sanitizeForPreview(_svgController.text);
    if (sanitized.trim().isEmpty || !sanitized.contains('<svg')) {
      return Center(
        child: Text(
          _l10n.svgInvalidSvg('空或无效的SVG内容'),
          textAlign: TextAlign.center,
        ),
      );
    }
    return InteractiveViewer(
      boundaryMargin: const EdgeInsets.all(20),
      minScale: 0.5,
      maxScale: 4.0,
      child: Center(
        child: SvgPicture.string(
          sanitized,
          fit: BoxFit.contain,
          allowDrawingOutsideViewBox: false,
          alignment: Alignment.center,
          placeholderBuilder:
              (context) => const SizedBox(
                width: 32,
                height: 32,
                child: CircularProgressIndicator(strokeWidth: 2),
              ),
          clipBehavior: Clip.hardEdge,
          colorFilter: null,
          errorBuilder: (context, error, stackTrace) {
            return Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.error_outline, color: Colors.red),
                  const SizedBox(height: 8),
                  Text(
                    _l10n.svgInvalidSvg(error.toString()),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            );
          },
        ),
      ),
    );
  }
}
