import 'dart:io';

import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../../services/service_locator.dart';
import '../config/app_theme.dart';
import '../generated/l10n/app_localizations.dart';
import 'svg_document.dart';
import 'svg_editor_screen.dart';
import '../content/content_save_button.dart';
import '../models/content_item.dart';

/// SVG管理屏幕
class SvgManagerScreen extends StatefulWidget {
  /// 可选：外部打开文件时传入的初始 SVG 内容
  final String? initialSvgContent;

  /// 可选：外部打开文件时传入的标题
  final String? initialTitle;

  /// 构造函数
  const SvgManagerScreen({
    super.key,
    this.initialSvgContent,
    this.initialTitle,
  });

  @override
  State<SvgManagerScreen> createState() => _SvgManagerScreenState();
}

class _SvgManagerScreenState extends State<SvgManagerScreen> {
  /// SVG服务
  final _svgService = ServiceLocator().svgService;

  /// 是否正在加载
  bool _isLoading = true;

  /// 文档列表
  List<SvgDocument> _documents = [];

  /// 当前选中的文档
  SvgDocument? _selectedDocument;

  /// 本地化
  late AppLocalizations _l10n;

  @override
  void initState() {
    super.initState();
    _loadDocuments();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _l10n = AppLocalizations.of(context);
  }

  /// 加载文档
  Future<void> _loadDocuments() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // 确保SVG服务已初始化
      await _svgService.initialize();

      // 如果通过外部文件打开，优先使用传入的内容渲染
      final String? externalSvg = widget.initialSvgContent;
      final String? externalTitle = widget.initialTitle;

      final settings = ServiceLocator().settingsService.settings;
      final docs = _svgService.documents;
      SvgDocument selected;

      if (externalSvg != null && externalSvg.trim().isNotEmpty) {
        selected = SvgDocument(
          title:
              externalTitle?.trim().isNotEmpty == true
                  ? externalTitle!.trim()
                  : 'imported.svg',
          content: externalSvg,
        );
      } else {
        selected =
            docs.isNotEmpty
                ? docs.first
                : (settings.useDefaultInitialTextSvg
                    ? _svgService.getBuiltinPreset(settings.svgPreset)
                    : SvgDocument(title: 'Starter', content: ''));
      }

      // 兜底：若选中的文档内容仍为空且允许内置示例，则使用内置预设
      if (selected.content.trim().isEmpty &&
          settings.useDefaultInitialTextSvg) {
        selected = _svgService.getBuiltinPreset(settings.svgPreset);
      }

      setState(() {
        _documents = docs;
        _selectedDocument = selected;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });

      _showSnackBar(_l10n.svgLoadFailed(e.toString()));
    }
  }

  /// 导入SVG文件
  Future<void> _importSvgFile() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['svg'],
        allowMultiple: true,
      );

      if (result != null && result.files.isNotEmpty) {
        setState(() {
          _isLoading = true;
        });

        for (final file in result.files) {
          if (file.path != null) {
            await _svgService.importFromFile(File(file.path!));
          }
        }

        await _loadDocuments();
      }
    } catch (e) {
      _showSnackBar(_l10n.svgImportFailed(e.toString()));
    }
  }

  /// 创建新SVG
  void _createNewSvg() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const SvgEditorScreen()),
    ).then((_) => _loadDocuments());
  }

  /// 编辑SVG
  void _editSvg(SvgDocument document) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder:
            (context) => SvgEditorScreen(
              documentId: document.id,
              initialSvgContent: document.content,
              initialTitle: document.title,
            ),
      ),
    ).then((_) => _loadDocuments());
  }

  /// 编辑当前选中的文档（若为内置预设且未持久化，则以初始内容打开）
  void _editCurrent() {
    final doc = _selectedDocument;
    if (doc == null) return;

    final exists = _documents.any((d) => d.id == doc.id);
    if (exists) {
      _editSvg(doc);
    } else {
      Navigator.push(
        context,
        MaterialPageRoute(
          builder:
              (context) => SvgEditorScreen(
                initialSvgContent: doc.content,
                initialTitle: doc.title,
              ),
        ),
      ).then((_) => _loadDocuments());
    }
  }

  /// 分享SVG
  Future<void> _shareSvg(SvgDocument document) async {
    try {
      await document.share();
    } catch (e) {
      _showSnackBar(_l10n.svgShareSvgFailed(e.toString()));
    }
  }

  /// 分享为PNG
  Future<void> _shareAsPng(SvgDocument document) async {
    try {
      await _svgService.shareAsPng(document, context);
    } catch (e) {
      _showSnackBar(_l10n.svgSharePngFailed(e.toString()));
    }
  }

  /// 显示提示消息
  void _showSnackBar(String message) {
    ScaffoldMessenger.of(
      context,
    ).showSnackBar(SnackBar(content: Text(message)));
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDarkMode = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor:
          isDarkMode ? AppTheme.darkBgColor : AppTheme.bgLightColor,
      appBar: AppBar(
        title: Text(
          _l10n.svgManagerTitle,
          style: const TextStyle(fontWeight: FontWeight.w600, fontSize: 20),
        ),
        backgroundColor:
            isDarkMode ? AppTheme.darkBgLightColor : AppTheme.bgWhiteColor,
        foregroundColor:
            isDarkMode ? AppTheme.darkTextColor : AppTheme.textDarkColor,
        elevation: 0,
        scrolledUnderElevation: 1,
        shadowColor:
            isDarkMode ? AppTheme.darkBorderColor : AppTheme.borderColor,
        actions: [
          if (_selectedDocument != null)
            ContentSaveButton(
              title: _selectedDocument!.title,
              content: _selectedDocument!.content,
              contentType: ContentType.svg,
              onSaved: (item) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text(_l10n.svgSavedToLibrary),
                    backgroundColor: AppTheme.greenDark,
                    behavior: SnackBarBehavior.floating,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(
                        AppTheme.borderRadiusSM,
                      ),
                    ),
                  ),
                );
              },
            ),
          IconButton(
            icon: const Icon(Icons.add, size: 22),
            onPressed: _createNewSvg,
            tooltip: _l10n.svgNewTooltip,
            color: AppTheme.primaryColor,
          ),
        ],
      ),
      body:
          _isLoading
              ? Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    const CircularProgressIndicator(),
                    const SizedBox(height: 16),
                    Text(_l10n.svgLoading, style: theme.textTheme.bodyMedium),
                  ],
                ),
              )
              : _buildSinglePreview(),
      floatingActionButton: null,
    );
  }

  Widget _buildSinglePreview() {
    final isDarkMode = Theme.of(context).brightness == Brightness.dark;
    final doc = _selectedDocument;
    if (doc == null) return const SizedBox.shrink();

    return Padding(
      padding: const EdgeInsets.all(16.0),
      child: Column(
        children: [
          Expanded(
            child: Container(
              width: double.infinity,
              decoration: BoxDecoration(
                color:
                    isDarkMode
                        ? AppTheme.darkBgLightColor
                        : AppTheme.bgWhiteColor,
                borderRadius: BorderRadius.circular(AppTheme.borderRadiusLG),
                boxShadow: [
                  BoxShadow(
                    color:
                        isDarkMode
                            ? Colors.black.withValues(alpha: 0.3)
                            : Colors.black.withValues(alpha: 0.08),
                    blurRadius: 16,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              clipBehavior: Clip.antiAlias,
              child: Center(
                child: Padding(
                  padding: const EdgeInsets.all(12),
                  child: _buildSvgPreviewWidget(
                    _svgService.sanitizeForPreview(doc.content),
                  ),
                ),
              ),
            ),
          ),
          const SizedBox(height: 16),
          Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: _editCurrent,
                  icon: const Icon(Icons.edit_outlined),
                  label: Text(_l10n.svgEdit),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppTheme.primaryColor,
                    foregroundColor: Colors.white,
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: _importSvgFile,
                  icon: const Icon(Icons.download_outlined),
                  label: Text(_l10n.svgImportSvgFile),
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: () => _shareSvg(doc),
                  icon: const Icon(Icons.share_outlined),
                  label: Text(_l10n.svgShare),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: () => _shareAsPng(doc),
                  icon: const Icon(Icons.image_outlined),
                  label: Text(_l10n.svgShareAsPng),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildSvgPreviewWidget(String svg) {
    if (svg.trim().isEmpty) {
      return Text(_l10n.svgNoContent);
    }
    return SvgPicture.string(
      svg,
      fit: BoxFit.contain,
      allowDrawingOutsideViewBox: false,
      alignment: Alignment.center,
      placeholderBuilder:
          (context) => const SizedBox(
            width: 32,
            height: 32,
            child: CircularProgressIndicator(strokeWidth: 2),
          ),
      clipBehavior: Clip.hardEdge,
      colorFilter: null,
      errorBuilder: (context, error, stackTrace) {
        return Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Icon(Icons.error_outline, color: Colors.red),
            const SizedBox(height: 8),
            Text(
              _l10n.svgInvalidSvg(error.toString()),
              textAlign: TextAlign.center,
            ),
          ],
        );
      },
    );
  }

  // 列表操作控件移除

  // 无需日期格式化
}
