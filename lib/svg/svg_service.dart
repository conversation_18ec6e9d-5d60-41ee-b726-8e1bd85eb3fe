import 'dart:async';
import 'dart:convert';
import 'dart:io';
import 'dart:ui' as ui;

import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:flutter/services.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:image_gallery_saver/image_gallery_saver.dart';
import 'package:permission_handler/permission_handler.dart';
import '../services/service_locator.dart';
import 'svg_document.dart';

/// SVG服务类
class SvgService {
  /// 存储键
  static const String _keySvgDocuments = 'svg_documents';

  /// 单例实例
  static final SvgService _instance = SvgService._internal();

  /// 工厂构造函数
  factory SvgService() => _instance;

  /// 内部构造函数
  SvgService._internal();

  /// 保存的SVG文档列表
  final List<SvgDocument> _documents = [];

  /// 获取所有SVG文档
  List<SvgDocument> get documents => List.unmodifiable(_documents);

  /// 初始化服务
  Future<void> initialize() async {
    // 从存储中加载SVG文档
    await _loadDocuments();

    // 迁移：移除对滤镜依赖的旧版内置SVG，替换为简化版本以兼容 flutter_svg 预览
    bool migrated = false;
    for (var i = 0; i < _documents.length; i++) {
      final doc = _documents[i];
      final hasUnsupportedFilter =
          doc.content.contains('<filter') ||
          doc.content.contains('feGaussianBlur') ||
          doc.content.contains('feDropShadow');
      if (hasUnsupportedFilter) {
        if (doc.title.contains('Neon Orb')) {
          _documents[i] = SvgDocument(
            id: doc.id,
            content: _svgNeonOrb,
            title: doc.title,
            createdAt: doc.createdAt,
            updatedAt: doc.updatedAt,
          );
          migrated = true;
        } else if (doc.title.contains('Aurora Card')) {
          _documents[i] = SvgDocument(
            id: doc.id,
            content: _svgAuroraCard,
            title: doc.title,
            createdAt: doc.createdAt,
            updatedAt: doc.updatedAt,
          );
          migrated = true;
        } else if (doc.title.contains('Glossy Wave')) {
          _documents[i] = SvgDocument(
            id: doc.id,
            content: _svgGlossyWave,
            title: doc.title,
            createdAt: doc.createdAt,
            updatedAt: doc.updatedAt,
          );
          migrated = true;
        }
      }
    }
    if (migrated) {
      await _saveDocuments();
    }

    // 若用户关闭了默认示例且现有文档全部为内置预设，则清空（避免旧版本遗留的内置示例继续显示）
    try {
      final settings = ServiceLocator().settingsService.settings;
      if (!settings.useDefaultInitialTextSvg && _documents.isNotEmpty) {
        final hasOnlyBuiltin = _documents.every(
          (d) => _isBuiltinTitle(d.title),
        );
        if (hasOnlyBuiltin) {
          _documents.clear();
          await _saveDocuments();
        }
      }
    } catch (_) {}

    // 如果为空，且设置允许，注入内置炫酷SVG（仅当允许默认示例且启用内置SVG）
    if (_documents.isEmpty) {
      try {
        final settings = ServiceLocator().settingsService.settings;
        // 需同时满足「启用内置SVG」与「允许默认示例」
        if (settings.enableBuiltinSvg && settings.useDefaultInitialTextSvg) {
          final builtin = _getBuiltinSvgByPreset(settings.svgPreset);
          if (builtin != null) {
            await addDocument(builtin);
          }
        }
      } catch (e) {
        debugPrint('注入内置SVG失败: $e');
      }
    }
  }

  /// 从存储中加载SVG文档
  Future<void> _loadDocuments() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonString = prefs.getString(_keySvgDocuments);

      if (jsonString != null) {
        final jsonList = jsonDecode(jsonString) as List;
        _documents.clear();

        for (final item in jsonList) {
          _documents.add(SvgDocument.fromJson(item as Map<String, dynamic>));
        }
      }
    } catch (e) {
      debugPrint('加载SVG文档失败: $e');
    }
  }

  /// 根据预设名获取内置SVG
  SvgDocument? _getBuiltinSvgByPreset(String preset) {
    switch (preset) {
      case 'minimal_logo':
        return SvgDocument(title: 'Minimal Logo', content: _svgMinimalLogo);
      case 'neon_orb':
        return SvgDocument(title: 'Neon Orb', content: _svgNeonOrb);
      case 'aurora_card':
        return SvgDocument(title: 'Aurora Card', content: _svgAuroraCard);
      case 'glossy_wave':
        return SvgDocument(title: 'Glossy Wave', content: _svgGlossyWave);
    }
    return SvgDocument(title: 'Minimal Logo', content: _svgMinimalLogo);
  }

  bool _isBuiltinTitle(String title) {
    return title == 'Minimal Logo' ||
        title == 'Neon Orb' ||
        title == 'Aurora Card' ||
        title == 'Glossy Wave';
  }

  /// 公共方法：获取指定预设的内置SVG文档（不持久化）
  SvgDocument getBuiltinPreset(String preset) {
    return _getBuiltinSvgByPreset(preset) ??
        SvgDocument(title: 'Neon Orb', content: _svgNeonOrb);
  }

  /// 对SVG进行“预览消毒”：移除 flutter_svg 可能不完全支持的节点与属性
  String sanitizeForPreview(String svg) {
    if (svg.isEmpty) return svg;
    var result = svg;
    // 移除 filter/fe* 定义块
    result = result.replaceAll(RegExp(r'<filter[\s\S]*?<\/filter>'), '');
    result = result.replaceAll(
      RegExp(r'<fe[A-Za-z0-9][\s\S]*?>[\s\S]*?<\/[\s\S]*?>'),
      '',
    );
    // 移除 gradient 定义与对其的引用
    result = result.replaceAll(
      RegExp(r'<linearGradient[\s\S]*?<\/linearGradient>'),
      '',
    );
    result = result.replaceAll(
      RegExp(r'<radialGradient[\s\S]*?<\/radialGradient>'),
      '',
    );
    result = result.replaceAll(RegExp(r'<defs[\s\S]*?<\/defs>'), '');
    result = result.replaceAll(RegExp(r'fill="url\(#.*?\)"'), 'fill="#60A5FA"');
    result = result.replaceAll(
      RegExp(r'stroke="url\(#.*?\)"'),
      'stroke="#60A5FA"',
    );
    // 移除对 filter 的引用
    result = result.replaceAll(RegExp(r'filter="url\(#.*?\)"'), '');
    // 移除 foreignObject
    result = result.replaceAll(
      RegExp(r'<foreignObject[\s\S]*?<\/foreignObject>'),
      '',
    );
    // 移除可能存在的脚本/样式（谨慎）
    result = result.replaceAll(RegExp(r'<script[\s\S]*?<\/script>'), '');
    result = result.replaceAll(RegExp(r'<style[\s\S]*?<\/style>'), '');
    // 移除注释
    result = result.replaceAll(RegExp(r'<!--([\s\S]*?)-->'), '');
    // 将 offset="NN%" 归一化为 0-1 小数
    result = result.replaceAllMapped(
      RegExp(r'offset="([0-9]+(?:\.[0-9]+)?)%"'),
      (m) {
        final v = double.tryParse(m.group(1) ?? '0') ?? 0.0;
        final f = (v / 100.0).toStringAsFixed(4);
        return 'offset="$f"';
      },
    );
    // 将 width/height 的百分比替换为固定像素，避免解析百分比为 double 失败
    result = result.replaceAll(RegExp(r'\bwidth="[0-9.]+%"'), 'width="300"');
    result = result.replaceAll(RegExp(r'\bheight="[0-9.]+%"'), 'height="300"');
    // 去除多余空白
    return result;
  }

  // 简约内置SVG：几何Logo（高兼容）
  static const String _svgMinimalLogo = '''
<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 200 200" width="200" height="200">
  <rect width="200" height="200" rx="24" fill="#0B1220"/>
  <circle cx="100" cy="100" r="56" fill="#60A5FA"/>
  <path d="M100 52 L132 100 L100 148 L68 100 Z" fill="#22D3EE" opacity="0.8"/>
  <circle cx="100" cy="100" r="24" fill="#34D399"/>
</svg>
''';

  // 内置炫酷SVG：柔光霓虹球体
  static const String _svgNeonOrb = '''
<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 300 300" width="300" height="300">
  <rect width="300" height="300" fill="#0B1220"/>
  <circle cx="150" cy="150" r="80" fill="#60A5FA"/>
  <circle cx="150" cy="150" r="110" fill="none" stroke="#22D3EE" stroke-width="2"/>
  <circle cx="150" cy="150" r="130" fill="none" stroke="#34D399" stroke-width="1" opacity="0.4"/>
</svg>
''';

  // 内置炫酷SVG：极光卡片
  static const String _svgAuroraCard = '''
<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 800 500" width="800" height="500">
  <rect width="800" height="500" rx="24" fill="#0B1220"/>
  <circle cx="260" cy="160" r="160" fill="#22D3EE" opacity="0.35"/>
  <circle cx="520" cy="260" r="180" fill="#34D399" opacity="0.25"/>
  <circle cx="420" cy="220" r="140" fill="#A78BFA" opacity="0.2"/>
  <rect x="40" y="40" width="720" height="420" rx="20" fill="#0B1220" opacity="0.55" stroke="#22D3EE" stroke-width="1"/>
  <circle cx="740" cy="88" r="8" fill="#22D3EE"/>
  <circle cx="760" cy="88" r="8" fill="#34D399"/>
</svg>
''';

  // 内置炫酷SVG：光泽波浪
  static const String _svgGlossyWave = '''
<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 800 300" width="800" height="300">
  <rect width="800" height="300" fill="#0B1220"/>
  <path d="M0,180 C120,140 240,220 360,180 C480,140 600,220 720,180 C780,160 820,160 800,200 L800,300 L0,300 Z" fill="#60A5FA"/>
  <path d="M0,165 C120,125 240,205 360,165 C480,125 600,205 720,165 C780,145 820,145 800,185 L800,210 L0,210 Z" fill="#FFFFFF" opacity="0.3"/>
</svg>
''';

  /// 保存SVG文档到存储
  Future<void> _saveDocuments() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final jsonList = _documents.map((doc) => doc.toJson()).toList();
      final jsonString = jsonEncode(jsonList);

      await prefs.setString(_keySvgDocuments, jsonString);
    } catch (e) {
      debugPrint('保存SVG文档失败: $e');
    }
  }

  /// 添加SVG文档
  Future<void> addDocument(SvgDocument document) async {
    _documents.add(document);
    await _saveDocuments();
  }

  /// 更新SVG文档
  Future<void> updateDocument(SvgDocument document) async {
    final index = _documents.indexWhere((doc) => doc.id == document.id);

    if (index != -1) {
      _documents[index] = document;
      await _saveDocuments();
    }
  }

  /// 删除SVG文档
  Future<void> deleteDocument(String id) async {
    _documents.removeWhere((doc) => doc.id == id);
    await _saveDocuments();
  }

  /// 获取SVG文档
  SvgDocument? getDocument(String id) {
    return _documents.firstWhere((doc) => doc.id == id);
  }

  /// 从文件导入SVG
  Future<SvgDocument?> importFromFile(File file) async {
    try {
      final document = await SvgDocument.fromFile(file);
      await addDocument(document);
      return document;
    } catch (e) {
      debugPrint('导入SVG文件失败: $e');
      return null;
    }
  }

  /// 从字符串创建SVG
  Future<SvgDocument> createFromString(
    String svgContent, {
    String? title,
  }) async {
    final document = SvgDocument(content: svgContent, title: title ?? '未命名SVG');

    await addDocument(document);
    return document;
  }

  /// 将SVG转换为PNG - 使用Flutter Widget渲染系统
  Future<Uint8List?> convertToPng(
    SvgDocument document,
    BuildContext context, {
    double? width,
    double? height,
    double scale = 3.0,
  }) async {
    try {
      // 使用默认尺寸或指定尺寸
      final svgWidth = width ?? 600.0;
      final svgHeight = height ?? 600.0;

      // 验证SVG内容是否有效
      if (document.content.isEmpty) {
        debugPrint('SVG内容为空');
        return null;
      }

      // 检查SVG内容是否有效
      if (!document.content.contains('<svg') ||
          !document.content.contains('</svg>')) {
        debugPrint('SVG内容无效: 缺少<svg>标签');
        return null;
      }

      // 使用 Overlay + RepaintBoundary 将 Widget 渲染为 PNG 字节
      final pngData = await _captureSvgToPngBytes(
        context: context,
        svgContent: sanitizeForPreview(document.content),
        width: svgWidth,
        height: svgHeight,
        pixelRatio: scale,
      );

      if (pngData != null) {
        debugPrint('SVG转PNG成功: ${pngData.length} 字节');
      } else {
        debugPrint('SVG转PNG失败: 无法渲染SVG内容');
      }

      return pngData;
    } catch (e, stackTrace) {
      debugPrint('SVG转PNG失败: $e');
      debugPrint('堆栈跟踪: $stackTrace');
      return null;
    }
  }

  /// 使用 Overlay + RepaintBoundary 将 SVG 渲染为 PNG 字节
  Future<Uint8List?> _captureSvgToPngBytes({
    required BuildContext context,
    required String svgContent,
    required double width,
    required double height,
    double pixelRatio = 3.0,
    Color backgroundColor = Colors.white,
  }) async {
    try {
      final overlay = Overlay.of(context);

      final repaintKey = GlobalKey();

      late OverlayEntry entry;
      entry = OverlayEntry(
        builder: (context) {
          return IgnorePointer(
            ignoring: true,
            child: Material(
              type: MaterialType.transparency,
              child: Center(
                child: RepaintBoundary(
                  key: repaintKey,
                  child: Container(
                    color: backgroundColor,
                    width: width,
                    height: height,
                    child: SvgPicture.string(
                      svgContent,
                      fit: BoxFit.contain,
                      allowDrawingOutsideViewBox: false,
                      alignment: Alignment.center,
                    ),
                  ),
                ),
              ),
            ),
          );
        },
      );

      overlay.insert(entry);

      // 等待一帧，确保绘制完成
      await Future<void>.delayed(const Duration(milliseconds: 16));
      await WidgetsBinding.instance.endOfFrame;

      final boundary =
          repaintKey.currentContext?.findRenderObject()
              as RenderRepaintBoundary?;
      if (boundary == null) {
        debugPrint('未找到 RenderRepaintBoundary');
        entry.remove();
        return null;
      }

      final ui.Image image = await boundary.toImage(pixelRatio: pixelRatio);
      final byteData = await image.toByteData(format: ui.ImageByteFormat.png);
      image.dispose();

      entry.remove();

      if (byteData == null) return null;
      return byteData.buffer.asUint8List();
    } catch (e) {
      debugPrint('Overlay 截图失败: $e');
      return null;
    }
  }

  Future<bool> _ensureGalleryPermissions() async {
    try {
      // iOS: 请求照片库添加权限；Android: 请求存储/照片权限（尽量轻量）
      final permissions = <Permission>[Permission.photos, Permission.storage];
      final statuses = await permissions.request();
      final granted = statuses.values.any((s) => s.isGranted);
      return granted;
    } catch (_) {
      // 若权限请求流程异常，尝试继续（部分平台不需要权限）
      return true;
    }
  }

  /// 分享为PNG
  Future<void> shareAsPng(SvgDocument document, BuildContext context) async {
    try {
      final pngData = await convertToPng(document, context);

      if (pngData != null) {
        // 1) 保存到系统相册
        await _ensureGalleryPermissions();
        try {
          final result = await ImageGallerySaver.saveImage(
            pngData,
            name: '${document.title}_${DateTime.now().millisecondsSinceEpoch}',
            quality: 100,
          );
          debugPrint('保存到相册结果: $result');
        } catch (e) {
          debugPrint('保存到相册失败: $e');
        }

        // 2) 同时保存到临时文件并调起分享
        final tempDir = await getTemporaryDirectory();
        final tempFile = File(
          '${tempDir.path}/${document.title}_${DateTime.now().millisecondsSinceEpoch}.png',
        );
        await tempFile.writeAsBytes(pngData);

        await SharePlus.instance.share(
          ShareParams(
            files: [
              XFile(
                tempFile.path,
                mimeType: 'image/png',
                name: '${document.title}.png',
              ),
            ],
            subject: document.title,
            text: document.title,
          ),
        );
      } else {
        debugPrint('PNG数据为空，无法分享');
      }
    } catch (e) {
      debugPrint('分享PNG失败: $e');
      rethrow;
    }
  }

  /// 保存为PNG
  Future<File?> saveAsPng(SvgDocument document, BuildContext context) async {
    try {
      final pngData = await convertToPng(document, context);

      if (pngData != null) {
        // 保存到系统相册
        await _ensureGalleryPermissions();
        try {
          final result = await ImageGallerySaver.saveImage(
            pngData,
            name: '${document.title}_${DateTime.now().millisecondsSinceEpoch}',
            quality: 100,
          );
          debugPrint('保存到相册结果: $result');
        } catch (e) {
          debugPrint('保存到相册失败: $e');
        }

        // 同时保存PNG到应用文档目录
        final docDir = await getApplicationDocumentsDirectory();
        final pngFile = File(
          '${docDir.path}/${document.title}_${DateTime.now().millisecondsSinceEpoch}.png',
        );

        await pngFile.writeAsBytes(pngData);
        return pngFile;
      }

      return null;
    } catch (e) {
      debugPrint('保存PNG失败: $e');
      return null;
    }
  }

  /// 从Markdown内容中提取SVG代码
  List<String> extractSvgFromMarkdown(String markdown) {
    final svgRegex = RegExp(r'```svg\s*([\s\S]*?)\s*```');
    final matches = svgRegex.allMatches(markdown);

    return matches.map((match) => match.group(1) ?? '').toList();
  }

  /// 从HTML内容中提取SVG代码
  List<String> extractSvgFromHtml(String html) {
    final svgRegex = RegExp(r'<svg[\s\S]*?<\/svg>');
    final matches = svgRegex.allMatches(html);

    return matches.map((match) => match.group(0) ?? '').toList();
  }
}
