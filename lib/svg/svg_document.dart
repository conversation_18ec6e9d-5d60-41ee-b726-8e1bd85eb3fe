import 'dart:io';

import 'package:path_provider/path_provider.dart';
import 'package:share_plus/share_plus.dart';
import 'package:uuid/uuid.dart';

/// SVG文档模型
class SvgDocument {
  /// 文档ID
  final String id;

  /// SVG内容
  String content;

  /// 文档标题
  String title;

  /// 创建时间
  final DateTime createdAt;

  /// 最后修改时间
  DateTime updatedAt;

  /// 构造函数
  SvgDocument({
    String? id,
    required this.content,
    String? title,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) : id = id ?? const Uuid().v4(),
       title = title ?? '未命名SVG',
       createdAt = createdAt ?? DateTime.now(),
       updatedAt = updatedAt ?? DateTime.now();

  /// 从JSON创建
  factory SvgDocument.fromJson(Map<String, dynamic> json) {
    return SvgDocument(
      id: json['id'] as String,
      content: json['content'] as String,
      title: json['title'] as String,
      createdAt: DateTime.parse(json['createdAt'] as String),
      updatedAt: DateTime.parse(json['updatedAt'] as String),
    );
  }

  /// 转换为JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'content': content,
      'title': title,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
    };
  }

  /// 更新内容
  void updateContent(String newContent) {
    content = newContent;
    updatedAt = DateTime.now();
  }

  /// 更新标题
  void updateTitle(String newTitle) {
    title = newTitle;
    updatedAt = DateTime.now();
  }

  /// 从文件加载SVG
  static Future<SvgDocument> fromFile(File file) async {
    final content = await file.readAsString();
    final fileName = file.path.split('/').last;
    final title = fileName.replaceAll('.svg', '');

    return SvgDocument(content: content, title: title);
  }

  /// 保存到文件
  Future<File> saveToFile() async {
    final directory = await getApplicationDocumentsDirectory();
    final fileName =
        '${title.replaceAll(' ', '_')}_${DateTime.now().millisecondsSinceEpoch}.svg';
    final filePath = '${directory.path}/$fileName';

    final file = File(filePath);
    return await file.writeAsString(content);
  }

  /// 分享SVG
  Future<void> share() async {
    final file = await saveToFile();
    final sanitizedTitle = title.endsWith('.svg') ? title : '$title.svg';
    await SharePlus.instance.share(
      ShareParams(
        files: [
          XFile(file.path, mimeType: 'image/svg+xml', name: sanitizedTitle),
        ],
        subject: sanitizedTitle,
        text: sanitizedTitle,
      ),
    );
  }

  /// 复制SVG文档
  SvgDocument copy() {
    return SvgDocument(
      content: content,
      title: '$title - 副本',
      createdAt: DateTime.now(),
    );
  }
}
