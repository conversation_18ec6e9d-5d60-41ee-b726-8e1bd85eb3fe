import 'package:flutter/material.dart';
import 'marquee_text.dart';

/// 跑马灯测试页面
class MarqueeTestPage extends StatelessWidget {
  const MarqueeTestPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('跑马灯测试')),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              '短文本（不需要跑马灯）:',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Container(
              width: 200,
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey),
                borderRadius: BorderRadius.circular(4),
              ),
              child: const SimpleMarqueeText(
                '短文本',
                style: TextStyle(fontSize: 16),
              ),
            ),
            const SizedBox(height: 24),

            const Text(
              '长文本（需要跑马灯）:',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Container(
              width: 200,
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey),
                borderRadius: BorderRadius.circular(4),
              ),
              child: const SimpleMarqueeText(
                '这是一个非常长的文本，应该会触发跑马灯效果',
                style: TextStyle(fontSize: 16),
                speed: 30.0,
                pauseDuration: 1.0,
              ),
            ),
            const SizedBox(height: 24),

            const Text(
              '英文长文本:',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Container(
              width: 200,
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.grey),
                borderRadius: BorderRadius.circular(4),
              ),
              child: const SimpleMarqueeText(
                'This is a very long English text that should trigger marquee effect',
                style: TextStyle(fontSize: 16),
                speed: 30.0,
                pauseDuration: 1.0,
              ),
            ),
            const SizedBox(height: 24),

            const Text(
              '订阅名称示例:',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Container(
              width: 250,
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                gradient: const LinearGradient(
                  colors: [Colors.purple, Colors.blue],
                ),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const SimpleMarqueeText(
                'ContentPal Professional Monthly Subscription',
                style: TextStyle(
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                  color: Colors.white,
                ),
                speed: 25.0,
                pauseDuration: 2.0,
              ),
            ),
            const SizedBox(height: 24),

            const Text(
              '简单跑马灯测试:',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Container(
              width: 200,
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.red),
                borderRadius: BorderRadius.circular(4),
              ),
              child: const EasyMarqueeText(
                'This is a very long text that should scroll horizontally',
                style: TextStyle(fontSize: 16),
                speed: 50.0,
                pauseDuration: 1.0,
              ),
            ),
            const SizedBox(height: 24),

            const Text(
              '超简单跑马灯测试（推荐）:',
              style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Container(
              width: 200,
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                border: Border.all(color: Colors.green),
                borderRadius: BorderRadius.circular(4),
              ),
              child: const SuperSimpleMarqueeText(
                'This is a very long text that should scroll smoothly without being cut off',
                style: TextStyle(fontSize: 16),
                speed: 50.0,
                pauseDuration: 1.0,
              ),
            ),
            const SizedBox(height: 24),

            ElevatedButton(
              onPressed: () {
                Navigator.of(context).pop();
              },
              child: const Text('返回'),
            ),
          ],
        ),
      ),
    );
  }
}
