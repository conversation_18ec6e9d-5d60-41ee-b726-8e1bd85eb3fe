import 'package:flutter/material.dart';

/// 跑马灯文本组件
/// 当文本超出可用宽度时自动滚动显示
class MarqueeText extends StatefulWidget {
  /// 要显示的文本
  final String text;

  /// 文本样式
  final TextStyle? style;

  /// 滚动速度（像素/秒）
  final double speed;

  /// 暂停时间（秒）
  final double pauseDuration;

  /// 是否启用跑马灯效果
  final bool enabled;

  /// 文本对齐方式
  final TextAlign textAlign;

  /// 最大行数
  final int? maxLines;

  const MarqueeText(
    this.text, {
    super.key,
    this.style,
    this.speed = 50.0,
    this.pauseDuration = 1.0,
    this.enabled = true,
    this.textAlign = TextAlign.start,
    this.maxLines = 1,
  });

  @override
  State<MarqueeText> createState() => _MarqueeTextState();
}

class _MarqueeTextState extends State<MarqueeText>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;
  late ScrollController _scrollController;

  bool _needsScrolling = false;

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
    _controller = AnimationController(
      duration: Duration(
        seconds: (widget.text.length / widget.speed * 10).round(),
      ),
      vsync: this,
    );

    _setupAnimation();
  }

  void _setupAnimation() {
    _animation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(parent: _controller, curve: Curves.linear));

    _animation.addListener(() {
      if (_needsScrolling && _scrollController.hasClients) {
        final maxScroll = _scrollController.position.maxScrollExtent;
        _scrollController.jumpTo(maxScroll * _animation.value);
      }
    });

    _animation.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        Future.delayed(
          Duration(milliseconds: (widget.pauseDuration * 1000).round()),
          () {
            if (mounted && _needsScrolling) {
              _controller.reset();
              _controller.forward();
            }
          },
        );
      }
    });
  }

  @override
  void didUpdateWidget(MarqueeText oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.text != widget.text ||
        oldWidget.speed != widget.speed ||
        oldWidget.pauseDuration != widget.pauseDuration) {
      _controller.duration = Duration(
        seconds: (widget.text.length / widget.speed * 10).round(),
      );
      _setupAnimation();
      WidgetsBinding.instance.addPostFrameCallback(
        (_) => _checkIfScrollingNeeded(),
      );
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _checkIfScrollingNeeded() {
    if (!widget.enabled) {
      setState(() {
        _needsScrolling = false;
      });
      return;
    }

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted && _scrollController.hasClients) {
        final maxScroll = _scrollController.position.maxScrollExtent;
        final needsScrolling = maxScroll > 0;

        if (needsScrolling != _needsScrolling) {
          setState(() {
            _needsScrolling = needsScrolling;
          });

          if (_needsScrolling) {
            Future.delayed(
              Duration(milliseconds: (widget.pauseDuration * 1000).round()),
              () {
                if (mounted) {
                  _controller.forward();
                }
              },
            );
          } else {
            _controller.stop();
            _controller.reset();
          }
        }
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        return SingleChildScrollView(
          controller: _scrollController,
          scrollDirection: Axis.horizontal,
          physics: const NeverScrollableScrollPhysics(),
          child: ConstrainedBox(
            constraints: BoxConstraints(minWidth: constraints.maxWidth),
            child: Text(
              widget.text,
              style: widget.style,
              textAlign: widget.textAlign,
              maxLines: widget.maxLines,
              overflow: TextOverflow.visible,
            ),
          ),
        );
      },
    );
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    WidgetsBinding.instance.addPostFrameCallback(
      (_) => _checkIfScrollingNeeded(),
    );
  }
}

/// 简化版跑马灯文本组件
/// 专门用于单行文本的跑马灯效果
class SimpleMarqueeText extends StatefulWidget {
  final String text;
  final TextStyle? style;
  final double speed;
  final double pauseDuration;

  const SimpleMarqueeText(
    this.text, {
    super.key,
    this.style,
    this.speed = 30.0,
    this.pauseDuration = 2.0,
  });

  @override
  State<SimpleMarqueeText> createState() => _SimpleMarqueeTextState();
}

/// 更简单的跑马灯实现
class EasyMarqueeText extends StatefulWidget {
  final String text;
  final TextStyle? style;
  final double speed;
  final double pauseDuration;

  const EasyMarqueeText(
    this.text, {
    super.key,
    this.style,
    this.speed = 50.0,
    this.pauseDuration = 1.0,
  });

  @override
  State<EasyMarqueeText> createState() => _EasyMarqueeTextState();
}

/// 超简单跑马灯实现
class SuperSimpleMarqueeText extends StatefulWidget {
  final String text;
  final TextStyle? style;
  final double speed;
  final double pauseDuration;

  const SuperSimpleMarqueeText(
    this.text, {
    super.key,
    this.style,
    this.speed = 50.0,
    this.pauseDuration = 1.0,
  });

  @override
  State<SuperSimpleMarqueeText> createState() => _SuperSimpleMarqueeTextState();
}

class _SimpleMarqueeTextState extends State<SimpleMarqueeText>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;
  late ScrollController _scrollController;
  bool _needsMarquee = false;
  double _textWidth = 0;
  double _containerWidth = 0;

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
    _controller = AnimationController(
      duration: const Duration(seconds: 3),
      vsync: this,
    );

    _animation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(parent: _controller, curve: Curves.linear));

    _animation.addListener(() {
      if (_needsMarquee && _scrollController.hasClients) {
        final maxScroll = _scrollController.position.maxScrollExtent;
        _scrollController.jumpTo(maxScroll * _animation.value);
      }
    });

    _animation.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        Future.delayed(
          Duration(milliseconds: (widget.pauseDuration * 1000).round()),
          () {
            if (mounted && _needsMarquee) {
              _controller.reset();
              _controller.forward();
            }
          },
        );
      }
    });

    WidgetsBinding.instance.addPostFrameCallback((_) => _checkTextOverflow());
  }

  @override
  void didUpdateWidget(SimpleMarqueeText oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.text != widget.text) {
      WidgetsBinding.instance.addPostFrameCallback((_) => _checkTextOverflow());
    }
  }

  void _checkTextOverflow() {
    if (!mounted) return;

    // 使用TextPainter来准确测量文本宽度
    final textPainter = TextPainter(
      text: TextSpan(text: widget.text, style: widget.style),
      textDirection: TextDirection.ltr,
      maxLines: 1,
    );
    textPainter.layout();

    _textWidth = textPainter.size.width;

    // 获取容器宽度
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        final renderBox = context.findRenderObject() as RenderBox?;
        if (renderBox != null) {
          _containerWidth = renderBox.size.width;
          final needsMarquee = _textWidth > _containerWidth;

          if (needsMarquee != _needsMarquee) {
            setState(() {
              _needsMarquee = needsMarquee;
            });

            if (_needsMarquee) {
              // 根据文本长度调整动画时长
              final duration = Duration(
                milliseconds: ((_textWidth / widget.speed) * 1000)
                    .round()
                    .clamp(2000, 8000),
              );
              _controller.duration = duration;

              Future.delayed(
                Duration(milliseconds: (widget.pauseDuration * 1000).round()),
                () {
                  if (mounted) {
                    _controller.forward();
                  }
                },
              );
            } else {
              _controller.stop();
              _controller.reset();
            }
          }
        }
      }
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        _containerWidth = constraints.maxWidth;

        if (_needsMarquee) {
          return ClipRect(
            child: SingleChildScrollView(
              controller: _scrollController,
              scrollDirection: Axis.horizontal,
              physics: const NeverScrollableScrollPhysics(),
              child: Text(
                widget.text,
                style: widget.style,
                maxLines: 1,
                overflow: TextOverflow.visible,
              ),
            ),
          );
        } else {
          return Text(
            widget.text,
            style: widget.style,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          );
        }
      },
    );
  }
}

/// 简单易用的跑马灯实现
class _EasyMarqueeTextState extends State<EasyMarqueeText>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<double> _animation;
  bool _isOverflowing = false;

  @override
  void initState() {
    super.initState();
    _controller = AnimationController(
      duration: Duration(
        milliseconds: (widget.text.length * 80).clamp(3000, 8000),
      ),
      vsync: this,
    );

    _animation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(parent: _controller, curve: Curves.linear));

    _animation.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        Future.delayed(
          Duration(milliseconds: (widget.pauseDuration * 1000).round()),
          () {
            if (mounted && _isOverflowing) {
              _controller.reset();
              _controller.forward();
            }
          },
        );
      }
    });

    WidgetsBinding.instance.addPostFrameCallback((_) => _checkOverflow());
  }

  void _checkOverflow() {
    if (!mounted) return;

    // 使用TextPainter测量文本宽度
    final textPainter = TextPainter(
      text: TextSpan(text: widget.text, style: widget.style),
      textDirection: TextDirection.ltr,
      maxLines: 1,
    );
    textPainter.layout();

    final textWidth = textPainter.size.width;
    final containerWidth = context.size?.width ?? 0;

    final shouldOverflow = textWidth > containerWidth && containerWidth > 0;

    if (shouldOverflow != _isOverflowing) {
      setState(() {
        _isOverflowing = shouldOverflow;
      });

      if (_isOverflowing) {
        Future.delayed(
          Duration(milliseconds: (widget.pauseDuration * 1000).round()),
          () {
            if (mounted) {
              _controller.forward();
            }
          },
        );
      } else {
        _controller.stop();
        _controller.reset();
      }
    }
  }

  @override
  void didUpdateWidget(EasyMarqueeText oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.text != widget.text) {
      WidgetsBinding.instance.addPostFrameCallback((_) => _checkOverflow());
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (!_isOverflowing) {
      return Text(
        widget.text,
        style: widget.style,
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      );
    }

    return LayoutBuilder(
      builder: (context, constraints) {
        final containerWidth = constraints.maxWidth;

        // 使用TextPainter测量文本宽度
        final textPainter = TextPainter(
          text: TextSpan(text: widget.text, style: widget.style),
          textDirection: TextDirection.ltr,
          maxLines: 1,
        );
        textPainter.layout();

        final textWidth = textPainter.size.width;
        final scrollDistance = textWidth - containerWidth + 20; // 添加一些边距

        return ClipRect(
          child: AnimatedBuilder(
            animation: _animation,
            builder: (context, child) {
              return Transform.translate(
                offset: Offset(-_animation.value * scrollDistance, 0),
                child: Row(
                  children: [
                    Text(
                      widget.text,
                      style: widget.style,
                      maxLines: 1,
                      overflow: TextOverflow.visible,
                    ),
                    SizedBox(width: 20), // 间距
                    Text(
                      widget.text,
                      style: widget.style,
                      maxLines: 1,
                      overflow: TextOverflow.visible,
                    ),
                  ],
                ),
              );
            },
          ),
        );
      },
    );
  }
}

/// 超简单跑马灯实现 - 最可靠的版本
class _SuperSimpleMarqueeTextState extends State<SuperSimpleMarqueeText>
    with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late ScrollController _scrollController;
  bool _needsMarquee = false;

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
    _controller = AnimationController(
      duration: const Duration(seconds: 5),
      vsync: this,
    );

    _controller.addStatusListener((status) {
      if (status == AnimationStatus.completed) {
        Future.delayed(
          Duration(milliseconds: (widget.pauseDuration * 1000).round()),
          () {
            if (mounted && _needsMarquee) {
              _controller.reset();
              _controller.forward();
            }
          },
        );
      }
    });

    WidgetsBinding.instance.addPostFrameCallback((_) => _checkIfNeedsMarquee());
  }

  void _checkIfNeedsMarquee() {
    if (!mounted) return;

    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (!mounted) return;

      // 使用TextPainter测量文本的真实宽度
      final textPainter = TextPainter(
        text: TextSpan(text: widget.text, style: widget.style),
        textDirection: TextDirection.ltr,
        maxLines: 1,
      );
      textPainter.layout();

      final textWidth = textPainter.size.width;

      // 获取当前widget的渲染宽度
      final renderBox = context.findRenderObject() as RenderBox?;
      final containerWidth = renderBox?.size.width ?? 0;

      final needsMarquee = textWidth > containerWidth && containerWidth > 0;

      if (needsMarquee != _needsMarquee) {
        setState(() {
          _needsMarquee = needsMarquee;
        });

        if (_needsMarquee) {
          // 清除之前的监听器
          _controller.removeListener(_animationListener);
          // 设置新的动画监听器
          _controller.addListener(_animationListener);

          // 延迟开始动画
          Future.delayed(
            Duration(milliseconds: (widget.pauseDuration * 1000).round()),
            () {
              if (mounted) {
                _controller.forward();
              }
            },
          );
        } else {
          _controller.stop();
          _controller.reset();
        }
      }
    });
  }

  void _animationListener() {
    if (_scrollController.hasClients) {
      final maxScroll = _scrollController.position.maxScrollExtent;
      _scrollController.jumpTo(maxScroll * _controller.value);
    }
  }

  @override
  void didUpdateWidget(SuperSimpleMarqueeText oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.text != widget.text) {
      WidgetsBinding.instance.addPostFrameCallback(
        (_) => _checkIfNeedsMarquee(),
      );
    }
  }

  @override
  void dispose() {
    _controller.removeListener(_animationListener);
    _controller.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (!_needsMarquee) {
      return Text(
        widget.text,
        style: widget.style,
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      );
    }

    return LayoutBuilder(
      builder: (context, constraints) {
        return ClipRect(
          child: SingleChildScrollView(
            controller: _scrollController,
            scrollDirection: Axis.horizontal,
            physics: const NeverScrollableScrollPhysics(),
            child: ConstrainedBox(
              constraints: BoxConstraints(minWidth: constraints.maxWidth),
              child: Row(
                children: [
                  Text(widget.text, style: widget.style, maxLines: 1),
                  const SizedBox(width: 50), // 间距
                  Text(widget.text, style: widget.style, maxLines: 1),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}
