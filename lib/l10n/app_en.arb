{"@@locale": "en", "@@last_modified": "2024-01-15T10:00:00.000Z", "appName": "ContentPal", "@appName": {"description": "The name of the application"}, "appNameChinese": "内容君", "@appNameChinese": {"description": "The Chinese name of the application"}, "appDescription": "Professional content processing tool that makes content creation easier", "@appDescription": {"description": "Description of the application"}, "home": "Home", "@home": {"description": "Home page title"}, "settings": "Settings", "@settings": {"description": "Settings page title"}, "language": "Language", "@language": {"description": "Language setting label"}, "theme": "Theme", "@theme": {"description": "Theme setting label"}, "lightTheme": "Light", "@lightTheme": {"description": "Light theme option"}, "darkTheme": "Dark", "@darkTheme": {"description": "Dark theme option"}, "systemTheme": "System", "@systemTheme": {"description": "System theme option"}, "sharedText": "Shared Text", "@sharedText": {"description": "Title for shared text content"}, "failedToGetInitialIntent": "Failed to get initial intent: {error}", "@failedToGetInitialIntent": {"description": "Error message when initial intent cannot be retrieved"}, "failedToLoadThemeSettings": "Failed to load theme settings: {error}", "@failedToLoadThemeSettings": {"description": "Error message when theme settings cannot be loaded"}, "themeMaterialYou": "Material You", "@themeMaterialYou": {"description": "Material You theme name"}, "themeMorandi": "Morandi Style", "@themeMorandi": {"description": "Morandi theme name"}, "themeMonochrome": "Minimalist Black & White", "@themeMonochrome": {"description": "Monochrome theme name"}, "themeNature": "Natural Colors", "@themeNature": {"description": "Nature theme name"}, "themeTech": "Tech Style", "@themeTech": {"description": "Technology theme name"}, "themeChinese": "Traditional Chinese Colors", "@themeChinese": {"description": "Chinese traditional colors theme name"}, "themeMaterialYouDesc": "Dynamic theme automatically extracted from wallpaper", "@themeMaterialYouDesc": {"description": "Material You theme description"}, "themeMorandiDesc": "Soft and elegant Morandi color palette", "@themeMorandiDesc": {"description": "Morandi theme description"}, "themeMonochromeDesc": "Simple and pure black and white color scheme", "@themeMonochromeDesc": {"description": "Monochrome theme description"}, "themeNatureDesc": "Comfortable and natural ecological color system", "@themeNatureDesc": {"description": "Nature theme description"}, "themeTechDesc": "Futuristic technology colors", "@themeTechDesc": {"description": "Technology theme description"}, "themeChineseDesc": "Eastern aesthetics combining tradition and modernity", "@themeChineseDesc": {"description": "Chinese traditional colors theme description"}, "markdown": "<PERSON><PERSON>", "@markdown": {"description": "Markdown module name"}, "textCards": "Text Cards", "@textCards": {"description": "Text Cards module name"}, "textCardSelectColor": "Select Color", "textCardStyleApplied": "Style Applied", "textCardSplitFailed": "Split failed: {error}", "textCardCreateFailed": "Create failed: {error}", "textCardEditCard": "Edit Card", "textCardPreviousStep": "Previous Step", "textCardSaveToContentLibrary": "Save to Content Library", "textCardStartExportingImage": "Starting image export...", "textCardImageSavedSuccess": "✅ Image successfully saved to photo album", "textCardPleaseEnterContent": "Please enter card content", "textCardDeleteCard": "Delete Card", "textCardDeleteConfirm": "Are you sure you want to delete this card? This action cannot be undone.", "textCardCategory": "Category: {category}", "textCardDescription": "Description: {description}", "textCardClose": "Close", "textCardUseTemplate": "Use Template", "textCardConfirmExport": "Confirm Export", "textCardQuality": "Quality", "textCardIncludeWatermark": "Include Watermark", "textCardPreviewInfo": "Preview Info", "textCardRatio": "Ratio: {ratio}", "textCardQualityPercent": "Quality: {quality}%", "textCardWatermarkStatus": "Watermark: {status}", "textCardDimensions": "Dimensions", "textCardInclude": "Include", "textCardNotInclude": "Not Include", "textCardAddCard": "Add Card", "textCardAddNewCard": "Add New Card", "textCardEdit": "Edit", "textCardExportingImage": "Exporting image...", "textCardExportSuccess": "✅ Export successful! Saved to photo album", "textCardExportFailed": "Export failed:", "textCardAddWatermark": "Add Watermark", "textCardAddWatermarkDesc": "Add app watermark to image corner", "textCardIncludeTitle": "Include Title", "textCardIncludeTitleDesc": "Show title in exported image", "textCardIncludeTimestamp": "Include Timestamp", "textCardIncludeTimestampDesc": "Show creation time in image", "textCardImageQuality": "Image Quality", "textCardTextStyleCustomization": "Text Style Customization", "textCardExportCurrentCard": "Export Current Card", "textCardBatchExport": "Batch Export", "textCardClearSelection": "Clear Selection", "textCardResetStyle": "Reset Style", "textCardResetStyleConfirm": "Are you sure you want to clear all text styles? This action cannot be undone.", "textCardExportAsImage": "Export as Image", "textCardExportAsImageDesc": "Save this card as image", "textCardEditCardDesc": "Modify title and content", "textCardDeleteCardDesc": "Remove from document", "textCardDeleteCardConfirm": "Are you sure you want to delete card \"{title}\"? This action cannot be undone.", "textCardEditDocument": "Edit Document", "textCardUniformStyle": "Uniform Style", "textCardExportImage": "Export Image", "textCardInsertSeparator": "Insert Separator", "textCardPleaseEnterTitleAndCards": "Please enter title and ensure at least one card", "textCardSaveFailed": "Save failed", "textCardContentRendering": "Content Rendering", "textCardExportWithTitle": "Export - {title}", "textCardShare": "Share", "textCardSaveToAlbum": "Save to Album", "textCardUnderstood": "Understood", "textCardStartExperience": "Start Experience", "textCardFeatureDemo": "Feature Demo", "textCardGotIt": "Got it", "textCardStartUsing": "Start Using", "textCardPreviewEffect": "Preview Effect", "textCardExportInfo": "Export Info", "textCardImageDimensions": "Image Dimensions", "textCardAspectRatio": "Aspect Ratio", "textCardFileSize": "File Size", "textCardUsageScenario": "<PERSON><PERSON>", "textCardBestQuality": "Best Quality (100%)", "textCardWatermarkDescription": "Add app identifier at the bottom of the image", "pdf": "PDF", "@pdf": {"description": "PDF module name"}, "voice": "Voice", "@voice": {"description": "Voice module name"}, "html": "HTML", "@html": {"description": "HTML module name"}, "svg": "SVG", "@svg": {"description": "SVG module name"}, "content": "Content", "@content": {"description": "Content field"}, "trafficGuide": "Traffic Guide", "@trafficGuide": {"description": "Traffic Guide module name"}, "create": "Create", "@create": {"description": "Create button text"}, "edit": "Edit", "@edit": {"description": "Edit button text"}, "delete": "Delete", "@delete": {"description": "Delete button text"}, "save": "Save", "@save": {"description": "Save button text"}, "cancel": "Cancel", "@cancel": {"description": "Cancel button text"}, "confirm": "OK", "@confirm": {"description": "Confirm button"}, "yes": "Yes", "@yes": {"description": "Yes button text"}, "no": "No", "@no": {"description": "No button text"}, "ok": "OK", "@ok": {"description": "OK button text"}, "loading": "Loading...", "@loading": {"description": "Loading indicator text"}, "error": "Error", "@error": {"description": "Error message prefix"}, "success": "Success", "@success": {"description": "Success message prefix"}, "warning": "Warning", "@warning": {"description": "Warning message prefix"}, "info": "Info", "@info": {"description": "Info message prefix"}, "search": "Search", "@search": {"description": "Search placeholder text"}, "searchHint": "Enter search terms...", "@searchHint": {"description": "Search input hint text"}, "noResults": "No results found", "@noResults": {"description": "No search results message"}, "tryAgain": "Try Again", "@tryAgain": {"description": "Try again button text"}, "refresh": "Refresh", "@refresh": {"description": "Refresh button text"}, "share": "Share", "@share": {"description": "Share button text"}, "export": "Export", "@export": {"description": "Export button text"}, "import": "Import", "@import": {"description": "Import button text"}, "copy": "Copy", "@copy": {"description": "Copy button text"}, "paste": "Paste", "@paste": {"description": "Paste button text"}, "cut": "Cut", "@cut": {"description": "Cut button text"}, "undo": "Undo", "@undo": {"description": "Undo button text"}, "redo": "Redo", "@redo": {"description": "Redo button text"}, "selectAll": "Select All", "@selectAll": {"description": "Select all button"}, "close": "Close", "@close": {"description": "Close button"}, "back": "Back", "@back": {"description": "Back button text"}, "next": "Next", "@next": {"description": "Next button text"}, "previous": "Previous", "@previous": {"description": "Previous button text"}, "done": "Done", "@done": {"description": "Done button text"}, "finish": "Finish", "@finish": {"description": "Finish button text"}, "skip": "<PERSON><PERSON>", "@skip": {"description": "Skip button text"}, "continueAction": "Continue", "@continueAction": {"description": "Continue button text"}, "retry": "Retry", "@retry": {"description": "Retry button text"}, "reset": "Reset", "@reset": {"description": "Reset button text"}, "clear": "Clear", "@clear": {"description": "Clear button text"}, "apply": "Apply", "@apply": {"description": "Apply button text"}, "preview": "Preview", "@preview": {"description": "Preview button text"}, "download": "Download", "@download": {"description": "Download button text"}, "upload": "Upload", "@upload": {"description": "Upload button text"}, "file": "File", "@file": {"description": "File label"}, "folder": "Folder", "@folder": {"description": "Folder label"}, "name": "Name", "@name": {"description": "Name field label"}, "title": "Title", "@title": {"description": "Title field label"}, "description": "Description", "@description": {"description": "Description field label"}, "size": "Size", "@size": {"description": "Size field label"}, "date": "Date", "@date": {"description": "Date field label"}, "time": "Time", "@time": {"description": "Time field label"}, "type": "Type", "@type": {"description": "Type field label"}, "status": "Status", "@status": {"description": "Status field label"}, "version": "Version", "@version": {"description": "Version field label"}, "author": "Author", "@author": {"description": "Author field label"}, "tags": "Tags", "@tags": {"description": "Tags field label"}, "category": "Category", "@category": {"description": "Category field label"}, "priority": "Priority", "@priority": {"description": "Priority field label"}, "high": "High", "@high": {"description": "High priority"}, "medium": "Medium", "@medium": {"description": "Medium priority"}, "low": "Low", "@low": {"description": "Low priority"}, "enabled": "Enabled", "@enabled": {"description": "Enabled status"}, "disabled": "Disabled", "@disabled": {"description": "Disabled status"}, "online": "Online", "@online": {"description": "Online status"}, "offline": "Offline", "@offline": {"description": "Offline status"}, "connected": "Connected", "@connected": {"description": "Connected status"}, "disconnected": "Disconnected", "@disconnected": {"description": "Disconnected status"}, "available": "Available", "@available": {"description": "Available status"}, "unavailable": "Unavailable", "@unavailable": {"description": "Unavailable status"}, "active": "Active", "@active": {"description": "Status text for active subscription"}, "inactive": "Inactive", "@inactive": {"description": "Status text for inactive subscription"}, "public": "Public", "@public": {"description": "Public visibility"}, "private": "Private", "@private": {"description": "Private visibility"}, "draft": "Draft", "@draft": {"description": "Draft status"}, "published": "Published", "@published": {"description": "Published status"}, "archived": "Archived", "@archived": {"description": "Archived status"}, "pdfProfessionalTool": "PDF Professional Tool", "@pdfProfessionalTool": {"description": "PDF module title"}, "pdfToolDescription": "Powerful PDF processing capabilities that make document management easier", "@pdfToolDescription": {"description": "PDF module description"}, "securityEncryption": "Security Encryption", "@securityEncryption": {"description": "PDF security feature title"}, "passwordProtectionPermissionControl": "Password Protection\nPermission Control", "@passwordProtectionPermissionControl": {"description": "PDF security feature description"}, "intelligentAnnotation": "Intelligent Annotation", "@intelligentAnnotation": {"description": "PDF annotation feature title"}, "highlightMarkingTextAnnotation": "Highlight Marking\nText Annotation", "@highlightMarkingTextAnnotation": {"description": "PDF annotation feature description"}, "quickSearch": "Quick Search", "@quickSearch": {"description": "PDF search feature title"}, "fullTextSearchContentLocation": "Full-text Search\nContent Location", "@fullTextSearchContentLocation": {"description": "PDF search feature description"}, "convenientSharing": "Convenient Sharing", "@convenientSharing": {"description": "PDF sharing feature title"}, "multipleFormatsOneClickExport": "Multiple Formats\nOne-click Export", "@multipleFormatsOneClickExport": {"description": "PDF sharing feature description"}, "welcomeToPdfTool": "Welcome to PDF Professional Tool!", "@welcomeToPdfTool": {"description": "Welcome message for PDF tool"}, "importFirstPdfDocument": "Import First PDF Document", "@importFirstPdfDocument": {"description": "Import PDF button text"}, "appearance": "Appearance", "@appearance": {"description": "Appearance settings section title"}, "followSystem": "Follow System", "@followSystem": {"description": "Follow system theme option"}, "languageChangeEffect": "Language changes will take effect immediately", "@languageChangeEffect": {"description": "Language change notification message"}, "contentLibrary": "Content Library", "@contentLibrary": {"description": "Content library title"}, "manageAllCards": "Manage All Cards", "@manageAllCards": {"description": "Content library subtitle"}, "templateLibrary": "Template Library", "@templateLibrary": {"description": "Template library title"}, "browseBeautifulTemplates": "Browse Beautiful Templates", "@browseBeautifulTemplates": {"description": "Template library subtitle"}, "inputTextToSplit": "Input Text to Split", "@inputTextToSplit": {"description": "Input text to split title"}, "pasteOrInputLongText": "Paste or input long text content", "@pasteOrInputLongText": {"description": "Paste or input long text description"}, "pasteClipboard": "Paste Clipboard", "@pasteClipboard": {"description": "Paste clipboard button"}, "clearContent": "Clear Content", "@clearContent": {"description": "Clear content button"}, "cardNumber": "Card {number}", "@cardNumber": {"description": "Default card title with number", "placeholders": {"number": {"type": "int", "description": "Card number"}}}, "loadingDemoData": "Loading demo data...", "@loadingDemoData": {"description": "Loading demo data message"}, "modernUIDesign": "✨ Modern UI Design\n🖼️ Render Result Preview\n📱 Block Mode Support\n⚡ High Performance Experience", "@modernUIDesign": {"description": "Feature description for content library"}, "editFunction": "Edit function: {title}", "@editFunction": {"description": "Edit function message", "placeholders": {"title": {"type": "String", "description": "Item title"}}}, "deleted": "Deleted: {title}", "@deleted": {"description": "Deleted item message", "placeholders": {"title": {"type": "String", "description": "Item title"}}}, "shareFunction": "Share function: {title}", "@shareFunction": {"description": "Share function message", "placeholders": {"title": {"type": "String", "description": "Item title"}}}, "createNewContent": "Create New Content", "@createNewContent": {"description": "Create new content title"}, "selectContentType": "Select the type of content you want to create", "@selectContentType": {"description": "Create content type selection description"}, "bold": "**Bold**", "@bold": {"description": "Bold markdown syntax"}, "italic": "*Italic*", "@italic": {"description": "Italic markdown syntax"}, "heading1": "# Heading 1", "@heading1": {"description": "Heading 1 markdown syntax"}, "heading2": "## Heading 2", "@heading2": {"description": "Heading 2 markdown syntax"}, "heading3": "### Heading 3", "@heading3": {"description": "Heading 3 markdown syntax"}, "list": "- List item\n- List item", "@list": {"description": "List markdown syntax"}, "link": "[Link text](URL)", "@link": {"description": "Link markdown syntax"}, "image": "![Image description](Image URL)", "@image": {"description": "Image markdown syntax"}, "code": "`Code`", "@code": {"description": "Inline code markdown syntax"}, "codeBlock": "```\nCode block\n```", "@codeBlock": {"description": "Code block markdown syntax"}, "quote": "> Quote text", "@quote": {"description": "Quote markdown syntax"}, "table": "| Column 1 | Column 2 |\n| --- | --- |\n| Content 1 | Content 2 |", "@table": {"description": "Table markdown syntax"}, "myContentLibrary": "My Content Library", "@myContentLibrary": {"description": "My content library title"}, "manageAndBrowseContent": "Manage and browse all your content", "@manageAndBrowseContent": {"description": "Content library description"}, "contentTools": "Content Tools", "@contentTools": {"description": "Content tools section title"}, "recommendedTools": "Recommended Tools", "@recommendedTools": {"description": "Recommended tools section title"}, "markdownTitle": "<PERSON><PERSON>", "@markdownTitle": {"description": "Markdown tool title"}, "markdownDescription": "Document editing and rendering", "@markdownDescription": {"description": "Markdown tool description"}, "textCardsTitle": "Text Cards", "@textCardsTitle": {"description": "Text cards tool title"}, "textCardsDescription": "Knowledge card customization and rendering", "@textCardsDescription": {"description": "Text cards tool description"}, "trafficGuideTitle": "Traffic Guide", "@trafficGuideTitle": {"description": "Traffic guide module title"}, "trafficGuideDescription": "Traffic image and text processing", "@trafficGuideDescription": {"description": "Traffic guide tool description"}, "fileTools": "File Tools", "@fileTools": {"description": "File tools section title"}, "svgTitle": "SVG", "@svgTitle": {"description": "SVG tool title"}, "svgDescription": "Vector graphics processing", "@svgDescription": {"description": "SVG tool description"}, "htmlTitle": "HTML", "@htmlTitle": {"description": "HTML tool title"}, "htmlDescription": "Web content editing", "@htmlDescription": {"description": "HTML tool description"}, "loadingContent": "Loading content...", "@loadingContent": {"description": "Loading content message"}, "languageChangedTo": "Language changed to {language}", "@languageChangedTo": {"description": "Language change success message", "placeholders": {"language": {"type": "String", "description": "Language name"}}}, "developer": "Developer", "@developer": {"description": "Developer settings section title"}, "contentLibraryDemo": "Content Library Demo", "@contentLibraryDemo": {"description": "Content library demo title"}, "viewNewContentLibraryFeatures": "View new content library features", "@viewNewContentLibraryFeatures": {"description": "Content library demo description"}, "i18nDemo": "Internationalization Demo", "@i18nDemo": {"description": "I18n demo title"}, "viewMultiLanguageSupport": "View multi-language support effects", "@viewMultiLanguageSupport": {"description": "I18n demo description"}, "about": "About", "@about": {"description": "About settings section title"}, "versionInfo": "Version Info", "@versionInfo": {"description": "Version info title"}, "helpAndFeedback": "Help & Feedback", "@helpAndFeedback": {"description": "Help and feedback title"}, "getHelpOrProvideFeedback": "Get help or provide feedback", "@getHelpOrProvideFeedback": {"description": "Help and feedback description"}, "helpAndFeedbackContent": "If you have any questions or suggestions, please contact us through the following methods:\n\nEmail: <EMAIL>", "@helpAndFeedbackContent": {"description": "Help and feedback dialog content"}, "selectTheme": "Select Theme", "@selectTheme": {"description": "Select theme dialog title"}, "lightMode": "Light Mode", "@lightMode": {"description": "Light theme mode"}, "darkMode": "Dark Mode", "@darkMode": {"description": "Dark theme mode"}, "systemMode": "Follow System", "@systemMode": {"description": "System theme mode"}, "personalizeYourAppExperience": "Personalize your app experience", "@personalizeYourAppExperience": {"description": "Settings page subtitle"}, "useDefaultInitialText": "Use Default Initial Text", "@useDefaultInitialText": {"description": "Setting to use default initial text"}, "useDefaultInitialTextDescription": "Auto-fill default example content when entering modules", "@useDefaultInitialTextDescription": {"description": "Description for default initial text setting"}, "contentSettings": "Content Settings", "@contentSettings": {"description": "Content settings section title"}, "trafficGuideImageGenerator": "Image Generator", "@trafficGuideImageGenerator": {"description": "Image generator tool title"}, "trafficGuideImageGeneratorSubtitle": "Generate images for traffic across platforms", "@trafficGuideImageGeneratorSubtitle": {"description": "Subtitle for image generator"}, "trafficGuideTabText": "Text", "@trafficGuideTabText": {"description": "Text tab label"}, "trafficGuideTabTemplate": "Template", "@trafficGuideTabTemplate": {"description": "Template tab label"}, "trafficGuideTabEffects": "Effects", "@trafficGuideTabEffects": {"description": "Effects tab label"}, "trafficGuideTextContent": "Text Content", "@trafficGuideTextContent": {"description": "Text content field label"}, "trafficGuideTextHint": "Enter text to display...", "@trafficGuideTextHint": {"description": "Text content field hint"}, "trafficGuideFontSettings": "Font Settings", "@trafficGuideFontSettings": {"description": "Font settings section title"}, "trafficGuideFontSize": "Font Size", "@trafficGuideFontSize": {"description": "Font size setting"}, "trafficGuideColorSettings": "Color Settings", "@trafficGuideColorSettings": {"description": "Color settings section title"}, "trafficGuideTextColor": "Text Color", "@trafficGuideTextColor": {"description": "Text color field label"}, "trafficGuideBackgroundColor": "Background Color", "@trafficGuideBackgroundColor": {"description": "Background color field label"}, "trafficGuideVisualEffects": "Visual Effects", "@trafficGuideVisualEffects": {"description": "Visual effects section title"}, "trafficGuideNoiseLevel": "Noise Level", "@trafficGuideNoiseLevel": {"description": "Noise level slider label"}, "trafficGuideDistortionLevel": "Distortion Level", "@trafficGuideDistortionLevel": {"description": "Distortion level slider label"}, "trafficGuideAddWatermark": "Add Watermark", "@trafficGuideAddWatermark": {"description": "Add watermark checkbox label"}, "trafficGuideWatermarkText": "Watermark Text", "@trafficGuideWatermarkText": {"description": "Watermark text field label"}, "trafficGuideWatermarkHint": "Enter watermark text...", "@trafficGuideWatermarkHint": {"description": "Watermark hint text"}, "trafficGuideExport": "Export", "@trafficGuideExport": {"description": "Export button text"}, "trafficGuideSelectTemplateFirst": "Please select a template first", "@trafficGuideSelectTemplateFirst": {"description": "Template selection error"}, "trafficGuideImageSavedSuccess": "Image saved successfully", "@trafficGuideImageSavedSuccess": {"description": "Image save success message"}, "trafficGuideSaveFailed": "Save failed: {error}", "@trafficGuideSaveFailed": {"description": "Save error message"}, "trafficGuidePermissionPermanentlyDenied": "Permission permanently denied", "@trafficGuidePermissionPermanentlyDenied": {"description": "Permission permanently denied error"}, "trafficGuidePermissionRequired": "Permission required", "@trafficGuidePermissionRequired": {"description": "Permission required error"}, "trafficGuideSaveFailedWithMessage": "Save failed: {message}", "@trafficGuideSaveFailedWithMessage": {"description": "Save failed with message error"}, "trafficGuideSaveFailedEmptyResult": "Save failed: empty result", "@trafficGuideSaveFailedEmptyResult": {"description": "Save failed empty result error"}, "markdownPreview": "Preview", "@markdownPreview": {"description": "Preview tab label"}, "markdownContentLabel": "Markdown Content", "@markdownContentLabel": {"description": "Markdown content label"}, "markdownRenderModeLabel": "Render Mode:", "@markdownRenderModeLabel": {"description": "Render mode label"}, "markdownNormalMode": "Normal Mode", "@markdownNormalMode": {"description": "Normal render mode"}, "markdownBlockMode": "Block Mode", "@markdownBlockMode": {"description": "Block render mode"}, "markdownConfigTab": "Config", "@markdownConfigTab": {"description": "Configuration tab label"}, "markdownManageTab": "Manage", "@markdownManageTab": {"description": "Management tab label"}, "markdownPreviewTab": "Preview", "@markdownPreviewTab": {"description": "Preview tab label"}, "markdownBlockInfo": "Block Information", "@markdownBlockInfo": {"description": "Block information section title"}, "markdownTotalBlocks": "Total Blocks", "@markdownTotalBlocks": {"description": "Total blocks count label"}, "markdownVisibleBlocks": "Visible Blocks", "@markdownVisibleBlocks": {"description": "Visible blocks count label"}, "markdownEditorTitle": "Markdown Editor", "@markdownEditorTitle": {"description": "Markdown editor title"}, "markdownPreviewTitle": "Markdown Preview", "@markdownPreviewTitle": {"description": "Markdown preview title"}, "markdownTitleLabel": "Title", "@markdownTitleLabel": {"description": "Title field label"}, "markdownSubtitleLabel": "Subtitle (Optional)", "@markdownSubtitleLabel": {"description": "Subtitle field label"}, "markdownUntitledDocument": "Untitled Document", "@markdownUntitledDocument": {"description": "Default document title"}, "markdownUntitledSection": "Untitled Section", "@markdownUntitledSection": {"description": "Default section title"}, "markdownSplitSections": "Split Sections", "@markdownSplitSections": {"description": "Split sections button text"}, "markdownSaveDocument": "Save Document", "@markdownSaveDocument": {"description": "Save document button text in markdown module"}, "markdownActionOptions": "Action Options", "@markdownActionOptions": {"description": "Action options title in markdown module"}, "markdownShareImage": "Share Image", "@markdownShareImage": {"description": "Share image button text in markdown module"}, "markdownCopyContent": "Copy Content", "@markdownCopyContent": {"description": "Copy content option"}, "markdownSaveToAlbum": "Save to Album", "@markdownSaveToAlbum": {"description": "Save to album button text in markdown module"}, "commonCancel": "Cancel", "@commonCancel": {"description": "Cancel button text"}, "commonReset": "Reset", "@commonReset": {"description": "Reset button text"}, "commonSelectAll": "Select All", "@commonSelectAll": {"description": "Select all button text"}, "commonDeselectAll": "Deselect All", "@commonDeselectAll": {"description": "Deselect all button text"}, "markdownShowSelected": "Show Selected", "@markdownShowSelected": {"description": "Show selected blocks button text in markdown module"}, "markdownHideSelected": "<PERSON>de Selected", "@markdownHideSelected": {"description": "Hide selected blocks button text in markdown module"}, "markdownExportSelected": "Export Selected", "@markdownExportSelected": {"description": "Export selected blocks button text in markdown module"}, "markdownHideBlock": "Hide Block", "@markdownHideBlock": {"description": "Hide block action text in markdown module"}, "markdownShowBlock": "Show Block", "@markdownShowBlock": {"description": "Show block action text in markdown module"}, "markdownExportAsImage": "Export as Image", "@markdownExportAsImage": {"description": "Export as image option in markdown module"}, "markdownExportAsMarkdown": "Export as <PERSON><PERSON>", "@markdownExportAsMarkdown": {"description": "Export as markdown option in markdown module"}, "commonGotIt": "Got it", "@commonGotIt": {"description": "Got it button text"}, "markdownBlockRenderSettings": "Block Render Settings", "@markdownBlockRenderSettings": {"description": "Block render settings title"}, "markdownBasicSettings": "Basic Settings", "@markdownBasicSettings": {"description": "Basic settings section title"}, "markdownEnableBlockRender": "Enable Block Rendering", "@markdownEnableBlockRender": {"description": "Enable block rendering option"}, "markdownSeparatorSettings": "Separator <PERSON>s", "@markdownSeparatorSettings": {"description": "Separator settings section title"}, "markdownSplitByH1": "Split by H1 Headers", "@markdownSplitByH1": {"description": "Split by H1 headers option"}, "markdownSplitByH2": "Split by H2 Headers", "@markdownSplitByH2": {"description": "Split by H2 headers option"}, "markdownCustomSeparatorPattern": "Custom Separator Pattern (Regex)", "@markdownCustomSeparatorPattern": {"description": "Custom separator pattern label"}, "markdownAppearanceSettings": "Appearance Settings", "@markdownAppearanceSettings": {"description": "Appearance settings section title"}, "markdownBlockSpacing": "Block Spacing", "@markdownBlockSpacing": {"description": "Block spacing setting"}, "markdownSectionSplitSettings": "Section Split Settings", "@markdownSectionSplitSettings": {"description": "Section split settings title"}, "markdownSplitByHorizontalRule": "Split by Horizontal Rule", "@markdownSplitByHorizontalRule": {"description": "Split by horizontal rule option"}, "markdownMaxSectionLength": "Maximum Section Length", "@markdownMaxSectionLength": {"description": "Maximum section length setting"}, "commonUnlimited": "Unlimited", "@commonUnlimited": {"description": "Unlimited option"}, "markdownSetMaxSectionLength": "Set Maximum Section Length", "@markdownSetMaxSectionLength": {"description": "Set maximum section length dialog title"}, "markdownMaxCharacters": "Maximum Characters", "@markdownMaxCharacters": {"description": "Maximum characters field label"}, "markdownLeaveEmptyUnlimited": "Leave empty for unlimited", "@markdownLeaveEmptyUnlimited": {"description": "Helper text for unlimited characters"}, "templateSimpleName": "Simple", "@templateSimpleName": {"description": "Simple template name"}, "templateSimpleDescription": "Clean and minimalist design style", "@templateSimpleDescription": {"description": "Simple template description"}, "templateModernName": "Modern", "@templateModernName": {"description": "Modern template name"}, "templateModernDescription": "Modern design style with shadow effects", "@templateModernDescription": {"description": "Modern template description"}, "templateElegantName": "Elegant", "@templateElegantName": {"description": "Elegant template name"}, "templateElegantDescription": "Elegant design style with thin borders", "@templateElegantDescription": {"description": "Elegant template description"}, "templateCodeName": "Code", "@templateCodeName": {"description": "Code template name"}, "templateCodeDescription": "Dark theme suitable for code display", "@templateCodeDescription": {"description": "Code template description"}, "templateCardName": "Card", "@templateCardName": {"description": "Card template name"}, "templateCardDescription": "Social media card style design", "@templateCardDescription": {"description": "Card template description"}, "templateMorandiName": "<PERSON><PERSON>", "@templateMorandiName": {"description": "Morandi template name"}, "templateMorandiDescription": "Premium Morandi color palette, soft and elegant", "@templateMorandiDescription": {"description": "Morandi template description"}, "templateChineseBlueName": "Chinese Blue", "@templateChineseBlueName": {"description": "Chinese blue template name"}, "templateChineseBlueDescription": "Traditional Chinese porcelain color and pattern design", "@templateChineseBlueDescription": {"description": "Chinese blue template description"}, "templateChineseVermilionName": "Chinese Vermilion", "@templateChineseVermilionName": {"description": "Chinese vermilion template name"}, "templateChineseVermilionDescription": "Traditional Chinese vermilion color, elegant and solemn", "@templateChineseVermilionDescription": {"description": "Chinese vermilion template description"}, "templateGradientPurpleName": "Grad<PERSON> Purple", "@templateGradientPurpleName": {"description": "Gradient purple template name"}, "templateGradientPurpleDescription": "Modern purple-blue gradient background, stylish and elegant", "@templateGradientPurpleDescription": {"description": "Gradient purple template description"}, "templateFestiveRedName": "Festive Red", "@templateFestiveRedName": {"description": "Festive red template name"}, "templateFestiveRedDescription": "Festive theme, suitable for occasions like Spring Festival", "@templateFestiveRedDescription": {"description": "Festive red template description"}, "templateBambooSlipName": "Bamboo Slip", "@templateBambooSlipName": {"description": "Bamboo slip template name"}, "templateBambooSlipDescription": "Traditional bamboo slip style, rich in ancient charm", "@templateBambooSlipDescription": {"description": "Bamboo slip template description"}, "watermarkPositionTopLeft": "Top Left", "@watermarkPositionTopLeft": {"description": "Watermark position top left"}, "watermarkPositionTopCenter": "Top Center", "@watermarkPositionTopCenter": {"description": "Watermark position top center"}, "watermarkPositionTopRight": "Top Right", "@watermarkPositionTopRight": {"description": "Watermark position top right"}, "watermarkPositionBottomLeft": "Bottom Left", "@watermarkPositionBottomLeft": {"description": "Watermark position bottom left"}, "watermarkPositionBottomCenter": "Bottom Center", "@watermarkPositionBottomCenter": {"description": "Watermark position bottom center"}, "watermarkPositionBottomRight": "Bottom Right", "@watermarkPositionBottomRight": {"description": "Watermark position bottom right"}, "watermarkPositionTiled": "Tiled", "@watermarkPositionTiled": {"description": "Watermark position tiled"}, "markdownEnterContentFirst": "Please enter Markdown content first", "@markdownEnterContentFirst": {"description": "Error message for empty content"}, "markdownSplitSuccess": "Successfully split into {count} sections", "@markdownSplitSuccess": {"description": "Success message for splitting sections", "placeholders": {"count": {"type": "int", "description": "Number of sections"}}}, "markdownSplitError": "Error splitting sections: {error}", "@markdownSplitError": {"description": "Error message for splitting sections", "placeholders": {"error": {"type": "String", "description": "Error details"}}}, "markdownNoSectionsToPreview": "No sections to preview", "@markdownNoSectionsToPreview": {"description": "Error message for no sections"}, "markdownSplitContentFirst": "Please split Markdown content first", "@markdownSplitContentFirst": {"description": "Error message for unsplitted content"}, "markdownDocumentSaveSuccess": "Document saved successfully", "@markdownDocumentSaveSuccess": {"description": "Success message for saving document"}, "markdownDocumentSaveError": "Failed to save document: {error}", "@markdownDocumentSaveError": {"description": "Error message for saving document", "placeholders": {"error": {"type": "String", "description": "Error details"}}}, "errorStoragePermissionRequired": "Storage permission required to save images", "@errorStoragePermissionRequired": {"description": "Storage permission error message"}, "markdownNoContentToPreview": "No content to preview", "@markdownNoContentToPreview": {"description": "No content to preview message"}, "markdownImageGenerationFailed": "Image generation failed, unable to share", "@markdownImageGenerationFailed": {"description": "Image generation failed message"}, "markdownShareError": "Share failed: {error}", "@markdownShareError": {"description": "Share error message", "placeholders": {"error": {"type": "String", "description": "Error details"}}}, "markdownEnableBlockModeFirst": "Please enable block mode first", "@markdownEnableBlockModeFirst": {"description": "Error message for disabled block mode"}, "markdownNoBlocks": "No blocks available", "@markdownNoBlocks": {"description": "No blocks message"}, "markdownEnableBlockRenderToList": "Enable block rendering to view block list", "@markdownEnableBlockRenderToList": {"description": "Helper text for enabling block rendering"}, "commonContentCopied": "Content copied to clipboard", "@commonContentCopied": {"description": "Content copied message"}, "markdownResetDemo": "Reset Demo", "@markdownResetDemo": {"description": "Reset demo button text"}, "commonHelp": "Help", "@commonHelp": {"description": "Help button text"}, "markdownBlockRenderHelp": "Block Render Help", "@markdownBlockRenderHelp": {"description": "Block render help title"}, "markdownFeatureDescription": "Feature Description:", "@markdownFeatureDescription": {"description": "Feature description label"}, "markdownOperationMethod": "Operation Method:", "@markdownOperationMethod": {"description": "Operation method label"}, "commonTips": "Tips:", "@commonTips": {"description": "Tips label"}, "markdownSectionSettings": "Section Settings", "@markdownSectionSettings": {"description": "Section settings tooltip"}, "markdownSelectTemplate": "Select Template", "@markdownSelectTemplate": {"description": "Select template tooltip"}, "markdownSelectHtmlTemplate": "Select HTML Template", "@markdownSelectHtmlTemplate": {"description": "Select HTML template title"}, "commonPreview": "Preview", "@commonPreview": {"description": "Preview button text"}, "markdownSaveImage": "Save Image", "@markdownSaveImage": {"description": "Save image button text in markdown module"}, "commonShare": "Share", "@commonShare": {"description": "Share button text"}, "commonShowAll": "Show All", "@commonShowAll": {"description": "Show all filter option"}, "commonShowVisibleOnly": "Show Visible Only", "@commonShowVisibleOnly": {"description": "Show visible only filter option"}, "commonSortByIndex": "Sort by Index", "@commonSortByIndex": {"description": "Sort by index option"}, "commonSortByTitle": "Sort by Title", "@commonSortByTitle": {"description": "Sort by title option"}, "commonSortByType": "Sort by Type", "@commonSortByType": {"description": "Sort by type option"}, "commonSortByLength": "Sort by Length", "@commonSortByLength": {"description": "Sort by length option"}, "markdownBlockCount": "{count} blocks", "@markdownBlockCount": {"description": "Block count text", "placeholders": {"count": {"type": "int", "description": "Number of blocks"}}}, "commonCharacterCount": "{count} characters", "@commonCharacterCount": {"description": "Character count text", "placeholders": {"count": {"type": "int", "description": "Number of characters"}}}, "markdownSelectedBlockCount": "{count} blocks selected", "@markdownSelectedBlockCount": {"description": "Selected block count text", "placeholders": {"count": {"type": "int", "description": "Number of selected blocks"}}}, "commonTotal": "Total", "@commonTotal": {"description": "Total label"}, "commonVisible": "Visible", "@commonVisible": {"description": "Visible label"}, "commonHidden": "Hidden", "@commonHidden": {"description": "Hidden label"}, "markdownContentPlaceholder": "Enter Markdown content here...", "@markdownContentPlaceholder": {"description": "Markdown content placeholder"}, "markdownClickSplitButton": "Click split button to split <PERSON><PERSON> into sections", "@markdownClickSplitButton": {"description": "Helper text for splitting content"}, "markdownHorizontalRuleHelper": "Split when encountering three or more -, *, or _ symbols", "@markdownHorizontalRuleHelper": {"description": "Horizontal rule helper text"}, "markdownH1SplitHelper": "Split when encountering # H1 headers", "@markdownH1SplitHelper": {"description": "H1 split helper text"}, "markdownCharacterCount": "{count} characters", "@markdownCharacterCount": {"description": "Character count text", "placeholders": {"count": {"type": "int", "description": "Number of characters"}}}, "markdownAutoSplitHelper": "Automatically split long sections into multiple sections", "@markdownAutoSplitHelper": {"description": "Auto split helper text"}, "markdownSeparatorExample": "Example: Three or more consecutive hyphens", "@markdownSeparatorExample": {"description": "Separator example text"}, "markdownH1SeparatorHelper": "Use # H1 headers as block separators", "@markdownH1SeparatorHelper": {"description": "H1 separator helper text"}, "markdownH2SeparatorHelper": "Use ## H2 headers as block separators", "@markdownH2SeparatorHelper": {"description": "H2 separator helper text"}, "markdownBlockRenderHelper": "When enabled, Markdown content will be displayed in blocks according to set rules", "@markdownBlockRenderHelper": {"description": "Block render helper text"}, "markdownExportBlocks": "Export Blocks", "@markdownExportBlocks": {"description": "Export blocks option in markdown module"}, "markdownGenerateSummary": "Generate Summary Report", "@markdownGenerateSummary": {"description": "Generate summary report option in markdown module"}, "markdownImageExportSuccess": "Image exported: {filePath}", "@markdownImageExportSuccess": {"description": "Image export success message", "placeholders": {"filePath": {"type": "String", "description": "Exported file path"}}}, "markdownExportError": "Export failed: {error}", "@markdownExportError": {"description": "Export error message", "placeholders": {"error": {"type": "String", "description": "Error details"}}}, "markdownMarkdownExportSuccess": "Markdown file exported: {filePath}", "@markdownMarkdownExportSuccess": {"description": "Markdown export success message", "placeholders": {"filePath": {"type": "String", "description": "Exported file path"}}}, "markdownSummaryGenerated": "Summary report generated: {filePath}", "@markdownSummaryGenerated": {"description": "Summary generated message", "placeholders": {"filePath": {"type": "String", "description": "Generated file path"}}}, "markdownSummaryError": "Report generation failed: {error}", "@markdownSummaryError": {"description": "Summary generation error message", "placeholders": {"error": {"type": "String", "description": "Error details"}}}, "markdownGeneratingImage": "Generating image ({current}/{total})", "@markdownGeneratingImage": {"description": "Generating image progress message", "placeholders": {"current": {"type": "int", "description": "Current page number"}, "total": {"type": "int", "description": "Total pages"}}}, "markdownImagesSavedSuccess": "Successfully saved {count} images to album", "@markdownImagesSavedSuccess": {"description": "Images saved success message", "placeholders": {"count": {"type": "int", "description": "Number of images saved"}}}, "templateChineseBlueWatermark": "青花", "@templateChineseBlueWatermark": {"description": "Chinese blue template watermark"}, "templateChineseVermilionWatermark": "赤", "@templateChineseVermilionWatermark": {"description": "Chinese vermilion template watermark"}, "templateFestiveRedWatermark": "福", "@templateFestiveRedWatermark": {"description": "Festive red template watermark"}, "templateBambooSlipWatermark": "竹", "@templateBambooSlipWatermark": {"description": "Bamboo slip template watermark"}, "markdownBlockManagement": "Block Management", "@markdownBlockManagement": {"description": "Block management title"}, "markdownExportOptions": "Export Options", "@markdownExportOptions": {"description": "Export options title in markdown module"}, "commonDeselect": "Deselect", "@commonDeselect": {"description": "Deselect button text"}, "commonSelect": "Select", "@commonSelect": {"description": "Select button text"}, "commonLoading": "Loading...", "@commonLoading": {"description": "Loading text"}, "commonConfirm": "Confirm", "@commonConfirm": {"description": "Confirm button text"}, "commonEdit": "Edit", "@commonEdit": {"description": "Edit button text"}, "commonDelete": "Delete", "@commonDelete": {"description": "Delete button text"}, "commonAdd": "Add", "@commonAdd": {"description": "Add button text"}, "commonRemove": "Remove", "@commonRemove": {"description": "Remove button text"}, "commonApply": "Apply", "@commonApply": {"description": "Apply button text"}, "commonClose": "Close", "@commonClose": {"description": "Close button text"}, "commonOpen": "Open", "@commonOpen": {"description": "Open button text"}, "commonView": "View", "@commonView": {"description": "View button text"}, "commonBrowse": "Browse", "@commonBrowse": {"description": "Browse button text"}, "commonSearch": "Search", "@commonSearch": {"description": "Search button text"}, "commonFilter": "Filter", "@commonFilter": {"description": "Filter button text"}, "commonSort": "Sort", "@commonSort": {"description": "Sort button text"}, "commonRefresh": "Refresh", "@commonRefresh": {"description": "Refresh button text"}, "commonReload": "Reload", "@commonReload": {"description": "Reload button text"}, "commonRetry": "Retry", "@commonRetry": {"description": "Retry button text"}, "commonContinue": "Continue", "@commonContinue": {"description": "Continue button text"}, "commonFinish": "Finish", "@commonFinish": {"description": "Finish button text"}, "commonSkip": "<PERSON><PERSON>", "@commonSkip": {"description": "Skip button text"}, "commonBack": "Back", "@commonBack": {"description": "Back button text"}, "commonNext": "Next", "@commonNext": {"description": "Next button text"}, "commonPrevious": "Previous", "@commonPrevious": {"description": "Previous button text"}, "commonDone": "Done", "@commonDone": {"description": "Done button text"}, "commonStart": "Start", "@commonStart": {"description": "Start button text"}, "commonStop": "Stop", "@commonStop": {"description": "Stop button text"}, "commonPause": "Pause", "@commonPause": {"description": "Pause button text"}, "commonResume": "Resume", "@commonResume": {"description": "Resume button text"}, "commonPlay": "Play", "@commonPlay": {"description": "Play button text"}, "commonMute": "Mute", "@commonMute": {"description": "Mute button text"}, "commonUnmute": "Unmute", "@commonUnmute": {"description": "Unmute button text"}, "commonVolumeUp": "Volume Up", "@commonVolumeUp": {"description": "Volume up button text"}, "commonVolumeDown": "Volume Down", "@commonVolumeDown": {"description": "Volume down button text"}, "commonFullscreen": "Fullscreen", "@commonFullscreen": {"description": "Fullscreen button text"}, "commonExitFullscreen": "Exit Fullscreen", "@commonExitFullscreen": {"description": "Exit fullscreen button text"}, "commonZoomIn": "Zoom In", "@commonZoomIn": {"description": "Zoom in button text"}, "commonZoomOut": "Zoom Out", "@commonZoomOut": {"description": "Zoom out button text"}, "commonZoomReset": "Zoom Reset", "@commonZoomReset": {"description": "Zoom reset button text"}, "commonRotateLeft": "Rotate Left", "@commonRotateLeft": {"description": "Rotate left button text"}, "commonRotateRight": "Rotate Right", "@commonRotateRight": {"description": "Rotate right button text"}, "commonFlipHorizontal": "<PERSON><PERSON>", "@commonFlipHorizontal": {"description": "Flip horizontal button text"}, "commonFlipVertical": "Flip Vertical", "@commonFlipVertical": {"description": "Flip vertical button text"}, "commonCrop": "Crop", "@commonCrop": {"description": "Crop button text"}, "commonResize": "Resize", "@commonResize": {"description": "Resize button text"}, "commonRotate": "Rotate", "@commonRotate": {"description": "Rotate button text"}, "commonFlip": "Flip", "@commonFlip": {"description": "Flip button text"}, "commonMirror": "Mirror", "@commonMirror": {"description": "Mirror button text"}, "commonSkew": "Skew", "@commonSkew": {"description": "Skew button text"}, "commonDistort": "Distort", "@commonDistort": {"description": "Distort button text"}, "commonBlur": "Blur", "@commonBlur": {"description": "Blur button text"}, "commonSharpen": "Sharpen", "@commonSharpen": {"description": "Sharpen button text"}, "commonBrightness": "Brightness", "@commonBrightness": {"description": "Brightness button text"}, "commonContrast": "Contrast", "@commonContrast": {"description": "Contrast button text"}, "commonSaturation": "Saturation", "@commonSaturation": {"description": "Saturation button text"}, "commonHue": "<PERSON><PERSON>", "@commonHue": {"description": "Hue button text"}, "commonGamma": "Gamma", "@commonGamma": {"description": "Gamma button text"}, "commonExposure": "Exposure", "@commonExposure": {"description": "Exposure button text"}, "commonVignette": "Vignette", "@commonVignette": {"description": "Vignette button text"}, "commonGrain": "Grain", "@commonGrain": {"description": "Grain button text"}, "commonNoise": "Noise", "@commonNoise": {"description": "Noise button text"}, "commonPixelate": "Pixelate", "@commonPixelate": {"description": "Pixelate button text"}, "commonPosterize": "Post<PERSON>ze", "@commonPosterize": {"description": "Posterize button text"}, "commonDither": "<PERSON><PERSON>", "@commonDither": {"description": "Dither button text"}, "commonThreshold": "<PERSON><PERSON><PERSON><PERSON>", "@commonThreshold": {"description": "Threshold button text"}, "commonQuantize": "Quantize", "@commonQuantize": {"description": "Quantize button text"}, "commonDesaturate": "Desaturate", "@commonDesaturate": {"description": "Desaturate button text"}, "commonSaturate": "Saturate", "@commonSaturate": {"description": "Saturate button text"}, "commonInvert": "Invert", "@commonInvert": {"description": "Invert button text"}, "commonGrayscale": "Grayscale", "@commonGrayscale": {"description": "Grayscale button text"}, "commonSepia": "Sepia", "@commonSepia": {"description": "Sepia button text"}, "commonVintage": "Vintage", "@commonVintage": {"description": "Vintage button text"}, "commonRetro": "Retro", "@commonRetro": {"description": "Retro button text"}, "commonBlackAndWhite": "Black and White", "@commonBlackAndWhite": {"description": "Black and white button text"}, "commonCool": "Cool", "@commonCool": {"description": "Cool button text"}, "commonWarm": "Warm", "@commonWarm": {"description": "Warm button text"}, "commonFade": "Fade", "@commonFade": {"description": "Fade button text"}, "commonDuotone": "Duotone", "@commonDuotone": {"description": "Duotone button text"}, "commonTricolor": "Tricolor", "@commonTricolor": {"description": "Tricolor button text"}, "commonMonochrome": "Monochrome", "@commonMonochrome": {"description": "Monochrome button text"}, "commonPolychrome": "Polychrome", "@commonPolychrome": {"description": "Polychrome button text"}, "commonRainbow": "Rainbow", "@commonRainbow": {"description": "Rainbow button text"}, "commonGradient": "Gradient", "@commonGradient": {"description": "Gradient button text"}, "commonPattern": "Pattern", "@commonPattern": {"description": "Pattern button text"}, "commonTexture": "Texture", "@commonTexture": {"description": "Texture button text"}, "commonBorder": "Border", "@commonBorder": {"description": "Border button text"}, "commonFrame": "<PERSON>ame", "@commonFrame": {"description": "Frame button text"}, "commonShadow": "Shadow", "@commonShadow": {"description": "Shadow button text"}, "commonGlow": "Glow", "@commonGlow": {"description": "Glow button text"}, "commonNeon": "Neon", "@commonNeon": {"description": "Neon button text"}, "commonLight": "Light", "@commonLight": {"description": "Light button text"}, "commonDark": "Dark", "@commonDark": {"description": "Dark button text"}, "commonBright": "<PERSON>", "@commonBright": {"description": "Bright button text"}, "commonDim": "<PERSON><PERSON>", "@commonDim": {"description": "Dim button text"}, "commonClear": "Clear", "@commonClear": {"description": "Clear button text"}, "commonCloudy": "Cloudy", "@commonCloudy": {"description": "Cloudy button text"}, "commonFoggy": "Foggy", "@commonFoggy": {"description": "Foggy button text"}, "commonHazy": "Hazy", "@commonHazy": {"description": "Hazy button text"}, "commonSmoky": "Smoky", "@commonSmoky": {"description": "Smoky button text"}, "commonDusty": "<PERSON>", "@commonDusty": {"description": "Dusty button text"}, "commonMisty": "<PERSON>", "@commonMisty": {"description": "Misty button text"}, "commonFrosty": "<PERSON><PERSON>", "@commonFrosty": {"description": "Frosty button text"}, "commonIcy": "<PERSON><PERSON>", "@commonIcy": {"description": "Icy button text"}, "commonSnowy": "Snowy", "@commonSnowy": {"description": "Snowy button text"}, "commonRainy": "Rainy", "@commonRainy": {"description": "Rainy button text"}, "commonStormy": "Stormy", "@commonStormy": {"description": "Stormy button text"}, "commonWindy": "Windy", "@commonWindy": {"description": "Windy button text"}, "commonBreezy": "Breezy", "@commonBreezy": {"description": "Breezy button text"}, "commonCalm": "Calm", "@commonCalm": {"description": "Calm button text"}, "commonStill": "Still", "@commonStill": {"description": "Still button text"}, "commonQuiet": "Quiet", "@commonQuiet": {"description": "Quiet button text"}, "commonSilent": "Silent", "@commonSilent": {"description": "Silent button text"}, "commonPeaceful": "Peaceful", "@commonPeaceful": {"description": "Peaceful button text"}, "commonSerene": "<PERSON><PERSON>", "@commonSerene": {"description": "Serene button text"}, "commonTranquil": "Tranquil", "@commonTranquil": {"description": "Tranquil button text"}, "commonPlacid": "Placid", "@commonPlacid": {"description": "Placid button text"}, "commonSmooth": "Smooth", "@commonSmooth": {"description": "Smooth button text"}, "commonRough": "<PERSON>", "@commonRough": {"description": "Rough button text"}, "commonCoarse": "<PERSON><PERSON><PERSON>", "@commonCoarse": {"description": "Coarse button text"}, "commonFine": "Fine", "@commonFine": {"description": "Fine button text"}, "commonSoft": "Soft", "@commonSoft": {"description": "Soft button text"}, "commonHard": "Hard", "@commonHard": {"description": "Hard button text"}, "commonTough": "Tough", "@commonTough": {"description": "Tough button text"}, "commonStrong": "Strong", "@commonStrong": {"description": "Strong button text"}, "commonWeak": "Weak", "@commonWeak": {"description": "Weak button text"}, "commonGentle": "Gentle", "@commonGentle": {"description": "Gentle button text"}, "commonMild": "Mild", "@commonMild": {"description": "Mild button text"}, "commonHarsh": "Hars<PERSON>", "@commonHarsh": {"description": "Harsh button text"}, "commonSevere": "Severe", "@commonSevere": {"description": "Severe button text"}, "commonExtreme": "Extreme", "@commonExtreme": {"description": "Extreme button text"}, "commonIntense": "Intense", "@commonIntense": {"description": "Intense button text"}, "commonModerate": "Moderate", "@commonModerate": {"description": "Moderate button text"}, "commonAverage": "Average", "@commonAverage": {"description": "Average button text"}, "commonNormal": "Normal", "@commonNormal": {"description": "Normal button text"}, "commonStandard": "Standard", "@commonStandard": {"description": "Standard button text"}, "commonRegular": "Regular", "@commonRegular": {"description": "Regular button text"}, "commonTypical": "Typical", "@commonTypical": {"description": "Typical button text"}, "commonUsual": "Usual", "@commonUsual": {"description": "Usual button text"}, "commonCommon": "Common", "@commonCommon": {"description": "Common button text"}, "commonOrdinary": "Ordinary", "@commonOrdinary": {"description": "Ordinary button text"}, "commonGeneral": "General", "@commonGeneral": {"description": "General button text"}, "commonBasic": "Basic", "@commonBasic": {"description": "Basic button text"}, "commonSimple": "Simple", "@commonSimple": {"description": "Simple button text"}, "commonEasy": "Easy", "@commonEasy": {"description": "Easy button text"}, "commonDifficult": "<PERSON><PERSON><PERSON><PERSON>", "@commonDifficult": {"description": "Difficult button text"}, "commonComplex": "Complex", "@commonComplex": {"description": "Complex button text"}, "commonComplicated": "Complicated", "@commonComplicated": {"description": "Complicated button text"}, "commonAdvanced": "Advanced", "@commonAdvanced": {"description": "Advanced button text"}, "commonExpert": "Expert", "@commonExpert": {"description": "Expert button text"}, "commonProfessional": "Professional", "@commonProfessional": {"description": "Professional button text"}, "commonSpecialized": "Specialized", "@commonSpecialized": {"description": "Specialized button text"}, "commonTechnical": "Technical", "@commonTechnical": {"description": "Technical button text"}, "commonScientific": "Scientific", "@commonScientific": {"description": "Scientific button text"}, "commonAcademic": "Academic", "@commonAcademic": {"description": "Academic button text"}, "commonEducational": "Educational", "@commonEducational": {"description": "Educational button text"}, "commonInstructional": "Instructional", "@commonInstructional": {"description": "Instructional button text"}, "commonTutorial": "Tutorial", "@commonTutorial": {"description": "Tutorial button text"}, "commonGuide": "Guide", "@commonGuide": {"description": "Guide button text"}, "commonManual": "Manual", "@commonManual": {"description": "Manual button text"}, "commonHandbook": "Handbook", "@commonHandbook": {"description": "Handbook button text"}, "commonReference": "Reference", "@commonReference": {"description": "Reference button text"}, "commonDocumentation": "Documentation", "@commonDocumentation": {"description": "Documentation button text"}, "commonSupport": "Support", "@commonSupport": {"description": "Support button text"}, "commonAssistance": "Assistance", "@commonAssistance": {"description": "Assistance button text"}, "commonAid": "Aid", "@commonAid": {"description": "Aid button text"}, "commonService": "Service", "@commonService": {"description": "Service button text"}, "commonMaintenance": "Maintenance", "@commonMaintenance": {"description": "Maintenance button text"}, "commonRepair": "Repair", "@commonRepair": {"description": "Repair button text"}, "commonFix": "Fix", "@commonFix": {"description": "Fix button text"}, "commonSolve": "Solve", "@commonSolve": {"description": "Solve button text"}, "commonResolve": "Resolve", "@commonResolve": {"description": "Resolve button text"}, "commonAddress": "Address", "@commonAddress": {"description": "Address button text"}, "commonHandle": "<PERSON><PERSON>", "@commonHandle": {"description": "Handle button text"}, "commonManage": "Manage", "@commonManage": {"description": "Manage button text"}, "commonControl": "Control", "@commonControl": {"description": "Control button text"}, "commonDirect": "Direct", "@commonDirect": {"description": "Direct button text"}, "commonLead": "Lead", "@commonLead": {"description": "Lead button text"}, "commonConduct": "Conduct", "@commonConduct": {"description": "Conduct button text"}, "commonOperate": "Operate", "@commonOperate": {"description": "Operate button text"}, "commonRun": "Run", "@commonRun": {"description": "Run button text"}, "commonExecute": "Execute", "@commonExecute": {"description": "Execute button text"}, "commonPerform": "Perform", "@commonPerform": {"description": "Perform button text"}, "commonImplement": "Implement", "@commonImplement": {"description": "Implement button text"}, "commonCarryOut": "Carry Out", "@commonCarryOut": {"description": "Carry out button text"}, "commonAccomplish": "Accomplish", "@commonAccomplish": {"description": "Accomplish button text"}, "commonAchieve": "Achieve", "@commonAchieve": {"description": "Achieve button text"}, "commonAttain": "<PERSON><PERSON>", "@commonAttain": {"description": "Attain button text"}, "commonReach": "Reach", "@commonReach": {"description": "Reach button text"}, "commonObtain": "Obtain", "@commonObtain": {"description": "Obtain button text"}, "commonGet": "Get", "@commonGet": {"description": "Get button text"}, "commonAcquire": "Acquire", "@commonAcquire": {"description": "Acquire button text"}, "commonGain": "<PERSON><PERSON>", "@commonGain": {"description": "Gain button text"}, "commonReceive": "Receive", "@commonReceive": {"description": "Receive button text"}, "commonCollect": "Collect", "@commonCollect": {"description": "Collect button text"}, "commonGather": "<PERSON><PERSON>", "@commonGather": {"description": "Gather button text"}, "commonAssemble": "Assemble", "@commonAssemble": {"description": "Assemble button text"}, "commonCompile": "Compile", "@commonCompile": {"description": "Compile button text"}, "commonCombine": "Combine", "@commonCombine": {"description": "Combine button text"}, "commonMerge": "<PERSON><PERSON>", "@commonMerge": {"description": "Merge button text"}, "commonJoin": "Join", "@commonJoin": {"description": "Join button text"}, "commonUnite": "Unite", "@commonUnite": {"description": "Unite button text"}, "commonConnect": "Connect", "@commonConnect": {"description": "Connect button text"}, "commonLink": "Link", "@commonLink": {"description": "Link button text"}, "commonAttach": "Attach", "@commonAttach": {"description": "Attach button text"}, "commonFasten": "<PERSON><PERSON>", "@commonFasten": {"description": "Fasten button text"}, "commonSecure": "Secure", "@commonSecure": {"description": "Secure button text"}, "commonTie": "Tie", "@commonTie": {"description": "Tie button text"}, "commonBind": "Bind", "@commonBind": {"description": "Bind button text"}, "commonWrap": "Wrap", "@commonWrap": {"description": "Wrap button text"}, "commonCover": "Cover", "@commonCover": {"description": "Cover button text"}, "commonEnclose": "Enclose", "@commonEnclose": {"description": "Enclose button text"}, "commonSurround": "Surround", "@commonSurround": {"description": "Surround button text"}, "commonEnvelop": "Envelop", "@commonEnvelop": {"description": "Envelop button text"}, "commonContain": "Contain", "@commonContain": {"description": "Contain button text"}, "commonInclude": "Include", "@commonInclude": {"description": "Include button text"}, "commonInvolve": "Involve", "@commonInvolve": {"description": "Involve button text"}, "commonEmbrace": "Em<PERSON>ce", "@commonEmbrace": {"description": "Embrace button text"}, "commonEncompass": "Encompass", "@commonEncompass": {"description": "Encompass button text"}, "commonSpan": "Span", "@commonSpan": {"description": "Span button text"}, "commonExtend": "Extend", "@commonExtend": {"description": "Extend button text"}, "commonStretch": "<PERSON><PERSON><PERSON>", "@commonStretch": {"description": "Stretch button text"}, "commonExpand": "Expand", "@commonExpand": {"description": "Expand button text"}, "commonGrow": "Grow", "@commonGrow": {"description": "Grow button text"}, "commonIncrease": "Increase", "@commonIncrease": {"description": "Increase button text"}, "commonEnlarge": "Enlarge", "@commonEnlarge": {"description": "Enlarge button text"}, "commonMagnify": "Magnify", "@commonMagnify": {"description": "Magnify button text"}, "commonAmplify": "Amplify", "@commonAmplify": {"description": "Amplify button text"}, "commonBoost": "Boost", "@commonBoost": {"description": "Boost button text"}, "commonEnhance": "<PERSON><PERSON>ce", "@commonEnhance": {"description": "Enhance button text"}, "commonImprove": "Improve", "@commonImprove": {"description": "Improve button text"}, "commonBetter": "Better", "@commonBetter": {"description": "Better button text"}, "commonUpgrade": "Upgrade", "@commonUpgrade": {"description": "Upgrade button text"}, "commonAdvance": "Advance", "@commonAdvance": {"description": "Advance button text"}, "commonProgress": "Progress", "@commonProgress": {"description": "Progress button text"}, "commonDevelop": "Develop", "@commonDevelop": {"description": "Develop button text"}, "commonEvolve": "Evolve", "@commonEvolve": {"description": "Evolve button text"}, "commonMature": "Mature", "@commonMature": {"description": "Mature button text"}, "commonRipe": "R<PERSON>e", "@commonRipe": {"description": "Ripe button text"}, "commonPerfect": "Perfect", "@commonPerfect": {"description": "Perfect button text"}, "complete": "Complete", "@complete": {"description": "Complete button text"}, "commonComplete": "Complete", "@commonComplete": {"description": "Complete button text"}, "commonEnd": "End", "@commonEnd": {"description": "End button text"}, "commonTerminate": "Terminate", "@commonTerminate": {"description": "Terminate button text"}, "commonConclude": "Conclude", "@commonConclude": {"description": "Conclude button text"}, "commonFinalize": "Finalize", "@commonFinalize": {"description": "Finalize button text"}, "commonShut": "Shut", "@commonShut": {"description": "Shut button text"}, "commonSeal": "Seal", "@commonSeal": {"description": "Seal button text"}, "commonLock": "Lock", "@commonLock": {"description": "Lock button text"}, "commonTighten": "<PERSON>ighten", "@commonTighten": {"description": "Tighten button text"}, "commonOrganize": "Organize", "@commonOrganize": {"description": "Organize button text"}, "commonArrange": "<PERSON><PERSON><PERSON>", "@commonArrange": {"description": "Arrange button text"}, "commonOrder": "Order", "@commonOrder": {"description": "Order button text"}, "commonClassify": "Classify", "@commonClassify": {"description": "Classify button text"}, "commonCategorize": "Categorize", "@commonCategorize": {"description": "Categorize button text"}, "commonGroup": "Group", "@commonGroup": {"description": "Group button text"}, "commonCluster": "Cluster", "@commonCluster": {"description": "Cluster button text"}, "commonBunch": "Bunch", "@commonBunch": {"description": "Bunch button text"}, "commonBundle": "Bundle", "@commonBundle": {"description": "Bundle button text"}, "commonPack": "Pack", "@commonPack": {"description": "Pack button text"}, "commonPackage": "Package", "@commonPackage": {"description": "Package button text"}, "commonHold": "Hold", "@commonHold": {"description": "Hold button text"}, "commonCarry": "Carry", "@commonCarry": {"description": "Carry button text"}, "commonBear": "Bear", "@commonBear": {"description": "Bear button text"}, "commonSustain": "<PERSON><PERSON><PERSON>", "@commonSustain": {"description": "Sustain button text"}, "commonMaintain": "Maintain", "@commonMaintain": {"description": "Maintain button text"}, "commonKeep": "Keep", "@commonKeep": {"description": "Keep button text"}, "commonRetain": "<PERSON><PERSON>", "@commonRetain": {"description": "Retain button text"}, "commonPreserve": "Preserve", "@commonPreserve": {"description": "Preserve button text"}, "commonConserve": "Conserve", "@commonConserve": {"description": "Conserve button text"}, "commonSave": "Save", "@commonSave": {"description": "Save button text"}, "commonStore": "Store", "@commonStore": {"description": "Store button text"}, "commonReserve": "Reserve", "@commonReserve": {"description": "Reserve button text"}, "commonSetAside": "Set Aside", "@commonSetAside": {"description": "Set aside button text"}, "commonPutAway": "Put Away", "@commonPutAway": {"description": "Put away button text"}, "commonPlace": "Place", "@commonPlace": {"description": "Place button text"}, "commonPosition": "Position", "@commonPosition": {"description": "Position button text"}, "commonLocate": "Locate", "@commonLocate": {"description": "Locate button text"}, "commonSituate": "Situate", "@commonSituate": {"description": "Situate button text"}, "commonInstall": "Install", "@commonInstall": {"description": "Install button text"}, "commonSet": "Set", "@commonSet": {"description": "Set button text"}, "commonEstablish": "Establish", "@commonEstablish": {"description": "Establish button text"}, "commonFound": "Found", "@commonFound": {"description": "Found button text"}, "commonCreate": "Create", "@commonCreate": {"description": "Create button text"}, "commonMake": "Make", "@commonMake": {"description": "Make button text"}, "commonBuild": "Build", "@commonBuild": {"description": "Build button text"}, "commonConstruct": "Construct", "@commonConstruct": {"description": "Construct button text"}, "commonForm": "Form", "@commonForm": {"description": "Form button text"}, "commonShape": "<PERSON><PERSON><PERSON>", "@commonShape": {"description": "Shape button text"}, "commonMold": "Mold", "@commonMold": {"description": "Mold button text"}, "commonFashion": "Fashion", "@commonFashion": {"description": "Fashion button text"}, "commonDesign": "Design", "@commonDesign": {"description": "Design button text"}, "commonPlan": "Plan", "@commonPlan": {"description": "Plan button text"}, "commonDevise": "<PERSON><PERSON>", "@commonDevise": {"description": "Devise button text"}, "commonConceive": "Conceive", "@commonConceive": {"description": "Conceive button text"}, "commonImagine": "Imagine", "@commonImagine": {"description": "Imagine button text"}, "commonEnvision": "Envision", "@commonEnvision": {"description": "Envision button text"}, "commonVisualize": "Visualize", "@commonVisualize": {"description": "Visualize button text"}, "commonDream": "Dream", "@commonDream": {"description": "Dream button text"}, "commonThink": "Think", "@commonThink": {"description": "Think button text"}, "commonConsider": "Consider", "@commonConsider": {"description": "Consider button text"}, "commonPonder": "<PERSON><PERSON>", "@commonPonder": {"description": "Ponder button text"}, "commonReflect": "Reflect", "@commonReflect": {"description": "Reflect button text"}, "commonMeditate": "Meditate", "@commonMeditate": {"description": "Meditate button text"}, "commonContemplate": "Contemplate", "@commonContemplate": {"description": "Contemplate button text"}, "commonStudy": "Study", "@commonStudy": {"description": "Study button text"}, "commonLearn": "Learn", "@commonLearn": {"description": "Learn button text"}, "commonDiscover": "Discover", "@commonDiscover": {"description": "Discover button text"}, "commonFind": "Find", "@commonFind": {"description": "Find button text"}, "commonUncover": "Uncover", "@commonUncover": {"description": "Uncover button text"}, "commonReveal": "Reveal", "@commonReveal": {"description": "Reveal button text"}, "commonExpose": "Expose", "@commonExpose": {"description": "Expose button text"}, "commonDisclose": "Disclose", "@commonDisclose": {"description": "Disclose button text"}, "commonDivulge": "Divulge", "@commonDivulge": {"description": "Divulge button text"}, "commonTell": "Tell", "@commonTell": {"description": "Tell button text"}, "commonInform": "Inform", "@commonInform": {"description": "Inform button text"}, "commonNotify": "Notify", "@commonNotify": {"description": "Notify button text"}, "commonAnnounce": "Announce", "@commonAnnounce": {"description": "Announce button text"}, "commonDeclare": "<PERSON><PERSON><PERSON>", "@commonDeclare": {"description": "Declare button text"}, "commonProclaim": "Proclaim", "@commonProclaim": {"description": "Proclaim button text"}, "commonPronounce": "Pronounce", "@commonPronounce": {"description": "Pronounce button text"}, "commonState": "State", "@commonState": {"description": "State button text"}, "commonExpress": "Express", "@commonExpress": {"description": "Express button text"}, "commonVoice": "Voice", "@commonVoice": {"description": "Voice button text"}, "commonArticulate": "Articulate", "@commonArticulate": {"description": "Articulate button text"}, "commonUtter": "<PERSON><PERSON>", "@commonUtter": {"description": "Utter button text"}, "commonSay": "Say", "@commonSay": {"description": "Say button text"}, "commonSpeak": "Speak", "@commonSpeak": {"description": "Speak button text"}, "commonTalk": "Talk", "@commonTalk": {"description": "Talk button text"}, "commonConverse": "Converse", "@commonConverse": {"description": "Converse button text"}, "commonCommunicate": "Communicate", "@commonCommunicate": {"description": "Communicate button text"}, "commonCorrespond": "Correspond", "@commonCorrespond": {"description": "Correspond button text"}, "commonContact": "Contact", "@commonContact": {"description": "Contact button text"}, "commonApproach": "Approach", "@commonApproach": {"description": "Approach button text"}, "commonAccost": "Accost", "@commonAccost": {"description": "Accost button text"}, "commonGreet": "Greet", "@commonGreet": {"description": "Greet button text"}, "commonWelcome": "Welcome", "@commonWelcome": {"description": "Welcome button text"}, "commonAccept": "Accept", "@commonAccept": {"description": "Accept button text"}, "commonTake": "Take", "@commonTake": {"description": "Take button text"}, "commonProcure": "Procure", "@commonProcure": {"description": "Procure button text"}, "commonPurchase": "Purchase", "@commonPurchase": {"description": "Purchase button text"}, "commonBuy": "Buy", "@commonBuy": {"description": "Buy button text"}, "commonShop": "Shop", "@commonShop": {"description": "Shop button text"}, "commonTrade": "Trade", "@commonTrade": {"description": "Trade button text"}, "commonExchange": "Exchange", "@commonExchange": {"description": "Exchange button text"}, "commonSwap": "<PERSON><PERSON><PERSON>", "@commonSwap": {"description": "Swap button text"}, "commonSwitch": "Switch", "@commonSwitch": {"description": "Switch button text"}, "commonChange": "Change", "@commonChange": {"description": "Change button text"}, "commonAlter": "Alter", "@commonAlter": {"description": "Alter button text"}, "commonModify": "Modify", "@commonModify": {"description": "Modify button text"}, "commonAdjust": "Adjust", "@commonAdjust": {"description": "Adjust button text"}, "commonTweak": "Tweak", "@commonTweak": {"description": "Tweak button text"}, "commonFineTune": "Fine Tune", "@commonFineTune": {"description": "Fine tune button text"}, "commonOptimize": "Optimize", "@commonOptimize": {"description": "Optimize button text"}, "commonRefine": "Refine", "@commonRefine": {"description": "Refine button text"}, "commonPolish": "Polish", "@commonPolish": {"description": "Polish button text"}, "commonCease": "Cease", "@commonCease": {"description": "Cease button text"}, "commonHalt": "Halt", "@commonHalt": {"description": "Halt button text"}, "commonBreak": "Break", "@commonBreak": {"description": "Break button text"}, "commonInterrupt": "Interrupt", "@commonInterrupt": {"description": "Interrupt button text"}, "commonSuspend": "Suspend", "@commonSuspend": {"description": "Suspend button text"}, "commonDelay": "Delay", "@commonDelay": {"description": "Delay button text"}, "commonPostpone": "Postpone", "@commonPostpone": {"description": "Postpone button text"}, "commonDefer": "Defer", "@commonDefer": {"description": "Defer button text"}, "commonWait": "Wait", "@commonWait": {"description": "Wait button text"}, "commonRemain": "<PERSON><PERSON><PERSON>", "@commonRemain": {"description": "Remain button text"}, "commonStay": "Stay", "@commonStay": {"description": "Stay button text"}, "commonProceed": "Proceed", "@commonProceed": {"description": "Proceed button text"}, "commonMove": "Move", "@commonMove": {"description": "Move button text"}, "commonGo": "Go", "@commonGo": {"description": "Go button text"}, "commonTravel": "Travel", "@commonTravel": {"description": "Travel button text"}, "commonJourney": "Journey", "@commonJourney": {"description": "Journey button text"}, "commonPass": "Pass", "@commonPass": {"description": "Pass button text"}, "commonCross": "Cross", "@commonCross": {"description": "Cross button text"}, "commonTransit": "Transit", "@commonTransit": {"description": "Transit button text"}, "commonTransfer": "Transfer", "@commonTransfer": {"description": "Transfer button text"}, "commonConvey": "<PERSON><PERSON>", "@commonConvey": {"description": "Convey button text"}, "commonTransport": "Transport", "@commonTransport": {"description": "Transport button text"}, "commonBring": "Bring", "@commonBring": {"description": "Bring button text"}, "commonFetch": "<PERSON>tch", "@commonFetch": {"description": "Fetch button text"}, "commonSalute": "Salute", "@commonSalute": {"description": "Salute button text"}, "commonHail": "<PERSON>l", "@commonHail": {"description": "Hail button text"}, "commonNigh": "<PERSON>gh", "@commonNigh": {"description": "Nigh button text"}, "commonDrawNear": "Draw Near", "@commonDrawNear": {"description": "Draw near button text"}, "commonCome": "Come", "@commonCome": {"description": "Come button text"}, "commonArrive": "Arrive", "@commonArrive": {"description": "Arrive button text"}, "commonLand": "Land", "@commonLand": {"description": "Land button text"}, "commonEnter": "Enter", "@commonEnter": {"description": "Enter button text"}, "commonAccess": "Access", "@commonAccess": {"description": "Access button text"}, "commonGoOn": "Go On", "@commonGoOn": {"description": "Go on button text"}, "commonKeepOn": "Keep On", "@commonKeepOn": {"description": "Keep on button text"}, "commonCarryOn": "Carry On", "@commonCarryOn": {"description": "Carry on button text"}, "commonPersist": "Persist", "@commonPersist": {"description": "Persist button text"}, "commonPersevere": "Persevere", "@commonPersevere": {"description": "Persevere button text"}, "commonEndure": "Endure", "@commonEndure": {"description": "Endure button text"}, "commonLast": "Last", "@commonLast": {"description": "Last button text"}, "markdownSaveButton": "Save", "@markdownSaveButton": {"description": "Save button text in markdown module"}, "markdownCancelButton": "Cancel", "@markdownCancelButton": {"description": "Cancel button text in markdown module"}, "markdownConfirmButton": "Confirm", "@markdownConfirmButton": {"description": "Confirm button text in markdown module"}, "markdownResetButton": "Reset", "@markdownResetButton": {"description": "Reset button text in markdown module"}, "markdownApplyButton": "Apply", "@markdownApplyButton": {"description": "Apply button text in markdown module"}, "markdownCloseButton": "Close", "@markdownCloseButton": {"description": "Close button text in markdown module"}, "markdownSelectButton": "Select", "@markdownSelectButton": {"description": "Select button text in markdown module"}, "markdownBrowseButton": "Browse", "@markdownBrowseButton": {"description": "Browse button text in markdown module"}, "markdownSearchButton": "Search", "@markdownSearchButton": {"description": "Search button text in markdown module"}, "markdownClearButton": "Clear", "@markdownClearButton": {"description": "Clear button text in markdown module"}, "markdownDeleteButton": "Delete", "@markdownDeleteButton": {"description": "Delete button text in markdown module"}, "markdownEditButton": "Edit", "@markdownEditButton": {"description": "Edit button text in markdown module"}, "markdownExportButton": "Export", "@markdownExportButton": {"description": "Export button text in markdown module"}, "markdownImportButton": "Import", "@markdownImportButton": {"description": "Import button text in markdown module"}, "markdownShareButton": "Share", "@markdownShareButton": {"description": "Share button text in markdown module"}, "markdownCopyButton": "Copy", "@markdownCopyButton": {"description": "Copy button text in markdown module"}, "markdownPasteButton": "Paste", "@markdownPasteButton": {"description": "Paste button text in markdown module"}, "markdownCutButton": "Cut", "@markdownCutButton": {"description": "Cut button text in markdown module"}, "markdownUndoButton": "Undo", "@markdownUndoButton": {"description": "Undo button text in markdown module"}, "markdownRedoButton": "Redo", "@markdownRedoButton": {"description": "Redo button text in markdown module"}, "markdownEditTab": "Edit", "@markdownEditTab": {"description": "Edit tab label in markdown editor"}, "markdownTemplateTab": "Template", "@markdownTemplateTab": {"description": "Template tab label in markdown editor"}, "markdownStyleTab": "Style", "@markdownStyleTab": {"description": "Style tab label in markdown editor"}, "markdownWatermarkTab": "Watermark", "@markdownWatermarkTab": {"description": "Watermark tab label in markdown editor"}, "markdownBlockTab": "Block", "@markdownBlockTab": {"description": "Block tab label in markdown editor"}, "markdownTemplateSelector": "Template Selector", "@markdownTemplateSelector": {"description": "Template selector title in markdown module"}, "markdownStyleSelector": "Style Selector", "@markdownStyleSelector": {"description": "Style selector title in markdown module"}, "markdownWatermarkSettings": "Watermark Settings", "@markdownWatermarkSettings": {"description": "Watermark settings title in markdown module"}, "markdownBlockSettings": "Block Settings", "@markdownBlockSettings": {"description": "Block settings title in markdown module"}, "markdownBlockConfigPanel": "Block Configuration Panel", "@markdownBlockConfigPanel": {"description": "Block configuration panel title in markdown module"}, "markdownBlockManagerPanel": "Block Management Panel", "@markdownBlockManagerPanel": {"description": "Block management panel title in markdown module"}, "markdownTextLabel": "Text", "@markdownTextLabel": {"description": "Text field label in markdown module"}, "markdownMarkdownContent": "Markdown Content", "@markdownMarkdownContent": {"description": "Markdown content field label in markdown module"}, "markdownWatermarkText": "Watermark Text", "@markdownWatermarkText": {"description": "Watermark text field label in markdown module"}, "markdownEnterWatermarkText": "Enter watermark text", "@markdownEnterWatermarkText": {"description": "Watermark text input hint in markdown module"}, "markdownEnterMarkdownContent": "Enter Markdown content...", "@markdownEnterMarkdownContent": {"description": "Markdown content input hint in markdown module"}, "markdownFontSettings": "Font Settings", "@markdownFontSettings": {"description": "Font settings section title in markdown module"}, "markdownFontSize": "Font Size", "@markdownFontSize": {"description": "Font size label in markdown module"}, "markdownFontFamily": "Font Family", "@markdownFontFamily": {"description": "Font family label in markdown module"}, "markdownCodeFont": "Code Font", "@markdownCodeFont": {"description": "Code font label in markdown module"}, "markdownColorSettings": "Color Settings", "@markdownColorSettings": {"description": "Color settings section title in markdown module"}, "markdownTextColor": "Text Color", "@markdownTextColor": {"description": "Text color label in markdown module"}, "markdownBackgroundColor": "Background Color", "@markdownBackgroundColor": {"description": "Background color label in markdown module"}, "markdownBorderColor": "Border Color", "@markdownBorderColor": {"description": "Border color label in markdown module"}, "markdownBorderWidth": "Border Width", "@markdownBorderWidth": {"description": "Border width label in markdown module"}, "markdownShadowSettings": "Shadow Settings", "@markdownShadowSettings": {"description": "Shadow settings section title in markdown module"}, "markdownShadowColor": "Shadow Color", "@markdownShadowColor": {"description": "Shadow color label in markdown module"}, "markdownBorderRadius": "Border Radius", "@markdownBorderRadius": {"description": "Border radius label in markdown module"}, "markdownPadding": "Padding", "@markdownPadding": {"description": "Padding label in markdown module"}, "markdownMargin": "<PERSON><PERSON>", "@markdownMargin": {"description": "Margin label in markdown module"}, "markdownWatermarkContent": "Watermark Content", "@markdownWatermarkContent": {"description": "Watermark content section title in markdown module"}, "markdownWatermarkTextStyle": "Text Style", "@markdownWatermarkTextStyle": {"description": "Watermark text style label in markdown module"}, "markdownWatermarkNormal": "Normal", "@markdownWatermarkNormal": {"description": "Normal watermark style option in markdown module"}, "markdownWatermarkBold": "Bold", "@markdownWatermarkBold": {"description": "Bold watermark style option in markdown module"}, "markdownWatermarkItalic": "Italic", "@markdownWatermarkItalic": {"description": "Italic watermark style option in markdown module"}, "markdownWatermarkPosition": "Display Position", "@markdownWatermarkPosition": {"description": "Watermark position label in markdown module"}, "markdownWatermarkTextColor": "Text Color", "@markdownWatermarkTextColor": {"description": "Text color label in watermark settings"}, "markdownWatermarkOpacity": "Opacity", "@markdownWatermarkOpacity": {"description": "Opacity slider label in watermark settings"}, "markdownWatermarkFontSize": "Font Size", "@markdownWatermarkFontSize": {"description": "Font size slider label in watermark settings"}, "markdownWatermarkRotation": "Rotation", "@markdownWatermarkRotation": {"description": "Rotation slider label in watermark settings"}, "markdownWatermarkTileSettings": "Tile Settings", "@markdownWatermarkTileSettings": {"description": "Tile settings section title in watermark settings"}, "markdownWatermarkHorizontalSpacing": "Horizontal Spacing", "@markdownWatermarkHorizontalSpacing": {"description": "Watermark horizontal spacing label in markdown module"}, "markdownWatermarkVerticalSpacing": "Vertical Spacing", "@markdownWatermarkVerticalSpacing": {"description": "Watermark vertical spacing label in markdown module"}, "markdownSelectWatermarkColor": "Select Watermark Color", "@markdownSelectWatermarkColor": {"description": "Select watermark color button text in markdown module"}, "markdownResetToAppName": "Reset to App Name", "@markdownResetToAppName": {"description": "Reset to app name button text in markdown module"}, "markdownShowBlockTitle": "Show Block Title", "@markdownShowBlockTitle": {"description": "Show block title option in markdown module"}, "markdownShowBlockBorder": "Show Block Border", "@markdownShowBlockBorder": {"description": "Show block border option in markdown module"}, "markdownSortByIndex": "Sort by Index", "@markdownSortByIndex": {"description": "Sort by index option in markdown module"}, "markdownSortByTitle": "Sort by Title", "@markdownSortByTitle": {"description": "Sort by title option in markdown module"}, "markdownSortByType": "Sort by Type", "@markdownSortByType": {"description": "Sort by type option in markdown module"}, "markdownSortByLength": "Sort by Length", "@markdownSortByLength": {"description": "Sort by length option in markdown module"}, "markdownShareResult": "Share Result", "@markdownShareResult": {"description": "Share result button text in markdown module"}, "markdownExportResult": "Export Result", "@markdownExportResult": {"description": "Export result button text in markdown module"}, "markdownSaveSuccess": "Save Success", "@markdownSaveSuccess": {"description": "Save success message in markdown module"}, "markdownSaveFailed": "Save Failed", "@markdownSaveFailed": {"description": "Save failed message in markdown module"}, "markdownTemplateDescription": "Template Description", "@markdownTemplateDescription": {"description": "Template description label in markdown module"}, "markdownTemplateFeatures": "Template Features", "@markdownTemplateFeatures": {"description": "Template features label in markdown module"}, "markdownBorderStyle": "Border Style", "@markdownBorderStyle": {"description": "Border style label in markdown module"}, "markdownShadowEffect": "Shadow Effect", "@markdownShadowEffect": {"description": "Shadow effect label in markdown module"}, "markdownShowHeader": "Show Header", "@markdownShowHeader": {"description": "Show header option in markdown module"}, "markdownInnerShadow": "Inner Shadow", "@markdownInnerShadow": {"description": "Inner shadow option in markdown module"}, "markdownHeadingAlignment": "Heading Alignment", "@markdownHeadingAlignment": {"description": "Heading alignment label in markdown module"}, "markdownLeftAlign": "Left Align", "@markdownLeftAlign": {"description": "Left align option in markdown module"}, "markdownCenterAlign": "Center Align", "@markdownCenterAlign": {"description": "Center align option in markdown module"}, "markdownRightAlign": "Right Align", "@markdownRightAlign": {"description": "Right align option in markdown module"}, "markdownGradientBackground": "Gradient Background", "@markdownGradientBackground": {"description": "Gradient background option in markdown module"}, "markdownBackgroundPattern": "<PERSON> Pattern", "@markdownBackgroundPattern": {"description": "Background pattern option in markdown module"}, "markdownListItemStyle": "List Item Style", "@markdownListItemStyle": {"description": "List item style label in markdown module"}, "markdownCheckboxStyle": "Checkbox Style", "@markdownCheckboxStyle": {"description": "Checkbox style label in markdown module"}, "markdownMoreActions": "More Actions", "@markdownMoreActions": {"description": "More actions button tooltip in markdown editor"}, "markdownShareImageSubtitle": "Share rendered result with others", "@markdownShareImageSubtitle": {"description": "Share image subtitle in markdown module"}, "markdownCopyContentSubtitle": "Copy Markdown text to clipboard", "@markdownCopyContentSubtitle": {"description": "Copy content subtitle in markdown module"}, "markdownSaveToAlbumSubtitle": "Save image to local album", "@markdownSaveToAlbumSubtitle": {"description": "Save to album subtitle in markdown module"}, "markdownOperationOptions": "Operation Options", "@markdownOperationOptions": {"description": "Operation options title in markdown module"}, "markdownSelectColor": "Select Color", "@markdownSelectColor": {"description": "Select color button text in markdown module"}, "markdownChooseColor": "Choose Color", "@markdownChooseColor": {"description": "Choose color button text in markdown module"}, "markdownColorPicker": "Color Picker", "@markdownColorPicker": {"description": "Color picker title in markdown module"}, "markdownResetSettings": "Reset Settings", "@markdownResetSettings": {"description": "Reset settings button text in markdown module"}, "markdownApplySettings": "Apply Settings", "@markdownApplySettings": {"description": "Apply settings button text in markdown module"}, "markdownLoading": "Loading...", "@markdownLoading": {"description": "Loading message in markdown module"}, "markdownGenerating": "Generating...", "@markdownGenerating": {"description": "Generating message in markdown module"}, "markdownProcessing": "Processing...", "@markdownProcessing": {"description": "Processing message in markdown module"}, "markdownSaving": "Saving...", "@markdownSaving": {"description": "Saving message in markdown module"}, "markdownExporting": "Exporting...", "@markdownExporting": {"description": "Exporting message in markdown module"}, "markdownSharing": "Sharing...", "@markdownSharing": {"description": "Sharing message in markdown module"}, "markdownCopying": "Copying...", "@markdownCopying": {"description": "Copying message in markdown module"}, "markdownSuccess": "Success", "@markdownSuccess": {"description": "Success message in markdown module"}, "markdownError": "Error", "@markdownError": {"description": "Error message in markdown module"}, "markdownWarning": "Warning", "@markdownWarning": {"description": "Warning message in markdown module"}, "markdownInfo": "Info", "@markdownInfo": {"description": "Info message in markdown module"}, "markdownComplete": "Complete", "@markdownComplete": {"description": "Complete message in markdown module"}, "markdownFailed": "Failed", "@markdownFailed": {"description": "Failed message in markdown module"}, "markdownCancelled": "Cancelled", "@markdownCancelled": {"description": "Cancelled message in markdown module"}, "markdownContentSaved": "Content saved to content library", "@markdownContentSaved": {"description": "Content saved message in markdown module"}, "markdownTemplateSelected": "Template \"{name}\" selected", "@markdownTemplateSelected": {"description": "Template selected message in markdown module", "placeholders": {"name": {"type": "String", "description": "Template name"}}}, "markdownSaveError": "Save failed: {error}", "@markdownSaveError": {"description": "Save error message in markdown module", "placeholders": {"error": {"type": "String", "description": "Error details"}}}, "markdownLoadError": "Load error: {error}", "@markdownLoadError": {"description": "Load error message in markdown module", "placeholders": {"error": {"type": "String", "description": "Error details"}}}, "markdownProcessError": "Process error: {error}", "@markdownProcessError": {"description": "Process error message in markdown module", "placeholders": {"error": {"type": "String", "description": "Error details"}}}, "markdownBlockModeEnabled": "Block mode enabled", "@markdownBlockModeEnabled": {"description": "Block mode enabled message in markdown module"}, "markdownBlockModeDisabled": "Block mode disabled", "@markdownBlockModeDisabled": {"description": "Block mode disabled message in markdown module"}, "markdownBlockAdded": "Block added", "@markdownBlockAdded": {"description": "Block added message in markdown module"}, "markdownBlockRemoved": "Block removed", "@markdownBlockRemoved": {"description": "Block removed message in markdown module"}, "markdownBlockUpdated": "Block updated", "@markdownBlockUpdated": {"description": "Block updated message in markdown module"}, "markdownBlockHidden": "Block hidden", "@markdownBlockHidden": {"description": "Block hidden message in markdown module"}, "markdownBlockShown": "Block shown", "@markdownBlockShown": {"description": "Block shown message in markdown module"}, "markdownBlockSelected": "Block selected", "@markdownBlockSelected": {"description": "Block selected message in markdown module"}, "markdownBlockDeselected": "Block deselected", "@markdownBlockDeselected": {"description": "Block deselected message in markdown module"}, "markdownBlockMoved": "Block moved", "@markdownBlockMoved": {"description": "Block moved message in markdown module"}, "markdownBlockResized": "Block resized", "@markdownBlockResized": {"description": "Block resized message in markdown module"}, "markdownBlockReordered": "Block reordered", "@markdownBlockReordered": {"description": "Block reordered message in markdown module"}, "markdownBlockExported": "Block exported", "@markdownBlockExported": {"description": "Block exported message in markdown module"}, "markdownBlockImported": "Block imported", "@markdownBlockImported": {"description": "Block imported message in markdown module"}, "markdownBlockRenderFeature1": "• Block rendering can split long documents into multiple independent blocks", "@markdownBlockRenderFeature1": {"description": "Block render feature description 1 in markdown module"}, "markdownBlockRenderFeature2": "• Each block can be individually displayed or hidden", "@markdownBlockRenderFeature2": {"description": "Block render feature description 2 in markdown module"}, "markdownBlockRenderFeature3": "• Support multiple separation methods: headers, custom separators, manual separation", "@markdownBlockRenderFeature3": {"description": "Block render feature description 3 in markdown module"}, "markdownBlockRenderFeature4": "• Adjust block settings in the left configuration panel", "@markdownBlockRenderFeature4": {"description": "Block render feature description 4 in markdown module"}, "markdownBlockRenderFeature5": "• Click in the preview area to add new separator bars", "@markdownBlockRenderFeature5": {"description": "Block render feature description 5 in markdown module"}, "markdownBlockRenderFeature6": "• Drag separator bars to readjust block positions", "@markdownBlockRenderFeature6": {"description": "Block render feature description 6 in markdown module"}, "markdownBlockRenderFeature7": "• Click the eye icon on block title bars to hide/show blocks", "@markdownBlockRenderFeature7": {"description": "Block render feature description 7 in markdown module"}, "markdownBlockRenderFeature8": "• Different types of blocks are distinguished by different colored borders", "@markdownBlockRenderFeature8": {"description": "Block render feature description 8 in markdown module"}, "markdownBlockRenderFeature9": "• Blue: H1 header blocks", "@markdownBlockRenderFeature9": {"description": "Block render feature description 9 in markdown module"}, "markdownBlockRenderFeature10": "• Green: H2 header blocks", "@markdownBlockRenderFeature10": {"description": "Block render feature description 10 in markdown module"}, "markdownBlockRenderFeature11": "• Orange: Custom separator blocks", "@markdownBlockRenderFeature11": {"description": "Block render feature description 11 in markdown module"}, "markdownBlockRenderFeature12": "• Gray: Manual separator blocks", "@markdownBlockRenderFeature12": {"description": "Block render feature description 12 in markdown module"}, "markdownGotIt": "Got It", "@markdownGotIt": {"description": "Got it button text in markdown module"}, "markdownIKnow": "I Know", "@markdownIKnow": {"description": "I know button text in markdown module"}, "markdownUnderstood": "Understood", "@markdownUnderstood": {"description": "Understood button text in markdown module"}, "markdownAlignLeft": "Left", "@markdownAlignLeft": {"description": "Left align option in markdown module"}, "markdownAlignCenter": "Center", "@markdownAlignCenter": {"description": "Center align option in markdown module"}, "markdownAlignRight": "Right", "@markdownAlignRight": {"description": "Right align option in markdown module"}, "markdownPositionTopLeft": "Top Left", "@markdownPositionTopLeft": {"description": "Top left position option in markdown module"}, "markdownPositionTopCenter": "Top Center", "@markdownPositionTopCenter": {"description": "Top center position option in markdown module"}, "markdownPositionTopRight": "Top Right", "@markdownPositionTopRight": {"description": "Top right position option in markdown module"}, "markdownPositionBottomLeft": "Bottom Left", "@markdownPositionBottomLeft": {"description": "Bottom left position option in markdown module"}, "markdownPositionBottomCenter": "Bottom Center", "@markdownPositionBottomCenter": {"description": "Bottom center position option in markdown module"}, "markdownPositionBottomRight": "Bottom Right", "@markdownPositionBottomRight": {"description": "Bottom right position option in markdown module"}, "markdownPositionTiled": "Tiled", "@markdownPositionTiled": {"description": "Tiled position option in markdown module"}, "markdownExportingBlocks": "Exporting blocks...", "@markdownExportingBlocks": {"description": "Exporting blocks message in markdown module"}, "markdownGeneratingReport": "Generating report...", "@markdownGeneratingReport": {"description": "Generating report message in markdown module"}, "markdownProcessingComplete": "Processing complete", "@markdownProcessingComplete": {"description": "Processing complete message in markdown module"}, "markdownOperationSuccessful": "Operation successful", "@markdownOperationSuccessful": {"description": "Operation successful message in markdown module"}, "markdownOperationFailed": "Operation failed", "@markdownOperationFailed": {"description": "Operation failed message in markdown module"}, "markdownWatermarkVisible": "Show Watermark", "@markdownWatermarkVisible": {"description": "Show watermark text in markdown module"}, "markdownWatermarkHidden": "Hide Watermark", "@markdownWatermarkHidden": {"description": "Hide watermark text in markdown module"}, "markdownWatermarkPositionAppearance": "Position & Appearance", "@markdownWatermarkPositionAppearance": {"description": "Position and appearance section title in watermark settings"}, "markdownWatermarkDisplayPosition": "Display Position", "@markdownWatermarkDisplayPosition": {"description": "Display position dropdown label in watermark settings"}, "markdownWatermarkHorizontalGap": "Horizontal Gap", "@markdownWatermarkHorizontalGap": {"description": "Horizontal gap slider label in watermark settings"}, "markdownWatermarkVerticalGap": "Vertical Gap", "@markdownWatermarkVerticalGap": {"description": "Vertical gap slider label in watermark settings"}, "markdownWatermarkSelectColor": "Select Watermark Color", "@markdownWatermarkSelectColor": {"description": "Color picker dialog title in watermark settings"}, "markdownWatermarkCancel": "Cancel", "@markdownWatermarkCancel": {"description": "Cancel button text in watermark color picker"}, "markdownWatermarkConfirm": "Confirm", "@markdownWatermarkConfirm": {"description": "Confirm button text in watermark color picker"}, "voiceInitializationFailed": "Initialization failed: {error}", "@voiceInitializationFailed": {"description": "Initialization failed message"}, "voicePlayingAllRecordings": "Playing all recordings", "@voicePlayingAllRecordings": {"description": "Message when playing all voice recordings"}, "voiceMyRecordings": "My Recordings", "@voiceMyRecordings": {"description": "Voice recordings list page title"}, "voicePlayAll": "Play All", "@voicePlayAll": {"description": "Play all button"}, "voiceNoRecordings": "No voice recordings", "@voiceNoRecordings": {"description": "Empty state message when no recordings exist"}, "voiceStartRecording": "Start Recording", "@voiceStartRecording": {"description": "Start recording button"}, "voiceTapToStartRecording": "Tap the button below to record your first voice", "@voiceTapToStartRecording": {"description": "Instruction text for empty state"}, "voiceConfirmDelete": "Confirm Delete", "@voiceConfirmDelete": {"description": "Delete confirmation dialog title"}, "voiceDeleteConfirmation": "Are you sure you want to delete this voice recording?", "@voiceDeleteConfirmation": {"description": "Delete confirmation dialog message"}, "voiceCancel": "Cancel", "@voiceCancel": {"description": "Cancel button text"}, "voiceDelete": "Delete", "@voiceDelete": {"description": "Delete button text"}, "voiceRecordingDeleted": "Recording deleted", "@voiceRecordingDeleted": {"description": "Message when recording is deleted"}, "voiceTranscriptionContent": "Transcription:", "@voiceTranscriptionContent": {"description": "Transcription content label"}, "voiceToday": "Today", "@voiceToday": {"description": "Today prefix for date formatting"}, "voiceYesterday": "Yesterday", "@voiceYesterday": {"description": "Yesterday prefix for date formatting"}, "voiceRecording": "Recording", "@voiceRecording": {"description": "Recording text"}, "voiceRecordingPageTitle": "Voice Recording", "@voiceRecordingPageTitle": {"description": "Voice recording page title"}, "voiceRequestPermission": "Request Permission", "@voiceRequestPermission": {"description": "Request permission button text"}, "voiceOpenSettings": "Open Settings", "@voiceOpenSettings": {"description": "Open settings message"}, "voiceRecordingInProgress": "Recording in progress...", "@voiceRecordingInProgress": {"description": "Recording in progress status"}, "voiceReadyToRecord": "Ready to start recording", "@voiceReadyToRecord": {"description": "Ready to record status"}, "voiceStartSpeaking": "Please start speaking...", "@voiceStartSpeaking": {"description": "Prompt text to start speaking"}, "voiceClickToStart": "Click to start", "@voiceClickToStart": {"description": "Click to start instruction"}, "voiceClickToStop": "Click to stop", "@voiceClickToStop": {"description": "Click to stop instruction"}, "voiceRecordingStopped": "Recording stopped", "@voiceRecordingStopped": {"description": "Message when recording is stopped"}, "voiceRecordingFailed": "Recording failed", "@voiceRecordingFailed": {"description": "Message when recording fails"}, "voiceStopRecordingFailed": "Failed to stop recording: {error}", "@voiceStopRecordingFailed": {"description": "Stop recording failed message"}, "voiceRecordingFileInvalid": "Recording file is invalid, please try again", "@voiceRecordingFileInvalid": {"description": "Invalid recording file message"}, "voiceRecordingFileNotFound": "Recording file not found, recording may have failed", "@voiceRecordingFileNotFound": {"description": "Message when recording file is not found"}, "voiceSaveRecording": "Save Recording", "@voiceSaveRecording": {"description": "Save recording dialog title"}, "voiceTitle": "Title", "@voiceTitle": {"description": "Title field label"}, "voiceDuration": "Duration", "@voiceDuration": {"description": "Duration label"}, "voiceTranscription": "Transcription", "@voiceTranscription": {"description": "Transcription label"}, "voiceRecordingAndTranscriptionSaved": "Recording and transcription text saved", "@voiceRecordingAndTranscriptionSaved": {"description": "Recording and transcription saved message"}, "voiceRecordingSaved": "Recording saved", "@voiceRecordingSaved": {"description": "Recording saved message"}, "voiceSaveRecordingFailed": "Failed to save recording: {error}", "@voiceSaveRecordingFailed": {"description": "Save recording failure message"}, "voiceNoMicrophonePermission": "No microphone permission, cannot record", "@voiceNoMicrophonePermission": {"description": "No microphone permission message"}, "voicePermissionRequired": "Permission Required", "@voicePermissionRequired": {"description": "Permission required dialog title"}, "voicePermissionInstructions": "Please follow these steps to enable permissions:", "@voicePermissionInstructions": {"description": "Permission instruction text"}, "voicePermissionStep1": "1. <PERSON>lick \"Go to Settings\" button", "@voicePermissionStep1": {"description": "Permission setup step 1"}, "voicePermissionStep2": "2. <PERSON><PERSON> \"Privacy & Security\" in Settings", "@voicePermissionStep2": {"description": "Permission setup step 2"}, "voicePermissionStep3": "3. <PERSON><PERSON> \"Microphone\" and \"Speech Recognition\" respectively", "@voicePermissionStep3": {"description": "Permission setup step 3"}, "voicePermissionStep4": "4. Find \"内容君\" in the list and enable permissions", "@voicePermissionStep4": {"description": "Permission setup step 4"}, "voicePermissionNote": "Note: If you don't see the app, please return to the app and click \"Request Permission\" again, then check settings again", "@voicePermissionNote": {"description": "Permission setup note"}, "voiceGoToSettings": "Go to Settings", "@voiceGoToSettings": {"description": "Go to settings button text"}, "voiceEnableMicrophonePermission": "Please enable microphone permission in settings to record", "@voiceEnableMicrophonePermission": {"description": "Permission enable instruction"}, "voiceInitializationError": "Initialization Error", "@voiceInitializationError": {"description": "Initialization error dialog title"}, "voiceRequestPermissionAgain": "Request Permission Again", "@voiceRequestPermissionAgain": {"description": "Request permission again button text"}, "voiceStartRecordingFailed": "Failed to start recording: {error}", "@voiceStartRecordingFailed": {"description": "Start recording failed message"}, "voiceNeedMicrophonePermission": "Need microphone permission to use recording feature", "@voiceNeedMicrophonePermission": {"description": "Permission requirement message"}, "voiceNeedMicrophonePermissionForRecording": "Need microphone permission to record", "@voiceNeedMicrophonePermissionForRecording": {"description": "Permission requirement message for recording"}, "voiceSpeechRecognitionInitFailed": "Speech recognition initialization failed, please ensure microphone permission is granted", "@voiceSpeechRecognitionInitFailed": {"description": "Speech recognition initialization failure message"}, "voiceRecordingTitle": "Voice Recording", "@voiceRecordingTitle": {"description": "Voice recording page title"}, "voicePermissionGuide": "Please follow these steps to enable permissions:", "@voicePermissionGuide": {"description": "Permission guide instruction"}, "voicePermissionGuideStep1": "1. <PERSON>lick \"Go to Settings\" button", "@voicePermissionGuideStep1": {"description": "Permission guide step 1"}, "voicePermissionGuideStep2": "2. <PERSON><PERSON> \"Privacy & Security\" in settings", "@voicePermissionGuideStep2": {"description": "Permission guide step 2"}, "voicePermissionGuideStep3": "3. <PERSON><PERSON> \"Microphone\" and \"Speech Recognition\" respectively", "@voicePermissionGuideStep3": {"description": "Permission guide step 3"}, "voicePermissionGuideStep4": "4. Find \"内容君\" in the list and enable permissions", "@voicePermissionGuideStep4": {"description": "Permission guide step 4"}, "voicePermissionGuideNote": "Note: If you cannot find the app, please return to the app and click \"Request Permission\" again, then check settings again", "@voicePermissionGuideNote": {"description": "Permission guide note"}, "voiceRecordingTitleLabel": "Title", "@voiceRecordingTitleLabel": {"description": "Recording title field label"}, "voiceRecordingDuration": "Duration: {duration}", "@voiceRecordingDuration": {"description": "Recording duration label"}, "voiceRecordingTranscription": "Transcription: {transcription}", "@voiceRecordingTranscription": {"description": "Recording transcription label"}, "voiceRecordingFileNotExist": "Recording file does not exist, recording may have failed", "@voiceRecordingFileNotExist": {"description": "Recording file not found message"}, "voicePleaseStartSpeaking": "Please start speaking...", "@voicePleaseStartSpeaking": {"description": "Please start speaking prompt"}, "voiceClickToStartRecording": "Click button below to start recording\nVoice will be converted to text in real-time", "@voiceClickToStartRecording": {"description": "Start recording instruction"}, "voiceSpeechRecognitionInProgress": "Speech recognition in progress, real-time transcription displayed above", "@voiceSpeechRecognitionInProgress": {"description": "Speech recognition in progress message"}, "voiceSave": "Save", "@voiceSave": {"description": "Save button text"}, "trafficGuideContentTools": "Content Tools", "@trafficGuideContentTools": {"description": "Content tools section title"}, "trafficGuideToolsDescription": "Choose the right tool to create traffic content", "@trafficGuideToolsDescription": {"description": "Description for content tools section"}, "trafficGuideTextTransformer": "Text Transformer", "@trafficGuideTextTransformer": {"description": "Text transformer tool title"}, "trafficGuideTextTransformerSubtitle": "Emoji conversion and character obfuscation", "@trafficGuideTextTransformerSubtitle": {"description": "Subtitle for text transformer"}, "trafficGuideWatermarkProcessor": "Watermark Processor", "@trafficGuideWatermarkProcessor": {"description": "Watermark processor tool title"}, "trafficGuideWatermarkProcessorSubtitle": "Add and remove invisible watermarks", "@trafficGuideWatermarkProcessorSubtitle": {"description": "Subtitle for watermark processor"}, "trafficGuideNewProject": "New Project", "@trafficGuideNewProject": {"description": "New project button title"}, "trafficGuideNewProjectSubtitle": "Create traffic project configuration", "@trafficGuideNewProjectSubtitle": {"description": "Subtitle for new project"}, "trafficGuideMyProjects": "My Projects", "@trafficGuideMyProjects": {"description": "My projects section title"}, "trafficGuideProjectsDescription": "Manage your traffic project configurations", "@trafficGuideProjectsDescription": {"description": "Description for projects section"}, "trafficGuideNoProjects": "No Projects", "@trafficGuideNoProjects": {"description": "No projects message"}, "trafficGuideNoProjectsDescription": "Click 'New Project' to create your first traffic project", "@trafficGuideNoProjectsDescription": {"description": "Description for no projects state"}, "trafficGuideRefresh": "Refresh", "@trafficGuideRefresh": {"description": "Refresh tooltip"}, "trafficGuideLoading": "Loading...", "@trafficGuideLoading": {"description": "Loading message"}, "trafficGuideLastUpdated": "Updated: {date}", "@trafficGuideLastUpdated": {"description": "Last updated date format"}, "trafficGuideConfirmDelete": "Confirm Delete", "@trafficGuideConfirmDelete": {"description": "Confirm delete dialog title"}, "trafficGuideDeleteConfirmation": "Are you sure you want to delete project \"{name}\"?", "@trafficGuideDeleteConfirmation": {"description": "Delete confirmation message"}, "trafficGuideProjectDeleted": "Project \"{name}\" deleted", "@trafficGuideProjectDeleted": {"description": "Project deleted success message"}, "trafficGuideEdit": "Edit", "@trafficGuideEdit": {"description": "Edit action"}, "trafficGuideDelete": "Delete", "@trafficGuideDelete": {"description": "Delete action"}, "trafficGuideProjectEditor": "Project Editor", "@trafficGuideProjectEditor": {"description": "Project editor page title"}, "trafficGuideBasicInfo": "Basic Information", "@trafficGuideBasicInfo": {"description": "Basic information section title"}, "trafficGuideProjectName": "Project Name", "@trafficGuideProjectName": {"description": "Project name field label"}, "trafficGuideProjectNameHint": "Enter project name", "@trafficGuideProjectNameHint": {"description": "Project name field hint"}, "trafficGuideProjectDescription": "Project Description", "@trafficGuideProjectDescription": {"description": "Project description field label"}, "trafficGuideProjectDescriptionHint": "Enter project description", "@trafficGuideProjectDescriptionHint": {"description": "Project description field hint"}, "trafficGuideProjectNameRequired": "Please enter project name", "@trafficGuideProjectNameRequired": {"description": "Project name validation error"}, "trafficGuideImageConfig": "Image Configuration", "@trafficGuideImageConfig": {"description": "Image configuration section title"}, "trafficGuideDefaultText": "Default Text", "@trafficGuideDefaultText": {"description": "Default text field label"}, "trafficGuideDefaultTextHint": "Enter default display text", "@trafficGuideDefaultTextHint": {"description": "Default text field hint"}, "trafficGuideFontFamily": "Font Family", "@trafficGuideFontFamily": {"description": "Font family field label"}, "trafficGuideTextTransformConfig": "Text Transform Configuration", "@trafficGuideTextTransformConfig": {"description": "Text transform configuration section title"}, "trafficGuideEmojiConversion": "Emoji Conversion", "@trafficGuideEmojiConversion": {"description": "Emoji conversion toggle title"}, "trafficGuideEmojiConversionSubtitle": "Convert numbers and letters to special Unicode characters", "@trafficGuideEmojiConversionSubtitle": {"description": "Emoji conversion subtitle"}, "trafficGuideUnicodeVariation": "Unicode Variation", "@trafficGuideUnicodeVariation": {"description": "Unicode variation toggle title"}, "trafficGuideUnicodeVariationSubtitle": "Add diacritical characters and special Unicode", "@trafficGuideUnicodeVariationSubtitle": {"description": "Unicode variation subtitle"}, "trafficGuideInvisibleChars": "Invisible Characters", "@trafficGuideInvisibleChars": {"description": "Invisible characters toggle title"}, "trafficGuideInvisibleCharsSubtitle": "Insert invisible characters in text", "@trafficGuideInvisibleCharsSubtitle": {"description": "Invisible characters subtitle"}, "trafficGuideSensitiveWordMasking": "Sensitive Word Masking", "@trafficGuideSensitiveWordMasking": {"description": "Sensitive word masking toggle title"}, "trafficGuideSensitiveWordMaskingSubtitle": "Apply character obfuscation to sensitive words", "@trafficGuideSensitiveWordMaskingSubtitle": {"description": "Sensitive word masking subtitle"}, "trafficGuideSensitiveWords": "Sensitive Words", "@trafficGuideSensitiveWords": {"description": "Sensitive words field label"}, "trafficGuideSensitiveWordsHint": "Enter sensitive words, separated by commas", "@trafficGuideSensitiveWordsHint": {"description": "Sensitive words field hint"}, "trafficGuideWatermarkConfig": "Watermark Configuration", "@trafficGuideWatermarkConfig": {"description": "Watermark configuration section title"}, "trafficGuideWatermarkTextHint": "Enter watermark content", "@trafficGuideWatermarkTextHint": {"description": "Watermark text field hint"}, "trafficGuideInvisibleWatermark": "Invisible Watermark", "@trafficGuideInvisibleWatermark": {"description": "Invisible watermark option"}, "trafficGuideOpacity": "Opacity", "@trafficGuideOpacity": {"description": "Opacity setting"}, "trafficGuideWatermarkFontSize": "Font Size", "@trafficGuideWatermarkFontSize": {"description": "Watermark font size field label"}, "trafficGuideProjectSaved": "Project saved successfully", "@trafficGuideProjectSaved": {"description": "Project save success message"}, "trafficGuideSaveProject": "Save Project", "@trafficGuideSaveProject": {"description": "Save project button text"}, "trafficGuideSaving": "Saving...", "@trafficGuideSaving": {"description": "Saving progress text"}, "trafficGuideNewProjectName": "New Project", "@trafficGuideNewProjectName": {"description": "Default new project name"}, "trafficGuideNewProjectDescription": "Traffic project configuration", "@trafficGuideNewProjectDescription": {"description": "Default new project description"}, "trafficGuideImageGeneratorTitle": "Traffic Image Generator", "@trafficGuideImageGeneratorTitle": {"description": "Image generator screen title"}, "trafficGuideImageConfiguration": "Image Configuration", "@trafficGuideImageConfiguration": {"description": "Image configuration section title"}, "trafficGuideTextRequired": "Please enter text content", "@trafficGuideTextRequired": {"description": "Text content validation error"}, "trafficGuideInterferenceSettings": "Interference Settings", "@trafficGuideInterferenceSettings": {"description": "Interference settings section title"}, "trafficGuideInterferenceLevel": "Interference Level", "@trafficGuideInterferenceLevel": {"description": "Interference level slider label"}, "trafficGuideWatermarkSettings": "Watermark Settings", "@trafficGuideWatermarkSettings": {"description": "Watermark settings section"}, "trafficGuideWatermarkContent": "Watermark Content", "@trafficGuideWatermarkContent": {"description": "Watermark content label"}, "trafficGuideWatermarkContentHint": "Enter watermark content...", "@trafficGuideWatermarkContentHint": {"description": "Hint for watermark content input"}, "trafficGuidePreview": "Preview", "@trafficGuidePreview": {"description": "Preview button text"}, "trafficGuideSaveToAlbum": "Save to Album", "@trafficGuideSaveToAlbum": {"description": "Save to album tooltip"}, "trafficGuideShare": "Share", "@trafficGuideShare": {"description": "Share tooltip"}, "trafficGuideSelectColor": "Select Color", "@trafficGuideSelectColor": {"description": "Select color dialog title"}, "trafficGuideBlack": "Black", "@trafficGuideBlack": {"description": "Black color name"}, "trafficGuideWhite": "White", "@trafficGuideWhite": {"description": "White color name"}, "trafficGuideRed": "Red", "@trafficGuideRed": {"description": "Red color name"}, "trafficGuideGreen": "Green", "@trafficGuideGreen": {"description": "Green color name"}, "trafficGuideBlue": "Blue", "@trafficGuideBlue": {"description": "Blue color name"}, "trafficGuideYellow": "Yellow", "@trafficGuideYellow": {"description": "Yellow color name"}, "trafficGuidePurple": "Purple", "@trafficGuidePurple": {"description": "Purple color name"}, "trafficGuideCyan": "<PERSON><PERSON>", "@trafficGuideCyan": {"description": "Cyan color name"}, "trafficGuideGenerateImage": "Generate Image", "@trafficGuideGenerateImage": {"description": "Generate image button text"}, "trafficGuideGenerating": "Generating...", "@trafficGuideGenerating": {"description": "Generating progress text"}, "trafficGuideImageGenerationFailed": "Image generation failed: {error}", "@trafficGuideImageGenerationFailed": {"description": "Image generation error message"}, "trafficGuideLongPressToSave": "Long press image to save to album", "@trafficGuideLongPressToSave": {"description": "Long press to save hint"}, "trafficGuideShareFeatureInProgress": "Share feature in development...", "@trafficGuideShareFeatureInProgress": {"description": "Share feature in development message"}, "trafficGuideTextTransformerTitle": "Text Transformer", "@trafficGuideTextTransformerTitle": {"description": "Text transformer screen title"}, "trafficGuideTransformSettings": "Transform Settings", "@trafficGuideTransformSettings": {"description": "Transform settings section title"}, "trafficGuideTransformText": "Transform Text", "@trafficGuideTransformText": {"description": "Transform text button text"}, "trafficGuideTransforming": "Transforming...", "@trafficGuideTransforming": {"description": "Transforming progress text"}, "trafficGuideInputText": "Input Text", "@trafficGuideInputText": {"description": "Input text section title"}, "trafficGuideCharacters": "characters", "@trafficGuideCharacters": {"description": "Characters count label"}, "trafficGuideInputHint": "Enter text to transform...", "@trafficGuideInputHint": {"description": "Input text field hint"}, "trafficGuideTransformResult": "Transform Result", "@trafficGuideTransformResult": {"description": "Transform result section title"}, "trafficGuideResultHint": "Transformed text will appear here...", "@trafficGuideResultHint": {"description": "Transform result field hint"}, "trafficGuideTransformFailed": "Transform failed: {error}", "@trafficGuideTransformFailed": {"description": "Transform error message"}, "trafficGuideCopyResult": "<PERSON><PERSON> Result", "@trafficGuideCopyResult": {"description": "Copy result button text"}, "trafficGuideClear": "Clear", "@trafficGuideClear": {"description": "Clear button text"}, "trafficGuideSettings": "Settings", "@trafficGuideSettings": {"description": "Settings tooltip"}, "trafficGuideAdvancedSettings": "Advanced Settings", "@trafficGuideAdvancedSettings": {"description": "Advanced settings section"}, "trafficGuideCopiedToClipboard": "Copied to clipboard", "@trafficGuideCopiedToClipboard": {"description": "Copied to clipboard message"}, "trafficGuideCustomCharacterMapping": "Custom Character Mapping", "@trafficGuideCustomCharacterMapping": {"description": "Custom character mapping section title"}, "trafficGuideMappingFormat": "Format: original=target (one per line)", "@trafficGuideMappingFormat": {"description": "Mapping format hint"}, "trafficGuideMappingExample": "Example:\na=ᴀ\nb=ʙ", "@trafficGuideMappingExample": {"description": "Mapping example"}, "trafficGuideConfirm": "Confirm", "@trafficGuideConfirm": {"description": "Confirm button text"}, "trafficGuideProcessingMode": "Processing Mode", "@trafficGuideProcessingMode": {"description": "Processing mode selection"}, "trafficGuideAddWatermarkMode": "Add Watermark", "@trafficGuideAddWatermarkMode": {"description": "Add watermark mode option"}, "trafficGuideRemoveWatermarkMode": "Remove Watermark", "@trafficGuideRemoveWatermarkMode": {"description": "Remove watermark mode option"}, "trafficGuideProcessText": "Process Text", "@trafficGuideProcessText": {"description": "Process text button text"}, "trafficGuideProcessing": "Processing...", "@trafficGuideProcessing": {"description": "Processing status message"}, "trafficGuideOriginalText": "Original Text", "@trafficGuideOriginalText": {"description": "Original text section label"}, "trafficGuideWatermarkedText": "Watermarked Text", "@trafficGuideWatermarkedText": {"description": "Watermarked text section label"}, "trafficGuideProcessHint": "Processing result will be displayed here", "@trafficGuideProcessHint": {"description": "Hint for process result display area"}, "trafficGuideWatermarkIdentifier": "Watermark Identifier", "@trafficGuideWatermarkIdentifier": {"description": "Watermark identifier label"}, "trafficGuideWatermarkIdentifierHint": "Enter watermark identifier to remove...", "@trafficGuideWatermarkIdentifierHint": {"description": "Hint for watermark identifier input"}, "trafficGuideRotationAngle": "Rotation Angle", "@trafficGuideRotationAngle": {"description": "Rotation angle setting"}, "trafficGuideEnterTextToProcess": "Please enter text to process", "@trafficGuideEnterTextToProcess": {"description": "Validation message for empty text input"}, "trafficGuideEnterWatermarkContent": "Please enter watermark content", "@trafficGuideEnterWatermarkContent": {"description": "Validation message for empty watermark content"}, "trafficGuideSensitiveWordsList": "Sensitive Words List", "@trafficGuideSensitiveWordsList": {"description": "Sensitive words list label"}, "trafficGuideSensitiveWordsListHint": "Enter sensitive words, separated by commas", "@trafficGuideSensitiveWordsListHint": {"description": "Sensitive words list hint text"}, "trafficGuideWatermarkAddHint": "Enter text content that needs watermark added", "@trafficGuideWatermarkAddHint": {"description": "Hint for text input when adding watermark"}, "trafficGuideWatermarkRemoveHint": "Enter text content that needs watermark removed", "@trafficGuideWatermarkRemoveHint": {"description": "Hint for text input when removing watermark"}, "textCardsHomePageTitle": "Text Cards", "@textCardsHomePageTitle": {"description": "Text Cards home page title"}, "textCardsHomePageSubtitle": "Modern Style • Inline Editing • HD Export", "@textCardsHomePageSubtitle": {"description": "Text Cards home page subtitle"}, "textCardsStartCreating": "Start Creating", "@textCardsStartCreating": {"description": "Start creating button text"}, "textCardsQuickActions": "Quick Actions", "@textCardsQuickActions": {"description": "Quick actions section title"}, "textCardsTemplateLibrary": "Template Library", "@textCardsTemplateLibrary": {"description": "Template library action title"}, "textCardsTemplateLibrarySubtitle": "16+ Beautiful Templates", "@textCardsTemplateLibrarySubtitle": {"description": "Template library action subtitle"}, "textCardsSmartSplit": "Smart Split", "@textCardsSmartSplit": {"description": "Smart split action title"}, "textCardsSmartSplitSubtitle": "Long Text Segmentation", "@textCardsSmartSplitSubtitle": {"description": "Smart split action subtitle"}, "textCardsContentLibrary": "Content Library", "@textCardsContentLibrary": {"description": "Content library action title"}, "textCardsContentLibrarySubtitle": "Manage All Cards", "@textCardsContentLibrarySubtitle": {"description": "Content library action subtitle"}, "textCardsShare": "Share", "@textCardsShare": {"description": "Share action title"}, "textCardsShareSubtitle": "Export HD Images", "@textCardsShareSubtitle": {"description": "Share action subtitle"}, "textCardsFeatures": "Features", "@textCardsFeatures": {"description": "Features section title"}, "textCardsModernTemplates": "Modern Style Templates", "@textCardsModernTemplates": {"description": "Modern templates feature title"}, "textCardsModernTemplatesDesc": "Carefully designed modern social and reading style templates to make your content more attractive", "@textCardsModernTemplatesDesc": {"description": "Modern templates feature description"}, "textCardsInlineEditing": "Inline Text Editing", "@textCardsInlineEditing": {"description": "Inline editing feature title"}, "textCardsInlineEditingDesc": "Select any text fragment, adjust font, color, size in real-time, what you see is what you get", "@textCardsInlineEditingDesc": {"description": "Inline editing feature description"}, "textCardsHDExport": "HD Image Export", "@textCardsHDExport": {"description": "HD export feature title"}, "textCardsHDExportDesc": "Support multiple resolutions and aspect ratios, save to album with one click, perfect for all platforms", "@textCardsHDExportDesc": {"description": "HD export feature description"}, "textCardsViewContentLibrary": "View My Content Library", "@textCardsViewContentLibrary": {"description": "View content library button text"}, "textCardsManageCards": "Manage and browse all created cards", "@textCardsManageCards": {"description": "Content library button subtitle"}, "textCardsCreate": "Create", "@textCardsCreate": {"description": "Create button text"}, "textCardsPleaseCreateCardFirst": "Please create a card first", "@textCardsPleaseCreateCardFirst": {"description": "Message when user tries to share without creating a card"}, "textCardsCardCreatedSuccess": "Card created successfully! Saved to content library", "@textCardsCardCreatedSuccess": {"description": "Success message when card is created"}, "textCardsBatchCreateSuccess": "Batch creation successful! Created {count} cards", "@textCardsBatchCreateSuccess": {"description": "Success message for batch creation"}, "textCardsBatchCreatePartial": "Batch creation completed! Successfully created {success}/{total} cards", "@textCardsBatchCreatePartial": {"description": "Partial success message for batch creation"}, "textCardsCreateFailed": "Creation failed: {error}", "@textCardsCreateFailed": {"description": "Error message when card creation fails"}, "textCardsBatchCreateFailed": "Batch creation failed: {error}", "@textCardsBatchCreateFailed": {"description": "Error message when batch creation fails"}, "textCardsEditorTitle": "Card Editor", "@textCardsEditorTitle": {"description": "Card editor page title"}, "textCardsCreateCard": "Create Card", "@textCardsCreateCard": {"description": "Create card title"}, "textCardsEditCard": "Edit Card", "@textCardsEditCard": {"description": "Edit card title"}, "textCardsSave": "Save", "@textCardsSave": {"description": "Save button text"}, "textCardsEdit": "Edit", "@textCardsEdit": {"description": "Edit tab text"}, "textCardsPreview": "Preview", "@textCardsPreview": {"description": "Preview tab text"}, "textCardsEnterCardTitle": "Enter card title...", "@textCardsEnterCardTitle": {"description": "Card title input hint"}, "textCardsEnterContent": "Enter content...\n\nSupports Markdown format:\n• **Bold**\n• *Italic*\n• • Unordered list\n• 1. Ordered list", "@textCardsEnterContent": {"description": "Content input hint with markdown examples"}, "textCardsContentRequired": "Please enter content", "@textCardsContentRequired": {"description": "Validation message when content is empty"}, "textCardsSaveFailed": "Save failed: {error}", "@textCardsSaveFailed": {"description": "Error message when save fails"}, "textCardsChangeTemplate": "Change Template", "@textCardsChangeTemplate": {"description": "Change template dialog title"}, "textCardsHideTitle": "Hide Title", "@textCardsHideTitle": {"description": "Hide title tooltip"}, "textCardsShowTitle": "Show Title", "@textCardsShowTitle": {"description": "Show title tooltip"}, "textCardsBoldText": "Bold text", "@textCardsBoldText": {"description": "Bold text placeholder"}, "textCardsItalicText": "Italic text", "@textCardsItalicText": {"description": "Italic text placeholder"}, "textCardsUnderlineText": "Underline text", "@textCardsUnderlineText": {"description": "Underline text placeholder"}, "textCardsPreviewPlaceholder": "Enter content in the edit tab to see preview...", "@textCardsPreviewPlaceholder": {"description": "Preview placeholder text"}, "textCardsContentEditor": "Content Editor", "@textCardsContentEditor": {"description": "Content editor page title"}, "textCardsEnterTitle": "Enter title (optional)", "@textCardsEnterTitle": {"description": "Title input hint"}, "textCardsAddSplitMarker": "Add Split Marker", "@textCardsAddSplitMarker": {"description": "Add split marker tooltip"}, "textCardsEnterRenderer": "<PERSON><PERSON>", "@textCardsEnterRenderer": {"description": "Enter renderer tooltip"}, "textCardsSplitMarkerInfo": "Set {count} split markers, will generate {sections} cards", "@textCardsSplitMarkerInfo": {"description": "Split marker info text"}, "textCardsEnterContentHint": "Enter or paste your content here...\n\nTips:\n- Use # to create headings\n- Use - or * to create lists\n- Use > to create quotes\n- Click split button to add split marker at cursor position", "@textCardsEnterContentHint": {"description": "Content input hint with tips"}, "textCardsTextPreview": "Text Preview", "@textCardsTextPreview": {"description": "Text preview label"}, "textCardsPreviewWillAppear": "Preview will appear when content is entered", "@textCardsPreviewWillAppear": {"description": "Preview placeholder text"}, "textCardsSelectSplitMarker": "Select Split Marker", "@textCardsSelectSplitMarker": {"description": "Split marker dialog title"}, "textCardsPredefinedMarkers": "Predefined Markers:", "@textCardsPredefinedMarkers": {"description": "Predefined markers label"}, "textCardsCustomMarker": "Custom Marker:", "@textCardsCustomMarker": {"description": "Custom marker label"}, "textCardsEnterCustomMarker": "Enter custom split marker", "@textCardsEnterCustomMarker": {"description": "Custom marker input hint"}, "textCardsUseCustom": "Use Custom", "@textCardsUseCustom": {"description": "Use custom marker button"}, "textCardsUnnamedCard": "Unnamed Card", "@textCardsUnnamedCard": {"description": "Default card name"}, "textCardsUnnamedDocument": "Unnamed Document", "@textCardsUnnamedDocument": {"description": "Default document name"}, "svgEditorTitle": "SVG Editor", "@svgEditorTitle": {"description": "SVG editor page title"}, "svgManagerTitle": "SVG Manager", "@svgManagerTitle": {"description": "SVG manager page title"}, "svgUntitled": "Untitled SVG", "@svgUntitled": {"description": "Default SVG name"}, "svgEditTab": "Edit", "@svgEditTab": {"description": "Edit tab label"}, "svgPreviewTab": "Preview", "@svgPreviewTab": {"description": "Preview tab label"}, "svgMoreActions": "More Actions", "@svgMoreActions": {"description": "More actions tooltip"}, "svgImportFile": "Import SVG File", "@svgImportFile": {"description": "Import SVG file menu item"}, "svgSave": "Save", "@svgSave": {"description": "Save button"}, "svgExportPng": "Export as PNG", "@svgExportPng": {"description": "Export as PNG menu item"}, "svgSharePng": "Share as PNG", "@svgSharePng": {"description": "Share as PNG menu item"}, "svgShareSvg": "Share SVG", "@svgShareSvg": {"description": "Share SVG menu item"}, "svgRename": "<PERSON><PERSON>", "@svgRename": {"description": "Rename menu item"}, "svgSaveChanges": "Save Changes", "@svgSaveChanges": {"description": "Save changes button"}, "svgEnterFileName": "Enter SVG File Name", "@svgEnterFileName": {"description": "File name input dialog title"}, "svgFileName": "File Name", "@svgFileName": {"description": "File name input label"}, "svgFileNameHint": "Enter SVG file name", "@svgFileNameHint": {"description": "File name input hint"}, "svgFileNameRequired": "File name cannot be empty", "@svgFileNameRequired": {"description": "File name validation error"}, "svgProcessing": "Processing...", "@svgProcessing": {"description": "Processing message"}, "svgLoading": "Loading...", "@svgLoading": {"description": "Loading message"}, "svgDocumentNotFound": "Document not found", "@svgDocumentNotFound": {"description": "Document not found error"}, "svgLoadDocumentFailed": "Failed to load document: {error}", "@svgLoadDocumentFailed": {"description": "Load document error with parameter"}, "svgCreateDocumentFailed": "Failed to create document: {error}", "@svgCreateDocumentFailed": {"description": "Create document error with parameter"}, "svgSaveDocumentFailed": "Failed to save document: {error}", "@svgSaveDocumentFailed": {"description": "Save document error with parameter"}, "svgSaveSuccess": "Saved successfully", "@svgSaveSuccess": {"description": "Save success message"}, "svgImportFailed": "Failed to import SVG file: {error}", "@svgImportFailed": {"description": "Import file error with parameter"}, "svgExportPngSuccess": "PNG export successful: {path}", "@svgExportPngSuccess": {"description": "PNG export success message with parameter"}, "svgExportPngFailed": "PNG export failed", "@svgExportPngFailed": {"description": "PNG export failed message"}, "svgShareSvgFailed": "Failed to share SVG: {error}", "@svgShareSvgFailed": {"description": "Share SVG error with parameter"}, "svgSharePngFailed": "Failed to share PNG: {error}", "@svgSharePngFailed": {"description": "Share PNG error with parameter"}, "svgInvalidSvg": "Invalid SVG: {error}", "@svgInvalidSvg": {"description": "Invalid SVG error with parameter"}, "svgSaveFirst": "Please save document first", "@svgSaveFirst": {"description": "Save first reminder"}, "svgEnterSvgCode": "Enter SVG code", "@svgEnterSvgCode": {"description": "SVG code input hint"}, "svgNoContent": "No SVG content", "@svgNoContent": {"description": "No content message"}, "svgEnterCodeInEditor": "Please enter SVG code in the editor tab", "@svgEnterCodeInEditor": {"description": "Instruction message"}, "svgCreateNew": "Create New SVG", "@svgCreateNew": {"description": "Create new SVG button"}, "svgImport": "Import SVG", "@svgImport": {"description": "Import SVG button"}, "svgImportSvgFile": "Import SVG File", "@svgImportSvgFile": {"description": "Import SVG file button"}, "svgImportTooltip": "Import SVG", "@svgImportTooltip": {"description": "Import tooltip"}, "svgNewTooltip": "New SVG", "@svgNewTooltip": {"description": "New SVG tooltip"}, "svgCreateNewTooltip": "Create new SVG", "@svgCreateNewTooltip": {"description": "Create new SVG tooltip"}, "svgNoDocuments": "No SVG Documents", "@svgNoDocuments": {"description": "No documents message"}, "svgNoDocumentsDesc": "Create a new SVG document or import existing files to get started", "@svgNoDocumentsDesc": {"description": "No documents description"}, "svgLoadFailed": "Failed to load documents: {error}", "@svgLoadFailed": {"description": "Load documents error with parameter"}, "svgDeleteConfirm": "Confirm Delete", "@svgDeleteConfirm": {"description": "Delete confirmation dialog title"}, "svgDeleteConfirmMessage": "Are you sure you want to delete \"{title}\"?", "@svgDeleteConfirmMessage": {"description": "Delete confirmation message with parameter"}, "svgDeleteFailed": "Failed to delete document: {error}", "@svgDeleteFailed": {"description": "Delete document error with parameter"}, "svgEdit": "Edit", "@svgEdit": {"description": "Edit button"}, "svgShare": "Share", "@svgShare": {"description": "Share button"}, "svgShareAsPng": "Share as PNG", "@svgShareAsPng": {"description": "Share as PNG button"}, "svgDelete": "Delete", "@svgDelete": {"description": "Delete button"}, "svgSavedToLibrary": "SVG saved to content library", "@svgSavedToLibrary": {"description": "Saved to library message"}, "svgCreatedAt": "Created: {date}", "@svgCreatedAt": {"description": "Created date with parameter"}, "@pdfModuleComment": "PDF Module Localization", "pdfManagerTitle": "PDF Document Management", "@pdfManagerTitle": {"description": "PDF manager page title"}, "pdfSearch": "Search...", "@pdfSearch": {"description": "PDF search hint text"}, "pdfSearchHint": "Search...", "@pdfSearchHint": {"description": "Search hint text"}, "pdfNoDocuments": "No PDF documents found", "@pdfNoDocuments": {"description": "No documents found message"}, "pdfTryDifferentSearch": "Try using different search keywords", "@pdfTryDifferentSearch": {"description": "Search suggestion"}, "pdfConfirmDelete": "Confirm Delete", "@pdfConfirmDelete": {"description": "Delete confirmation title"}, "pdfDeleteConfirm": "Are you sure you want to delete \"{filename}\"? This action cannot be undone.", "@pdfDeleteConfirm": {"description": "Delete confirmation message with filename"}, "pdfBatchDeleteConfirm": "Are you sure you want to delete {count} selected files? This action cannot be undone.", "@pdfBatchDeleteConfirm": {"description": "<PERSON><PERSON> delete confirmation message"}, "pdfCancel": "Cancel", "@pdfCancel": {"description": "Cancel button"}, "pdfDelete": "Delete", "@pdfDelete": {"description": "Delete button"}, "pdfImport": "Import PDF", "@pdfImport": {"description": "Import PDF button"}, "pdfImportTooltip": "Import PDF", "@pdfImportTooltip": {"description": "Import PDF tooltip"}, "pdfSecuritySettings": "PDF Security Settings", "@pdfSecuritySettings": {"description": "PDF security settings title"}, "pdfSelect": "Select", "@pdfSelect": {"description": "Select menu item"}, "pdfMerge": "<PERSON><PERSON>", "@pdfMerge": {"description": "Merge button"}, "pdfSelectAtLeastTwo": "Please select at least two PDF files to merge", "@pdfSelectAtLeastTwo": {"description": "Merge validation message"}, "pdfMergeSuccess": "PDF merge successful", "@pdfMergeSuccess": {"description": "Merge success message"}, "pdfMergeFailed": "PDF merge failed", "@pdfMergeFailed": {"description": "<PERSON><PERSON> failed message"}, "pdfIntelligentCenter": "PDF Intelligent Management Center", "@pdfIntelligentCenter": {"description": "PDF center title"}, "pdfCenterSubtitle": "Professional PDF tool integrating reading, editing, security, and sharing", "@pdfCenterSubtitle": {"description": "PDF center subtitle"}, "pdfVersion": "v2.0 Professional", "@pdfVersion": {"description": "Version info"}, "pdfCoreFeatures": "Core Features", "@pdfCoreFeatures": {"description": "Core features section title"}, "pdfProfessional": "Professional", "@pdfProfessional": {"description": "Professional label"}, "pdfSecurityEncryption": "Security Encryption", "@pdfSecurityEncryption": {"description": "Security feature title"}, "pdfPasswordProtection": "Password Protection", "@pdfPasswordProtection": {"description": "Password protection feature"}, "pdfPermissionControl": "Permission Control", "@pdfPermissionControl": {"description": "Permission control feature"}, "pdfSmartAnnotations": "Smart Annotations", "@pdfSmartAnnotations": {"description": "Annotations feature title"}, "pdfHighlight": "Highlight", "@pdfHighlight": {"description": "Highlight feature"}, "pdfTextAnnotations": "Text Annotations", "@pdfTextAnnotations": {"description": "Text annotations feature"}, "pdfFastSearch": "Fast Search", "@pdfFastSearch": {"description": "Search feature title"}, "pdfFullTextSearch": "Full Text Search", "@pdfFullTextSearch": {"description": "Full text search feature"}, "pdfPreciseLocation": "Precise Location", "@pdfPreciseLocation": {"description": "Precise location feature"}, "pdfDocumentMerge": "Document Merge", "@pdfDocumentMerge": {"description": "Document merge feature title"}, "pdfMultiFileMerge": "Multi-file Merge", "@pdfMultiFileMerge": {"description": "Multi-file merge feature"}, "pdfEasySharing": "Easy Sharing", "@pdfEasySharing": {"description": "Sharing feature title"}, "pdfOneClickShare": "One-click Share", "@pdfOneClickShare": {"description": "One-click share feature"}, "pdfCloudSync": "Cloud Sync", "@pdfCloudSync": {"description": "Cloud sync feature title"}, "pdfAutoSync": "Auto Sync", "@pdfAutoSync": {"description": "Auto sync feature"}, "pdfQuickStart": "Quick Start", "@pdfQuickStart": {"description": "Quick start section"}, "pdfImportDocument": "Import PDF Document", "@pdfImportDocument": {"description": "Import PDF button"}, "pdfViewDemo": "View Demo", "@pdfViewDemo": {"description": "View demo button"}, "pdfHelp": "Help", "@pdfHelp": {"description": "Help button"}, "pdfSupportInfo": "Supports .pdf format files, up to 100MB", "@pdfSupportInfo": {"description": "File support info"}, "pdfModified": "Modified: {date}", "@pdfModified": {"description": "Modified date with parameter"}, "pdfJustNow": "Just now", "@pdfJustNow": {"description": "Just now time format"}, "pdfMinutesAgo": "{minutes} minutes ago", "@pdfMinutesAgo": {"description": "Minutes ago format"}, "pdfHoursAgo": "{hours} hours ago", "@pdfHoursAgo": {"description": "Hours ago format"}, "pdfDaysAgo": "{days} days ago", "@pdfDaysAgo": {"description": "Days ago format"}, "pdfPages": "{count} pages", "@pdfPages": {"description": "Page count format"}, "pdfSecurityStatus": "Security Status", "@pdfSecurityStatus": {"description": "Security status"}, "pdfProtected": "Protected", "@pdfProtected": {"description": "Protected status"}, "pdfRestricted": "Restricted", "@pdfRestricted": {"description": "Restricted status"}, "pdfUnprotected": "Unprotected", "@pdfUnprotected": {"description": "Unprotected status"}, "pdfUsageStats": "Usage Statistics", "@pdfUsageStats": {"description": "Usage statistics section"}, "pdfTotalDocuments": "Total Documents", "@pdfTotalDocuments": {"description": "Total documents stat"}, "pdfTodayProcessed": "Today Processed", "@pdfTodayProcessed": {"description": "Today processed stat"}, "pdfStorageSpace": "Storage Space", "@pdfStorageSpace": {"description": "Storage space stat"}, "pdfTips": "Usage Tips", "@pdfTips": {"description": "Usage tips section"}, "pdfLongPressSelect": "Long Press to Select", "@pdfLongPressSelect": {"description": "Long press tip"}, "pdfLongPressDesc": "Long press document cards to enter multi-select mode for batch operations", "@pdfLongPressDesc": {"description": "Long press description"}, "pdfSecurityTip": "Security Encryption", "@pdfSecurityTip": {"description": "Security tip"}, "pdfSecurityTipDesc": "Set password protection for important documents to ensure information security", "@pdfSecurityTipDesc": {"description": "Security tip description"}, "pdfMergeTip": "Document Merge", "@pdfMergeTip": {"description": "Merge tip"}, "pdfMergeTipDesc": "Select multiple PDF documents to merge into a single file with one click", "@pdfMergeTipDesc": {"description": "Merge tip description"}, "@pdfViewerComment": "PDF Viewer Localization", "pdfViewerTitle": "PDF Viewer", "@pdfViewerTitle": {"description": "PDF viewer page title"}, "pdfSearchText": "Search text", "@pdfSearchText": {"description": "Search text field label"}, "pdfShowAnnotations": "Show Annotations", "@pdfShowAnnotations": {"description": "Show annotations menu item"}, "pdfHideAnnotations": "Hide Annotations", "@pdfHideAnnotations": {"description": "Hide annotations menu item"}, "pdfDocumentInfo": "Document Info", "@pdfDocumentInfo": {"description": "Document info menu item"}, "pdfAnnotationDetails": "Annotation Details", "@pdfAnnotationDetails": {"description": "Annotation details title"}, "pdfAuthor": "Author: {author}", "@pdfAuthor": {"description": "Annotation author label"}, "pdfCreatedAt": "Created: {time}", "@pdfCreatedAt": {"description": "Annotation creation time"}, "pdfContent": "Content: {content}", "@pdfContent": {"description": "Annotation content"}, "pdfHighlightedText": "Highlighted text: {text}", "@pdfHighlightedText": {"description": "Highlighted text content"}, "pdfFileName": "File Name", "@pdfFileName": {"description": "File name label"}, "pdfFileSize": "Size", "@pdfFileSize": {"description": "File size label"}, "pdfPageCount": "Pages", "@pdfPageCount": {"description": "Page count label"}, "pdfCreatedDate": "Created", "@pdfCreatedDate": {"description": "Created date label"}, "pdfModifiedDate": "Modified", "@pdfModifiedDate": {"description": "Modified date label"}, "pdfAnnotationCount": "Annotations", "@pdfAnnotationCount": {"description": "Annotation count label"}, "pdfClose": "Close", "@pdfClose": {"description": "Close button"}, "@pdfSecurityComment": "PDF Security Localization", "pdfSecurityTitle": "PDF Security Settings", "@pdfSecurityTitle": {"description": "PDF security page title"}, "pdfDocumentInfoSection": "Document Information", "@pdfDocumentInfoSection": {"description": "Document info section title"}, "pdfStatus": "Status: {status}", "@pdfStatus": {"description": "Document status"}, "pdfDecryptPdf": "Decrypt PDF", "@pdfDecryptPdf": {"description": "Decrypt PDF section title"}, "pdfEncryptedDesc": "This PDF is encrypted, please enter password to decrypt", "@pdfEncryptedDesc": {"description": "Encrypted PDF description"}, "pdfCurrentPassword": "Current Password", "@pdfCurrentPassword": {"description": "Current password field"}, "pdfCurrentPasswordHint": "Enter current password", "@pdfCurrentPasswordHint": {"description": "Current password hint"}, "pdfDecryptButton": "Decrypt", "@pdfDecryptButton": {"description": "Decrypt button"}, "pdfEncryptionSettings": "Encryption Settings", "@pdfEncryptionSettings": {"description": "Encryption settings section"}, "pdfUserPassword": "User Password *", "@pdfUserPassword": {"description": "User password field"}, "pdfUserPasswordHint": "Password to open PDF", "@pdfUserPasswordHint": {"description": "User password hint"}, "pdfOwnerPassword": "Owner Password (Optional)", "@pdfOwnerPassword": {"description": "Owner password field"}, "pdfOwnerPasswordHint": "Password to modify permissions", "@pdfOwnerPasswordHint": {"description": "Owner password hint"}, "pdfPermissionSettings": "Permission Settings", "@pdfPermissionSettings": {"description": "Permission settings section"}, "pdfAllowPrint": "Allow Printing", "@pdfAllowPrint": {"description": "Allow print permission"}, "pdfAllowPrintDesc": "Allow users to print PDF document", "@pdfAllowPrintDesc": {"description": "Allow print permission description"}, "pdfAllowCopy": "Allow Copying", "@pdfAllowCopy": {"description": "Allow copy permission"}, "pdfAllowCopyDesc": "Allow users to copy PDF content", "@pdfAllowCopyDesc": {"description": "Allow copy permission description"}, "pdfAllowEdit": "Allow Editing", "@pdfAllowEdit": {"description": "Allow edit permission"}, "pdfAllowEditDesc": "Allow users to edit PDF document", "@pdfAllowEditDesc": {"description": "Allow edit permission description"}, "pdfAllowEditAnnotations": "Allow Edit Annotations", "@pdfAllowEditAnnotations": {"description": "Allow edit annotations permission"}, "pdfAllowEditAnnotationsDesc": "Allow users to add or edit annotations", "@pdfAllowEditAnnotationsDesc": {"description": "Allow edit annotations permission description"}, "pdfAllowFillForms": "Allow Fill Forms", "@pdfAllowFillForms": {"description": "Allow fill forms permission"}, "pdfAllowFillFormsDesc": "Allow users to fill form fields", "@pdfAllowFillFormsDesc": {"description": "Allow fill forms permission description"}, "pdfAllowExtractPages": "Allow Extract Pages", "@pdfAllowExtractPages": {"description": "Allow extract pages permission"}, "pdfAllowExtractPagesDesc": "Allow users to extract page content", "@pdfAllowExtractPagesDesc": {"description": "Allow extract pages permission description"}, "pdfAllowAssembleDocument": "Allow Assemble Document", "@pdfAllowAssembleDocument": {"description": "Allow assemble document permission"}, "pdfAllowAssembleDocumentDesc": "Allow users to insert, delete, rotate pages", "@pdfAllowAssembleDocumentDesc": {"description": "Allow assemble document permission description"}, "pdfAllowHighQualityPrint": "Allow High Quality Print", "@pdfAllowHighQualityPrint": {"description": "Allow high quality print permission"}, "pdfAllowHighQualityPrintDesc": "Allow users to print in high quality", "@pdfAllowHighQualityPrintDesc": {"description": "Allow high quality print permission description"}, "pdfPresetPermissions": "Preset Permissions", "@pdfPresetPermissions": {"description": "Preset permissions section"}, "pdfAllPermissions": "All Permissions", "@pdfAllPermissions": {"description": "All permissions preset"}, "pdfBasicPermissions": "Basic Permissions", "@pdfBasicPermissions": {"description": "Basic permissions preset"}, "pdfReadOnly": "Read Only", "@pdfReadOnly": {"description": "Read only preset"}, "pdfSetPermissionsOnly": "Set Permissions Only", "@pdfSetPermissionsOnly": {"description": "Set permissions only button"}, "pdfEncryptAndSetPermissions": "Encrypt and Set Permissions", "@pdfEncryptAndSetPermissions": {"description": "Encrypt and set permissions button"}, "pdfEnterUserPassword": "Please enter user password", "@pdfEnterUserPassword": {"description": "User password validation message"}, "pdfEncryptSuccess": "PDF encrypted successfully", "@pdfEncryptSuccess": {"description": "PDF encryption success message"}, "pdfEncryptFailed": "PDF encryption failed", "@pdfEncryptFailed": {"description": "PDF encryption failed message"}, "pdfEncryptionFailed": "Encryption failed: {error}", "@pdfEncryptionFailed": {"description": "Encryption failed with error"}, "pdfEnterCurrentPassword": "Please enter current password", "@pdfEnterCurrentPassword": {"description": "Current password hint"}, "pdfDecryptSuccess": "PDF decrypted successfully", "@pdfDecryptSuccess": {"description": "PDF decryption success message"}, "pdfDecryptFailed": "PDF decryption failed, please check password", "@pdfDecryptFailed": {"description": "PDF decryption failed message"}, "pdfDecryptionFailed": "Decryption failed: {error}", "@pdfDecryptionFailed": {"description": "Decryption failed with error"}, "pdfPermissionsSetSuccess": "Permissions set successfully", "@pdfPermissionsSetSuccess": {"description": "Permissions set success message"}, "pdfPermissionsSetFailed": "Permissions set failed", "@pdfPermissionsSetFailed": {"description": "Permissions set failed message"}, "pdfSetPermissionsFailed": "Settings failed: {error}", "@pdfSetPermissionsFailed": {"description": "Set permissions failed with error"}, "htmlManagerTitle": "HTML Management", "@htmlManagerTitle": {"description": "HTML manager screen title"}, "htmlManagerDescription": "Create and edit HTML documents, all content will be automatically saved to the content library for unified management", "@htmlManagerDescription": {"description": "HTML manager description"}, "htmlCreateNew": "Create New HTML", "@htmlCreateNew": {"description": "Create new HTML button"}, "htmlImportFile": "Import HTML File", "@htmlImportFile": {"description": "Import HTML file button"}, "htmlImporting": "Importing...", "@htmlImporting": {"description": "Importing HTML status"}, "htmlImportSuccess": "Import Successful", "@htmlImportSuccess": {"description": "Import success dialog title"}, "htmlImportSuccessMessage": "Successfully imported {count} HTML files to content library", "@htmlImportSuccessMessage": {"description": "Import success message with count"}, "htmlImportFailed": "Failed to import HTML file: {error}", "@htmlImportFailed": {"description": "Import failed error message"}, "htmlImportingProgress": "Importing HTML files ({imported}/{total})", "@htmlImportingProgress": {"description": "Import progress message"}, "htmlViewContentLibrary": "View Content Library", "@htmlViewContentLibrary": {"description": "View content library link"}, "htmlEditorTitle": "HTML Editor", "@htmlEditorTitle": {"description": "HTML editor screen title"}, "htmlNewDocument": "New HTML Document", "@htmlNewDocument": {"description": "New HTML document default title"}, "htmlInputFilename": "Enter HTML Filename", "@htmlInputFilename": {"description": "Filename input dialog title"}, "htmlFilename": "Filename", "@htmlFilename": {"description": "Filename input label"}, "htmlFilenameHint": "Please enter HTML filename", "@htmlFilenameHint": {"description": "Filename input hint"}, "htmlFilenameEmpty": "Filename cannot be empty", "@htmlFilenameEmpty": {"description": "Filename empty error message"}, "htmlCancel": "Cancel", "@htmlCancel": {"description": "Cancel button"}, "htmlConfirm": "Confirm", "@htmlConfirm": {"description": "Confirm button"}, "htmlSaveSuccess": "Save successful", "@htmlSaveSuccess": {"description": "Save success message"}, "htmlDocumentNotFound": "Document not found", "@htmlDocumentNotFound": {"description": "Document not found error"}, "htmlLoadDocumentFailed": "Failed to load document: {error}", "@htmlLoadDocumentFailed": {"description": "Load document failed error"}, "htmlCreateDocumentFailed": "Failed to create document: {error}", "@htmlCreateDocumentFailed": {"description": "Create document failed error"}, "htmlSaveDocumentFailed": "Failed to save document: {error}", "@htmlSaveDocumentFailed": {"description": "Save document failed error"}, "htmlImportFileFailed": "Failed to import HTML file: {error}", "@htmlImportFileFailed": {"description": "Import file failed error"}, "htmlExportImageFailed": "Failed to export image: {error}", "@htmlExportImageFailed": {"description": "Export image failed error"}, "htmlShareHtmlFailed": "Failed to share HTML: {error}", "@htmlShareHtmlFailed": {"description": "Share HTML failed error"}, "htmlShareImageFailed": "Failed to share image: {error}", "@htmlShareImageFailed": {"description": "Share image failed error"}, "htmlSaveToLibraryFailed": "Failed to save to content library: {error}", "@htmlSaveToLibraryFailed": {"description": "Save to library failed error"}, "htmlPleaseSaveFirst": "Please save document first", "@htmlPleaseSaveFirst": {"description": "Please save first message"}, "htmlSelectSaveLocation": "Select save location", "@htmlSelectSaveLocation": {"description": "Select save location dialog title"}, "htmlExportImageSuccess": "Export image successful: {path}", "@htmlExportImageSuccess": {"description": "Export image success message"}, "htmlSavedToLibrary": "Saved to content library: {title}", "@htmlSavedToLibrary": {"description": "Saved to library message"}, "htmlProcessing": "Processing...", "@htmlProcessing": {"description": "Processing status"}, "htmlProcessingLargeText": "Processing large text...", "@htmlProcessingLargeText": {"description": "Processing large text status"}, "htmlInputHtmlCode": "Enter HTML code", "@htmlInputHtmlCode": {"description": "HTML input hint"}, "htmlNoContent": "No HTML content", "@htmlNoContent": {"description": "No HTML content message"}, "htmlPleaseInputHtml": "Please enter HTML code", "@htmlPleaseInputHtml": {"description": "Please input HTML message"}, "htmlEditMode": "Edit Mode", "@htmlEditMode": {"description": "Edit mode tooltip"}, "htmlPreviewMode": "Preview Mode", "@htmlPreviewMode": {"description": "Preview mode tooltip"}, "htmlSingleScreenMode": "Single Screen Mode", "@htmlSingleScreenMode": {"description": "Single screen mode tooltip"}, "htmlSplitScreenMode": "Split Screen Mode", "@htmlSplitScreenMode": {"description": "Split screen mode tooltip"}, "htmlMoreActions": "More Actions", "@htmlMoreActions": {"description": "More actions tooltip"}, "htmlImportHtmlFile": "Import HTML File", "@htmlImportHtmlFile": {"description": "Import HTML file menu item"}, "htmlExportAsImage": "Export as Image", "@htmlExportAsImage": {"description": "Export as image menu item"}, "htmlShareAsImage": "Share as Image", "@htmlShareAsImage": {"description": "Share as image menu item"}, "htmlShareHtml": "Share HTML", "@htmlShareHtml": {"description": "Share HTML menu item"}, "htmlRename": "<PERSON><PERSON>", "@htmlRename": {"description": "Rename menu item"}, "htmlSaveToLibrary": "Save to Content Library", "@htmlSaveToLibrary": {"description": "Save to library menu item"}, "htmlSaveToLibraryTooltip": "Save to Content Library", "@htmlSaveToLibraryTooltip": {"description": "Save to library button tooltip"}, "htmlSaveTooltip": "Save", "@htmlSaveTooltip": {"description": "Save button tooltip"}, "htmlSaveToGallery": "Save to Gallery", "@htmlSaveToGallery": {"description": "Save to gallery button label"}, "htmlNewHtmlTooltip": "New HTML", "@htmlNewHtmlTooltip": {"description": "New HTML button tooltip"}, "htmlImportHtmlTooltip": "Import HTML", "@htmlImportHtmlTooltip": {"description": "Import HTML button tooltip"}, "settingsLanguagePreference": "Select your preferred app language", "@settingsLanguagePreference": {"description": "Language preference description"}, "settingsStorageManagement": "Storage Management", "@settingsStorageManagement": {"description": "Storage management title"}, "settingsHelpCenter": "Help Center", "@settingsHelpCenter": {"description": "Help center title"}, "settingsFeedback": "<PERSON><PERSON><PERSON>", "@settingsFeedback": {"description": "Feedback title"}, "settingsVersionInfo": "Version Info", "@settingsVersionInfo": {"description": "Version info setting"}, "settingsHelpAndFeedback": "Help & Feedback", "@settingsHelpAndFeedback": {"description": "Help and feedback setting"}, "settingsGetHelpOrProvideFeedback": "Get help or provide feedback", "@settingsGetHelpOrProvideFeedback": {"description": "Help and feedback description"}, "settingsHelpAndFeedbackContent": "If you need help or have suggestions, please contact us through the feedback page.", "@settingsHelpAndFeedbackContent": {"description": "Help and feedback dialog content"}, "settingsOk": "OK", "@settingsOk": {"description": "OK button"}, "settingsSelectTheme": "Select Theme", "@settingsSelectTheme": {"description": "Theme selection dialog title"}, "settingsSystemMode": "System", "@settingsSystemMode": {"description": "System theme mode"}, "settingsLightMode": "Light", "@settingsLightMode": {"description": "Light theme mode"}, "settingsDarkMode": "Dark", "@settingsDarkMode": {"description": "Dark theme mode"}, "storageLoadingStorageInfo": "Loading storage information", "@storageLoadingStorageInfo": {"description": "Loading storage info message"}, "storageLoadStorageInfoFailed": "Failed to load storage information: {error}", "@storageLoadStorageInfoFailed": {"description": "Storage info load error message"}, "storageTotalUsage": "Total Storage Usage", "@storageTotalUsage": {"description": "Total storage usage label"}, "storageDetails": "Storage Details", "@storageDetails": {"description": "Storage details section title"}, "storageAppData": "App Data", "@storageAppData": {"description": "App data label"}, "storageCacheFiles": "<PERSON><PERSON>", "@storageCacheFiles": {"description": "Cache files label"}, "storageContentData": "Content Data", "@storageContentData": {"description": "Content data label"}, "storageVoiceFiles": "Voice Files", "@storageVoiceFiles": {"description": "Voice files label"}, "storageImageFiles": "Image Files", "@storageImageFiles": {"description": "Image files label"}, "storageSettingsData": "Settings Data", "@storageSettingsData": {"description": "Settings data label"}, "storageCleanupOptions": "Cleanup Options", "@storageCleanupOptions": {"description": "Cleanup options section title"}, "storageClearCache": "<PERSON>ache", "@storageClearCache": {"description": "Clear cache button"}, "storageClearCacheDesc": "Delete temporary files and cache data", "@storageClearCacheDesc": {"description": "Clear cache description"}, "storageClearTempFiles": "Clear Temporary Files", "@storageClearTempFiles": {"description": "Clear temporary files button"}, "storageClearTempFilesDesc": "Delete temporary files generated during processing", "@storageClearTempFilesDesc": {"description": "Clear temporary files description"}, "storageDataManagement": "Data Management", "@storageDataManagement": {"description": "Data management section title"}, "storageExportData": "Export Data", "@storageExportData": {"description": "Export data button"}, "storageExportDataDesc": "Export app data to files", "@storageExportDataDesc": {"description": "Export data description"}, "storageImportData": "Import Data", "@storageImportData": {"description": "Import data button"}, "storageImportDataDesc": "Import app data from files", "@storageImportDataDesc": {"description": "Import data description"}, "storageResetAppData": "Reset App Data", "@storageResetAppData": {"description": "Reset app data button"}, "storageResetAppDataDesc": "Clear all data and restore default settings", "@storageResetAppDataDesc": {"description": "Reset app data description"}, "storageCacheCleared": "<PERSON><PERSON> cleared successfully", "@storageCacheCleared": {"description": "<PERSON><PERSON> cleared success message"}, "storageClearCacheFailed": "Failed to clear cache: {error}", "@storageClearCacheFailed": {"description": "Cache clear error message"}, "storageTempFilesCleared": "Temporary files cleared successfully", "@storageTempFilesCleared": {"description": "Temp files cleared success message"}, "storageClearTempFilesFailed": "Failed to clear temporary files: {error}", "@storageClearTempFilesFailed": {"description": "Temp files clear error message"}, "storageVoiceManagementInDevelopment": "Voice file management feature is in development", "@storageVoiceManagementInDevelopment": {"description": "Voice management development message"}, "storageImageManagementInDevelopment": "Image file management feature is in development", "@storageImageManagementInDevelopment": {"description": "Image management development message"}, "storageDataExportInDevelopment": "Data export feature is in development", "@storageDataExportInDevelopment": {"description": "Data export development message"}, "storageDataImportInDevelopment": "Data import feature is in development", "@storageDataImportInDevelopment": {"description": "Data import development message"}, "storageResetDataTitle": "Reset App Data", "@storageResetDataTitle": {"description": "Reset data dialog title"}, "storageResetDataMessage": "This operation will delete all data and restore default settings. This cannot be undone. Are you sure you want to continue?", "@storageResetDataMessage": {"description": "Reset data dialog message"}, "storageCancel": "Cancel", "@storageCancel": {"description": "Cancel button"}, "storageConfirm": "Confirm", "@storageConfirm": {"description": "Confirm button"}, "storageDataResetComplete": "App data reset completed", "@storageDataResetComplete": {"description": "Data reset success message"}, "storageDataResetFailed": "Failed to reset app data: {error}", "@storageDataResetFailed": {"description": "Data reset error message"}, "iosSettingsGeneral": "General", "@iosSettingsGeneral": {"description": "General settings section"}, "iosSettingsLanguageRegion": "Language & Region", "@iosSettingsLanguageRegion": {"description": "Language and region setting"}, "iosSettingsDisplayBrightness": "Display & Brightness", "@iosSettingsDisplayBrightness": {"description": "Display and brightness section"}, "iosSettingsAppearance": "Appearance", "@iosSettingsAppearance": {"description": "Appearance setting"}, "iosSettingsPrivacySecurity": "Privacy & Security", "@iosSettingsPrivacySecurity": {"description": "Privacy and security section"}, "iosSettingsPrivacySettings": "Privacy Settings", "@iosSettingsPrivacySettings": {"description": "Privacy settings option"}, "iosSettingsStorage": "Storage", "@iosSettingsStorage": {"description": "Storage section"}, "iosSettingsStorageManagement": "Storage Management", "@iosSettingsStorageManagement": {"description": "Storage management option"}, "iosSettingsViewStorageUsage": "View storage usage", "@iosSettingsViewStorageUsage": {"description": "Storage management description"}, "iosSettingsDataImportExport": "Data Import/Export", "@iosSettingsDataImportExport": {"description": "Data import/export option"}, "iosSettingsBackupRestoreData": "Backup and restore data", "@iosSettingsBackupRestoreData": {"description": "Data management description"}, "iosSettingsSupport": "Support", "@iosSettingsSupport": {"description": "Support section"}, "iosSettingsHelpCenter": "Help Center", "@iosSettingsHelpCenter": {"description": "Help center option"}, "iosSettingsFeedback": "<PERSON><PERSON><PERSON>", "@iosSettingsFeedback": {"description": "Feedback option"}, "iosSettingsRateApp": "Rate App", "@iosSettingsRateApp": {"description": "Rate app option"}, "iosSettingsAbout": "About", "@iosSettingsAbout": {"description": "About section"}, "iosSettingsAboutApp": "About App", "@iosSettingsAboutApp": {"description": "About app option"}, "iosSettingsCurrentLanguage": "English", "@iosSettingsCurrentLanguage": {"description": "Current language display"}, "iosSettingsLightTheme": "Light", "@iosSettingsLightTheme": {"description": "Light theme option"}, "iosSettingsDarkTheme": "Dark", "@iosSettingsDarkTheme": {"description": "Dark theme option"}, "iosSettingsSystemTheme": "System", "@iosSettingsSystemTheme": {"description": "System theme option"}, "iosSettingsSelectLanguage": "Select Language", "@iosSettingsSelectLanguage": {"description": "Language selection title"}, "iosSettingsSimplifiedChinese": "简体中文", "@iosSettingsSimplifiedChinese": {"description": "Simplified Chinese option"}, "iosSettingsTraditionalChinese": "繁體中文", "@iosSettingsTraditionalChinese": {"description": "Traditional Chinese option"}, "iosSettingsEnglish": "English", "@iosSettingsEnglish": {"description": "English option"}, "iosSettingsLanguageSelected": "Language selected: {language}", "@iosSettingsLanguageSelected": {"description": "Language selection message"}, "iosSettingsSelectAppearance": "Select Appearance", "@iosSettingsSelectAppearance": {"description": "Appearance selection title"}, "iosSettingsPrivacyContent": "We value your privacy. All data processing is done locally and will not be uploaded to servers.", "@iosSettingsPrivacyContent": {"description": "Privacy settings content"}, "iosSettingsPrivacyManage": "You can manage your data in settings at any time.", "@iosSettingsPrivacyManage": {"description": "Privacy settings management text"}, "iosSettingsUnderstand": "Understand", "@iosSettingsUnderstand": {"description": "Understand button"}, "iosSettingsDataManagementTitle": "Data Management", "@iosSettingsDataManagementTitle": {"description": "Data management title"}, "iosSettingsSelectOperation": "Select operation to perform", "@iosSettingsSelectOperation": {"description": "Data management message"}, "iosSettingsExportData": "Export Data", "@iosSettingsExportData": {"description": "Export data option"}, "iosSettingsImportData": "Import Data", "@iosSettingsImportData": {"description": "Import data option"}, "iosSettingsCreateBackup": "Create Backup", "@iosSettingsCreateBackup": {"description": "Create backup option"}, "iosSettingsRateAppTitle": "Rate App", "@iosSettingsRateAppTitle": {"description": "Rate app dialog title"}, "iosSettingsRateAppMessage": "Do you like this app? Please rate us on the App Store!", "@iosSettingsRateAppMessage": {"description": "Rate app dialog message"}, "iosSettingsLater": "Later", "@iosSettingsLater": {"description": "Later button"}, "iosSettingsRateNow": "Rate Now", "@iosSettingsRateNow": {"description": "Rate now button"}, "iosSettingsCannotOpenAppStore": "Cannot open App Store", "@iosSettingsCannotOpenAppStore": {"description": "App store error message"}, "iosSettingsAppStoreError": "Error opening App Store", "@iosSettingsAppStoreError": {"description": "App store error details"}, "iosSettingsVersion": "Version: {version}", "@iosSettingsVersion": {"description": "Version info format"}, "iosSettingsAppDescription": "A powerful content management tool to help you create and manage various formats of content more efficiently.", "@iosSettingsAppDescription": {"description": "App description text"}, "iosSettingsCopyright": "© 2023-2024 ContentPal Team", "@iosSettingsCopyright": {"description": "Copyright text"}, "iosSettingsClose": "Close", "@iosSettingsClose": {"description": "Close button"}, "iosSettingsOK": "OK", "@iosSettingsOK": {"description": "OK button"}, "iosSettingsError": "Error", "@iosSettingsError": {"description": "Error dialog title"}, "iosSettingsDataExportInDevelopment": "Data export feature is in development...", "@iosSettingsDataExportInDevelopment": {"description": "Data export development message"}, "iosSettingsDataImportInDevelopment": "Data import feature is in development...", "@iosSettingsDataImportInDevelopment": {"description": "Data import development message"}, "iosSettingsBackupInDevelopment": "Backup feature is in development...", "@iosSettingsBackupInDevelopment": {"description": "Backup development message"}, "helpCenterTitle": "Help Center", "@helpCenterTitle": {"description": "Help center title"}, "helpCenterNeedHelp": "Need Help?", "@helpCenterNeedHelp": {"description": "Need help title"}, "helpCenterDescription": "Find answers to common questions or contact us for support", "@helpCenterDescription": {"description": "Help center description"}, "helpCenterContactSupport": "Contact Support", "@helpCenterContactSupport": {"description": "Contact support button"}, "helpCenterUserManual": "User Manual", "@helpCenterUserManual": {"description": "User manual button"}, "helpCenterGettingStarted": "Getting Started", "@helpCenterGettingStarted": {"description": "Getting started help item"}, "helpCenterGettingStartedContent": "Welcome to 内容君! You can select feature modules from the home page, such as text cards, Markdown editing, PDF processing, etc.", "@helpCenterGettingStartedContent": {"description": "Getting started help content"}, "helpCenterTextCards": "Text Cards", "@helpCenterTextCards": {"description": "Text cards help item"}, "helpCenterTextCardsContent": "Text cards feature helps you convert text content into beautiful card images, supporting multiple templates and style customization.", "@helpCenterTextCardsContent": {"description": "Text cards help content"}, "helpCenterMarkdownEditing": "Markdown Editing", "@helpCenterMarkdownEditing": {"description": "Markdown editing help item"}, "helpCenterMarkdownEditingContent": "Markdown editor supports real-time preview, multiple themes, export to HTML/PDF and other features, making your document writing more efficient.", "@helpCenterMarkdownEditingContent": {"description": "Markdown editing help content"}, "helpCenterTrafficGuideGeneration": "Traffic Guide Generation", "@helpCenterTrafficGuideGeneration": {"description": "Traffic guide generation help item"}, "helpCenterTrafficGuideGenerationContent": "Traffic guide generation feature can create eye-catching marketing images, supporting anti-theft features like interference and watermarks.", "@helpCenterTrafficGuideGenerationContent": {"description": "Traffic guide generation help content"}, "helpCenterVoiceFeatures": "Voice Features", "@helpCenterVoiceFeatures": {"description": "Voice features help item"}, "helpCenterVoiceFeaturesContent": "Voice features include recording, transcription, text-to-speech, etc., supporting multiple languages and high-quality voice processing.", "@helpCenterVoiceFeaturesContent": {"description": "Voice features help content"}, "helpCenterPDFProcessing": "PDF Processing", "@helpCenterPDFProcessing": {"description": "PDF processing help item"}, "helpCenterPDFProcessingContent": "PDF processing features support viewing, annotation, security settings, etc., allowing you to better manage PDF documents.", "@helpCenterPDFProcessingContent": {"description": "PDF processing help content"}, "helpCenterDataSyncBackup": "Data Sync & Backup", "@helpCenterDataSyncBackup": {"description": "Data sync backup help item"}, "helpCenterDataSyncBackupContent": "Your data is automatically saved locally. It is recommended to regularly use the export feature to backup important content.", "@helpCenterDataSyncBackupContent": {"description": "Data sync backup help content"}, "helpCenterPrivacySecurity": "Privacy & Security", "@helpCenterPrivacySecurity": {"description": "Privacy security help item"}, "helpCenterPrivacySecurityContent": "We value your privacy, all data processing is done locally and will not be uploaded to servers.", "@helpCenterPrivacySecurityContent": {"description": "Privacy security help content"}, "helpCenterSearchHelp": "Search Help", "@helpCenterSearchHelp": {"description": "Search help dialog title"}, "helpCenterSearchPlaceholder": "Enter keywords to search...", "@helpCenterSearchPlaceholder": {"description": "Search placeholder text"}, "helpCenterSearchCancel": "Cancel", "@helpCenterSearchCancel": {"description": "Search cancel button"}, "helpCenterSearch": "Search", "@helpCenterSearch": {"description": "Search button"}, "helpCenterSupportRequestSubject": "ContentPal Support Request", "@helpCenterSupportRequestSubject": {"description": "Support email subject"}, "helpCenterSupportRequestBody": "Please describe the issue you encountered...", "@helpCenterSupportRequestBody": {"description": "Support email body"}, "helpCenterCannotOpenEmailApp": "Cannot open email app", "@helpCenterCannotOpenEmailApp": {"description": "Email app error message"}, "helpCenterEmailError": "Error sending email", "@helpCenterEmailError": {"description": "Email error message"}, "helpCenterCannotOpenManual": "Cannot open user manual", "@helpCenterCannotOpenManual": {"description": "Manual open error message"}, "helpCenterManualError": "Error opening user manual", "@helpCenterManualError": {"description": "Manual error message"}, "feedbackTitle": "<PERSON><PERSON><PERSON>", "@feedbackTitle": {"description": "Feedback screen title"}, "feedbackSubtitle": "Your opinion matters", "@feedbackSubtitle": {"description": "Feedback screen subtitle"}, "feedbackDescription": "Tell us your thoughts and help us improve the app", "@feedbackDescription": {"description": "Feedback screen description"}, "feedbackType": "Feedback Type", "@feedbackType": {"description": "Feedback type label"}, "feedbackTitleLabel": "Title", "@feedbackTitleLabel": {"description": "Feedback title label"}, "feedbackDetailedDescription": "Detailed Description", "@feedbackDetailedDescription": {"description": "Feedback description label"}, "feedbackContactEmail": "Contact Email (Optional)", "@feedbackContactEmail": {"description": "Contact email label"}, "feedbackIncludeSystemInfo": "Include System Info", "@feedbackIncludeSystemInfo": {"description": "System info option"}, "feedbackIncludeSystemInfoDesc": "Help us better diagnose issues", "@feedbackIncludeSystemInfoDesc": {"description": "System info description"}, "feedbackSubmit": "Submit <PERSON>", "@feedbackSubmit": {"description": "Submit feedback button"}, "feedbackBugReport": "Bug Report", "@feedbackBugReport": {"description": "Bug report type"}, "feedbackBugReportDesc": "Report errors or anomalies in the app", "@feedbackBugReportDesc": {"description": "Bug report description"}, "feedbackFeatureSuggestion": "Feature Suggestion", "@feedbackFeatureSuggestion": {"description": "Feature suggestion type"}, "feedbackFeatureSuggestionDesc": "Suggest new features or improvements", "@feedbackFeatureSuggestionDesc": {"description": "Feature suggestion description"}, "feedbackComplaint": "<PERSON><PERSON><PERSON><PERSON>", "@feedbackComplaint": {"description": "Complaint type"}, "feedbackComplaintDesc": "Complaints or issues with the app", "@feedbackComplaintDesc": {"description": "Complaint description"}, "feedbackPraise": "<PERSON>raise", "@feedbackPraise": {"description": "Praise type"}, "feedbackPraiseDesc": "Praise or positive feedback for the app", "@feedbackPraiseDesc": {"description": "Praise description"}, "feedbackTitleHint": "Please briefly describe your feedback", "@feedbackTitleHint": {"description": "Title field hint"}, "feedbackTitleRequired": "Please enter feedback title", "@feedbackTitleRequired": {"description": "Title required error"}, "feedbackDescriptionHint": "Please describe your issue, suggestion, or thoughts in detail...", "@feedbackDescriptionHint": {"description": "Description field hint"}, "feedbackDescriptionRequired": "Please enter detailed description", "@feedbackDescriptionRequired": {"description": "Description required error"}, "feedbackDescriptionTooShort": "Description must be at least 10 characters", "@feedbackDescriptionTooShort": {"description": "Description too short error"}, "feedbackEmailHint": "<EMAIL>", "@feedbackEmailHint": {"description": "Email field hint"}, "feedbackEmailInvalid": "Please enter a valid email address", "@feedbackEmailInvalid": {"description": "Email invalid error"}, "feedbackSubmitting": "Submitting...", "@feedbackSubmitting": {"description": "Submitting text"}, "feedbackSubmissionError": "Error submitting feedback: {error}", "@feedbackSubmissionError": {"description": "Submission error message"}, "feedbackSubmissionSuccessTitle": "<PERSON><PERSON><PERSON> Submitted Successfully", "@feedbackSubmissionSuccessTitle": {"description": "Success dialog title"}, "feedbackSubmissionSuccessMessage": "Thank you for your feedback! We will carefully consider your suggestions.", "@feedbackSubmissionSuccessMessage": {"description": "Success dialog message"}, "feedbackConfirm": "Confirm", "@feedbackConfirm": {"description": "Confirm button"}, "feedbackSystemInfoFailed": "Failed to get system information", "@feedbackSystemInfoFailed": {"description": "System info error message"}, "feedbackAppVersion": "App Version: {version}", "@feedbackAppVersion": {"description": "App version format"}, "feedbackBuildNumber": "Build Number: {buildNumber}", "@feedbackBuildNumber": {"description": "Build number format"}, "feedbackDevice": "Device: {device}", "@feedbackDevice": {"description": "Device format"}, "feedbackSystemVersion": "System Version: {version}", "@feedbackSystemVersion": {"description": "System version format"}, "feedbackDeviceModel": "Device Model: {model}", "@feedbackDeviceModel": {"description": "Device model format"}, "feedbackManufacturer": "Manufacturer: {manufacturer}", "@feedbackManufacturer": {"description": "Manufacturer format"}, "feedbackEmailSubject": "内容君 - {feedbackType}", "@feedbackEmailSubject": {"description": "Feedback email subject format"}, "feedbackCannotOpenEmailApp": "Cannot open email app", "@feedbackCannotOpenEmailApp": {"description": "Email app error message"}, "newContentLibraryExperience": "New Content Library Experience", "@newContentLibraryExperience": {"description": "Content library feature card title"}, "supportMultipleContentTypes": "Support multiple content types, prioritize rendering results", "@supportMultipleContentTypes": {"description": "Content library feature card subtitle"}, "contentLibraryDemoPage": "Content Library Demo", "@contentLibraryDemoPage": {"description": "Content library demo page title"}, "contentServiceLoadItemFailed": "Failed to load content item: {error}", "@contentServiceLoadItemFailed": {"description": "Content service load item error message"}, "contentServiceMustBeTextType": "Must be text type content", "@contentServiceMustBeTextType": {"description": "Content service text type validation error"}, "contentServiceMustBeImageType": "Must be image type content", "@contentServiceMustBeImageType": {"description": "Content service image type validation error"}, "contentServiceItemNotFound": "Content item not found", "@contentServiceItemNotFound": {"description": "Content service item not found error"}, "contentServiceNotInitialized": "ContentService not initialized", "@contentServiceNotInitialized": {"description": "Content service not initialized error"}, "contentServiceRenderFailed": "<PERSON><PERSON> failed: {error}", "@contentServiceRenderFailed": {"description": "Content service render failed error"}, "permissionHelperRequestStorageFailed": "Request storage permission failed: {error}", "@permissionHelperRequestStorageFailed": {"description": "Permission helper storage permission request failed error"}, "permissionHelperRequestCameraFailed": "Request camera permission failed: {error}", "@permissionHelperRequestCameraFailed": {"description": "Permission helper camera permission request failed error"}, "permissionHelperRequestMultipleFailed": "Request multiple permissions failed: {error}", "@permissionHelperRequestMultipleFailed": {"description": "Permission helper multiple permissions request failed error"}, "permissionHelperIosPermissionCheckFailed": "iOS permission check failed: {error}", "@permissionHelperIosPermissionCheckFailed": {"description": "Permission helper iOS permission check failed error"}, "chineseTraditionalColorTitle": "Traditional Chinese Colors", "@chineseTraditionalColorTitle": {"description": "Chinese traditional color theme selector title"}, "chineseTraditionalColorSubtitle": "Choose your favorite traditional color theme", "@chineseTraditionalColorSubtitle": {"description": "Chinese traditional color theme selector subtitle"}, "chineseTraditionalColorSystemTheme": "Follow System Theme", "@chineseTraditionalColorSystemTheme": {"description": "System theme option in Chinese traditional color selector"}, "chineseTraditionalColorSystemThemeDesc": "Use app default theme colors", "@chineseTraditionalColorSystemThemeDesc": {"description": "System theme description in Chinese traditional color selector"}, "chineseTraditionalColorSwitchedToTheme": "Switched to \"{themeName}\" theme", "@chineseTraditionalColorSwitchedToTheme": {"description": "Message when switching to a specific Chinese traditional color theme"}, "chineseTraditionalColorSwitchedToSystem": "Switched to system default theme", "@chineseTraditionalColorSwitchedToSystem": {"description": "Message when switching to system default theme"}, "subscriptionManagement": "Subscription Management", "@subscriptionManagement": {"description": "Subscription management page title"}, "upgradeSubscription": "Upgrade Subscription", "@upgradeSubscription": {"description": "Upgrade subscription button text"}, "upgradeSubscriptionDesc": "View and purchase higher-tier subscription plans", "@upgradeSubscriptionDesc": {"description": "Upgrade subscription description"}, "restorePurchase": "Restore Purchase", "@restorePurchase": {"description": "Restore purchase button text"}, "restorePurchaseDesc": "Restore your previous subscription purchases", "@restorePurchaseDesc": {"description": "Restore purchase description"}, "helpAndSupport": "Help and Support", "@helpAndSupport": {"description": "Help and support section title"}, "frequentlyAskedQuestions": "FAQ", "@frequentlyAskedQuestions": {"description": "FAQ button text"}, "frequentlyAskedQuestionsDesc": "View frequently asked questions about subscriptions", "@frequentlyAskedQuestionsDesc": {"description": "FAQ description"}, "contactCustomerService": "Contact Support", "@contactCustomerService": {"description": "Contact customer service button text"}, "contactCustomerServiceDesc": "Get help with subscription issues", "@contactCustomerServiceDesc": {"description": "Contact customer service description"}, "refundPolicy": "Refund Policy", "@refundPolicy": {"description": "Refund policy button text"}, "refundPolicyDesc": "Learn about our refund and cancellation policies", "@refundPolicyDesc": {"description": "Refund policy description"}, "restoringPurchase": "Restoring Purchase", "@restoringPurchase": {"description": "Title shown when restoring purchases"}, "communicatingWithAppStore": "Communicating with App Store...", "@communicatingWithAppStore": {"description": "Message shown while communicating with App Store during restore"}, "noRestorablePurchasesFound": "No restorable purchases found", "@noRestorablePurchasesFound": {"description": "Message when no purchases can be restored"}, "currentSubscription": "Current Subscription", "@currentSubscription": {"description": "Current subscription label"}, "freeVersion": "Free Version", "@freeVersion": {"description": "Free version subscription name"}, "availableFeatures": "Available Features", "@availableFeatures": {"description": "Available features section title"}, "basicProcessing": "Basic Processing", "@basicProcessing": {"description": "Basic processing feature name"}, "exportWithWatermark": "Export with Watermark", "@exportWithWatermark": {"description": "Export with watermark feature name"}, "unlimitedExport": "Unlimited Export", "@unlimitedExport": {"description": "Unlimited export feature name"}, "batchProcessing": "Batch Processing", "@batchProcessing": {"description": "Batch processing feature name"}, "advancedTools": "Advanced Tools", "@advancedTools": {"description": "Advanced tools feature name"}, "markdownSavedMarkdown": "Saved <PERSON>", "@markdownSavedMarkdown": {"description": "Title for saved markdown content"}, "textCardNoCardsYet": "No cards yet", "@textCardNoCardsYet": {"description": "Message shown when no cards are available"}, "@textCardAddCard": {"description": "Button to add a new card"}, "@textCardEditCard": {"description": "Title for card editing dialog"}, "textCardTitle": "Title", "@textCardTitle": {"description": "Title field label in card editor"}, "textCardContent": "Content", "@textCardContent": {"description": "Content field label in card editor"}, "textCardNeedPhotoPermission": "Photo album permission is required to save images, please enable permission in settings", "@textCardNeedPhotoPermission": {"description": "Permission request message for photo album access"}, "textCardScreenshotFailed": "Screenshot failed, please try again", "@textCardScreenshotFailed": {"description": "Error message when screenshot capture fails"}, "@textCardImageSavedSuccess": {"description": "Success message when image is saved"}, "@textCardExportFailed": {"description": "Error message prefix for export failures"}, "textCardPermissionDenied": "Permission denied, please enable photo album permission in settings", "@textCardPermissionDenied": {"description": "Error message when permission is denied"}, "@textCardExportingImage": {"description": "Message shown during image export"}, "textCardExportCard": "Export Card", "@textCardExportCard": {"description": "Title for exporting a single card"}, "textCardExportDocument": "Export Document", "@textCardExportDocument": {"description": "Title for exporting a document"}, "@textCardExportSuccess": {"description": "Success message for export"}, "textCardPreview": "Preview", "@textCardPreview": {"description": "Preview section title"}, "textCardExporting": "Exporting...", "@textCardExporting": {"description": "Message shown during export"}, "textCardStartExport": "Start Export", "@textCardStartExport": {"description": "Button to start export"}, "textCardExportSize": "Export Size", "@textCardExportSize": {"description": "Export size section title"}, "textCardTargetPlatform": "Target Platform", "@textCardTargetPlatform": {"description": "Target platform section title"}, "textCardFileFormat": "File Format", "@textCardFileFormat": {"description": "File format section title"}, "textCardWatermarkSettings": "Watermark Settings", "@textCardWatermarkSettings": {"description": "Watermark settings section title"}, "@textCardAddWatermark": {"description": "Switch to add watermark"}, "textCardAddWatermarkSubtitle": "Add app watermark to image corner", "@textCardAddWatermarkSubtitle": {"description": "Subtitle for watermark switch"}, "textCardCustomWatermarkText": "Custom Watermark Text", "@textCardCustomWatermarkText": {"description": "Label for custom watermark text field"}, "textCardCustomWatermarkHint": "Leave empty to use default watermark", "@textCardCustomWatermarkHint": {"description": "Hint for custom watermark text field"}, "textCardAdvancedOptions": "Advanced Options", "@textCardAdvancedOptions": {"description": "Advanced options section title"}, "@textCardIncludeTitle": {"description": "Switch to include title"}, "textCardIncludeTitleSubtitle": "Show title in exported image", "@textCardIncludeTitleSubtitle": {"description": "Subtitle for title switch"}, "@textCardIncludeTimestamp": {"description": "Switch to include timestamp"}, "textCardIncludeTimestampSubtitle": "Show creation time in image", "@textCardIncludeTimestampSubtitle": {"description": "Subtitle for timestamp switch"}, "textCardMore": "More", "@textCardMore": {"description": "More items label"}, "@textCardSelectColor": {"description": "Color picker dialog title"}, "textCardPart": "Part", "@textCardPart": {"description": "Part label for card sections"}, "textCardSection": "Section", "@textCardSection": {"description": "Section label for card sections"}, "@textCardTextStyleCustomization": {"description": "Title for text style customization"}, "@textCardExportCurrentCard": {"description": "Button to export current card"}, "@textCardBatchExport": {"description": "Button to export multiple cards"}, "@textCardClearSelection": {"description": "Button to clear text selection"}, "@textCardResetStyle": {"description": "Button to reset text style"}, "textCardSelectionInstructions": "Select any range of text below to apply styles to the selected content", "@textCardSelectionInstructions": {"description": "Instructions for text selection"}, "@textCardResetStyleConfirm": {"description": "Confirmation message for resetting styles"}, "textCardExportOptions": "Export Options", "@textCardExportOptions": {"description": "Export options section title"}, "textCardSaveToGallery": "Save to Gallery", "@textCardSaveToGallery": {"description": "Save to gallery button text"}, "textCardExportSingleCard": "Will export 1 card image", "@textCardExportSingleCard": {"description": "Single card export message"}, "textCardExportMultipleCards": "Will export", "@textCardExportMultipleCards": {"description": "Multiple cards export prefix"}, "textCardImages": "card images", "@textCardImages": {"description": "Card images suffix"}, "textCardCard": "Card", "@textCardCard": {"description": "Card label"}, "textCardCardNumber": "", "@textCardCardNumber": {"description": "Card number suffix (empty in English)"}, "textCardShareFromApp": "From 内容君", "@textCardShareFromApp": {"description": "Share message prefix"}, "textCardShareFailed": "Share failed", "@textCardShareFailed": {"description": "Share failed message"}, "textCardSavedToGallery": "Saved", "@textCardSavedToGallery": {"description": "Saved to gallery prefix"}, "textCardCardsToGallery": "cards to gallery", "@textCardCardsToGallery": {"description": "Cards to gallery suffix"}, "@textCardSaveFailed": {"description": "Save failed message"}, "@textCardEditDocument": {"description": "Edit document title"}, "textCardCreateDocument": "Create Document", "@textCardCreateDocument": {"description": "Create document title"}, "textCardTextEditMode": "Text Edit Mode", "@textCardTextEditMode": {"description": "Text edit mode label"}, "textCardPreviewMode": "Preview Mode", "@textCardPreviewMode": {"description": "Preview mode label"}, "textCardTotalCards": "Total", "@textCardTotalCards": {"description": "Total cards prefix"}, "textCardCards": "cards", "@textCardCards": {"description": "Cards suffix"}, "@textCardInsertSeparator": {"description": "Insert separator button"}, "textCardUseSeparator": "Use", "@textCardUseSeparator": {"description": "Use separator prefix"}, "textCardSeparatorHint": "to separate cards", "@textCardSeparatorHint": {"description": "Separator hint suffix"}, "textCardDocumentTitle": "Document Title", "@textCardDocumentTitle": {"description": "Document title label"}, "textCardDocumentTitleHint": "Give this set of cards a title...", "@textCardDocumentTitleHint": {"description": "Document title hint"}, "textCardDocumentContentHint": "Enter or paste long text...\n\n💡 Tips:\n• Click \"Insert Separator\" where you want to split\n• First line will be auto-recognized as title if it looks like one\n• Click \"Preview\" in top right to see split effect", "@textCardDocumentContentHint": {"description": "Document content hint"}, "@textCardUniformStyle": {"description": "Uniform style button"}, "textCardGoBackToEditMode": "Go back to edit mode and add separators to create cards", "@textCardGoBackToEditMode": {"description": "Instruction to go back to edit mode"}, "textCardListView": "List View", "@textCardListView": {"description": "List view tooltip"}, "textCardGridView": "Grid View", "@textCardGridView": {"description": "Grid view tooltip"}, "textCardTemplate": "Template", "@textCardTemplate": {"description": "Template label"}, "textCardNoCardsInDocument": "No cards in document", "@textCardNoCardsInDocument": {"description": "No cards in document message"}, "textCardEditDocumentToAddCards": "Edit document to add cards", "@textCardEditDocumentToAddCards": {"description": "Edit document to add cards hint"}, "textCardDaysAgo": "days ago", "@textCardDaysAgo": {"description": "Days ago suffix"}, "textCardHoursAgo": "hours ago", "@textCardHoursAgo": {"description": "Hours ago suffix"}, "textCardMinutesAgo": "minutes ago", "@textCardMinutesAgo": {"description": "Minutes ago suffix"}, "textCardJustNow": "just now", "@textCardJustNow": {"description": "Just now label"}, "textCardPureTextCustomRendering": "Pure Text Custom Rendering", "@textCardPureTextCustomRendering": {"description": "Pure text custom rendering title"}, "textCardRenderContentToCards": "Render your content into beautiful cards", "@textCardRenderContentToCards": {"description": "Render content to cards subtitle"}, "textCardDesignDescription": "This is a separated design: simple editor for content editing and splitting, powerful visual renderer for style customization and final display.", "@textCardDesignDescription": {"description": "Design description"}, "textCardSimpleEdit": "Simple Edit", "@textCardSimpleEdit": {"description": "Simple edit feature title"}, "textCardSimpleEditDesc": "Focus on pure text editing and content splitting, no complex format interference", "@textCardSimpleEditDesc": {"description": "Simple edit feature description"}, "textCardVisualRendering": "Visual Rendering", "@textCardVisualRendering": {"description": "Visual rendering feature title"}, "textCardVisualRenderingDesc": "WYSIWYG style customization, select text to directly modify styles", "@textCardVisualRenderingDesc": {"description": "Visual rendering feature description"}, "textCardSmartRecognition": "Smart Recognition", "@textCardSmartRecognition": {"description": "Smart recognition feature title"}, "textCardSmartRecognitionDesc": "Automatically recognize content types like headings, lists, quotes and render beautifully", "@textCardSmartRecognitionDesc": {"description": "Smart recognition feature description"}, "textCardExportShare": "Export & Share", "@textCardExportShare": {"description": "Export share feature title"}, "textCardExportShareDesc": "Support single card and batch export, easily share beautiful content", "@textCardExportShareDesc": {"description": "Export share feature description"}, "textCardCoreFeatures": "Core Features", "@textCardCoreFeatures": {"description": "Core features section title"}, "textCardMarkdownTip": "Tip: Supports Markdown format text input, including headings, lists, quotes, etc.", "@textCardMarkdownTip": {"description": "Markdown tip"}, "textCardStartCreating": "Start Creating", "@textCardStartCreating": {"description": "Start creating button text"}, "textCardClickToEditContent": "Click to edit content", "@textCardClickToEditContent": {"description": "Edit content hint text"}, "textCardsLightCategory": "Light", "@textCardsLightCategory": {"description": "Light template category"}, "textCardsDarkCategory": "Dark", "@textCardsDarkCategory": {"description": "Dark template category"}, "textCardsNatureCategory": "Nature", "@textCardsNatureCategory": {"description": "Nature template category"}, "textCardsWarmCategory": "Warm", "@textCardsWarmCategory": {"description": "Warm template category"}, "textCardsTechCategory": "Tech", "@textCardsTechCategory": {"description": "Tech template category"}, "textCardsElegantCategory": "Elegant", "@textCardsElegantCategory": {"description": "Elegant template category"}, "textCardsVintageCategory": "Vintage", "@textCardsVintageCategory": {"description": "Vintage template category"}, "textCardsPreviewEffect": "Preview Effect", "@textCardsPreviewEffect": {"description": "Preview effect title"}, "textCardCreateBeautifulCard": "Create Beautiful Card", "@textCardCreateBeautifulCard": {"description": "Create beautiful card button"}, "textCardsSelectTextToModifyStyle": "Select text to modify font, color and size", "@textCardsSelectTextToModifyStyle": {"description": "Tip for selecting text to modify style"}, "textCardsSelectTemplate": "Select Template", "@textCardsSelectTemplate": {"description": "Select template label"}, "textCardsTemplateGallery": "Template Gallery", "blockMarkdown": "Block Markdown", "cardCollection": "Card Collection", "contentDefaultsTitle": "De<PERSON><PERSON> Content", "markdownDefaultSampleTitle": "<PERSON><PERSON> Default <PERSON>", "markdownDefaultSampleDesc": "Auto-fill example content in Markdown", "textCardsDefaultSampleTitle": "Text Cards Default <PERSON>", "textCardsDefaultSampleDesc": "Auto-fill example content in Text Cards", "svgBuiltInPresetTitle": "SVG Built-in Preset", "svgBuiltInPresetDesc": "Show built-in SVG preset when no document exists", "htmlDefaultSampleTitle": "HTML Default Sample", "htmlDefaultSampleDesc": "Load built-in HTML sample when no document exists", "transformerExampleInputTitle": "Transformer Example Input", "transformerExampleInputDesc": "Auto-fill example input in Text Transformer", "privacyPolicyTitle": "Privacy Policy", "privacyPolicySubtitle": "Read our privacy policy", "openSourceLicensesTitle": "Open Source Licenses", "openSourceLicensesSubtitle": "View third‑party licenses", "subscriptionUpgradeTitle": "Upgrade to Premium", "subscriptionUpgradeSubtitle": "Unlock all premium features and enhance your AI experience", "subscriptionChoosePlan": "Choose your subscription plan", "subscriptionDiscountSavePercent": "Save {percent}%", "subscriptionLoadingPrice": "Loading price…", "subscriptionEquivalentToPerMonth": "Equivalent to {price}", "subscriptionIncludedFeatures": "Included features", "subscriptionSubscribeNowWithPrice": "Subscribe Now {price}", "subscriptionAgreementPrefix": "By subscribing, you agree to our ", "termsOfService": "Terms of Service", "privacyPolicy": "Privacy Policy", "subscriptionAgreementSuffix": ". Subscriptions auto‑renew and can be cancelled anytime.", "subscriptionDevModeNotice": "Development mode: Subscription uses mock data for now; real purchases will be enabled after App Store Connect approval", "diagnosticsTitle": "Diagnostics", "diagnosticsClose": "Close", "subscriptionDiagnosticsButton": "Diagnose Purchase Service", "restoreCompletedTitle": "Restore Completed", "restoreFailedTitle": "Restore Failed", "restoreCompletedMessage": "Your purchases have been successfully restored.", "restoreFailedMessageWithError": "An error occurred while restoring: {error}", "andText": "and", "subscriptionPlanMonthlyName": "ContentPal Premium (Monthly)", "subscriptionPlanMonthlyDesc": "Unlock all advanced content processing features", "subscriptionPlanYearlyName": "ContentPal Premium (Yearly)", "subscriptionPlanYearlyDesc": "Annual subscription, More economical!", "subscriptionPlanLifetimeName": "ContentPal Premium (Lifetime)", "subscriptionPlanLifetimeDesc": "One-time purchase, lifetime access", "subscriptionPlanFreeName": "Free", "subscriptionPlanFreeDesc": "Basic content processing features", "subscriptionFeatureUnlimitedExportsName": "Unlimited exports", "subscriptionFeatureUnlimitedExportsDesc": "Export processed content without limits", "subscriptionFeatureBatchProcessingName": "Batch processing", "subscriptionFeatureBatchProcessingDesc": "Process multiple files at once", "subscriptionFeatureAdvancedToolsName": "Advanced tools", "subscriptionFeatureAdvancedToolsDesc": "Access all advanced editing and processing tools", "subscriptionFeatureNoWatermarkName": "No watermark", "subscriptionFeatureNoWatermarkDesc": "Exports do not include watermarks", "subscriptionFeaturePrioritySupportName": "Priority support", "subscriptionFeaturePrioritySupportDesc": "Get priority customer support", "subscriptionFeatureFutureUpdatesName": "Future updates", "subscriptionFeatureFutureUpdatesDesc": "Receive all future feature updates", "subscriptionFeatureBasicProcessingName": "Basic processing", "subscriptionFeatureBasicProcessingDesc": "Basic content processing features", "subscriptionFeatureWatermarkedExportsName": "Watermarked exports", "subscriptionFeatureWatermarkedExportsDesc": "Exports include watermark", "subscriptionPeriodPerMonthSuffix": "/month", "subscriptionPeriodPerYearSuffix": "/year", "subscriptionPeriodLifetime": "Lifetime", "subscriptionPeriodFree": "Free", "purchaseSuccessMessage": "Purchase successful. Your subscription is now active. Thank you!", "@purchaseSuccessMessage": {"description": "Message shown when subscription purchase is successful"}, "currentPlan": "Current Plan", "@currentPlan": {"description": "Text shown when user is viewing their current subscription plan"}, "expired": "Expired", "@expired": {"description": "Status text for expired subscription"}, "daysUntilExpiry": "{days} days until expiry", "@daysUntilExpiry": {"description": "Text showing days until subscription expires", "placeholders": {"days": {"type": "int"}}}, "subscriptionExpired": "Expired", "@subscriptionExpired": {"description": "Text shown when subscription has expired"}, "@textCardsTemplateGallery": {"description": "Template gallery title"}, "textCardsBrowseTemplates": "Browse Templates", "@textCardsBrowseTemplates": {"description": "Browse templates button"}, "textCardsBeautifulTemplates": "beautiful templates", "@textCardsBeautifulTemplates": {"description": "Beautiful templates text"}, "textCardsAllCategories": "All", "@textCardsAllCategories": {"description": "All categories filter"}, "textCardsBusinessCategory": "Business", "@textCardsBusinessCategory": {"description": "Business category name"}, "textCardsAcademicCategory": "Academic", "@textCardsAcademicCategory": {"description": "Academic category name"}, "textCardsCreativeCategory": "Creative", "@textCardsCreativeCategory": {"description": "Creative category name"}, "textCardsMinimalCategory": "Minimal", "@textCardsMinimalCategory": {"description": "Minimal category name"}, "textCardsModernCategory": "Modern", "@textCardsModernCategory": {"description": "Modern category name"}, "@textCardSplitFailed": {"description": "Split failed error message"}, "@textCardCreateFailed": {"description": "Create failed error message"}, "textCardExportSettings": "Export Settings", "@textCardExportSettings": {"description": "Export settings title"}, "textCardImageSize": "Image Size", "@textCardImageSize": {"description": "Image size setting"}, "textCardImageRatio": "Image Ratio", "@textCardImageRatio": {"description": "Image ratio setting"}, "@textCardQuality": {"description": "Quality setting"}, "@textCardIncludeWatermark": {"description": "Include watermark option"}, "smartTextSplitter": "Smart Text Splitter", "@smartTextSplitter": {"description": "Smart text splitter title"}, "smartTextSplitterSubtitle": "Split long text into multiple cards intelligently", "@smartTextSplitterSubtitle": {"description": "Smart text splitter subtitle"}, "characterCount": "Character count: {count}", "@characterCount": {"description": "Character count format", "placeholders": {"count": {"type": "int", "description": "Character count"}}}, "splitConfig": "Split Configuration", "@splitConfig": {"description": "Split configuration title"}, "splitConfigDescription": "Choose split mode and related parameters", "@splitConfigDescription": {"description": "Split configuration description"}, "splitMode": "Split Mode", "@splitMode": {"description": "Split mode section title"}, "customSeparator": "Custom Separator", "@customSeparator": {"description": "Custom separator field"}, "customSeparatorHint": "Enter separator, such as: ---", "@customSeparatorHint": {"description": "Custom separator hint"}, "advancedOptions": "Advanced Options", "@advancedOptions": {"description": "Advanced options section title"}, "autoDetectTitles": "Auto-detect Titles", "@autoDetectTitles": {"description": "Auto-detect titles option"}, "autoDetectTitlesDescription": "Intelligently identify title content in text", "@autoDetectTitlesDescription": {"description": "Auto-detect titles description"}, "preserveFormatting": "Preserve Formatting", "@preserveFormatting": {"description": "Preserve formatting option"}, "preserveFormattingDescription": "Preserve original text Markdown formatting", "@preserveFormattingDescription": {"description": "Preserve formatting description"}, "smartMerge": "Smart Merge", "@smartMerge": {"description": "Smart merge option"}, "smartMergeDescription": "Automatically merge short paragraphs", "@smartMergeDescription": {"description": "Smart merge description"}, "maxLength": "Max Length", "@maxLength": {"description": "Max length setting"}, "maxLengthDescription": "Maximum characters per card", "@maxLengthDescription": {"description": "Max length description"}, "splitPreview": "Split Preview", "@splitPreview": {"description": "Split preview title"}, "totalCards": "Total {count} cards", "@totalCards": {"description": "Total cards count format", "placeholders": {"count": {"type": "int", "description": "Card count"}}}, "splitPreviewDescription": "Preview split results, can edit, merge or delete cards", "@splitPreviewDescription": {"description": "Split preview description"}, "deselectAll": "Deselect All", "@deselectAll": {"description": "Deselect all button"}, "deleteSelected": "Delete Selected", "@deleteSelected": {"description": "Delete selected button"}, "noSplitResults": "No Split Results", "@noSplitResults": {"description": "No split results message"}, "noSplitResultsDescription": "Please return to previous step to check input text and configuration", "@noSplitResultsDescription": {"description": "No split results description"}, "previousStep": "Previous", "@previousStep": {"description": "Previous step button"}, "nextStep": "Next", "@nextStep": {"description": "Next step button"}, "startSplitting": "Start Splitting", "@startSplitting": {"description": "Start splitting button"}, "createCards": "Create Cards", "@createCards": {"description": "Create cards button"}, "inputTextHint": "Enter or paste text content here...", "@inputTextHint": {"description": "Input text hint"}, "titleOptional": "Title (Optional)", "@titleOptional": {"description": "Title optional field"}, "exportSettings": "Export Settings", "@exportSettings": {"description": "Export settings title"}, "confirmExport": "Confirm Export", "@confirmExport": {"description": "Confirm export button"}, "previewInfo": "Preview Info", "@previewInfo": {"description": "Preview info section"}, "dimensions": "Dimensions", "@dimensions": {"description": "Dimensions label"}, "ratio": "<PERSON><PERSON>", "@ratio": {"description": "Ratio label"}, "qualityPercent": "Quality", "@qualityPercent": {"description": "Quality percentage label"}, "watermarkStatus": "Watermark", "@watermarkStatus": {"description": "Watermark status label"}, "include": "Include", "@include": {"description": "Include status"}, "notInclude": "Not Include", "@notInclude": {"description": "Not include status"}, "pixels": "pixels", "@pixels": {"description": "Pixels unit"}, "pdfCreatedTime": "Created", "@pdfCreatedTime": {"description": "Created time label"}, "pdfModifiedTime": "Modified", "@pdfModifiedTime": {"description": "Modified time label"}, "pdfPermissionsSuccess": "Permissions set successfully", "@pdfPermissionsSuccess": {"description": "Permissions set success message"}, "pdfPermissionsFailed": "Permissions set failed", "@pdfPermissionsFailed": {"description": "Permissions set failed message"}, "pdfSettingsFailed": "Settings failed: {error}", "@pdfSettingsFailed": {"description": "<PERSON><PERSON><PERSON> failed with error"}, "pdfDocumentInformation": "Document Information", "@pdfDocumentInformation": {"description": "Document information section title"}, "pdfEncryptedMessage": "This PDF is encrypted, please enter password to decrypt", "@pdfEncryptedMessage": {"description": "PDF encrypted message"}, "pdfSecurityEncrypted": "Encrypted", "@pdfSecurityEncrypted": {"description": "Encrypted security status"}, "pdfSecurityReadOnly": "Read Only", "@pdfSecurityReadOnly": {"description": "Read only security status"}, "pdfSecurityRestricted": "Restricted", "@pdfSecurityRestricted": {"description": "Restricted security status"}, "pdfSecurityOpen": "Open", "@pdfSecurityOpen": {"description": "Open security status"}, "pdfAllowCopying": "Allow Copying", "@pdfAllowCopying": {"description": "Allow copying permission"}, "pdfMultipleFormats": "Multiple Formats", "@pdfMultipleFormats": {"description": "Multiple formats feature"}, "pdfMultiDeviceSync": "Multi-device Sync", "@pdfMultiDeviceSync": {"description": "Multi-device sync feature"}, "pdfProtectYourDocuments": "Protect your important documents", "@pdfProtectYourDocuments": {"description": "Document protection subtitle"}, "pdfPasswordProtectionDesc": "Set user and owner passwords for PDF documents to ensure document security", "@pdfPasswordProtectionDesc": {"description": "Password protection description"}, "pdfPermissionControlDesc": "Fine-grained control over document printing, copying, editing and other permissions", "@pdfPermissionControlDesc": {"description": "Permission control description"}, "pdfEncryptionAlgorithm": "Encryption Algorithm", "@pdfEncryptionAlgorithm": {"description": "Encryption algorithm feature"}, "pdfEncryptionAlgorithmDesc": "Adopt industry-standard AES encryption algorithm to ensure document security", "@pdfEncryptionAlgorithmDesc": {"description": "Encryption algorithm description"}, "pdfImportToStart": "Import PDF to Start", "@pdfImportToStart": {"description": "Import PDF to start button"}, "pdfUsageTips": "Usage Tips", "@pdfUsageTips": {"description": "Usage tips section"}, "htmlUntitled": "Untitled HTML", "@htmlUntitled": {"description": "Default HTML document title"}, "htmlNewDocumentTitle": "New HTML Document", "@htmlNewDocumentTitle": {"description": "New HTML document title"}, "htmlCopy": "Copy", "@htmlCopy": {"description": "Copy suffix for document title"}, "htmlRenderError": "Cannot render HTML content", "@htmlRenderError": {"description": "Error message when HTML cannot be rendered"}, "voiceHomeTitle": "Voice Assistant", "@voiceHomeTitle": {"description": "Voice home page title"}, "voiceHomeSubtitle": "Record ideas, convert to text, smart reading", "@voiceHomeSubtitle": {"description": "Voice home page subtitle"}, "voiceUsageStats": "Usage Statistics", "@voiceUsageStats": {"description": "Usage statistics section title"}, "voiceRecordingCount": "Recording Count", "@voiceRecordingCount": {"description": "Recording count label"}, "voiceRecordingsUnit": "recordings", "@voiceRecordingsUnit": {"description": "Recordings unit"}, "voiceTotalDuration": "Total Duration", "@voiceTotalDuration": {"description": "Total duration label"}, "voiceCumulativeDuration": "Cumulative Duration", "@voiceCumulativeDuration": {"description": "Cumulative duration label"}, "voiceQuickActions": "Quick Actions", "@voiceQuickActions": {"description": "Quick actions section title"}, "voiceRecordNewVoice": "Record new voice", "@voiceRecordNewVoice": {"description": "Record new voice subtitle"}, "voiceTextToSpeech": "Text to Speech", "@voiceTextToSpeech": {"description": "Text to speech button"}, "voiceConvertTextToVoice": "Convert text to voice", "@voiceConvertTextToVoice": {"description": "Convert text to voice subtitle"}, "voicePowerfulFeatures": "Powerful Features", "@voicePowerfulFeatures": {"description": "Powerful features section title"}, "voiceSmartTranscription": "Smart Transcription", "@voiceSmartTranscription": {"description": "Smart transcription feature title"}, "voiceSmartTranscriptionDesc": "Automatically convert voice to text", "@voiceSmartTranscriptionDesc": {"description": "Smart transcription feature description"}, "voiceAudioAdjustment": "Audio Adjustment", "@voiceAudioAdjustment": {"description": "Audio adjustment feature title"}, "voiceAudioAdjustmentDesc": "Adjust speed, pitch and volume", "@voiceAudioAdjustmentDesc": {"description": "Audio adjustment feature description"}, "voicePlaylist": "Playlist", "@voicePlaylist": {"description": "Playlist feature title"}, "voicePlaylistDesc": "Manage and play multiple audio files", "@voicePlaylistDesc": {"description": "Playlist feature description"}, "voiceCloudSync": "Cloud Sync", "@voiceCloudSync": {"description": "Cloud sync feature title"}, "voiceCloudSyncDesc": "Sync your recordings across devices", "@voiceCloudSyncDesc": {"description": "Cloud sync feature description"}, "voiceRecentRecordings": "Recent Recordings", "@voiceRecentRecordings": {"description": "Recent recordings section title"}, "voiceViewAll": "View All", "@voiceViewAll": {"description": "View all button"}, "voiceNoRecordingsYet": "No recordings yet", "@voiceNoRecordingsYet": {"description": "No recordings yet message"}, "voiceStartFirstRecording": "Click the button below to start your first recording", "@voiceStartFirstRecording": {"description": "Start first recording instruction"}, "voiceRecordDetailTitle": "Voice Details", "@voiceRecordDetailTitle": {"description": "Voice record detail page title"}, "voiceSaveAllChanges": "Save all changes", "@voiceSaveAllChanges": {"description": "Save all changes tooltip"}, "voiceRecordingFileMayBeCorrupted": "Recording file may be corrupted, cannot play", "@voiceRecordingFileMayBeCorrupted": {"description": "Recording file corrupted message"}, "voiceAudioFileNotExistOrCorrupted": "Audio file does not exist or is corrupted", "@voiceAudioFileNotExistOrCorrupted": {"description": "Audio file not exist or corrupted message"}, "voiceFilePath": "File path: {path}", "@voiceFilePath": {"description": "File path label"}, "voiceRecheck": "Recheck", "@voiceRecheck": {"description": "Recheck button"}, "voiceFileStatusRechecked": "File status rechecked: {status}{corrupted}", "@voiceFileStatusRechecked": {"description": "File status rechecked message"}, "voiceFileExists": "file exists", "@voiceFileExists": {"description": "File exists status"}, "voiceFileNotExists": "file does not exist", "@voiceFileNotExists": {"description": "File not exists status"}, "voiceFileButCorrupted": ", but file may be corrupted", "@voiceFileButCorrupted": {"description": "File corrupted suffix"}, "voiceVoiceTranscription": "Voice Transcription", "@voiceVoiceTranscription": {"description": "Voice transcription section title"}, "voiceReadText": "Read Text", "@voiceReadText": {"description": "Read text button"}, "voiceNoTranscriptionText": "No transcription text yet", "@voiceNoTranscriptionText": {"description": "No transcription text hint"}, "voiceSaveTranscriptionText": "Save Transcription Text", "@voiceSaveTranscriptionText": {"description": "Save transcription text button"}, "voiceCreateTime": "Create Time: {time}", "@voiceCreateTime": {"description": "Create time label"}, "voicePlaying": "Playing...", "@voicePlaying": {"description": "Playing status"}, "voicePaused": "Paused", "@voicePaused": {"description": "Paused status"}, "voiceAudioFileNotExist": "Audio file does not exist, cannot play", "@voiceAudioFileNotExist": {"description": "Audio file not exist message"}, "voiceAudioDurationAbnormal": "Audio duration abnormal, may not play properly", "@voiceAudioDurationAbnormal": {"description": "Audio duration abnormal message"}, "voiceLoadAudioFailed": "Failed to load audio: {error}", "@voiceLoadAudioFailed": {"description": "Load audio failed message"}, "voicePlayFailed": "Play failed: {error}", "@voicePlayFailed": {"description": "Play failed message"}, "voiceTitleSaved": "Title saved", "@voiceTitleSaved": {"description": "Title saved message"}, "voiceTranscriptionTextSaved": "Transcription text saved", "@voiceTranscriptionTextSaved": {"description": "Transcription text saved message"}, "voiceTtsPlayerTitle": "Text to Speech", "@voiceTtsPlayerTitle": {"description": "TTS player page title"}, "voiceInputTextToRead": "Input text to read", "@voiceInputTextToRead": {"description": "Input text to read label"}, "voiceInputTextHint": "Input text to read, click \"Add\" button to add to playlist", "@voiceInputTextHint": {"description": "Input text hint"}, "voiceAddToPlaylist": "Add to playlist", "@voiceAddToPlaylist": {"description": "Add to playlist tooltip"}, "voiceAddedToPlaylist": "Added to playlist", "@voiceAddedToPlaylist": {"description": "Added to playlist message"}, "voiceTtsSettings": "TTS Settings", "@voiceTtsSettings": {"description": "TTS settings section title"}, "voiceSpeechRate": "Speech Rate:", "@voiceSpeechRate": {"description": "Speech rate label"}, "voicePitch": "Pitch:", "@voicePitch": {"description": "Pitch label"}, "voiceVolume": "Volume:", "@voiceVolume": {"description": "Volume label"}, "voiceLanguage": "Language:", "@voiceLanguage": {"description": "Language label"}, "voicePlaylistTitle": "Playlist", "@voicePlaylistTitle": {"description": "Playlist section title"}, "voiceStop": "Stop", "@voiceStop": {"description": "Stop button"}, "voicePlaylistEmpty": "Playlist is empty", "@voicePlaylistEmpty": {"description": "Playlist empty message"}, "voiceTranscriptionTitle": "Smart Transcription", "@voiceTranscriptionTitle": {"description": "Transcription page title"}, "voiceRealtimeTranscription": "Realtime Transcription", "@voiceRealtimeTranscription": {"description": "Realtime transcription mode"}, "voiceFileTranscription": "File Transcription", "@voiceFileTranscription": {"description": "File transcription mode"}, "voiceBatchTranscription": "Batch Transcription", "@voiceBatchTranscription": {"description": "Batch transcription mode"}, "voiceSelectAudioFile": "Select audio file for transcription", "@voiceSelectAudioFile": {"description": "Select audio file instruction"}, "voiceFileSelected": "File Selected", "@voiceFileSelected": {"description": "File selected status"}, "voiceSelectFile": "Select File", "@voiceSelectFile": {"description": "Select file button"}, "voiceReselectFile": "Reselect File", "@voiceReselectFile": {"description": "Reselect file button"}, "voiceTranscriptionResult": "Transcription Result", "@voiceTranscriptionResult": {"description": "Transcription result section title"}, "voiceBatchTranscriptionFeature": "Batch Transcription Feature", "@voiceBatchTranscriptionFeature": {"description": "Batch transcription feature title"}, "voiceSelectMultipleFiles": "Select multiple files for batch transcription", "@voiceSelectMultipleFiles": {"description": "Select multiple files instruction"}, "voiceSelectMultipleFilesBtn": "Select Multiple Files", "@voiceSelectMultipleFilesBtn": {"description": "Select multiple files button"}, "voiceBatchProcessingProgress": "Batch Processing Progress", "@voiceBatchProcessingProgress": {"description": "Batch processing progress section title"}, "voiceBatchTranscriptionInDev": "Batch transcription feature in development...", "@voiceBatchTranscriptionInDev": {"description": "Batch transcription in development message"}, "voiceReadyToStart": "Ready to start", "@voiceReadyToStart": {"description": "Ready to start status"}, "voiceTranscribing": "Transcribing...", "@voiceTranscribing": {"description": "Transcribing status"}, "voiceClickToStartRealtime": "Click button below to start realtime transcription", "@voiceClickToStartRealtime": {"description": "Click to start realtime transcription instruction"}, "voiceExport": "Export", "@voiceExport": {"description": "Export button"}, "voiceShare": "Share", "@voiceShare": {"description": "Share button"}, "voiceNoTranscriptionContent": "No transcription content to export", "@voiceNoTranscriptionContent": {"description": "No transcription content to export message"}, "voiceNoTranscriptionContentToShare": "No transcription content to share", "@voiceNoTranscriptionContentToShare": {"description": "No transcription content to share message"}, "voiceExportFeatureInDev": "Export feature in development...", "@voiceExportFeatureInDev": {"description": "Export feature in development message"}, "voiceShareFeatureInDev": "Share feature in development...", "@voiceShareFeatureInDev": {"description": "Share feature in development message"}, "voiceTranscriptionSettings": "Transcription Settings", "@voiceTranscriptionSettings": {"description": "Transcription settings dialog title"}, "voiceEnablePunctuation": "Enable Punctuation", "@voiceEnablePunctuation": {"description": "Enable punctuation setting"}, "voiceAutoAddPunctuation": "Automatically add punctuation", "@voiceAutoAddPunctuation": {"description": "Auto add punctuation description"}, "voiceSpeakerDetection": "Speaker Detection", "@voiceSpeakerDetection": {"description": "Speaker detection setting"}, "voiceDetectDifferentSpeakers": "Detect different speakers", "@voiceDetectDifferentSpeakers": {"description": "Detect different speakers description"}, "voiceConfidenceThreshold": "Confidence Threshold", "@voiceConfidenceThreshold": {"description": "Confidence threshold setting"}, "voicePermissionRequiredMessage": "Voice transcription feature requires microphone permission. Please enable microphone access in settings.", "@voicePermissionRequiredMessage": {"description": "Permission required message"}, "voiceRecordingComplete": "Recording complete", "@voiceRecordingComplete": {"description": "Recording complete message"}, "voiceRecordingFailedRetry": "Recording failed, please retry", "@voiceRecordingFailedRetry": {"description": "Recording failed retry message"}, "voiceSelectFileFailed": "Failed to select file: {error}", "@voiceSelectFileFailed": {"description": "Select file failed message"}, "voiceProcessAudioFileFailed": "Failed to process audio file: {error}", "@voiceProcessAudioFileFailed": {"description": "Process audio file failed message"}, "voiceSelectBatchFilesFailed": "Failed to select batch files: {error}", "@voiceSelectBatchFilesFailed": {"description": "Select batch files failed message"}, "voiceFilesSelected": "Selected {count} files", "@voiceFilesSelected": {"description": "Files selected message"}, "voiceNoTranscriptionContentToSave": "No transcription content to save", "@voiceNoTranscriptionContentToSave": {"description": "No transcription content to save message"}, "voiceSaveTranscriptionResult": "Save Transcription Result", "@voiceSaveTranscriptionResult": {"description": "Save transcription result dialog title"}, "voiceTranscriptionContentPreview": "Transcription content preview:", "@voiceTranscriptionContentPreview": {"description": "Transcription content preview label"}, "voiceTranscriptionResultSaved": "Transcription result saved", "@voiceTranscriptionResultSaved": {"description": "Transcription result saved message"}, "voiceSaveFailed": "Save failed: {error}", "@voiceSaveFailed": {"description": "Save failed message"}, "voiceTranscriptionFailedRetry": "Transcription failed, please retry", "@voiceTranscriptionFailedRetry": {"description": "Transcription failed retry message"}, "voiceSmartTranscriptionPageTitle": "Smart Transcription", "@voiceSmartTranscriptionPageTitle": {"description": "Smart transcription page title"}, "voiceInitializationFailedCheckPermission": "Initialization failed, please check microphone permission", "@voiceInitializationFailedCheckPermission": {"description": "Initialization failed check permission message"}, "voiceInitializationException": "Initialization exception: {error}", "@voiceInitializationException": {"description": "Initialization exception message"}, "voiceServiceInitializationFailed": "Service initialization failed", "@voiceServiceInitializationFailed": {"description": "Service initialization failed message"}, "voiceStartTranscriptionFailed": "Failed to start transcription", "@voiceStartTranscriptionFailed": {"description": "Start transcription failed message"}, "voiceTranscriptionIdle": "Ready", "@voiceTranscriptionIdle": {"description": "Transcription idle status"}, "voiceTranscriptionInProgress": "Transcribing...", "@voiceTranscriptionInProgress": {"description": "Transcription in progress status"}, "voiceTranscriptionCompleted": "Transcription Completed", "@voiceTranscriptionCompleted": {"description": "Transcription completed status"}, "voiceTranscriptionError": "Transcription Error", "@voiceTranscriptionError": {"description": "Transcription error status"}, "voiceLanguageSelector": "Language:", "@voiceLanguageSelector": {"description": "Language selector label"}, "voiceTranscriptionResultTitle": "Transcription Result", "@voiceTranscriptionResultTitle": {"description": "Transcription result title"}, "voiceClickToStartTranscription": "Click start button to begin transcription...", "@voiceClickToStartTranscription": {"description": "Click to start transcription instruction"}, "voiceStopTranscription": "Stop Transcription", "@voiceStopTranscription": {"description": "Stop transcription button"}, "voiceClear": "Clear", "@voiceClear": {"description": "Clear button"}, "voiceIosPermissionTestTitle": "iOS Permission Test", "@voiceIosPermissionTestTitle": {"description": "iOS permission test page title"}, "voiceDirectIosPermissionTest": "Direct iOS Permission Test", "@voiceDirectIosPermissionTest": {"description": "Direct iOS permission test title"}, "voicePageLoaded": "Page loaded", "@voicePageLoaded": {"description": "Page loaded message"}, "voiceInitialMicPermissionStatus": "Initial microphone permission status: {status}", "@voiceInitialMicPermissionStatus": {"description": "Initial microphone permission status message"}, "voiceInitialSpeechPermissionStatus": "Initial speech recognition permission status: {status}", "@voiceInitialSpeechPermissionStatus": {"description": "Initial speech recognition permission status message"}, "voiceNonIosPlatform": "Non-iOS platform, not checking permissions", "@voiceNonIosPlatform": {"description": "Non-iOS platform message"}, "voiceRequestMicPermission": "Request Microphone Permission", "@voiceRequestMicPermission": {"description": "Request microphone permission button"}, "voiceMicPermissionStatus": "Microphone permission status: {status}", "@voiceMicPermissionStatus": {"description": "Microphone permission status message"}, "voiceRequestSpeechPermission": "Request Speech Recognition Permission", "@voiceRequestSpeechPermission": {"description": "Request speech recognition permission button"}, "voiceSpeechPermissionStatus": "Speech recognition permission status: {status}", "@voiceSpeechPermissionStatus": {"description": "Speech recognition permission status message"}, "voiceTestRecording": "Test Recording", "@voiceTestRecording": {"description": "Test recording button"}, "voiceTestRecordingFunction": "Test recording function...", "@voiceTestRecordingFunction": {"description": "Test recording function message"}, "voiceRecorderInstanceCreated": "Recorder instance created", "@voiceRecorderInstanceCreated": {"description": "Recorder instance created message"}, "voiceRecorderInitialized": "Recorder initialized", "@voiceRecorderInitialized": {"description": "Recorder initialized message"}, "voiceRecorderTestComplete": "Recorder test complete", "@voiceRecorderTestComplete": {"description": "Recorder test complete message"}, "voiceRecorderClosed": "Recorder closed", "@voiceRecorderClosed": {"description": "Recorder closed message"}, "voiceRecorderError": "Recorder error: {error}", "@voiceRecorderError": {"description": "Recorder error message"}, "voiceTestSpeechRecognition": "Test Speech Recognition", "@voiceTestSpeechRecognition": {"description": "Test speech recognition button"}, "voiceTestSpeechRecognitionFunction": "Test speech recognition...", "@voiceTestSpeechRecognitionFunction": {"description": "Test speech recognition function message"}, "voiceSpeechRecognitionError": "Speech recognition error: {error}", "@voiceSpeechRecognitionError": {"description": "Speech recognition error message"}, "voiceSpeechRecognitionStatus": "Speech recognition status: {status}", "@voiceSpeechRecognitionStatus": {"description": "Speech recognition status message"}, "voiceSpeechRecognitionInit": "Speech recognition init: {success}", "@voiceSpeechRecognitionInit": {"description": "Speech recognition init message"}, "voiceStartListening": "Start listening...", "@voiceStartListening": {"description": "Start listening message"}, "voiceRecognitionResult": "Recognition result: {result}", "@voiceRecognitionResult": {"description": "Recognition result message"}, "voiceStopListening": "Stop listening", "@voiceStopListening": {"description": "Stop listening message"}, "voiceSpeechRecognitionTestError": "Speech recognition test error: {error}", "@voiceSpeechRecognitionTestError": {"description": "Speech recognition test error message"}, "voiceOpenAppSettings": "Open App Settings", "@voiceOpenAppSettings": {"description": "Open app settings button"}, "voiceOperationLog": "Operation Log:", "@voiceOperationLog": {"description": "Operation log label"}, "voiceIosPageLoaded": "Page loaded", "@voiceIosPageLoaded": {"description": "iOS permission test page loaded message"}, "voiceIosInitialMicrophonePermission": "Initial microphone permission status", "@voiceIosInitialMicrophonePermission": {"description": "Initial microphone permission status message"}, "voiceIosInitialSpeechPermission": "Initial speech recognition permission status", "@voiceIosInitialSpeechPermission": {"description": "Initial speech recognition permission status message"}, "voiceIosNonIosPlatform": "Non-iOS platform, not checking permissions", "@voiceIosNonIosPlatform": {"description": "Non-iOS platform message"}, "voiceIosRequestMicrophonePermission": "Requesting microphone permission...", "@voiceIosRequestMicrophonePermission": {"description": "Requesting microphone permission message"}, "voiceIosMicrophonePermissionStatus": "Microphone permission status", "@voiceIosMicrophonePermissionStatus": {"description": "Microphone permission status message"}, "voiceIosRequestSpeechPermission": "Requesting speech recognition permission...", "@voiceIosRequestSpeechPermission": {"description": "Requesting speech recognition permission message"}, "voiceIosSpeechPermissionStatus": "Speech recognition permission status", "@voiceIosSpeechPermissionStatus": {"description": "Speech recognition permission status message"}, "voiceIosTestRecordingFunction": "Testing recording function...", "@voiceIosTestRecordingFunction": {"description": "Testing recording function message"}, "voiceIosRecorderInstanceCreated": "Recorder instance created", "@voiceIosRecorderInstanceCreated": {"description": "Recorder instance created message"}, "voiceIosRecorderInitialized": "Recorder initialized", "@voiceIosRecorderInitialized": {"description": "Recorder initialized message"}, "voiceIosRecorderTestCompleted": "Recorder test completed", "@voiceIosRecorderTestCompleted": {"description": "Recorder test completed message"}, "voiceIosRecorderClosed": "Recorder closed", "@voiceIosRecorderClosed": {"description": "Recorder closed message"}, "voiceIosRecorderError": "Recorder error", "@voiceIosRecorderError": {"description": "Recorder error message"}, "voiceIosTestSpeechRecognition": "Testing speech recognition...", "@voiceIosTestSpeechRecognition": {"description": "Testing speech recognition message"}, "voiceIosSpeechRecognitionError": "Speech recognition error", "@voiceIosSpeechRecognitionError": {"description": "Speech recognition error message"}, "voiceIosSpeechRecognitionStatus": "Speech recognition status", "@voiceIosSpeechRecognitionStatus": {"description": "Speech recognition status message"}, "voiceIosSpeechRecognitionInitialization": "Speech recognition initialization", "@voiceIosSpeechRecognitionInitialization": {"description": "Speech recognition initialization message"}, "voiceIosSuccess": "Success", "@voiceIosSuccess": {"description": "Success message"}, "voiceIosFailed": "Failed", "@voiceIosFailed": {"description": "Failed message"}, "voiceIosStartListening": "Start listening...", "@voiceIosStartListening": {"description": "Start listening message"}, "voiceIosRecognitionResult": "Recognition result", "@voiceIosRecognitionResult": {"description": "Recognition result message"}, "voiceIosStopListening": "Stop listening", "@voiceIosStopListening": {"description": "Stop listening message"}, "voiceIosSpeechRecognitionTestError": "Speech recognition test error", "@voiceIosSpeechRecognitionTestError": {"description": "Speech recognition test error message"}, "voiceIosOpenAppSettings": "Open app settings", "@voiceIosOpenAppSettings": {"description": "Open app settings message"}, "voiceIosPermissionTest": "iOS Permission Test", "@voiceIosPermissionTest": {"description": "iOS permission test page title"}, "voiceIosDirectPermissionTest": "Direct iOS Permission Test", "@voiceIosDirectPermissionTest": {"description": "Direct iOS permission test title"}, "voiceIosOperationLogs": "Operation Logs", "@voiceIosOperationLogs": {"description": "Operation logs label"}, "voiceOK": "OK", "@voiceOK": {"description": "OK button text"}, "voiceMicrophonePermissionRequired": "Voice transcription requires microphone permission. Please allow microphone access in settings.", "@voiceMicrophonePermissionRequired": {"description": "Microphone permission required message"}, "voiceLanguageChineseSimplified": "中文（简体）", "@voiceLanguageChineseSimplified": {"description": "Chinese simplified language option"}, "voiceLanguageChineseTraditional": "中文（繁体）", "@voiceLanguageChineseTraditional": {"description": "Chinese traditional language option"}, "voiceLanguageEnglish": "English (US)", "@voiceLanguageEnglish": {"description": "English language option"}, "voiceLanguageJapanese": "日本語", "@voiceLanguageJapanese": {"description": "Japanese language option"}, "voiceLanguageKorean": "한국어", "@voiceLanguageKorean": {"description": "Korean language option"}, "trafficGuideWatermarkTitle": "Text Watermark Processing", "@trafficGuideWatermarkTitle": {"description": "Title for the traffic guide watermark processing screen"}, "trafficGuideAddWatermarkModeSubtitle": "Add visible or invisible watermark to text", "@trafficGuideAddWatermarkModeSubtitle": {"description": "Subtitle for add watermark mode"}, "trafficGuideRemoveWatermarkModeSubtitle": "Remove added watermark from text", "@trafficGuideRemoveWatermarkModeSubtitle": {"description": "Subtitle for remove watermark mode"}, "trafficGuideVisibleWatermark": "Visible Watermark", "@trafficGuideVisibleWatermark": {"description": "Visible watermark option"}, "trafficGuideWatermarkType": "Watermark Type", "@trafficGuideWatermarkType": {"description": "Watermark type selection"}, "trafficGuideProcessFailed": "Processing failed: {error}", "@trafficGuideProcessFailed": {"description": "Process failure message"}, "trafficGuideShowPreview": "Show Preview", "@trafficGuideShowPreview": {"description": "Show preview button text"}, "trafficGuideHidePreview": "Hide Preview", "@trafficGuideHidePreview": {"description": "Hide preview button text"}, "trafficGuideProcessSuccess": "Processing successful", "@trafficGuideProcessSuccess": {"description": "Process success message"}, "trafficGuideDetectedWatermark": "Detected Watermark", "@trafficGuideDetectedWatermark": {"description": "Label for detected watermark information"}, "trafficGuideUnknownWatermark": "Unknown Watermark", "@trafficGuideUnknownWatermark": {"description": "Label for unknown watermark type"}, "trafficGuideNoWatermarkDetected": "No Watermark Detected", "@trafficGuideNoWatermarkDetected": {"description": "Message when no watermark is found"}, "trafficGuideProcessedText": "Processed Text", "@trafficGuideProcessedText": {"description": "Label for processed text output"}, "trafficGuideInvisibleWatermarkInfo": "This will add an invisible watermark using special Unicode characters that won't be visible to readers but can be detected by this tool.", "@trafficGuideInvisibleWatermarkInfo": {"description": "Information about invisible watermark functionality"}, "trafficGuideWatermarkRemovedSuccess": "Watermark removed successfully!", "@trafficGuideWatermarkRemovedSuccess": {"description": "Success message when watermark is removed"}, "trafficGuideWatermarkAddedSuccess": "Watermark added successfully!", "@trafficGuideWatermarkAddedSuccess": {"description": "Success message when watermark is added"}, "exportSocialWeChatMoments": "WeChat Moments", "exportSocialWeibo": "Weibo Image", "exportSocialXiaohongshu": "Xiaohongshu", "exportSocialInstagram": "Instagram", "exportSocialTwitter": "Twitter", "exportWidthLabel": "<PERSON><PERSON><PERSON>", "exportHeightLabel": "Height", "exportOptimizeForSocial": "Social Media Optimization", "exportOptimizeForSocialSubtitle": "Optimize size for social platforms", "exportSocialPlatformSizes": "Social Platform Sizes", "exportSizeSmall": "Small (400×300)", "exportSizeMedium": "Medium (800×600)", "exportSizeLarge": "Large (1200×900)", "exportSizeCustom": "Custom", "general": "General", "@general": {"description": "General settings title"}, "appearanceAndBrightness": "Display & Brightness", "@appearanceAndBrightness": {"description": "Display & Brightness settings title"}, "privacyAndSecurity": "Privacy & Security", "@privacyAndSecurity": {"description": "Privacy & Security settings title"}, "storage": "Storage", "@storage": {"description": "Storage settings title"}, "support": "Support", "@support": {"description": "Support settings title"}, "languageAndRegion": "Language & Region", "@languageAndRegion": {"description": "Language & Region setting item"}, "privacySettings": "Privacy Settings", "@privacySettings": {"description": "Privacy Settings item"}, "storageManagement": "Storage Management", "@storageManagement": {"description": "Storage Management item"}, "viewStorageUsage": "View storage usage", "@viewStorageUsage": {"description": "View storage usage description"}, "dataImportExport": "Data Import & Export", "@dataImportExport": {"description": "Data Import & Export item"}, "backupAndRestoreData": "Backup and restore data", "@backupAndRestoreData": {"description": "Backup and restore data description"}, "helpCenter": "Help Center", "@helpCenter": {"description": "Help Center item"}, "feedback": "<PERSON><PERSON><PERSON>", "@feedback": {"description": "Feedback item"}, "rateApp": "Rate App", "@rateApp": {"description": "Rate App item"}, "aboutApp": "About App", "@aboutApp": {"description": "About App item"}, "dataManagement": "Data Management", "@dataManagement": {"description": "Data Management title"}, "selectOperation": "Select operation to perform", "@selectOperation": {"description": "Select operation prompt"}, "exportData": "Export Data", "@exportData": {"description": "Export data operation"}, "importData": "Import Data", "@importData": {"description": "Import data operation"}, "createBackup": "Create Backup", "@createBackup": {"description": "Create backup operation"}, "dataExportInProgress": "Data export feature is in development...", "@dataExportInProgress": {"description": "Data export in progress message"}, "dataImportInProgress": "Data import feature is in development...", "@dataImportInProgress": {"description": "Data import in progress message"}, "backupInProgress": "Backup feature is in development...", "@backupInProgress": {"description": "Backup in progress message"}, "doYouLikeThisApp": "Do you like this app? Please rate us on the App Store!", "@doYouLikeThisApp": {"description": "Rate app prompt"}, "later": "Later", "@later": {"description": "Later button"}, "goToRate": "Rate Now", "@goToRate": {"description": "Go to rate button"}, "cannotOpenAppStore": "Cannot open App Store", "@cannotOpenAppStore": {"description": "Cannot open App Store error"}, "errorOpeningAppStore": "Error occurred while opening App Store", "@errorOpeningAppStore": {"description": "Error opening App Store"}, "understand": "Understood", "@understand": {"description": "Understand button"}, "weValueYourPrivacy": "We value your privacy. All data processing is done locally and will not be uploaded to servers.", "@weValueYourPrivacy": {"description": "Privacy statement"}, "manageDataAnytime": "You can manage your data in settings at any time.", "@manageDataAnytime": {"description": "Data management statement"}, "languageSelected": "Language selected: {language}", "@languageSelected": {"description": "Language selection notification"}, "light": "Light", "@light": {"description": "Light theme"}, "dark": "Dark", "@dark": {"description": "Dark theme"}, "selectLanguage": "Select Language", "@selectLanguage": {"description": "Select language title"}, "selectAppearance": "Select Appearance", "@selectAppearance": {"description": "Select appearance title"}, "traditionalChinese": "Traditional Chinese", "@traditionalChinese": {"description": "Traditional Chinese"}, "simplifiedChinese": "Simplified Chinese", "@simplifiedChinese": {"description": "Simplified Chinese"}, "contentSaveButtonFavorite": "Favorite", "@contentSaveButtonFavorite": {"description": "Text for save/favorite button in content manager screens"}, "textTransformerSelectMode": "Select Transformation Mode", "@textTransformerSelectMode": {"description": "Title for template selection section"}, "textTransformerInputText": "Input Text", "@textTransformerInputText": {"description": "Label for input text section"}, "textTransformerOutputResult": "Transformation Result", "@textTransformerOutputResult": {"description": "Label for output result section"}, "textTransformerHint": "Enter text to transform here...", "@textTransformerHint": {"description": "Hint text for input field"}, "textTransformerOutputHint": "Transformed text will appear here...", "@textTransformerOutputHint": {"description": "Hint text for output field"}, "textTransformerCharacters": "characters", "@textTransformerCharacters": {"description": "Text for character count"}, "textTransformerTransform": "Transform", "@textTransformerTransform": {"description": "Transform button text"}, "textTransformerTransforming": "Transforming...", "@textTransformerTransforming": {"description": "Text shown while transforming"}, "textTransformerClearAll": "Clear All", "@textTransformerClearAll": {"description": "Clear all button tooltip"}, "textTransformerCopyResult": "<PERSON><PERSON> Result", "@textTransformerCopyResult": {"description": "Copy result button tooltip"}, "textTransformerCopied": "Copied to clipboard", "@textTransformerCopied": {"description": "Message shown when text is copied"}, "textTransformerTemplateEmojiName": "Emoji Conversion", "@textTransformerTemplateEmojiName": {"description": "Name for emoji template"}, "textTransformerTemplateEmojiDesc": "Convert text to special emoji characters", "@textTransformerTemplateEmojiDesc": {"description": "Description for emoji template"}, "textTransformerTemplateFancyName": "Fancy Letters", "@textTransformerTemplateFancyName": {"description": "Name for fancy letters template"}, "textTransformerTemplateFancyDesc": "Transform to elegant fancy letters", "@textTransformerTemplateFancyDesc": {"description": "Description for fancy letters template"}, "textTransformerTemplateBoldName": "Bold Text", "@textTransformerTemplateBoldName": {"description": "Name for bold text template"}, "textTransformerTemplateBoldDesc": "Convert to bold Unicode characters", "@textTransformerTemplateBoldDesc": {"description": "Description for bold text template"}, "textTransformerTemplateDecorativeName": "Decorative Text", "@textTransformerTemplateDecorativeName": {"description": "Name for decorative text template"}, "textTransformerTemplateDecorativeDesc": "Add decorative symbols", "@textTransformerTemplateDecorativeDesc": {"description": "Description for decorative text template"}, "textTransformerTemplateMixedName": "Mixed Effects", "@textTransformerTemplateMixedName": {"description": "Name for mixed effects template"}, "textTransformerTemplateMixedDesc": "Randomly combine multiple transformation effects", "@textTransformerTemplateMixedDesc": {"description": "Description for mixed effects template"}, "textTransformerTemplateInvisibleName": "Invisible Characters", "@textTransformerTemplateInvisibleName": {"description": "Name for invisible characters template"}, "textTransformerTemplateInvisibleDesc": "Add invisible characters to bypass detection", "@textTransformerTemplateInvisibleDesc": {"description": "Description for invisible characters template"}, "textTransformerTemplateUnicodeName": "Unicode Variants", "@textTransformerTemplateUnicodeName": {"description": "Name for Unicode variants template"}, "textTransformerTemplateUnicodeDesc": "Use Unicode variant characters", "@textTransformerTemplateUnicodeDesc": {"description": "Description for Unicode variants template"}, "textTransformerEffectEmojiDesc": "Convert numbers and letters to special Unicode characters", "@textTransformerEffectEmojiDesc": {"description": "Effect description for emoji mode"}, "textTransformerEffectFancyDesc": "Transform to elegant fancy letters", "@textTransformerEffectFancyDesc": {"description": "Effect description for fancy mode"}, "textTransformerEffectBoldDesc": "Convert to bold Unicode characters", "@textTransformerEffectBoldDesc": {"description": "Effect description for bold mode"}, "textTransformerEffectDecorativeDesc": "Add decorative symbols", "@textTransformerEffectDecorativeDesc": {"description": "Effect description for decorative mode"}, "textTransformerEffectMixedDesc": "Randomly combine multiple transformation effects", "@textTransformerEffectMixedDesc": {"description": "Effect description for mixed mode"}, "textTransformerEffectInvisibleDesc": "Add invisible characters between characters to bypass detection", "@textTransformerEffectInvisibleDesc": {"description": "Effect description for invisible mode"}, "textTransformerEffectUnicodeDesc": "Add diacritical marks to change character appearance", "@textTransformerEffectUnicodeDesc": {"description": "Effect description for Unicode mode"}, "textTransformerSample": "Sample Text", "@textTransformerSample": {"description": "Generic sample text for transformations"}, "textTransformerSampleInvisible": "Sensitive content detection bypass test", "@textTransformerSampleInvisible": {"description": "Sample text for invisible character mode"}, "textTransformerSampleUnicode": "Special character conversion test", "@textTransformerSampleUnicode": {"description": "Sample text for Unicode variant mode"}}