{"@@locale": "ja", "@@last_modified": "2024-01-15T10:00:00.000Z", "appName": "ContentPal", "appNameChinese": "内容君", "appDescription": "プロフェッショナルなコンテンツ処理ツール、コンテンツ作成をより簡単に", "home": "ホーム", "settings": "設定", "language": "言語", "theme": "テーマ", "lightTheme": "ライト", "darkTheme": "ダーク", "systemTheme": "システム", "markdown": "<PERSON><PERSON>", "textCards": "テキストカード", "textCardSelectColor": "色を選択", "textCardStyleApplied": "スタイルが適用されました", "textCardSplitFailed": "分割に失敗しました：{error}", "textCardCreateFailed": "カードの作成に失敗しました：{error}", "textCardEditCard": "カードを編集", "textCardPreviousStep": "前へ", "textCardSaveToContentLibrary": "コンテンツライブラリに保存", "textCardStartExportingImage": "画像のエクスポートを開始...", "textCardImageSavedSuccess": "✅ 画像がアルバムに正常に保存されました", "textCardPleaseEnterContent": "カードの内容を入力してください", "textCardDeleteCard": "カードを削除", "textCardDeleteConfirm": "このカードを削除してもよろしいですか？この操作は元に戻せません。", "textCardCategory": "カテゴリ：{category}", "textCardDescription": "説明：{description}", "textCardClose": "閉じる", "textCardUseTemplate": "テンプレートを使用", "textCardConfirmExport": "エクスポートを確認", "textCardQuality": "品質", "textCardIncludeWatermark": "透かしを含める", "textCardPreviewInfo": "プレビュー情報", "textCardRatio": "比率: {ratio}", "textCardQualityPercent": "品質: {quality}%", "textCardWatermarkStatus": "透かし: {status}", "textCardAddCard": "カードを追加", "textCardAddNewCard": "新しいカードを追加", "textCardEdit": "編集", "textCardExportingImage": "画像をエクスポート中...", "textCardExportSuccess": "✅ エクスポート成功！アルバムに保存されました", "textCardExportFailed": "❌ エクスポート失敗: {error}", "textCardAddWatermark": "透かしを追加", "textCardAddWatermarkDesc": "画像の隅にアプリの透かしを追加", "textCardIncludeTitle": "タイトルを含める", "textCardIncludeTitleDesc": "エクスポートした画像にタイトルを表示", "textCardIncludeTimestamp": "タイムスタンプを含める", "textCardIncludeTimestampDesc": "画像に作成時間を表示", "textCardImageQuality": "画像品質", "textCardTextStyleCustomization": "テキストスタイルのカスタマイズ - {title}", "textCardExportCurrentCard": "現在のカードをエクスポート", "textCardBatchExport": "バッチエクスポート", "textCardClearSelection": "選択をクリア", "textCardResetStyle": "スタイルをリセット", "textCardResetStyleConfirm": "すべてのテキストスタイルをクリアしてもよろしいですか？この操作は元に戻せません。", "textCardExportAsImage": "画像としてエクスポート", "textCardExportAsImageDesc": "このカードを画像として保存", "textCardEditCardDesc": "タイトルと内容を変更", "textCardDeleteCardDesc": "ドキュメントから削除", "textCardDeleteCardConfirm": "カード\"{title}\"を削除してもよろしいですか？この操作は元に戻せません。", "textCardEditDocument": "ドキュメントを編集", "textCardUniformStyle": "統一スタイル", "textCardExportImage": "画像をエクスポート", "textCardInsertSeparator": "区切りを挿入", "textCardPleaseEnterTitleAndCards": "タイトルを入力し、少なくとも1つのカードがあることを確認してください", "textCardSaveFailed": "保存に失敗しました: {error}", "textCardContentRendering": "コンテンツレンダリング", "textCardExportWithTitle": "エクスポート - {title}", "textCardShare": "共有", "textCardSaveToAlbum": "アルバムに保存", "textCardUnderstood": "理解しました", "textCardStartExperience": "体験を開始", "textCardFeatureDemo": "機能デモ", "textCardGotIt": "わかりました", "textCardStartUsing": "使用を開始", "textCardPreviewEffect": "プレビュー効果", "textCardExportInfo": "エクスポート情報", "textCardImageDimensions": "画像サイズ", "textCardAspectRatio": "アスペクト比", "textCardFileSize": "ファイルサイズ", "textCardUsageScenario": "使用シナリオ", "textCardBestQuality": "最高品質 (100%)", "textCardWatermarkDescription": "画像の下部にアプリ識別子を追加", "pdf": "PDF", "voice": "音声", "html": "HTML", "svg": "SVG", "content": "コンテンツ", "trafficGuide": "交通ガイド", "create": "作成", "edit": "編集", "delete": "削除", "save": "保存", "cancel": "キャンセル", "confirm": "OK", "yes": "はい", "no": "いいえ", "ok": "OK", "loading": "読み込み中...", "error": "エラー", "success": "成功", "warning": "警告", "info": "情報", "search": "検索", "searchHint": "検索内容を入力してください...", "noResults": "結果が見つかりません", "tryAgain": "再試行", "refresh": "更新", "share": "共有", "export": "エクスポート", "import": "インポート", "copy": "コピー", "paste": "貼り付け", "cut": "切り取り", "undo": "元に戻す", "redo": "やり直し", "selectAll": "すべて選択", "close": "閉じる", "back": "戻る", "next": "次へ", "previous": "前へ", "done": "完了", "finish": "終了", "skip": "スキップ", "continueAction": "続行", "retry": "再試行", "reset": "リセット", "clear": "クリア", "apply": "適用", "preview": "プレビュー", "download": "ダウンロード", "upload": "アップロード", "file": "ファイル", "folder": "フォルダ", "name": "名前", "title": "タイトル", "description": "説明", "size": "サイズ", "date": "日付", "time": "時間", "type": "タイプ", "status": "ステータス", "version": "バージョン", "author": "作成者", "tags": "タグ", "category": "カテゴリ", "priority": "優先度", "high": "高", "medium": "中", "low": "低", "enabled": "有効", "disabled": "無効", "online": "オンライン", "offline": "オフライン", "connected": "接続済み", "disconnected": "切断済み", "available": "利用可能", "unavailable": "利用不可", "active": "有効", "inactive": "無効", "public": "公開", "private": "非公開", "draft": "下書き", "published": "公開済み", "archived": "アーカイブ済み", "pdfProfessionalTool": "PDFプロフェッショナルツール", "pdfToolDescription": "強力なPDF処理機能で、文書管理をより簡単に", "securityEncryption": "セキュリティ暗号化", "passwordProtectionPermissionControl": "パスワード保護\n権限制御", "intelligentAnnotation": "インテリジェント注釈", "highlightMarkingTextAnnotation": "ハイライト\nテキスト注釈", "quickSearch": "クイック検索", "fullTextSearchContentLocation": "全文検索\nコンテンツ位置", "convenientSharing": "便利な共有", "multipleFormatsOneClickExport": "複数フォーマット\nワンクリックエクスポート", "welcomeToPdfTool": "PDFプロフェッショナルツールへようこそ！", "importFirstPdfDocument": "最初のPDF文書をインポート", "appearance": "外観", "followSystem": "システムに従う", "languageChangeEffect": "言語の変更は即座に有効になります", "contentLibrary": "コンテンツライブラリ", "manageAllCards": "すべてのカードを管理", "templateLibrary": "テンプレートライブラリ", "browseBeautifulTemplates": "美しいテンプレートを閲覧", "inputTextToSplit": "分割するテキストを入力", "pasteOrInputLongText": "長いテキストコンテンツを貼り付けまたは入力してください。システムが自動的に認識し、複数のカードに分割します", "pasteClipboard": "クリップボードから貼り付け", "clearContent": "コンテンツをクリア", "cardNumber": "カード {number}", "loadingDemoData": "デモデータを読み込み中...", "modernUIDesign": "✨ モダンUIデザイン\n🖼️ レンダリング結果プレビュー\n📱 ブロックモードサポート\n⚡ 高性能体験", "editFunction": "編集機能：{title}", "deleted": "削除されました：{title}", "shareFunction": "共有機能：{title}", "createNewContent": "新しいコンテンツを作成", "selectContentType": "作成するコンテンツの種類を選択してください", "bold": "**太字**", "italic": "*斜体*", "heading1": "# 見出し1", "heading2": "## 見出し2", "heading3": "### 見出し3", "list": "- リスト項目\n- リスト項目", "link": "[リンクテキスト](URL)", "image": "![画像説明](画像URL)", "code": "`コード`", "codeBlock": "```\nコードブロック\n```", "quote": "> 引用テキスト", "table": "| 列1 | 列2 |\n| --- | --- |\n| 内容1 | 内容2 |", "myContentLibrary": "マイコンテンツライブラリ", "manageAndBrowseContent": "すべてのコンテンツを管理・閲覧", "contentTools": "コンテンツツール", "recommendedTools": "おすすめツール", "markdownTitle": "<PERSON><PERSON>", "markdownDescription": "ドキュメント編集とレンダリング", "textCardsTitle": "テキストカード", "textCardsDescription": "ナレッジカードのカスタマイズとレンダリング", "trafficGuideTitle": "トラフィックガイド", "trafficGuideDescription": "トラフィック画像とテキスト処理", "fileTools": "ファイルツール", "svgTitle": "SVG", "svgDescription": "ベクターグラフィック処理", "htmlTitle": "HTML", "htmlDescription": "ウェブコンテンツ編集", "loadingContent": "コンテンツを読み込み中...", "languageChangedTo": "言語が{language}に変更されました", "developer": "開発者", "contentLibraryDemo": "コンテンツライブラリデモ", "viewNewContentLibraryFeatures": "新しいコンテンツライブラリ機能を表示", "i18nDemo": "国際化デモ", "viewMultiLanguageSupport": "多言語サポート効果を表示", "about": "について", "versionInfo": "バージョン情報", "helpAndFeedback": "ヘルプとフィードバック", "getHelpOrProvideFeedback": "ヘルプを取得またはフィードバックを提供", "helpAndFeedbackContent": "ご質問やご提案がございましたら、以下の方法でお問い合わせください：\n\nメール：<EMAIL>", "selectTheme": "テーマを選択", "lightMode": "ライトモード", "darkMode": "ダークモード", "systemMode": "システムに従う", "personalizeYourAppExperience": "アプリ体験をパーソナライズ", "useDefaultInitialText": "デフォルトの初期テキストを使用", "useDefaultInitialTextDescription": "モジュール入力時にデフォルトのサンプルコンテンツを自動入力", "contentSettings": "コンテンツ設定", "newContentLibraryExperience": "新しいコンテンツライブラリ体験", "@newContentLibraryExperience": {"description": "Content library feature card title"}, "supportMultipleContentTypes": "複数のコンテンツタイプをサポートし、レンダリング結果を優先表示", "@supportMultipleContentTypes": {"description": "Content library feature card subtitle"}, "contentLibraryDemoPage": "コンテンツライブラリデモ", "@contentLibraryDemoPage": {"description": "Content library demo page title"}, "contentServiceLoadItemFailed": "コンテンツ項目の読み込みに失敗しました: {error}", "@contentServiceLoadItemFailed": {"description": "Content service load item error message"}, "contentServiceMustBeTextType": "テキストタイプのコンテンツである必要があります", "@contentServiceMustBeTextType": {"description": "Content service text type validation error"}, "contentServiceMustBeImageType": "画像タイプのコンテンツである必要があります", "@contentServiceMustBeImageType": {"description": "Content service image type validation error"}, "contentServiceItemNotFound": "コンテンツ項目が見つかりません", "@contentServiceItemNotFound": {"description": "Content service item not found error"}, "contentServiceNotInitialized": "ContentService が初期化されていません", "@contentServiceNotInitialized": {"description": "Content service not initialized error"}, "contentServiceRenderFailed": "レンダリングに失敗しました: {error}", "@contentServiceRenderFailed": {"description": "Content service render failed error"}, "permissionHelperRequestStorageFailed": "ストレージ権限のリクエストに失敗しました: {error}", "@permissionHelperRequestStorageFailed": {"description": "Permission helper storage permission request failed error"}, "permissionHelperRequestCameraFailed": "カメラ権限のリクエストに失敗しました: {error}", "@permissionHelperRequestCameraFailed": {"description": "Permission helper camera permission request failed error"}, "permissionHelperRequestMultipleFailed": "複数権限のリクエストに失敗しました: {error}", "@permissionHelperRequestMultipleFailed": {"description": "Permission helper multiple permissions request failed error"}, "permissionHelperIosPermissionCheckFailed": "iOS権限チェックに失敗しました: {error}", "@permissionHelperIosPermissionCheckFailed": {"description": "Permission helper iOS permission check failed error"}, "chineseTraditionalColorTitle": "中国伝統色", "@chineseTraditionalColorTitle": {"description": "Chinese traditional color theme selector title"}, "chineseTraditionalColorSubtitle": "お好きな伝統的なカラーテーマを選択してください", "@chineseTraditionalColorSubtitle": {"description": "Chinese traditional color theme selector subtitle"}, "chineseTraditionalColorSystemTheme": "システムテーマに従う", "@chineseTraditionalColorSystemTheme": {"description": "System theme option in Chinese traditional color selector"}, "chineseTraditionalColorSystemThemeDesc": "アプリのデフォルトテーマカラーを使用", "@chineseTraditionalColorSystemThemeDesc": {"description": "System theme description in Chinese traditional color selector"}, "chineseTraditionalColorSwitchedToTheme": "「{themeName}」テーマに切り替えました", "@chineseTraditionalColorSwitchedToTheme": {"description": "Message when switching to a specific Chinese traditional color theme"}, "chineseTraditionalColorSwitchedToSystem": "システムデフォルトテーマに切り替えました", "@chineseTraditionalColorSwitchedToSystem": {"description": "Message when switching to system default theme"}, "subscriptionManagement": "サブスクリプション管理", "@subscriptionManagement": {"description": "Subscription management page title"}, "upgradeSubscription": "サブスクリプションをアップグレード", "@upgradeSubscription": {"description": "Upgrade subscription button text"}, "upgradeSubscriptionDesc": "より高度なサブスクリプションプランを表示・購入", "@upgradeSubscriptionDesc": {"description": "Upgrade subscription description"}, "restorePurchase": "購入を復元", "@restorePurchase": {"description": "Restore purchase button text"}, "restorePurchaseDesc": "以前のサブスクリプション購入を復元", "@restorePurchaseDesc": {"description": "Restore purchase description"}, "helpAndSupport": "ヘルプとサポート", "@helpAndSupport": {"description": "Help and support section title"}, "frequentlyAskedQuestions": "よくある質問", "@frequentlyAskedQuestions": {"description": "FAQ button text"}, "frequentlyAskedQuestionsDesc": "サブスクリプションに関するよくある質問を表示", "@frequentlyAskedQuestionsDesc": {"description": "FAQ description"}, "contactCustomerService": "サポートに連絡", "@contactCustomerService": {"description": "Contact customer service button text"}, "contactCustomerServiceDesc": "サブスクリプション問題に関するヘルプを取得", "@contactCustomerServiceDesc": {"description": "Contact customer service description"}, "refundPolicy": "返金ポリシー", "@refundPolicy": {"description": "Refund policy button text"}, "refundPolicyDesc": "当社の返金・キャンセルポリシーを確認", "@refundPolicyDesc": {"description": "Refund policy description"}, "restoringPurchase": "購入を復元中", "@restoringPurchase": {"description": "Restoring purchase dialog title"}, "communicatingWithAppStore": "App Storeと通信中...", "@communicatingWithAppStore": {"description": "Communicating with app store message"}, "noRestorablePurchasesFound": "復元可能な購入が見つかりません", "@noRestorablePurchasesFound": {"description": "No restorable purchases found message"}, "currentSubscription": "現在のサブスクリプション", "@currentSubscription": {"description": "Current subscription label"}, "freeVersion": "無料版", "@freeVersion": {"description": "Free version subscription name"}, "@active": {"description": "Active status label"}, "availableFeatures": "利用可能な機能", "@availableFeatures": {"description": "Available features section title"}, "basicProcessing": "基本処理", "@basicProcessing": {"description": "Basic processing feature name"}, "exportWithWatermark": "透かし付きエクスポート", "@exportWithWatermark": {"description": "Export with watermark feature name"}, "unlimitedExport": "無制限エクスポート", "@unlimitedExport": {"description": "Unlimited export feature name"}, "batchProcessing": "バッチ処理", "@batchProcessing": {"description": "Batch processing feature name"}, "advancedTools": "高度なツール", "@advancedTools": {"description": "Advanced tools feature name"}, "markdownEditTab": "編集", "@markdownEditTab": {"description": "Edit tab label in markdown editor"}, "markdownTemplateTab": "テンプレート", "@markdownTemplateTab": {"description": "Template tab label in markdown editor"}, "markdownStyleTab": "スタイル", "@markdownStyleTab": {"description": "Style tab label in markdown editor"}, "markdownWatermarkTab": "透かし", "@markdownWatermarkTab": {"description": "Watermark tab label in markdown editor"}, "markdownBlockTab": "ブロック", "@markdownBlockTab": {"description": "Block tab label in markdown editor"}, "markdownContentSaved": "コンテンツがコンテンツライブラリに保存されました", "@markdownContentSaved": {"description": "Content saved message in markdown module"}, "markdownSavedMarkdown": "保存された Markdown", "@markdownSavedMarkdown": {"description": "Title for saved markdown content"}, "markdownMoreActions": "その他の操作", "@markdownMoreActions": {"description": "More actions button tooltip in markdown editor"}, "sharedText": "共有テキスト", "failedToGetInitialIntent": "初期インテントの取得に失敗", "failedToLoadThemeSettings": "テーマ設定の読み込みに失敗", "themeMaterialYou": "Material You", "themeMorandi": "モランディスタイル", "themeMonochrome": "モノクローム", "themeNature": "ナチュラル", "themeTech": "テック", "themeChinese": "中国伝統色", "themeMaterialYouDesc": "壁紙から自動抽出されるダイナミックテーマ", "themeMorandiDesc": "柔らかくエレガントなモランディカラースキーム", "themeMonochromeDesc": "シンプルで純粋な白黒配色", "themeNatureDesc": "快適で自然なエコロジーカラー", "themeTechDesc": "未来感あふれるテクノロジーカラー", "themeChineseDesc": "伝統と現代を融合させた東洋美学", "trafficGuideImageGenerator": "画像ジェネレーター", "trafficGuideImageGeneratorSubtitle": "交通案内画像を作成", "trafficGuideTabText": "テキスト", "trafficGuideTabTemplate": "テンプレート", "trafficGuideTabEffects": "エフェクト", "trafficGuideTextContent": "テキスト内容", "trafficGuideTextHint": "テキスト内容を入力してください", "trafficGuideFontSettings": "フォント設定", "trafficGuideFontSize": "フォントサイズ", "trafficGuideColorSettings": "カラー設定", "trafficGuideTextColor": "テキストカラー", "trafficGuideBackgroundColor": "背景カラー", "trafficGuideVisualEffects": "ビジュアルエフェクト", "trafficGuideNoiseLevel": "ノイズレベル", "trafficGuideDistortionLevel": "歪みレベル", "trafficGuideAddWatermark": "ウォーターマーク追加", "trafficGuideWatermarkText": "ウォーターマークテキスト", "trafficGuideWatermarkHint": "ウォーターマークテキストを入力してください", "trafficGuideExport": "エクスポート", "trafficGuideSelectTemplateFirst": "まずテンプレートを選択してください", "trafficGuideImageSavedSuccess": "画像の保存に成功", "trafficGuideSaveFailed": "保存に失敗", "trafficGuidePermissionPermanentlyDenied": "権限が永続的に拒否されました", "trafficGuidePermissionRequired": "権限が必要です", "trafficGuideSaveFailedWithMessage": "保存に失敗：{message}", "trafficGuideSaveFailedEmptyResult": "保存に失敗：結果が空です", "markdownPreview": "プレビュー", "markdownContentLabel": "内容", "markdownRenderModeLabel": "レンダリングモード", "markdownNormalMode": "通常モード", "markdownBlockMode": "ブロックモード", "markdownConfigTab": "設定", "markdownManageTab": "管理", "markdownPreviewTab": "プレビュー", "markdownBlockInfo": "Block Information", "markdownTotalBlocks": "Total Blocks", "markdownVisibleBlocks": "Visible Blocks", "markdownEditorTitle": "Markdown Editor", "markdownPreviewTitle": "Markdown Preview", "markdownTitleLabel": "Title", "markdownSubtitleLabel": "Subtitle (Optional)", "markdownUntitledDocument": "Untitled Document", "markdownUntitledSection": "Untitled Section", "markdownSplitSections": "Split Sections", "markdownSaveDocument": "Save Document", "markdownActionOptions": "Action Options", "markdownShareImage": "Share Image", "markdownCopyContent": "Copy Content", "markdownSaveToAlbum": "Save to Album", "commonCancel": "Cancel", "commonReset": "Reset", "commonSelectAll": "Select All", "commonDeselectAll": "Deselect All", "markdownShowSelected": "Show Selected", "markdownHideSelected": "<PERSON>de Selected", "markdownExportSelected": "Export Selected", "markdownHideBlock": "Hide Block", "markdownShowBlock": "Show Block", "markdownExportAsImage": "Export as Image", "markdownExportAsMarkdown": "Export as <PERSON><PERSON>", "commonGotIt": "Got it", "markdownBlockRenderSettings": "Block Render Settings", "markdownBasicSettings": "Basic Settings", "markdownEnableBlockRender": "Enable Block Rendering", "markdownSeparatorSettings": "Separator <PERSON>s", "markdownSplitByH1": "Split by H1 Headers", "markdownSplitByH2": "Split by H2 Headers", "markdownCustomSeparatorPattern": "Custom Separator Pattern (Regex)", "markdownAppearanceSettings": "Appearance Settings", "markdownBlockSpacing": "Block Spacing", "markdownSectionSplitSettings": "Section Split Settings", "markdownSplitByHorizontalRule": "Split by Horizontal Rule", "markdownMaxSectionLength": "Maximum Section Length", "commonUnlimited": "Unlimited", "markdownSetMaxSectionLength": "Set Maximum Section Length", "markdownMaxCharacters": "Maximum Characters", "markdownLeaveEmptyUnlimited": "Leave empty for unlimited", "templateSimpleName": "Simple", "templateSimpleDescription": "Clean and minimalist design style", "templateModernName": "Modern", "templateModernDescription": "Modern design style with shadow effects", "templateElegantName": "Elegant", "templateElegantDescription": "Elegant design style with thin borders", "templateCodeName": "Code", "templateCodeDescription": "Dark theme suitable for code display", "templateCardName": "Card", "templateCardDescription": "Social media card style design", "templateMorandiName": "<PERSON><PERSON>", "templateMorandiDescription": "Premium Morandi color palette, soft and elegant", "templateChineseBlueName": "Chinese Blue", "templateChineseBlueDescription": "Traditional Chinese porcelain color and pattern design", "templateChineseVermilionName": "Chinese Vermilion", "templateChineseVermilionDescription": "Traditional Chinese vermilion color, elegant and solemn", "templateGradientPurpleName": "Grad<PERSON> Purple", "templateGradientPurpleDescription": "Modern purple-blue gradient background, stylish and elegant", "templateFestiveRedName": "Festive Red", "templateFestiveRedDescription": "Festive theme, suitable for occasions like Spring Festival", "templateBambooSlipName": "Bamboo Slip", "templateBambooSlipDescription": "Traditional bamboo slip style, rich in ancient charm", "watermarkPositionTopLeft": "Top Left", "watermarkPositionTopCenter": "Top Center", "watermarkPositionTopRight": "Top Right", "watermarkPositionBottomLeft": "Bottom Left", "watermarkPositionBottomCenter": "Bottom Center", "watermarkPositionBottomRight": "Bottom Right", "watermarkPositionTiled": "Tiled", "markdownEnterContentFirst": "Please enter Markdown content first", "markdownSplitSuccess": "Successfully split into {count} sections", "markdownSplitError": "Error splitting sections: {error}", "markdownNoSectionsToPreview": "No sections to preview", "markdownSplitContentFirst": "Please split Markdown content first", "markdownDocumentSaveSuccess": "Document saved successfully", "markdownDocumentSaveError": "Failed to save document: {error}", "errorStoragePermissionRequired": "Storage permission required to save images", "markdownNoContentToPreview": "No content to preview", "markdownImageGenerationFailed": "Image generation failed, unable to share", "markdownShareError": "Share failed: {error}", "markdownEnableBlockModeFirst": "Please enable block mode first", "markdownNoBlocks": "No blocks available", "markdownEnableBlockRenderToList": "Enable block rendering to view block list", "commonContentCopied": "Content copied to clipboard", "markdownResetDemo": "Reset Demo", "commonHelp": "Help", "markdownBlockRenderHelp": "Block Render Help", "markdownFeatureDescription": "Feature Description:", "markdownOperationMethod": "Operation Method:", "commonTips": "Tips:", "markdownSectionSettings": "Section Settings", "markdownSelectTemplate": "Select Template", "markdownSelectHtmlTemplate": "Select HTML Template", "commonPreview": "Preview", "markdownSaveImage": "Save Image", "commonShare": "Share", "commonShowAll": "Show All", "commonShowVisibleOnly": "Show Visible Only", "commonSortByIndex": "Sort by Index", "commonSortByTitle": "Sort by Title", "commonSortByType": "Sort by Type", "commonSortByLength": "Sort by Length", "markdownBlockCount": "{count} blocks", "commonCharacterCount": "{count} characters", "markdownSelectedBlockCount": "{count} blocks selected", "commonTotal": "Total", "commonVisible": "Visible", "commonHidden": "Hidden", "markdownContentPlaceholder": "Enter Markdown content here...", "markdownClickSplitButton": "Click split button to split <PERSON><PERSON> into sections", "markdownHorizontalRuleHelper": "Split when encountering three or more -, *, or _ symbols", "markdownH1SplitHelper": "Split when encountering # H1 headers", "markdownCharacterCount": "{count} characters", "markdownAutoSplitHelper": "Automatically split long sections into multiple sections", "markdownSeparatorExample": "Example: Three or more consecutive hyphens", "markdownH1SeparatorHelper": "Use # H1 headers as block separators", "markdownH2SeparatorHelper": "Use ## H2 headers as block separators", "markdownBlockRenderHelper": "When enabled, Markdown content will be displayed in blocks according to set rules", "markdownExportBlocks": "Export Blocks", "markdownGenerateSummary": "Generate Summary Report", "markdownImageExportSuccess": "Image exported: {filePath}", "markdownExportError": "Export failed: {error}", "markdownMarkdownExportSuccess": "Markdown file exported: {filePath}", "markdownSummaryGenerated": "Summary report generated: {filePath}", "markdownSummaryError": "Report generation failed: {error}", "markdownGeneratingImage": "Generating image ({current}/{total})", "markdownImagesSavedSuccess": "Successfully saved {count} images to album", "templateChineseBlueWatermark": "青花", "templateChineseVermilionWatermark": "赤", "templateFestiveRedWatermark": "福", "templateBambooSlipWatermark": "竹", "markdownBlockManagement": "Block Management", "markdownExportOptions": "Export Options", "commonDeselect": "Deselect", "commonSelect": "Select", "commonLoading": "Loading...", "commonConfirm": "Confirm", "commonEdit": "Edit", "commonDelete": "Delete", "commonAdd": "Add", "commonRemove": "Remove", "commonApply": "Apply", "commonClose": "Close", "commonOpen": "Open", "commonView": "View", "commonBrowse": "Browse", "commonSearch": "Search", "commonFilter": "Filter", "commonSort": "Sort", "commonRefresh": "Refresh", "commonReload": "Reload", "commonRetry": "Retry", "commonContinue": "Continue", "commonFinish": "Finish", "commonSkip": "<PERSON><PERSON>", "commonBack": "Back", "commonNext": "Next", "commonPrevious": "Previous", "commonDone": "Done", "commonStart": "Start", "commonStop": "Stop", "commonPause": "Pause", "commonResume": "Resume", "commonPlay": "Play", "commonMute": "Mute", "commonUnmute": "Unmute", "commonVolumeUp": "Volume Up", "commonVolumeDown": "Volume Down", "commonFullscreen": "Fullscreen", "commonExitFullscreen": "Exit Fullscreen", "commonZoomIn": "Zoom In", "commonZoomOut": "Zoom Out", "commonZoomReset": "Zoom Reset", "commonRotateLeft": "Rotate Left", "commonRotateRight": "Rotate Right", "commonFlipHorizontal": "<PERSON><PERSON>", "commonFlipVertical": "Flip Vertical", "commonCrop": "Crop", "commonResize": "Resize", "commonRotate": "Rotate", "commonFlip": "Flip", "commonMirror": "Mirror", "commonSkew": "Skew", "commonDistort": "Distort", "commonBlur": "Blur", "commonSharpen": "Sharpen", "commonBrightness": "Brightness", "commonContrast": "Contrast", "commonSaturation": "Saturation", "commonHue": "<PERSON><PERSON>", "commonGamma": "Gamma", "commonExposure": "Exposure", "commonVignette": "Vignette", "commonGrain": "Grain", "commonNoise": "Noise", "commonPixelate": "Pixelate", "commonPosterize": "Post<PERSON>ze", "commonDither": "<PERSON><PERSON>", "commonThreshold": "<PERSON><PERSON><PERSON><PERSON>", "commonQuantize": "Quantize", "commonDesaturate": "Desaturate", "commonSaturate": "Saturate", "commonInvert": "Invert", "commonGrayscale": "Grayscale", "commonSepia": "Sepia", "commonVintage": "Vintage", "commonRetro": "Retro", "commonBlackAndWhite": "Black and White", "commonCool": "Cool", "commonWarm": "Warm", "commonFade": "Fade", "commonDuotone": "Duotone", "commonTricolor": "Tricolor", "commonMonochrome": "Monochrome", "commonPolychrome": "Polychrome", "commonRainbow": "Rainbow", "commonGradient": "Gradient", "commonPattern": "Pattern", "commonTexture": "Texture", "commonBorder": "Border", "commonFrame": "<PERSON>ame", "commonShadow": "Shadow", "commonGlow": "Glow", "commonNeon": "Neon", "commonLight": "Light", "commonDark": "Dark", "commonBright": "<PERSON>", "commonDim": "<PERSON><PERSON>", "commonClear": "Clear", "commonCloudy": "Cloudy", "commonFoggy": "Foggy", "commonHazy": "Hazy", "commonSmoky": "Smoky", "commonDusty": "<PERSON>", "commonMisty": "<PERSON>", "commonFrosty": "<PERSON><PERSON>", "commonIcy": "<PERSON><PERSON>", "commonSnowy": "Snowy", "commonRainy": "Rainy", "commonStormy": "Stormy", "commonWindy": "Windy", "commonBreezy": "Breezy", "commonCalm": "Calm", "commonStill": "Still", "commonQuiet": "Quiet", "commonSilent": "Silent", "commonPeaceful": "Peaceful", "commonSerene": "<PERSON><PERSON>", "commonTranquil": "Tranquil", "commonPlacid": "Placid", "commonSmooth": "Smooth", "commonRough": "<PERSON>", "commonCoarse": "<PERSON><PERSON><PERSON>", "commonFine": "Fine", "commonSoft": "Soft", "commonHard": "Hard", "commonTough": "Tough", "commonStrong": "Strong", "commonWeak": "Weak", "commonGentle": "Gentle", "commonMild": "Mild", "commonHarsh": "Hars<PERSON>", "commonSevere": "Severe", "commonExtreme": "Extreme", "commonIntense": "Intense", "commonModerate": "Moderate", "commonAverage": "Average", "commonNormal": "Normal", "commonStandard": "Standard", "commonRegular": "Regular", "commonTypical": "Typical", "commonUsual": "Usual", "commonCommon": "Common", "commonOrdinary": "Ordinary", "commonGeneral": "General", "commonBasic": "Basic", "commonSimple": "Simple", "commonEasy": "Easy", "commonDifficult": "<PERSON><PERSON><PERSON><PERSON>", "commonComplex": "Complex", "commonComplicated": "Complicated", "commonAdvanced": "Advanced", "commonExpert": "Expert", "commonProfessional": "Professional", "commonSpecialized": "Specialized", "commonTechnical": "Technical", "commonScientific": "Scientific", "commonAcademic": "Academic", "commonEducational": "Educational", "commonInstructional": "Instructional", "commonTutorial": "Tutorial", "commonGuide": "Guide", "commonManual": "Manual", "commonHandbook": "Handbook", "commonReference": "Reference", "commonDocumentation": "Documentation", "commonSupport": "Support", "commonAssistance": "Assistance", "commonAid": "Aid", "commonService": "Service", "commonMaintenance": "Maintenance", "commonRepair": "Repair", "commonFix": "Fix", "commonSolve": "Solve", "commonResolve": "Resolve", "commonAddress": "Address", "commonHandle": "<PERSON><PERSON>", "commonManage": "Manage", "commonControl": "Control", "commonDirect": "Direct", "commonLead": "Lead", "commonConduct": "Conduct", "commonOperate": "Operate", "commonRun": "Run", "commonExecute": "Execute", "commonPerform": "Perform", "commonImplement": "Implement", "commonCarryOut": "Carry Out", "commonAccomplish": "Accomplish", "commonAchieve": "Achieve", "commonAttain": "<PERSON><PERSON>", "commonReach": "Reach", "commonObtain": "Obtain", "commonGet": "Get", "commonAcquire": "Acquire", "commonGain": "<PERSON><PERSON>", "commonReceive": "Receive", "commonCollect": "Collect", "commonGather": "<PERSON><PERSON>", "commonAssemble": "Assemble", "commonCompile": "Compile", "commonCombine": "Combine", "commonMerge": "<PERSON><PERSON>", "commonJoin": "Join", "commonUnite": "Unite", "commonConnect": "Connect", "commonLink": "Link", "commonAttach": "Attach", "commonFasten": "<PERSON><PERSON>", "commonSecure": "Secure", "commonTie": "Tie", "commonBind": "Bind", "commonWrap": "Wrap", "commonCover": "Cover", "commonEnclose": "Enclose", "commonSurround": "Surround", "commonEnvelop": "Envelop", "commonContain": "Contain", "commonInclude": "Include", "commonInvolve": "Involve", "commonEmbrace": "Em<PERSON>ce", "commonEncompass": "Encompass", "commonSpan": "Span", "commonExtend": "Extend", "commonStretch": "<PERSON><PERSON><PERSON>", "commonExpand": "Expand", "commonGrow": "Grow", "commonIncrease": "Increase", "commonEnlarge": "Enlarge", "commonMagnify": "Magnify", "commonAmplify": "Amplify", "commonBoost": "Boost", "commonEnhance": "<PERSON><PERSON>ce", "commonImprove": "Improve", "commonBetter": "Better", "commonUpgrade": "Upgrade", "commonAdvance": "Advance", "commonProgress": "Progress", "commonDevelop": "Develop", "commonEvolve": "Evolve", "commonMature": "Mature", "commonRipe": "R<PERSON>e", "commonPerfect": "Perfect", "complete": "完了", "commonComplete": "Complete", "commonEnd": "End", "commonTerminate": "Terminate", "commonConclude": "Conclude", "commonFinalize": "Finalize", "commonShut": "Shut", "commonSeal": "Seal", "commonLock": "Lock", "commonTighten": "<PERSON>ighten", "commonOrganize": "Organize", "commonArrange": "<PERSON><PERSON><PERSON>", "commonOrder": "Order", "commonClassify": "Classify", "commonCategorize": "Categorize", "commonGroup": "Group", "commonCluster": "Cluster", "commonBunch": "Bunch", "commonBundle": "Bundle", "commonPack": "Pack", "commonPackage": "Package", "commonHold": "Hold", "commonCarry": "Carry", "commonBear": "Bear", "commonSustain": "<PERSON><PERSON><PERSON>", "commonMaintain": "Maintain", "commonKeep": "Keep", "commonRetain": "<PERSON><PERSON>", "commonPreserve": "Preserve", "commonConserve": "Conserve", "commonSave": "Save", "commonStore": "Store", "commonReserve": "Reserve", "commonSetAside": "Set Aside", "commonPutAway": "Put Away", "commonPlace": "Place", "commonPosition": "Position", "commonLocate": "Locate", "commonSituate": "Situate", "commonInstall": "Install", "commonSet": "Set", "commonEstablish": "Establish", "commonFound": "Found", "commonCreate": "Create", "commonMake": "Make", "commonBuild": "Build", "commonConstruct": "Construct", "commonForm": "Form", "commonShape": "<PERSON><PERSON><PERSON>", "commonMold": "Mold", "commonFashion": "Fashion", "commonDesign": "Design", "commonPlan": "Plan", "commonDevise": "<PERSON><PERSON>", "commonConceive": "Conceive", "commonImagine": "Imagine", "commonEnvision": "Envision", "commonVisualize": "Visualize", "commonDream": "Dream", "commonThink": "Think", "commonConsider": "Consider", "commonPonder": "<PERSON><PERSON>", "commonReflect": "Reflect", "commonMeditate": "Meditate", "commonContemplate": "Contemplate", "commonStudy": "Study", "commonLearn": "Learn", "commonDiscover": "Discover", "commonFind": "Find", "commonUncover": "Uncover", "commonReveal": "Reveal", "commonExpose": "Expose", "commonDisclose": "Disclose", "commonDivulge": "Divulge", "commonTell": "Tell", "commonInform": "Inform", "commonNotify": "Notify", "commonAnnounce": "Announce", "commonDeclare": "<PERSON><PERSON><PERSON>", "commonProclaim": "Proclaim", "commonPronounce": "Pronounce", "commonState": "State", "commonExpress": "Express", "commonVoice": "Voice", "commonArticulate": "Articulate", "commonUtter": "<PERSON><PERSON>", "commonSay": "Say", "commonSpeak": "Speak", "commonTalk": "Talk", "commonConverse": "Converse", "commonCommunicate": "Communicate", "commonCorrespond": "Correspond", "commonContact": "Contact", "commonApproach": "Approach", "commonAccost": "Accost", "commonGreet": "Greet", "commonWelcome": "Welcome", "commonAccept": "Accept", "commonTake": "Take", "commonProcure": "Procure", "commonPurchase": "Purchase", "commonBuy": "Buy", "commonShop": "Shop", "commonTrade": "Trade", "commonExchange": "Exchange", "commonSwap": "<PERSON><PERSON><PERSON>", "commonSwitch": "Switch", "commonChange": "Change", "commonAlter": "Alter", "commonModify": "Modify", "commonAdjust": "Adjust", "commonTweak": "Tweak", "commonFineTune": "Fine Tune", "commonOptimize": "Optimize", "commonRefine": "Refine", "commonPolish": "Polish", "commonCease": "Cease", "commonHalt": "Halt", "commonBreak": "Break", "commonInterrupt": "Interrupt", "commonSuspend": "Suspend", "commonDelay": "Delay", "commonPostpone": "Postpone", "commonDefer": "Defer", "commonWait": "Wait", "commonRemain": "<PERSON><PERSON><PERSON>", "commonStay": "Stay", "commonProceed": "Proceed", "commonMove": "Move", "commonGo": "Go", "commonTravel": "Travel", "commonJourney": "Journey", "commonPass": "Pass", "commonCross": "Cross", "commonTransit": "Transit", "commonTransfer": "Transfer", "commonConvey": "<PERSON><PERSON>", "commonTransport": "Transport", "commonBring": "Bring", "commonFetch": "<PERSON>tch", "commonSalute": "Salute", "commonHail": "<PERSON>l", "commonNigh": "<PERSON>gh", "commonDrawNear": "Draw Near", "commonCome": "Come", "commonArrive": "Arrive", "commonLand": "Land", "commonEnter": "Enter", "commonAccess": "Access", "commonGoOn": "Go On", "commonKeepOn": "Keep On", "commonCarryOn": "Carry On", "commonPersist": "Persist", "commonPersevere": "Persevere", "commonEndure": "Endure", "commonLast": "Last", "markdownSaveButton": "Save", "markdownCancelButton": "Cancel", "markdownConfirmButton": "Confirm", "markdownResetButton": "Reset", "markdownApplyButton": "Apply", "markdownCloseButton": "Close", "markdownSelectButton": "Select", "markdownBrowseButton": "Browse", "markdownSearchButton": "Search", "markdownClearButton": "Clear", "markdownDeleteButton": "Delete", "markdownEditButton": "Edit", "markdownExportButton": "Export", "markdownImportButton": "Import", "markdownShareButton": "Share", "markdownCopyButton": "Copy", "markdownPasteButton": "Paste", "markdownCutButton": "Cut", "markdownUndoButton": "Undo", "markdownRedoButton": "Redo", "markdownTemplateSelector": "Template Selector", "markdownStyleSelector": "Style Selector", "markdownWatermarkSettings": "Watermark Settings", "markdownBlockSettings": "Block Settings", "markdownBlockConfigPanel": "Block Configuration Panel", "markdownBlockManagerPanel": "Block Management Panel", "markdownTextLabel": "Text", "markdownMarkdownContent": "Markdown Content", "markdownWatermarkText": "Watermark Text", "markdownEnterWatermarkText": "Enter watermark text", "markdownEnterMarkdownContent": "Enter Markdown content...", "markdownFontSettings": "Font Settings", "markdownFontSize": "Font Size", "markdownFontFamily": "Font Family", "markdownCodeFont": "Code Font", "markdownColorSettings": "Color Settings", "markdownTextColor": "Text Color", "markdownBackgroundColor": "Background Color", "markdownBorderColor": "Border Color", "markdownBorderWidth": "Border Width", "markdownShadowSettings": "Shadow Settings", "markdownShadowColor": "Shadow Color", "markdownBorderRadius": "Border Radius", "markdownPadding": "Padding", "markdownMargin": "<PERSON><PERSON>", "markdownWatermarkContent": "Watermark Content", "markdownWatermarkTextStyle": "Text Style", "markdownWatermarkNormal": "Normal", "markdownWatermarkBold": "Bold", "markdownWatermarkItalic": "Italic", "markdownWatermarkPosition": "Display Position", "markdownWatermarkTextColor": "Text Color", "markdownWatermarkOpacity": "Opacity", "markdownWatermarkFontSize": "Font Size", "markdownWatermarkRotation": "Rotation", "markdownWatermarkTileSettings": "Tile Settings", "markdownWatermarkHorizontalSpacing": "Horizontal Spacing", "markdownWatermarkVerticalSpacing": "Vertical Spacing", "markdownSelectWatermarkColor": "Select Watermark Color", "markdownResetToAppName": "Reset to App Name", "markdownShowBlockTitle": "Show Block Title", "markdownShowBlockBorder": "Show Block Border", "markdownSortByIndex": "Sort by Index", "markdownSortByTitle": "Sort by Title", "markdownSortByType": "Sort by Type", "markdownSortByLength": "Sort by Length", "markdownShareResult": "Share Result", "markdownExportResult": "Export Result", "markdownSaveSuccess": "Save Success", "markdownSaveFailed": "Save Failed", "markdownTemplateDescription": "Template Description", "markdownTemplateFeatures": "Template Features", "markdownBorderStyle": "Border Style", "markdownShadowEffect": "Shadow Effect", "markdownShowHeader": "Show Header", "markdownInnerShadow": "Inner Shadow", "markdownHeadingAlignment": "Heading Alignment", "markdownLeftAlign": "Left Align", "markdownCenterAlign": "Center Align", "markdownRightAlign": "Right Align", "markdownGradientBackground": "Gradient Background", "markdownBackgroundPattern": "<PERSON> Pattern", "markdownListItemStyle": "List Item Style", "markdownCheckboxStyle": "Checkbox Style", "markdownShareImageSubtitle": "Share rendered result with others", "markdownCopyContentSubtitle": "Copy Markdown text to clipboard", "markdownSaveToAlbumSubtitle": "Save image to local album", "markdownOperationOptions": "Operation Options", "markdownSelectColor": "Select Color", "markdownChooseColor": "Choose Color", "markdownColorPicker": "Color Picker", "markdownResetSettings": "Reset Settings", "markdownApplySettings": "Apply Settings", "markdownLoading": "Loading...", "markdownGenerating": "Generating...", "markdownProcessing": "Processing...", "markdownSaving": "Saving...", "markdownExporting": "Exporting...", "markdownSharing": "Sharing...", "markdownCopying": "Copying...", "markdownSuccess": "Success", "markdownError": "Error", "markdownWarning": "Warning", "markdownInfo": "Info", "markdownComplete": "Complete", "markdownFailed": "Failed", "markdownCancelled": "Cancelled", "markdownTemplateSelected": "Template \"{name}\" selected", "markdownSaveError": "Save failed: {error}", "markdownLoadError": "Load error: {error}", "markdownProcessError": "Process error: {error}", "markdownBlockModeEnabled": "Block mode enabled", "markdownBlockModeDisabled": "Block mode disabled", "markdownBlockAdded": "Block added", "markdownBlockRemoved": "Block removed", "markdownBlockUpdated": "Block updated", "markdownBlockHidden": "Block hidden", "markdownBlockShown": "Block shown", "markdownBlockSelected": "Block selected", "markdownBlockDeselected": "Block deselected", "markdownBlockMoved": "Block moved", "markdownBlockResized": "Block resized", "markdownBlockReordered": "Block reordered", "markdownBlockExported": "Block exported", "markdownBlockImported": "Block imported", "markdownBlockRenderFeature1": "• Block rendering can split long documents into multiple independent blocks", "markdownBlockRenderFeature2": "• Each block can be individually displayed or hidden", "markdownBlockRenderFeature3": "• Support multiple separation methods: headers, custom separators, manual separation", "markdownBlockRenderFeature4": "• Adjust block settings in the left configuration panel", "markdownBlockRenderFeature5": "• Click in the preview area to add new separator bars", "markdownBlockRenderFeature6": "• Drag separator bars to readjust block positions", "markdownBlockRenderFeature7": "• Click the eye icon on block title bars to hide/show blocks", "markdownBlockRenderFeature8": "• Different types of blocks are distinguished by different colored borders", "markdownBlockRenderFeature9": "• Blue: H1 header blocks", "markdownBlockRenderFeature10": "• Green: H2 header blocks", "markdownBlockRenderFeature11": "• Orange: Custom separator blocks", "markdownBlockRenderFeature12": "• Gray: Manual separator blocks", "markdownGotIt": "Got It", "markdownIKnow": "I Know", "markdownUnderstood": "Understood", "markdownAlignLeft": "Left", "markdownAlignCenter": "Center", "markdownAlignRight": "Right", "markdownPositionTopLeft": "Top Left", "markdownPositionTopCenter": "Top Center", "markdownPositionTopRight": "Top Right", "markdownPositionBottomLeft": "Bottom Left", "markdownPositionBottomCenter": "Bottom Center", "markdownPositionBottomRight": "Bottom Right", "markdownPositionTiled": "Tiled", "markdownExportingBlocks": "Exporting blocks...", "markdownGeneratingReport": "Generating report...", "markdownProcessingComplete": "Processing complete", "markdownOperationSuccessful": "Operation successful", "markdownOperationFailed": "Operation failed", "markdownWatermarkVisible": "Show Watermark", "markdownWatermarkHidden": "Hide Watermark", "markdownWatermarkPositionAppearance": "Position & Appearance", "markdownWatermarkDisplayPosition": "Display Position", "markdownWatermarkHorizontalGap": "Horizontal Gap", "markdownWatermarkVerticalGap": "Vertical Gap", "markdownWatermarkSelectColor": "Select Watermark Color", "markdownWatermarkCancel": "Cancel", "markdownWatermarkConfirm": "Confirm", "voiceInitializationFailed": "Initialization failed: {error}", "voicePlayingAllRecordings": "Playing all recordings", "voiceMyRecordings": "My Recordings", "voicePlayAll": "Play All", "voiceNoRecordings": "No voice recordings", "voiceStartRecording": "Start Recording", "voiceTapToStartRecording": "Tap the button below to record your first voice", "voiceConfirmDelete": "Confirm Delete", "voiceDeleteConfirmation": "Are you sure you want to delete this voice recording?", "voiceCancel": "Cancel", "voiceDelete": "Delete", "voiceRecordingDeleted": "Recording deleted", "voiceTranscriptionContent": "Transcription:", "voiceToday": "Today", "voiceYesterday": "Yesterday", "voiceRecording": "Recording", "voiceRecordingPageTitle": "Voice Recording", "voiceRequestPermission": "Request Permission", "voiceOpenSettings": "Open Settings", "voiceRecordingInProgress": "Recording in progress...", "voiceReadyToRecord": "Ready to start recording", "voiceStartSpeaking": "Please start speaking...", "voiceClickToStart": "Click to start", "voiceClickToStop": "Click to stop", "voiceRecordingStopped": "Recording stopped", "voiceRecordingFailed": "Recording failed", "voiceStopRecordingFailed": "Failed to stop recording: {error}", "voiceRecordingFileInvalid": "Recording file is invalid, please try again", "voiceRecordingFileNotFound": "Recording file not found, recording may have failed", "voiceSaveRecording": "Save Recording", "voiceTitle": "Title", "voiceDuration": "Duration", "voiceTranscription": "Transcription", "voiceRecordingAndTranscriptionSaved": "Recording and transcription text saved", "voiceRecordingSaved": "Recording saved", "voiceSaveRecordingFailed": "Failed to save recording: {error}", "voiceNoMicrophonePermission": "No microphone permission, cannot record", "voicePermissionRequired": "Permission Required", "voicePermissionInstructions": "Please follow these steps to enable permissions:", "voicePermissionStep1": "1. <PERSON>lick \"Go to Settings\" button", "voicePermissionStep2": "2. <PERSON><PERSON> \"Privacy & Security\" in Settings", "voicePermissionStep3": "3. <PERSON><PERSON> \"Microphone\" and \"Speech Recognition\" respectively", "voicePermissionStep4": "4. Find \"内容君\" in the list and enable permissions", "voicePermissionNote": "Note: If you don't see the app, please return to the app and click \"Request Permission\" again, then check settings again", "voiceGoToSettings": "Go to Settings", "voiceEnableMicrophonePermission": "Please enable microphone permission in settings to record", "voiceInitializationError": "Initialization Error", "voiceRequestPermissionAgain": "Request Permission Again", "voiceStartRecordingFailed": "Failed to start recording: {error}", "voiceNeedMicrophonePermission": "Need microphone permission to use recording feature", "voiceNeedMicrophonePermissionForRecording": "Need microphone permission to record", "voiceSpeechRecognitionInitFailed": "Speech recognition initialization failed, please ensure microphone permission is granted", "voiceRecordingTitle": "Voice Recording", "voicePermissionGuide": "Please follow these steps to enable permissions:", "voicePermissionGuideStep1": "1. <PERSON>lick \"Go to Settings\" button", "voicePermissionGuideStep2": "2. <PERSON><PERSON> \"Privacy & Security\" in settings", "voicePermissionGuideStep3": "3. <PERSON><PERSON> \"Microphone\" and \"Speech Recognition\" respectively", "voicePermissionGuideStep4": "4. Find \"内容君\" in the list and enable permissions", "voicePermissionGuideNote": "Note: If you cannot find the app, please return to the app and click \"Request Permission\" again, then check settings again", "voiceRecordingTitleLabel": "Title", "voiceRecordingDuration": "Duration: {duration}", "voiceRecordingTranscription": "Transcription: {transcription}", "voiceRecordingFileNotExist": "Recording file does not exist, recording may have failed", "voicePleaseStartSpeaking": "Please start speaking...", "voiceClickToStartRecording": "Click button below to start recording\nVoice will be converted to text in real-time", "voiceSpeechRecognitionInProgress": "Speech recognition in progress, real-time transcription displayed above", "voiceSave": "Save", "trafficGuideContentTools": "Content Tools", "trafficGuideToolsDescription": "Choose the right tool to create traffic content", "trafficGuideTextTransformer": "テキスト変換", "trafficGuideTextTransformerSubtitle": "絵文字変換と文字の難読化", "trafficGuideWatermarkProcessor": "Watermark Processor", "trafficGuideWatermarkProcessorSubtitle": "Add and remove invisible watermarks", "trafficGuideNewProject": "New Project", "trafficGuideNewProjectSubtitle": "Create traffic project configuration", "trafficGuideMyProjects": "My Projects", "trafficGuideProjectsDescription": "Manage your traffic project configurations", "trafficGuideNoProjects": "No Projects", "trafficGuideNoProjectsDescription": "Click 'New Project' to create your first traffic project", "trafficGuideRefresh": "Refresh", "trafficGuideLoading": "Loading...", "trafficGuideLastUpdated": "Updated: {date}", "trafficGuideConfirmDelete": "Confirm Delete", "trafficGuideDeleteConfirmation": "Are you sure you want to delete project \"{name}\"?", "trafficGuideProjectDeleted": "Project \"{name}\" deleted", "trafficGuideEdit": "Edit", "trafficGuideDelete": "Delete", "trafficGuideProjectEditor": "Project Editor", "trafficGuideBasicInfo": "Basic Information", "trafficGuideProjectName": "Project Name", "trafficGuideProjectNameHint": "Enter project name", "trafficGuideProjectDescription": "Project Description", "trafficGuideProjectDescriptionHint": "Enter project description", "trafficGuideProjectNameRequired": "Please enter project name", "trafficGuideImageConfig": "Image Configuration", "trafficGuideDefaultText": "Default Text", "trafficGuideDefaultTextHint": "Enter default display text", "trafficGuideFontFamily": "Font Family", "trafficGuideTextTransformConfig": "Text Transform Configuration", "trafficGuideEmojiConversion": "Emoji Conversion", "trafficGuideEmojiConversionSubtitle": "Convert numbers and letters to special Unicode characters", "trafficGuideUnicodeVariation": "Unicode Variation", "trafficGuideUnicodeVariationSubtitle": "Add diacritical characters and special Unicode", "trafficGuideInvisibleChars": "Invisible Characters", "trafficGuideInvisibleCharsSubtitle": "Insert invisible characters in text", "trafficGuideSensitiveWordMasking": "Sensitive Word Masking", "trafficGuideSensitiveWordMaskingSubtitle": "Apply character obfuscation to sensitive words", "trafficGuideSensitiveWords": "Sensitive Words", "trafficGuideSensitiveWordsHint": "Enter sensitive words, separated by commas", "trafficGuideWatermarkConfig": "Watermark Configuration", "trafficGuideWatermarkTextHint": "Enter watermark content", "trafficGuideInvisibleWatermark": "不可視透かし", "trafficGuideOpacity": "不透明度", "trafficGuideWatermarkFontSize": "Font Size", "trafficGuideProjectSaved": "Project saved successfully", "trafficGuideSaveProject": "Save Project", "trafficGuideSaving": "Saving...", "trafficGuideNewProjectName": "New Project", "trafficGuideNewProjectDescription": "Traffic project configuration", "trafficGuideImageGeneratorTitle": "Traffic Image Generator", "trafficGuideImageConfiguration": "Image Configuration", "trafficGuideTextRequired": "Please enter text content", "trafficGuideInterferenceSettings": "Interference Settings", "trafficGuideInterferenceLevel": "Interference Level", "trafficGuideWatermarkSettings": "透かし設定", "trafficGuideWatermarkContent": "透かしコンテンツ", "trafficGuideWatermarkContentHint": "透かしコンテンツを入力...", "trafficGuidePreview": "プレビュー", "trafficGuideSaveToAlbum": "Save to Album", "trafficGuideShare": "Share", "trafficGuideSelectColor": "Select Color", "trafficGuideBlack": "Black", "trafficGuideWhite": "White", "trafficGuideRed": "Red", "trafficGuideGreen": "Green", "trafficGuideBlue": "Blue", "trafficGuideYellow": "Yellow", "trafficGuidePurple": "Purple", "trafficGuideCyan": "<PERSON><PERSON>", "trafficGuideGenerateImage": "Generate Image", "trafficGuideGenerating": "Generating...", "trafficGuideImageGenerationFailed": "Image generation failed: {error}", "trafficGuideLongPressToSave": "Long press image to save to album", "trafficGuideShareFeatureInProgress": "Share feature in development...", "trafficGuideTextTransformerTitle": "テキスト変換", "trafficGuideTransformSettings": "Transform Settings", "trafficGuideTransformText": "Transform Text", "trafficGuideTransforming": "Transforming...", "trafficGuideInputText": "Input Text", "trafficGuideCharacters": "文字", "trafficGuideInputHint": "Enter text to transform...", "trafficGuideTransformResult": "Transform Result", "trafficGuideResultHint": "Transformed text will appear here...", "trafficGuideTransformFailed": "Transform failed: {error}", "trafficGuideCopyResult": "結果をコピー", "trafficGuideClear": "クリア", "trafficGuideSettings": "Settings", "trafficGuideAdvancedSettings": "詳細設定", "trafficGuideCopiedToClipboard": "クリップボードにコピーしました", "trafficGuideCustomCharacterMapping": "Custom Character Mapping", "trafficGuideMappingFormat": "Format: original=target (one per line)", "trafficGuideMappingExample": "Example:\na=ᴀ\nb=ʙ", "trafficGuideConfirm": "Confirm", "trafficGuideWatermarkTitle": "テキスト透かし処理", "trafficGuideProcessingMode": "処理モード", "trafficGuideAddWatermarkMode": "透かしを追加", "trafficGuideAddWatermarkModeSubtitle": "テキストに表示または非表示の透かしを追加", "trafficGuideRemoveWatermarkMode": "透かしを削除", "trafficGuideRemoveWatermarkModeSubtitle": "テキストから追加された透かしを削除", "trafficGuideProcessText": "Process Text", "trafficGuideProcessing": "処理中...", "trafficGuideOriginalText": "元のテキスト", "trafficGuideWatermarkedText": "透かし入りのテキスト", "trafficGuideProcessHint": "処理結果がここに表示されます", "trafficGuideWatermarkIdentifier": "透かし識別子", "trafficGuideWatermarkIdentifierHint": "削除する透かし識別子を入力...", "trafficGuideRotationAngle": "回転角度", "trafficGuideEnterTextToProcess": "処理するテキストを入力してください", "trafficGuideEnterWatermarkContent": "透かしコンテンツを入力してください", "trafficGuideProcessFailed": "処理に失敗しました: {error}", "trafficGuideSensitiveWordsList": "Sensitive Words List", "trafficGuideSensitiveWordsListHint": "Enter sensitive words, separated by commas", "trafficGuideWatermarkAddHint": "透かしを追加する必要があるテキストコンテンツを入力", "trafficGuideWatermarkRemoveHint": "透かしを削除する必要があるテキストコンテンツを入力", "textCardsHomePageTitle": "テキストカード", "textCardsHomePageSubtitle": "モダンなスタイル・インライン編集・高画質エクスポート", "textCardsStartCreating": "作成を始める", "textCardsQuickActions": "クイック操作", "textCardsTemplateLibrary": "テンプレートギャラリー", "textCardsTemplateLibrarySubtitle": "16種類以上の美しいテンプレート", "textCardsSmartSplit": "スマート分割", "textCardsSmartSplitSubtitle": "長文のセグメント化", "textCardsContentLibrary": "コンテンツライブラリ", "textCardsContentLibrarySubtitle": "すべてのカードを管理", "textCardsShare": "共有", "textCardsShareSubtitle": "高画質画像を書き出し", "textCardsFeatures": "機能", "textCardsModernTemplates": "モダンスタイルのテンプレート", "textCardsModernTemplatesDesc": "ソーシャル・読書向けのモダンなテンプレートを厳選し、コンテンツをより魅力的に", "textCardsInlineEditing": "インラインテキスト編集", "textCardsInlineEditingDesc": "任意のテキストを選択し、フォント・色・サイズをリアルタイムに調整", "textCardsHDExport": "高画質エクスポート", "textCardsHDExportDesc": "複数の解像度とアスペクト比に対応。ワンタップでアルバムに保存", "textCardsViewContentLibrary": "マイコンテンツライブラリを表示", "textCardsManageCards": "作成済みカードを管理・閲覧", "textCardsCreate": "作成", "textCardsPleaseCreateCardFirst": "Please create a card first", "textCardsCardCreatedSuccess": "Card created successfully! Saved to content library", "textCardsBatchCreateSuccess": "Batch creation successful! Created {count} cards", "textCardsBatchCreatePartial": "Batch creation completed! Successfully created {success}/{total} cards", "textCardsCreateFailed": "Creation failed: {error}", "textCardsBatchCreateFailed": "Batch creation failed: {error}", "textCardsEditorTitle": "Card Editor", "textCardsCreateCard": "Create Card", "textCardsEditCard": "Edit Card", "textCardsSave": "Save", "textCardsEdit": "Edit", "textCardsPreview": "Preview", "textCardsEnterCardTitle": "Enter card title...", "textCardsEnterContent": "Enter content...\n\nSupports Markdown format:\n• **Bold**\n• *Italic*\n• • Unordered list\n• 1. Ordered list", "textCardsContentRequired": "Please enter content", "textCardsSaveFailed": "Save failed: {error}", "textCardsChangeTemplate": "Change Template", "textCardsHideTitle": "Hide Title", "textCardsShowTitle": "Show Title", "textCardsBoldText": "Bold text", "textCardsItalicText": "Italic text", "textCardsUnderlineText": "Underline text", "textCardsPreviewPlaceholder": "Enter content in the edit tab to see preview...", "textCardsContentEditor": "Content Editor", "textCardsEnterTitle": "Enter title (optional)", "textCardsAddSplitMarker": "Add Split Marker", "textCardsEnterRenderer": "<PERSON><PERSON>", "textCardsSplitMarkerInfo": "Set {count} split markers, will generate {sections} cards", "textCardsEnterContentHint": "Enter or paste your content here...\n\nTips:\n- Use # to create headings\n- Use - or * to create lists\n- Use > to create quotes\n- Click split button to add split marker at cursor position", "textCardsTextPreview": "Text Preview", "textCardsPreviewWillAppear": "Preview will appear when content is entered", "textCardsSelectSplitMarker": "Select Split Marker", "textCardsPredefinedMarkers": "Predefined Markers:", "textCardsCustomMarker": "Custom Marker:", "textCardsEnterCustomMarker": "Enter custom split marker", "textCardsUseCustom": "Use Custom", "textCardsUnnamedCard": "Unnamed Card", "textCardsUnnamedDocument": "Unnamed Document", "svgEditorTitle": "SVG Editor", "svgManagerTitle": "SVG Manager", "svgUntitled": "Untitled SVG", "svgEditTab": "Edit", "svgPreviewTab": "Preview", "svgMoreActions": "More Actions", "svgImportFile": "Import SVG File", "svgSave": "Save", "svgExportPng": "Export as PNG", "svgSharePng": "PNG として共有", "svgShareSvg": "SVG を共有", "svgRename": "<PERSON><PERSON>", "svgSaveChanges": "Save Changes", "svgEnterFileName": "Enter SVG File Name", "svgFileName": "File Name", "svgFileNameHint": "Enter SVG file name", "svgFileNameRequired": "File name cannot be empty", "svgProcessing": "Processing...", "svgLoading": "Loading...", "svgDocumentNotFound": "Document not found", "svgLoadDocumentFailed": "Failed to load document: {error}", "svgCreateDocumentFailed": "Failed to create document: {error}", "svgSaveDocumentFailed": "Failed to save document: {error}", "svgSaveSuccess": "Saved successfully", "svgImportFailed": "Failed to import SVG file: {error}", "svgExportPngSuccess": "PNG export successful: {path}", "svgExportPngFailed": "PNG export failed", "svgShareSvgFailed": "Failed to share SVG: {error}", "svgSharePngFailed": "Failed to share PNG: {error}", "svgInvalidSvg": "Invalid SVG: {error}", "svgSaveFirst": "Please save document first", "svgEnterSvgCode": "Enter SVG code", "svgNoContent": "No SVG content", "svgEnterCodeInEditor": "Please enter SVG code in the editor tab", "svgCreateNew": "Create New SVG", "svgImport": "Import SVG", "svgImportSvgFile": "SVG ファイルをインポート", "svgImportTooltip": "Import SVG", "svgNewTooltip": "New SVG", "svgCreateNewTooltip": "Create new SVG", "svgNoDocuments": "No SVG Documents", "svgNoDocumentsDesc": "Create a new SVG document or import existing files to get started", "svgLoadFailed": "Failed to load documents: {error}", "svgDeleteConfirm": "Confirm Delete", "svgDeleteConfirmMessage": "Are you sure you want to delete \"{title}\"?", "svgDeleteFailed": "Failed to delete document: {error}", "svgEdit": "編集", "svgShare": "共有", "svgShareAsPng": "PNG として共有", "svgDelete": "Delete", "svgSavedToLibrary": "SVG saved to content library", "svgCreatedAt": "Created: {date}", "pdfManagerTitle": "PDF Document Management", "pdfSearch": "Search...", "pdfSearchHint": "Search...", "pdfNoDocuments": "No PDF documents found", "pdfTryDifferentSearch": "Try using different search keywords", "pdfConfirmDelete": "Confirm Delete", "pdfDeleteConfirm": "Are you sure you want to delete \"{filename}\"? This action cannot be undone.", "pdfBatchDeleteConfirm": "Are you sure you want to delete {count} selected files? This action cannot be undone.", "pdfCancel": "Cancel", "pdfDelete": "Delete", "pdfImport": "Import PDF", "pdfImportTooltip": "Import PDF", "pdfSecuritySettings": "PDF Security Settings", "pdfSelect": "Select", "pdfMerge": "<PERSON><PERSON>", "pdfSelectAtLeastTwo": "Please select at least two PDF files to merge", "pdfMergeSuccess": "PDF merge successful", "pdfMergeFailed": "PDF merge failed", "pdfIntelligentCenter": "PDF Intelligent Management Center", "pdfCenterSubtitle": "Professional PDF tool integrating reading, editing, security, and sharing", "pdfVersion": "v2.0 Professional", "pdfCoreFeatures": "Core Features", "pdfProfessional": "Professional", "pdfSecurityEncryption": "Security Encryption", "pdfPasswordProtection": "Password Protection", "pdfPermissionControl": "Permission Control", "pdfSmartAnnotations": "Smart Annotations", "pdfHighlight": "Highlight", "pdfTextAnnotations": "Text Annotations", "pdfFastSearch": "Fast Search", "pdfFullTextSearch": "Full Text Search", "pdfPreciseLocation": "Precise Location", "pdfDocumentMerge": "Document Merge", "pdfMultiFileMerge": "Multi-file Merge", "pdfEasySharing": "Easy Sharing", "pdfOneClickShare": "One-click Share", "pdfCloudSync": "Cloud Sync", "pdfAutoSync": "Auto Sync", "pdfQuickStart": "Quick Start", "pdfImportDocument": "Import PDF Document", "pdfViewDemo": "View Demo", "pdfHelp": "Help", "pdfSupportInfo": "Supports .pdf format files, up to 100MB", "pdfModified": "Modified: {date}", "pdfJustNow": "Just now", "pdfMinutesAgo": "{minutes} minutes ago", "pdfHoursAgo": "{hours} hours ago", "pdfDaysAgo": "{days} days ago", "pdfPages": "{count} pages", "pdfSecurityStatus": "Security Status", "pdfProtected": "Protected", "pdfRestricted": "Restricted", "pdfUnprotected": "Unprotected", "pdfUsageStats": "Usage Statistics", "pdfTotalDocuments": "Total Documents", "pdfTodayProcessed": "Today Processed", "pdfStorageSpace": "Storage Space", "pdfTips": "Usage Tips", "pdfLongPressSelect": "Long Press to Select", "pdfLongPressDesc": "Long press document cards to enter multi-select mode for batch operations", "pdfSecurityTip": "Security Encryption", "pdfSecurityTipDesc": "Set password protection for important documents to ensure information security", "pdfMergeTip": "Document Merge", "pdfMergeTipDesc": "Select multiple PDF documents to merge into a single file with one click", "pdfViewerTitle": "PDF Viewer", "pdfSearchText": "Search text", "pdfShowAnnotations": "Show Annotations", "pdfHideAnnotations": "Hide Annotations", "pdfDocumentInfo": "Document Info", "pdfAnnotationDetails": "Annotation Details", "pdfAuthor": "Author: {author}", "pdfCreatedAt": "Created: {time}", "pdfContent": "Content: {content}", "pdfHighlightedText": "Highlighted text: {text}", "pdfFileName": "File Name", "pdfFileSize": "Size", "pdfPageCount": "Pages", "pdfCreatedDate": "Created", "pdfModifiedDate": "Modified", "pdfAnnotationCount": "Annotations", "pdfClose": "Close", "pdfSecurityTitle": "PDF Security Settings", "pdfDocumentInfoSection": "Document Information", "pdfStatus": "Status: {status}", "pdfDecryptPdf": "Decrypt PDF", "pdfEncryptedDesc": "This PDF is encrypted, please enter password to decrypt", "pdfCurrentPassword": "Current Password", "pdfCurrentPasswordHint": "Enter current password", "pdfDecryptButton": "Decrypt", "pdfEncryptionSettings": "Encryption Settings", "pdfUserPassword": "User Password *", "pdfUserPasswordHint": "Password to open PDF", "pdfOwnerPassword": "Owner Password (Optional)", "pdfOwnerPasswordHint": "Password to modify permissions", "pdfPermissionSettings": "Permission Settings", "pdfAllowPrint": "Allow Printing", "pdfAllowPrintDesc": "Allow users to print PDF document", "pdfAllowCopy": "Allow Copying", "pdfAllowCopyDesc": "Allow users to copy PDF content", "pdfAllowEdit": "Allow Editing", "pdfAllowEditDesc": "Allow users to edit PDF document", "pdfAllowEditAnnotations": "Allow Edit Annotations", "pdfAllowEditAnnotationsDesc": "Allow users to add or edit annotations", "pdfAllowFillForms": "Allow Fill Forms", "pdfAllowFillFormsDesc": "Allow users to fill form fields", "pdfAllowExtractPages": "Allow Extract Pages", "pdfAllowExtractPagesDesc": "Allow users to extract page content", "pdfAllowAssembleDocument": "Allow Assemble Document", "pdfAllowAssembleDocumentDesc": "Allow users to insert, delete, rotate pages", "pdfAllowHighQualityPrint": "Allow High Quality Print", "pdfAllowHighQualityPrintDesc": "Allow users to print in high quality", "pdfPresetPermissions": "Preset Permissions", "pdfAllPermissions": "All Permissions", "pdfBasicPermissions": "Basic Permissions", "pdfReadOnly": "Read Only", "pdfSetPermissionsOnly": "Set Permissions Only", "pdfEncryptAndSetPermissions": "Encrypt and Set Permissions", "pdfEnterUserPassword": "Please enter user password", "pdfEncryptSuccess": "PDF encrypted successfully", "pdfEncryptFailed": "PDF encryption failed", "pdfEncryptionFailed": "Encryption failed: {error}", "pdfEnterCurrentPassword": "Please enter current password", "pdfDecryptSuccess": "PDF decrypted successfully", "pdfDecryptFailed": "PDF decryption failed, please check password", "pdfDecryptionFailed": "Decryption failed: {error}", "pdfPermissionsSetSuccess": "Permissions set successfully", "pdfPermissionsSetFailed": "Permissions set failed", "pdfSetPermissionsFailed": "Settings failed: {error}", "htmlManagerTitle": "HTML Management", "htmlManagerDescription": "Create and edit HTML documents, all content will be automatically saved to the content library for unified management", "htmlCreateNew": "Create New HTML", "htmlImportFile": "HTML ファイルをインポート", "htmlImporting": "Importing...", "htmlImportSuccess": "Import Successful", "htmlImportSuccessMessage": "Successfully imported {count} HTML files to content library", "htmlImportFailed": "Failed to import HTML file: {error}", "htmlImportingProgress": "Importing HTML files ({imported}/{total})", "htmlViewContentLibrary": "View Content Library", "htmlEditorTitle": "HTML Editor", "htmlNewDocument": "New HTML Document", "htmlInputFilename": "Enter HTML Filename", "htmlFilename": "Filename", "htmlFilenameHint": "Please enter HTML filename", "htmlFilenameEmpty": "Filename cannot be empty", "htmlCancel": "キャンセル", "htmlConfirm": "Confirm", "htmlSaveSuccess": "Save successful", "htmlDocumentNotFound": "Document not found", "htmlLoadDocumentFailed": "Failed to load document: {error}", "htmlCreateDocumentFailed": "Failed to create document: {error}", "htmlSaveDocumentFailed": "Failed to save document: {error}", "htmlImportFileFailed": "Failed to import HTML file: {error}", "htmlExportImageFailed": "Failed to export image: {error}", "htmlShareHtmlFailed": "Failed to share HTML: {error}", "htmlShareImageFailed": "Failed to share image: {error}", "htmlSaveToLibraryFailed": "Failed to save to content library: {error}", "htmlPleaseSaveFirst": "Please save document first", "htmlSelectSaveLocation": "Select save location", "htmlExportImageSuccess": "Export image successful: {path}", "htmlSavedToLibrary": "Saved to content library: {title}", "htmlProcessing": "処理中...", "htmlProcessingLargeText": "Processing large text...", "htmlInputHtmlCode": "Enter HTML code", "htmlNoContent": "No HTML content", "htmlPleaseInputHtml": "Please enter HTML code", "htmlEditMode": "Edit Mode", "htmlPreviewMode": "Preview Mode", "htmlSingleScreenMode": "Single Screen Mode", "htmlSplitScreenMode": "Split Screen Mode", "htmlMoreActions": "More Actions", "htmlImportHtmlFile": "HTML ファイルをインポート", "htmlExportAsImage": "画像としてエクスポート", "htmlShareAsImage": "画像として共有", "htmlShareHtml": "HTML を共有", "htmlRename": "<PERSON><PERSON>", "htmlSaveToLibrary": "Save to Content Library", "htmlSaveToLibraryTooltip": "Save to Content Library", "htmlSaveTooltip": "Save", "htmlSaveToGallery": "ギャラリーに保存", "htmlNewHtmlTooltip": "New HTML", "htmlImportHtmlTooltip": "Import HTML", "settingsLanguagePreference": "Select your preferred app language", "settingsStorageManagement": "Storage Management", "settingsHelpCenter": "Help Center", "settingsFeedback": "<PERSON><PERSON><PERSON>", "settingsVersionInfo": "Version Info", "settingsHelpAndFeedback": "Help & Feedback", "settingsGetHelpOrProvideFeedback": "Get help or provide feedback", "settingsHelpAndFeedbackContent": "If you need help or have suggestions, please contact us through the feedback page.", "settingsOk": "OK", "settingsSelectTheme": "Select Theme", "settingsSystemMode": "System", "settingsLightMode": "Light", "settingsDarkMode": "Dark", "storageLoadingStorageInfo": "Loading storage information", "storageLoadStorageInfoFailed": "Failed to load storage information: {error}", "storageTotalUsage": "Total Storage Usage", "storageDetails": "Storage Details", "storageAppData": "App Data", "storageCacheFiles": "<PERSON><PERSON>", "storageContentData": "Content Data", "storageVoiceFiles": "Voice Files", "storageImageFiles": "Image Files", "storageSettingsData": "Settings Data", "storageCleanupOptions": "Cleanup Options", "storageClearCache": "<PERSON>ache", "storageClearCacheDesc": "Delete temporary files and cache data", "storageClearTempFiles": "Clear Temporary Files", "storageClearTempFilesDesc": "Delete temporary files generated during processing", "storageDataManagement": "Data Management", "storageExportData": "Export Data", "storageExportDataDesc": "Export app data to files", "storageImportData": "Import Data", "storageImportDataDesc": "Import app data from files", "storageResetAppData": "Reset App Data", "storageResetAppDataDesc": "Clear all data and restore default settings", "storageCacheCleared": "<PERSON><PERSON> cleared successfully", "storageClearCacheFailed": "Failed to clear cache: {error}", "storageTempFilesCleared": "Temporary files cleared successfully", "storageClearTempFilesFailed": "Failed to clear temporary files: {error}", "storageVoiceManagementInDevelopment": "Voice file management feature is in development", "storageImageManagementInDevelopment": "Image file management feature is in development", "storageDataExportInDevelopment": "Data export feature is in development", "storageDataImportInDevelopment": "Data import feature is in development", "storageResetDataTitle": "Reset App Data", "storageResetDataMessage": "This operation will delete all data and restore default settings. This cannot be undone. Are you sure you want to continue?", "storageCancel": "Cancel", "storageConfirm": "Confirm", "storageDataResetComplete": "App data reset completed", "storageDataResetFailed": "Failed to reset app data: {error}", "iosSettingsGeneral": "General", "iosSettingsLanguageRegion": "Language & Region", "iosSettingsDisplayBrightness": "Display & Brightness", "iosSettingsAppearance": "Appearance", "iosSettingsPrivacySecurity": "Privacy & Security", "iosSettingsPrivacySettings": "Privacy Settings", "iosSettingsStorage": "Storage", "iosSettingsStorageManagement": "Storage Management", "iosSettingsViewStorageUsage": "View storage usage", "iosSettingsDataImportExport": "Data Import/Export", "iosSettingsBackupRestoreData": "Backup and restore data", "iosSettingsSupport": "Support", "iosSettingsHelpCenter": "Help Center", "iosSettingsFeedback": "<PERSON><PERSON><PERSON>", "iosSettingsRateApp": "Rate App", "iosSettingsAbout": "About", "iosSettingsAboutApp": "About App", "iosSettingsCurrentLanguage": "English", "iosSettingsLightTheme": "Light", "iosSettingsDarkTheme": "Dark", "iosSettingsSystemTheme": "System", "iosSettingsSelectLanguage": "Select Language", "iosSettingsSimplifiedChinese": "简体中文", "iosSettingsTraditionalChinese": "繁體中文", "iosSettingsEnglish": "English", "iosSettingsLanguageSelected": "Language selected: {language}", "iosSettingsSelectAppearance": "Select Appearance", "iosSettingsPrivacyContent": "We value your privacy. All data processing is done locally and will not be uploaded to servers.", "iosSettingsPrivacyManage": "You can manage your data in settings at any time.", "iosSettingsUnderstand": "Understand", "iosSettingsDataManagementTitle": "Data Management", "iosSettingsSelectOperation": "Select operation to perform", "iosSettingsExportData": "Export Data", "iosSettingsImportData": "Import Data", "iosSettingsCreateBackup": "Create Backup", "iosSettingsRateAppTitle": "Rate App", "iosSettingsRateAppMessage": "Do you like this app? Please rate us on the App Store!", "iosSettingsLater": "Later", "iosSettingsRateNow": "Rate Now", "iosSettingsCannotOpenAppStore": "Cannot open App Store", "iosSettingsAppStoreError": "Error opening App Store", "iosSettingsVersion": "Version: {version}", "iosSettingsAppDescription": "A powerful content management tool to help you create and manage various formats of content more efficiently.", "iosSettingsCopyright": "© 2023-2024 ContentPal Team", "iosSettingsClose": "Close", "iosSettingsOK": "OK", "iosSettingsError": "Error", "iosSettingsDataExportInDevelopment": "Data export feature is in development...", "iosSettingsDataImportInDevelopment": "Data import feature is in development...", "iosSettingsBackupInDevelopment": "Backup feature is in development...", "helpCenterTitle": "Help Center", "helpCenterNeedHelp": "Need Help?", "helpCenterDescription": "Find answers to common questions or contact us for support", "helpCenterContactSupport": "Contact Support", "helpCenterUserManual": "User Manual", "helpCenterGettingStarted": "Getting Started", "helpCenterGettingStartedContent": "Welcome to 内容君! You can select feature modules from the home page, such as text cards, Markdown editing, PDF processing, etc.", "helpCenterTextCards": "Text Cards", "helpCenterTextCardsContent": "Text cards feature helps you convert text content into beautiful card images, supporting multiple templates and style customization.", "helpCenterMarkdownEditing": "Markdown Editing", "helpCenterMarkdownEditingContent": "Markdown editor supports real-time preview, multiple themes, export to HTML/PDF and other features, making your document writing more efficient.", "helpCenterTrafficGuideGeneration": "Traffic Guide Generation", "helpCenterTrafficGuideGenerationContent": "Traffic guide generation feature can create eye-catching marketing images, supporting anti-theft features like interference and watermarks.", "helpCenterVoiceFeatures": "Voice Features", "helpCenterVoiceFeaturesContent": "Voice features include recording, transcription, text-to-speech, etc., supporting multiple languages and high-quality voice processing.", "helpCenterPDFProcessing": "PDF Processing", "helpCenterPDFProcessingContent": "PDF processing features support viewing, annotation, security settings, etc., allowing you to better manage PDF documents.", "helpCenterDataSyncBackup": "Data Sync & Backup", "helpCenterDataSyncBackupContent": "Your data is automatically saved locally. It is recommended to regularly use the export feature to backup important content.", "helpCenterPrivacySecurity": "Privacy & Security", "helpCenterPrivacySecurityContent": "We value your privacy, all data processing is done locally and will not be uploaded to servers.", "helpCenterSearchHelp": "Search Help", "helpCenterSearchPlaceholder": "Enter keywords to search...", "helpCenterSearchCancel": "Cancel", "helpCenterSearch": "Search", "helpCenterSupportRequestSubject": "ContentPal Support Request", "helpCenterSupportRequestBody": "Please describe the issue you encountered...", "helpCenterCannotOpenEmailApp": "Cannot open email app", "helpCenterEmailError": "Error sending email", "helpCenterCannotOpenManual": "Cannot open user manual", "helpCenterManualError": "Error opening user manual", "feedbackTitle": "<PERSON><PERSON><PERSON>", "feedbackSubtitle": "Your opinion matters", "feedbackDescription": "Tell us your thoughts and help us improve the app", "feedbackType": "Feedback Type", "feedbackTitleLabel": "Title", "feedbackDetailedDescription": "Detailed Description", "feedbackContactEmail": "Contact Email (Optional)", "feedbackIncludeSystemInfo": "Include System Info", "feedbackIncludeSystemInfoDesc": "Help us better diagnose issues", "feedbackSubmit": "Submit <PERSON>", "feedbackBugReport": "Bug Report", "feedbackBugReportDesc": "Report errors or anomalies in the app", "feedbackFeatureSuggestion": "Feature Suggestion", "feedbackFeatureSuggestionDesc": "Suggest new features or improvements", "feedbackComplaint": "<PERSON><PERSON><PERSON><PERSON>", "feedbackComplaintDesc": "Complaints or issues with the app", "feedbackPraise": "<PERSON>raise", "feedbackPraiseDesc": "Praise or positive feedback for the app", "feedbackTitleHint": "Please briefly describe your feedback", "feedbackTitleRequired": "Please enter feedback title", "feedbackDescriptionHint": "Please describe your issue, suggestion, or thoughts in detail...", "feedbackDescriptionRequired": "Please enter detailed description", "feedbackDescriptionTooShort": "Description must be at least 10 characters", "feedbackEmailHint": "<EMAIL>", "feedbackEmailInvalid": "Please enter a valid email address", "feedbackSubmitting": "Submitting...", "feedbackSubmissionError": "Error submitting feedback: {error}", "feedbackSubmissionSuccessTitle": "<PERSON><PERSON><PERSON> Submitted Successfully", "feedbackSubmissionSuccessMessage": "Thank you for your feedback! We will carefully consider your suggestions.", "feedbackConfirm": "Confirm", "feedbackSystemInfoFailed": "Failed to get system information", "feedbackAppVersion": "App Version: {version}", "feedbackBuildNumber": "Build Number: {buildNumber}", "feedbackDevice": "Device: {device}", "feedbackSystemVersion": "System Version: {version}", "feedbackDeviceModel": "Device Model: {model}", "feedbackManufacturer": "Manufacturer: {manufacturer}", "feedbackEmailSubject": "内容君 - {feedbackType}", "feedbackCannotOpenEmailApp": "Cannot open email app", "textCardNoCardsYet": "No cards yet", "textCardTitle": "Title", "textCardContent": "Content", "textCardNeedPhotoPermission": "Photo album permission is required to save images, please enable permission in settings", "textCardScreenshotFailed": "Screenshot failed, please try again", "textCardPermissionDenied": "Permission denied, please enable photo album permission in settings", "textCardExportCard": "Export Card", "textCardExportDocument": "Export Document", "textCardPreview": "Preview", "textCardExporting": "Exporting...", "textCardStartExport": "Start Export", "textCardExportSize": "Export Size", "textCardTargetPlatform": "Target Platform", "textCardFileFormat": "File Format", "textCardWatermarkSettings": "Watermark Settings", "textCardAddWatermarkSubtitle": "Add app watermark to image corner", "textCardCustomWatermarkText": "Custom Watermark Text", "textCardCustomWatermarkHint": "Leave empty to use default watermark", "textCardAdvancedOptions": "Advanced Options", "textCardIncludeTitleSubtitle": "Show title in exported image", "textCardIncludeTimestampSubtitle": "Show creation time in image", "textCardMore": "More", "textCardPart": "Part", "textCardSection": "Section", "textCardSelectionInstructions": "Select any range of text below to apply styles to the selected content", "textCardExportOptions": "Export Options", "textCardSaveToGallery": "Save to Gallery", "textCardExportSingleCard": "Will export 1 card image", "textCardExportMultipleCards": "Will export", "textCardImages": "card images", "textCardCard": "Card", "textCardCardNumber": "", "textCardShareFromApp": "From 内容君", "textCardShareFailed": "Share failed", "textCardSavedToGallery": "Saved", "textCardCardsToGallery": "cards to gallery", "textCardCreateDocument": "Create Document", "textCardTextEditMode": "Text Edit Mode", "textCardPreviewMode": "Preview Mode", "textCardTotalCards": "Total", "textCardCards": "cards", "textCardUseSeparator": "Use", "textCardSeparatorHint": "to separate cards", "textCardDocumentTitle": "Document Title", "textCardDocumentTitleHint": "Give this set of cards a title...", "textCardDocumentContentHint": "Enter or paste long text...\n\n💡 Tips:\n• Click \"Insert Separator\" where you want to split\n• First line will be auto-recognized as title if it looks like one\n• Click \"Preview\" in top right to see split effect", "textCardGoBackToEditMode": "Go back to edit mode and add separators to create cards", "textCardListView": "List View", "textCardGridView": "Grid View", "textCardTemplate": "Template", "textCardNoCardsInDocument": "No cards in document", "textCardEditDocumentToAddCards": "Edit document to add cards", "textCardDaysAgo": "days ago", "textCardHoursAgo": "hours ago", "textCardMinutesAgo": "minutes ago", "textCardJustNow": "just now", "textCardPureTextCustomRendering": "Pure Text Custom Rendering", "textCardRenderContentToCards": "Render your content into beautiful cards", "textCardDesignDescription": "This is a separated design: simple editor for content editing and splitting, powerful visual renderer for style customization and final display.", "textCardSimpleEdit": "Simple Edit", "textCardSimpleEditDesc": "Focus on pure text editing and content splitting, no complex format interference", "textCardVisualRendering": "Visual Rendering", "textCardVisualRenderingDesc": "WYSIWYG style customization, select text to directly modify styles", "textCardSmartRecognition": "Smart Recognition", "textCardSmartRecognitionDesc": "Automatically recognize content types like headings, lists, quotes and render beautifully", "textCardExportShare": "Export & Share", "textCardExportShareDesc": "Support single card and batch export, easily share beautiful content", "textCardCoreFeatures": "Core Features", "textCardMarkdownTip": "Tip: Supports Markdown format text input, including headings, lists, quotes, etc.", "textCardStartCreating": "Start Creating", "textCardClickToEditContent": "Click to edit content", "textCardsLightCategory": "Light", "textCardsDarkCategory": "ダーク", "textCardsNatureCategory": "ネイチャー", "textCardsWarmCategory": "ウォーム", "textCardsTechCategory": "テック", "textCardsElegantCategory": "エレガント", "textCardsVintageCategory": "ヴィンテージ", "textCardsPreviewEffect": "プレビュー効果", "textCardCreateBeautifulCard": "Create Beautiful Card", "textCardsSelectTextToModifyStyle": "Select text to modify font, color and size", "textCardsSelectTemplate": "Select Template", "textCardsTemplateGallery": "テンプレートギャラリー", "contentDefaultsTitle": "デフォルトのサンプルコンテンツ", "markdownDefaultSampleTitle": "Markdown デフォルトサンプル", "markdownDefaultSampleDesc": "Markdown に入るとサンプルを自動入力", "textCardsDefaultSampleTitle": "テキストカード デフォルトサンプル", "textCardsDefaultSampleDesc": "テキストカードに入るとサンプルを自動入力", "svgBuiltInPresetTitle": "SVG 内蔵プリセット", "svgBuiltInPresetDesc": "ドキュメントがない場合は内蔵 SVG プリセットを表示", "htmlDefaultSampleTitle": "HTML デフォルトサンプル", "htmlDefaultSampleDesc": "ドキュメントがない場合は内蔵 HTML サンプルを読み込み", "transformerExampleInputTitle": "テキスト変換 サンプル入力", "transformerExampleInputDesc": "テキスト変換に入るとサンプル入力を自動入力", "privacyPolicyTitle": "プライバシーポリシー", "privacyPolicySubtitle": "プライバシーポリシーを読む", "openSourceLicensesTitle": "オープンソースライセンス", "openSourceLicensesSubtitle": "サードパーティのライセンスを表示", "subscriptionUpgradeTitle": "プレミアムにアップグレード", "subscriptionUpgradeSubtitle": "すべての高度な機能を解放して、AI 体験を向上", "subscriptionChoosePlan": "購読プランを選択", "subscriptionDiscountSavePercent": "{percent}% 割引", "subscriptionLoadingPrice": "価格読み込み中…", "subscriptionEquivalentToPerMonth": "相当 {price}", "subscriptionIncludedFeatures": "含まれる機能", "subscriptionSubscribeNowWithPrice": "今すぐ購読 {price}", "subscriptionAgreementPrefix": "購読すると、次に同意したものとみなされます：", "termsOfService": "利用規約", "privacyPolicy": "プライバシーポリシー", "subscriptionAgreementSuffix": "。購読は自動更新され、いつでも解約できます。", "subscriptionDevModeNotice": "開発モード：現在は購読機能にモックデータを使用しています。App Store Connect 承認後に本番データが有効になります", "diagnosticsTitle": "診断結果", "diagnosticsClose": "閉じる", "subscriptionDiagnosticsButton": "購入サービスを診断", "restoreCompletedTitle": "復元完了", "restoreFailedTitle": "復元失敗", "restoreCompletedMessage": "購入の復元が完了しました。", "restoreFailedMessageWithError": "復元中にエラーが発生しました：{error}", "andText": "と", "subscriptionPlanMonthlyName": "ContentPal プレミアム（月額）", "subscriptionPlanMonthlyDesc": "すべての高度なコンテンツ処理機能を解放", "subscriptionPlanYearlyName": "ContentPal プレミアム（年額）", "subscriptionPlanYearlyDesc": "年間サブスクリプションの方が費用対効果が高い", "subscriptionPlanLifetimeName": "ContentPal プレミアム（買い切り）", "subscriptionPlanLifetimeDesc": "一度の購入で生涯アクセス", "subscriptionPlanFreeName": "無料版", "subscriptionPlanFreeDesc": "基本的なコンテンツ処理機能", "subscriptionFeatureUnlimitedExportsName": "無制限エクスポート", "subscriptionFeatureUnlimitedExportsDesc": "処理済みコンテンツを制限なく書き出し", "subscriptionFeatureBatchProcessingName": "バッチ処理", "subscriptionFeatureBatchProcessingDesc": "複数ファイルの一括処理に対応", "subscriptionFeatureAdvancedToolsName": "高度なツール", "subscriptionFeatureAdvancedToolsDesc": "高度な編集・処理ツールにアクセス", "subscriptionFeatureNoWatermarkName": "透かし無し", "subscriptionFeatureNoWatermarkDesc": "書き出しに透かしを含めない", "subscriptionFeaturePrioritySupportName": "優先サポート", "subscriptionFeaturePrioritySupportDesc": "優先的なカスタマーサポートを提供", "subscriptionFeatureFutureUpdatesName": "将来のアップデート", "subscriptionFeatureFutureUpdatesDesc": "今後のすべての機能アップデートを受け取る", "subscriptionFeatureBasicProcessingName": "基本処理", "subscriptionFeatureBasicProcessingDesc": "基本的なコンテンツ処理機能", "subscriptionFeatureWatermarkedExportsName": "透かし付きエクスポート", "subscriptionFeatureWatermarkedExportsDesc": "透かしを含んだ書き出し", "subscriptionPeriodPerMonthSuffix": "/月", "subscriptionPeriodPerYearSuffix": "/年", "subscriptionPeriodLifetime": "生涯", "subscriptionPeriodFree": "無料", "purchaseSuccessMessage": "購入が完了しました。サブスクリプションが有効になりました。ありがとうございます！", "currentPlan": "現在のプラン", "expired": "期限切れ", "daysUntilExpiry": "{days}日後に期限切れ", "subscriptionExpired": "期限切れ", "textCardsBrowseTemplates": "テンプレートを閲覧", "textCardsBeautifulTemplates": "個の美しいテンプレート", "textCardsAllCategories": "すべて", "blockMarkdown": "分割Markdown", "cardCollection": "カードコレクション", "textCardsBusinessCategory": "ビジネス", "textCardsAcademicCategory": "アカデミック", "textCardsCreativeCategory": "クリエイティブ", "textCardsMinimalCategory": "ミニマル", "textCardsModernCategory": "モダン", "textCardExportSettings": "Export Settings", "textCardImageSize": "Image Size", "textCardImageRatio": "Image Ratio", "smartTextSplitter": "Smart Text Splitter", "smartTextSplitterSubtitle": "Split long text into multiple cards intelligently", "characterCount": "Character count: {count}", "splitConfig": "Split Configuration", "splitConfigDescription": "Choose split mode and related parameters", "splitMode": "Split Mode", "customSeparator": "Custom Separator", "customSeparatorHint": "Enter separator, such as: ---", "advancedOptions": "Advanced Options", "autoDetectTitles": "Auto-detect Titles", "autoDetectTitlesDescription": "Intelligently identify title content in text", "preserveFormatting": "Preserve Formatting", "preserveFormattingDescription": "Preserve original text Markdown formatting", "smartMerge": "Smart Merge", "smartMergeDescription": "Automatically merge short paragraphs", "maxLength": "Max Length", "maxLengthDescription": "Maximum characters per card", "splitPreview": "Split Preview", "totalCards": "Total {count} cards", "splitPreviewDescription": "Preview split results, can edit, merge or delete cards", "deselectAll": "Deselect All", "deleteSelected": "Delete Selected", "noSplitResults": "No Split Results", "noSplitResultsDescription": "Please return to previous step to check input text and configuration", "previousStep": "Previous", "nextStep": "Next", "startSplitting": "Start Splitting", "createCards": "Create Cards", "inputTextHint": "Enter or paste text content here...", "titleOptional": "Title (Optional)", "exportSettings": "Export Settings", "confirmExport": "Confirm Export", "previewInfo": "Preview Info", "dimensions": "Dimensions", "ratio": "<PERSON><PERSON>", "qualityPercent": "Quality", "watermarkStatus": "Watermark", "include": "Include", "notInclude": "Not Include", "pixels": "pixels", "pdfCreatedTime": "Created", "pdfModifiedTime": "Modified", "pdfPermissionsSuccess": "Permissions set successfully", "pdfPermissionsFailed": "Permissions set failed", "pdfSettingsFailed": "Settings failed: {error}", "pdfDocumentInformation": "Document Information", "pdfEncryptedMessage": "This PDF is encrypted, please enter password to decrypt", "pdfSecurityEncrypted": "Encrypted", "pdfSecurityReadOnly": "Read Only", "pdfSecurityRestricted": "Restricted", "pdfSecurityOpen": "Open", "pdfAllowCopying": "Allow Copying", "pdfMultipleFormats": "Multiple Formats", "pdfMultiDeviceSync": "Multi-device Sync", "pdfProtectYourDocuments": "Protect your important documents", "pdfPasswordProtectionDesc": "Set user and owner passwords for PDF documents to ensure document security", "pdfPermissionControlDesc": "Fine-grained control over document printing, copying, editing and other permissions", "pdfEncryptionAlgorithm": "Encryption Algorithm", "pdfEncryptionAlgorithmDesc": "Adopt industry-standard AES encryption algorithm to ensure document security", "pdfImportToStart": "Import PDF to Start", "pdfUsageTips": "Usage Tips", "htmlUntitled": "Untitled HTML", "htmlNewDocumentTitle": "New HTML Document", "htmlCopy": "Copy", "htmlRenderError": "Cannot render HTML content", "voiceHomeTitle": "Voice Assistant", "voiceHomeSubtitle": "Record ideas, convert to text, smart reading", "voiceUsageStats": "Usage Statistics", "voiceRecordingCount": "Recording Count", "voiceRecordingsUnit": "recordings", "voiceTotalDuration": "Total Duration", "voiceCumulativeDuration": "Cumulative Duration", "voiceQuickActions": "Quick Actions", "voiceRecordNewVoice": "Record new voice", "voiceTextToSpeech": "Text to Speech", "voiceConvertTextToVoice": "Convert text to voice", "voicePowerfulFeatures": "Powerful Features", "voiceSmartTranscription": "Smart Transcription", "voiceSmartTranscriptionDesc": "Automatically convert voice to text", "voiceAudioAdjustment": "Audio Adjustment", "voiceAudioAdjustmentDesc": "Adjust speed, pitch and volume", "voicePlaylist": "Playlist", "voicePlaylistDesc": "Manage and play multiple audio files", "voiceCloudSync": "Cloud Sync", "voiceCloudSyncDesc": "Sync your recordings across devices", "voiceRecentRecordings": "Recent Recordings", "voiceViewAll": "View All", "voiceNoRecordingsYet": "No recordings yet", "voiceStartFirstRecording": "Click the button below to start your first recording", "voiceRecordDetailTitle": "Voice Details", "voiceSaveAllChanges": "Save all changes", "voiceRecordingFileMayBeCorrupted": "Recording file may be corrupted, cannot play", "voiceAudioFileNotExistOrCorrupted": "Audio file does not exist or is corrupted", "voiceFilePath": "File path: {path}", "voiceRecheck": "Recheck", "voiceFileStatusRechecked": "File status rechecked: {status}{corrupted}", "voiceFileExists": "file exists", "voiceFileNotExists": "file does not exist", "voiceFileButCorrupted": ", but file may be corrupted", "voiceVoiceTranscription": "Voice Transcription", "voiceReadText": "Read Text", "voiceNoTranscriptionText": "No transcription text yet", "voiceSaveTranscriptionText": "Save Transcription Text", "voiceCreateTime": "Create Time: {time}", "voicePlaying": "Playing...", "voicePaused": "Paused", "voiceAudioFileNotExist": "Audio file does not exist, cannot play", "voiceAudioDurationAbnormal": "Audio duration abnormal, may not play properly", "voiceLoadAudioFailed": "Failed to load audio: {error}", "voicePlayFailed": "Play failed: {error}", "voiceTitleSaved": "Title saved", "voiceTranscriptionTextSaved": "Transcription text saved", "voiceTtsPlayerTitle": "Text to Speech", "voiceInputTextToRead": "Input text to read", "voiceInputTextHint": "Input text to read, click \"Add\" button to add to playlist", "voiceAddToPlaylist": "Add to playlist", "voiceAddedToPlaylist": "Added to playlist", "voiceTtsSettings": "TTS Settings", "voiceSpeechRate": "Speech Rate:", "voicePitch": "Pitch:", "voiceVolume": "Volume:", "voiceLanguage": "Language:", "voicePlaylistTitle": "Playlist", "voiceStop": "Stop", "voicePlaylistEmpty": "Playlist is empty", "voiceTranscriptionTitle": "Smart Transcription", "voiceRealtimeTranscription": "Realtime Transcription", "voiceFileTranscription": "File Transcription", "voiceBatchTranscription": "Batch Transcription", "voiceSelectAudioFile": "Select audio file for transcription", "voiceFileSelected": "File Selected", "voiceSelectFile": "Select File", "voiceReselectFile": "Reselect File", "voiceTranscriptionResult": "Transcription Result", "voiceBatchTranscriptionFeature": "Batch Transcription Feature", "voiceSelectMultipleFiles": "Select multiple files for batch transcription", "voiceSelectMultipleFilesBtn": "Select Multiple Files", "voiceBatchProcessingProgress": "Batch Processing Progress", "voiceBatchTranscriptionInDev": "Batch transcription feature in development...", "voiceReadyToStart": "Ready to start", "voiceTranscribing": "Transcribing...", "voiceClickToStartRealtime": "Click button below to start realtime transcription", "voiceExport": "Export", "voiceShare": "Share", "voiceNoTranscriptionContent": "No transcription content to export", "voiceNoTranscriptionContentToShare": "No transcription content to share", "voiceExportFeatureInDev": "Export feature in development...", "voiceShareFeatureInDev": "Share feature in development...", "voiceTranscriptionSettings": "Transcription Settings", "voiceEnablePunctuation": "Enable Punctuation", "voiceAutoAddPunctuation": "Automatically add punctuation", "voiceSpeakerDetection": "Speaker Detection", "voiceDetectDifferentSpeakers": "Detect different speakers", "voiceConfidenceThreshold": "Confidence Threshold", "voicePermissionRequiredMessage": "Voice transcription feature requires microphone permission. Please enable microphone access in settings.", "voiceRecordingComplete": "Recording complete", "voiceRecordingFailedRetry": "Recording failed, please retry", "voiceSelectFileFailed": "Failed to select file: {error}", "voiceProcessAudioFileFailed": "Failed to process audio file: {error}", "voiceSelectBatchFilesFailed": "Failed to select batch files: {error}", "voiceFilesSelected": "Selected {count} files", "voiceNoTranscriptionContentToSave": "No transcription content to save", "voiceSaveTranscriptionResult": "Save Transcription Result", "voiceTranscriptionContentPreview": "Transcription content preview:", "voiceTranscriptionResultSaved": "Transcription result saved", "voiceSaveFailed": "Save failed: {error}", "voiceTranscriptionFailedRetry": "Transcription failed, please retry", "voiceSmartTranscriptionPageTitle": "Smart Transcription", "voiceInitializationFailedCheckPermission": "Initialization failed, please check microphone permission", "voiceInitializationException": "Initialization exception: {error}", "voiceServiceInitializationFailed": "Service initialization failed", "voiceStartTranscriptionFailed": "Failed to start transcription", "voiceTranscriptionIdle": "Ready", "voiceTranscriptionInProgress": "Transcribing...", "voiceTranscriptionCompleted": "Transcription Completed", "voiceTranscriptionError": "Transcription Error", "voiceLanguageSelector": "Language:", "voiceTranscriptionResultTitle": "Transcription Result", "voiceClickToStartTranscription": "Click start button to begin transcription...", "voiceStopTranscription": "Stop Transcription", "voiceClear": "Clear", "voiceIosPermissionTestTitle": "iOS Permission Test", "voiceDirectIosPermissionTest": "Direct iOS Permission Test", "voicePageLoaded": "Page loaded", "voiceInitialMicPermissionStatus": "Initial microphone permission status: {status}", "voiceInitialSpeechPermissionStatus": "Initial speech recognition permission status: {status}", "voiceNonIosPlatform": "Non-iOS platform, not checking permissions", "voiceRequestMicPermission": "Request Microphone Permission", "voiceMicPermissionStatus": "Microphone permission status: {status}", "voiceRequestSpeechPermission": "Request Speech Recognition Permission", "voiceSpeechPermissionStatus": "Speech recognition permission status: {status}", "voiceTestRecording": "Test Recording", "voiceTestRecordingFunction": "Test recording function...", "voiceRecorderInstanceCreated": "Recorder instance created", "voiceRecorderInitialized": "Recorder initialized", "voiceRecorderTestComplete": "Recorder test complete", "voiceRecorderClosed": "Recorder closed", "voiceRecorderError": "Recorder error: {error}", "voiceTestSpeechRecognition": "Test Speech Recognition", "voiceTestSpeechRecognitionFunction": "Test speech recognition...", "voiceSpeechRecognitionError": "Speech recognition error: {error}", "voiceSpeechRecognitionStatus": "Speech recognition status: {status}", "voiceSpeechRecognitionInit": "Speech recognition init: {success}", "voiceStartListening": "Start listening...", "voiceRecognitionResult": "Recognition result: {result}", "voiceStopListening": "Stop listening", "voiceSpeechRecognitionTestError": "Speech recognition test error: {error}", "voiceOpenAppSettings": "Open App Settings", "voiceOperationLog": "Operation Log:", "voiceIosPageLoaded": "Page loaded", "voiceIosInitialMicrophonePermission": "Initial microphone permission status", "voiceIosInitialSpeechPermission": "Initial speech recognition permission status", "voiceIosNonIosPlatform": "Non-iOS platform, not checking permissions", "voiceIosRequestMicrophonePermission": "Requesting microphone permission...", "voiceIosMicrophonePermissionStatus": "Microphone permission status", "voiceIosRequestSpeechPermission": "Requesting speech recognition permission...", "voiceIosSpeechPermissionStatus": "Speech recognition permission status", "voiceIosTestRecordingFunction": "Testing recording function...", "voiceIosRecorderInstanceCreated": "Recorder instance created", "voiceIosRecorderInitialized": "Recorder initialized", "voiceIosRecorderTestCompleted": "Recorder test completed", "voiceIosRecorderClosed": "Recorder closed", "voiceIosRecorderError": "Recorder error", "voiceIosTestSpeechRecognition": "Testing speech recognition...", "voiceIosSpeechRecognitionError": "Speech recognition error", "voiceIosSpeechRecognitionStatus": "Speech recognition status", "voiceIosSpeechRecognitionInitialization": "Speech recognition initialization", "voiceIosSuccess": "Success", "voiceIosFailed": "Failed", "voiceIosStartListening": "Start listening...", "voiceIosRecognitionResult": "Recognition result", "voiceIosStopListening": "Stop listening", "voiceIosSpeechRecognitionTestError": "Speech recognition test error", "voiceIosOpenAppSettings": "Open app settings", "voiceIosPermissionTest": "iOS Permission Test", "voiceIosDirectPermissionTest": "Direct iOS Permission Test", "voiceIosOperationLogs": "Operation Logs", "voiceOK": "OK", "voiceMicrophonePermissionRequired": "Voice transcription requires microphone permission. Please allow microphone access in settings.", "voiceLanguageChineseSimplified": "中文（简体）", "voiceLanguageChineseTraditional": "中文（繁体）", "voiceLanguageEnglish": "English (US)", "voiceLanguageJapanese": "日本語", "voiceLanguageKorean": "한국어", "trafficGuideVisibleWatermark": "可視透かし", "trafficGuideWatermarkType": "透かしの種類", "trafficGuideShowPreview": "プレビューを表示", "trafficGuideHidePreview": "プレビューを非表示", "trafficGuideProcessSuccess": "処理が成功しました", "trafficGuideDetectedWatermark": "透かしを検出", "@trafficGuideDetectedWatermark": {"description": "検出された透かし情報のラベル"}, "trafficGuideUnknownWatermark": "不明な透かし", "@trafficGuideUnknownWatermark": {"description": "不明な透かしタイプのラベル"}, "trafficGuideNoWatermarkDetected": "透かしは検出されませんでした", "@trafficGuideNoWatermarkDetected": {"description": "透かしが見つからない場合のメッセージ"}, "trafficGuideProcessedText": "処理されたテキスト", "@trafficGuideProcessedText": {"description": "処理されたテキスト出力のラベル"}, "trafficGuideInvisibleWatermarkInfo": "これは特殊なUnicode文字を使用して不可視の透かしを追加します。読者には見えませんが、このツールで検出できます。", "@trafficGuideInvisibleWatermarkInfo": {"description": "不可視透かし機能に関する情報"}, "trafficGuideWatermarkRemovedSuccess": "透かしの削除に成功しました！", "@trafficGuideWatermarkRemovedSuccess": {"description": "透かし削除成功時のメッセージ"}, "trafficGuideWatermarkAddedSuccess": "透かしの追加に成功しました！", "@trafficGuideWatermarkAddedSuccess": {"description": "透かし追加成功時のメッセージ"}, "textCardDimensions": "Dimensions", "textCardInclude": "Include", "textCardNotInclude": "Not Include", "exportSocialWeChatMoments": "WeChatモーメンツ", "exportSocialWeibo": "Weibo 画像", "exportSocialXiaohongshu": "小紅書", "exportSocialInstagram": "Instagram", "exportSocialTwitter": "Twitter", "exportWidthLabel": "幅", "exportHeightLabel": "高さ", "exportOptimizeForSocial": "ソーシャルメディア最適化", "exportOptimizeForSocialSubtitle": "ソーシャルプラットフォーム向けにサイズを最適化", "exportSocialPlatformSizes": "ソーシャルプラットフォームのサイズ", "exportSizeSmall": "小 (400×300)", "exportSizeMedium": "中 (800×600)", "exportSizeLarge": "大 (1200×900)", "exportSizeCustom": "カスタム", "general": "一般", "@general": {"description": "一般設定タイトル"}, "appearanceAndBrightness": "表示と明るさ", "@appearanceAndBrightness": {"description": "表示と明るさ設定タイトル"}, "privacyAndSecurity": "プライバシーとセキュリティ", "@privacyAndSecurity": {"description": "プライバシーとセキュリティ設定タイトル"}, "storage": "ストレージ", "@storage": {"description": "ストレージ設定タイトル"}, "support": "サポート", "@support": {"description": "サポート設定タイトル"}, "languageAndRegion": "言語と地域", "@languageAndRegion": {"description": "言語と地域設定項目"}, "privacySettings": "プライバシー設定", "@privacySettings": {"description": "プライバシー設定項目"}, "storageManagement": "ストレージ管理", "@storageManagement": {"description": "ストレージ管理項目"}, "viewStorageUsage": "ストレージ使用状況を表示", "@viewStorageUsage": {"description": "ストレージ使用状況の説明"}, "dataImportExport": "データのインポートとエクスポート", "@dataImportExport": {"description": "データインポート・エクスポート項目"}, "backupAndRestoreData": "データのバックアップと復元", "@backupAndRestoreData": {"description": "データのバックアップと復元の説明"}, "helpCenter": "ヘルプセンター", "@helpCenter": {"description": "ヘルプセンター項目"}, "feedback": "フィードバック", "@feedback": {"description": "フィードバック項目"}, "rateApp": "アプリを評価", "@rateApp": {"description": "アプリ評価項目"}, "aboutApp": "アプリについて", "@aboutApp": {"description": "アプリについて項目"}, "dataManagement": "データ管理", "@dataManagement": {"description": "データ管理タイトル"}, "selectOperation": "実行する操作を選択", "@selectOperation": {"description": "操作選択プロンプト"}, "exportData": "データをエクスポート", "@exportData": {"description": "データエクスポート操作"}, "importData": "データをインポート", "@importData": {"description": "データインポート操作"}, "createBackup": "バックアップを作成", "@createBackup": {"description": "バックアップ作成操作"}, "dataExportInProgress": "データエクスポート機能は開発中です...", "@dataExportInProgress": {"description": "データエクスポート進行中メッセージ"}, "dataImportInProgress": "データインポート機能は開発中です...", "@dataImportInProgress": {"description": "データインポート進行中メッセージ"}, "backupInProgress": "バックアップ機能は開発中です...", "@backupInProgress": {"description": "バックアップ進行中メッセージ"}, "doYouLikeThisApp": "このアプリは気に入りましたか？App Storeで評価してください！", "@doYouLikeThisApp": {"description": "アプリ評価プロンプト"}, "later": "後で", "@later": {"description": "後でボタン"}, "goToRate": "評価する", "@goToRate": {"description": "評価に行くボタン"}, "cannotOpenAppStore": "App Storeを開けません", "@cannotOpenAppStore": {"description": "App Storeを開けないエラー"}, "errorOpeningAppStore": "App Storeを開く際にエラーが発生しました", "@errorOpeningAppStore": {"description": "App Storeを開くエラー"}, "@close": {"description": "閉じるボタン"}, "@confirm": {"description": "確認ボタン"}, "understand": "理解しました", "@understand": {"description": "理解ボタン"}, "weValueYourPrivacy": "私たちはあなたのプライバシーを重視しています。すべてのデータ処理はローカルで行われ、サーバーにアップロードされることはありません。", "@weValueYourPrivacy": {"description": "プライバシー声明"}, "manageDataAnytime": "いつでも設定でデータを管理できます。", "@manageDataAnytime": {"description": "データ管理声明"}, "languageSelected": "言語が選択されました：{language}", "@languageSelected": {"description": "言語選択通知"}, "light": "ライト", "@light": {"description": "ライトテーマ"}, "dark": "ダーク", "@dark": {"description": "ダークテーマ"}, "selectLanguage": "言語を選択", "@selectLanguage": {"description": "言語選択タイトル"}, "selectAppearance": "外観を選択", "@selectAppearance": {"description": "外観選択タイトル"}, "traditionalChinese": "繁体字中国語", "@traditionalChinese": {"description": "繁体字中国語"}, "simplifiedChinese": "簡体字中国語", "@simplifiedChinese": {"description": "簡体字中国語"}, "contentSaveButtonFavorite": "お気に入り", "@contentSaveButtonFavorite": {"description": "コンテンツマネージャー画面の保存/お気に入りボタンテキスト"}, "textTransformerSelectMode": "変換モードを選択", "@textTransformerSelectMode": {"description": "テンプレート選択セクションのタイトル"}, "textTransformerInputText": "入力テキスト", "@textTransformerInputText": {"description": "入力テキストセクションのラベル"}, "textTransformerOutputResult": "変換結果", "@textTransformerOutputResult": {"description": "出力結果セクションのラベル"}, "textTransformerHint": "変換するテキストをここに入力...", "@textTransformerHint": {"description": "入力フィールドのヒントテキスト"}, "textTransformerOutputHint": "変換されたテキストがここに表示されます...", "@textTransformerOutputHint": {"description": "出力フィールドのヒントテキスト"}, "textTransformerCharacters": "文字", "@textTransformerCharacters": {"description": "文字数のテキスト"}, "textTransformerTransform": "効果を切り替え", "@textTransformerTransform": {"description": "変換ボタンのテキスト"}, "textTransformerTransforming": "変換中...", "@textTransformerTransforming": {"description": "変換中に表示されるテキスト"}, "textTransformerClearAll": "すべてクリア", "@textTransformerClearAll": {"description": "すべてクリアボタンのツールチップ"}, "textTransformerCopyResult": "結果をコピー", "@textTransformerCopyResult": {"description": "結果をコピーボタンのツールチップ"}, "textTransformerCopied": "クリップボードにコピーしました", "@textTransformerCopied": {"description": "テキストがコピーされたときに表示されるメッセージ"}, "textTransformerTemplateEmojiName": "絵文字変換", "@textTransformerTemplateEmojiName": {"description": "絵文字テンプレートの名前"}, "textTransformerTemplateEmojiDesc": "テキストを特殊な絵文字に変換", "@textTransformerTemplateEmojiDesc": {"description": "絵文字テンプレートの説明"}, "textTransformerTemplateFancyName": "装飾文字", "@textTransformerTemplateFancyName": {"description": "装飾文字テンプレートの名前"}, "textTransformerTemplateFancyDesc": "エレガントな装飾文字に変換", "@textTransformerTemplateFancyDesc": {"description": "装飾文字テンプレートの説明"}, "textTransformerTemplateBoldName": "太字テキスト", "@textTransformerTemplateBoldName": {"description": "太字テキストテンプレートの名前"}, "textTransformerTemplateBoldDesc": "太字のUnicode文字に変換", "@textTransformerTemplateBoldDesc": {"description": "太字テキストテンプレートの説明"}, "textTransformerTemplateDecorativeName": "装飾テキスト", "@textTransformerTemplateDecorativeName": {"description": "装飾テキストテンプレートの名前"}, "textTransformerTemplateDecorativeDesc": "装飾記号を追加", "@textTransformerTemplateDecorativeDesc": {"description": "装飾テキストテンプレートの説明"}, "textTransformerTemplateMixedName": "混合効果", "@textTransformerTemplateMixedName": {"description": "混合効果テンプレートの名前"}, "textTransformerTemplateMixedDesc": "複数の変換効果をランダムに組み合わせ", "@textTransformerTemplateMixedDesc": {"description": "混合効果テンプレートの説明"}, "textTransformerTemplateInvisibleName": "不可視文字", "@textTransformerTemplateInvisibleName": {"description": "不可視文字テンプレートの名前"}, "textTransformerTemplateInvisibleDesc": "検出を回避するために不可視文字を追加", "@textTransformerTemplateInvisibleDesc": {"description": "不可視文字テンプレートの説明"}, "textTransformerTemplateUnicodeName": "Unicodeバリアント", "@textTransformerTemplateUnicodeName": {"description": "Unicodeバリアントテンプレートの名前"}, "textTransformerTemplateUnicodeDesc": "Unicodeバリアント文字を使用", "@textTransformerTemplateUnicodeDesc": {"description": "Unicodeバリアントテンプレートの説明"}, "textTransformerEffectEmojiDesc": "数字と文字を特殊なUnicode文字に変換", "@textTransformerEffectEmojiDesc": {"description": "絵文字モードの効果説明"}, "textTransformerEffectFancyDesc": "エレガントな装飾文字に変換", "@textTransformerEffectFancyDesc": {"description": "装飾文字モードの効果説明"}, "textTransformerEffectBoldDesc": "太字のUnicode文字に変換", "@textTransformerEffectBoldDesc": {"description": "太字モードの効果説明"}, "textTransformerEffectDecorativeDesc": "装飾記号を追加", "@textTransformerEffectDecorativeDesc": {"description": "装飾モードの効果説明"}, "textTransformerEffectMixedDesc": "複数の変換効果をランダムに組み合わせ", "@textTransformerEffectMixedDesc": {"description": "混合モードの効果説明"}, "textTransformerEffectInvisibleDesc": "文字間に不可視文字を追加して検出を回避", "@textTransformerEffectInvisibleDesc": {"description": "不可視モードの効果説明"}, "textTransformerEffectUnicodeDesc": "ダイアクリティカルマークを追加して文字の外観を変更", "@textTransformerEffectUnicodeDesc": {"description": "Unicodeバリアントモードの効果説明"}, "textTransformerSample": "サンプルテキスト", "@textTransformerSample": {"description": "変換用の汎用サンプルテキスト"}, "textTransformerSampleInvisible": "機密コンテンツ検出回避テスト", "@textTransformerSampleInvisible": {"description": "不可視文字モードのサンプルテキスト"}, "textTransformerSampleUnicode": "特殊文字変換テスト", "@textTransformerSampleUnicode": {"description": "Unicodeバリアントモードのサンプルテキスト"}}