import 'package:flutter/material.dart';

import '../generated/l10n/app_localizations.dart';
import '../services/service_locator.dart';

class ContentDefaultsSettingsPage extends StatefulWidget {
  const ContentDefaultsSettingsPage({super.key});

  @override
  State<ContentDefaultsSettingsPage> createState() =>
      _ContentDefaultsSettingsPageState();
}

class _ContentDefaultsSettingsPageState
    extends State<ContentDefaultsSettingsPage> {
  bool _global = true;
  bool _markdown = true;
  bool _textCards = true;
  bool _svg = true;
  bool _html = true;
  bool _transformer = true;

  @override
  void initState() {
    super.initState();
    final s = ServiceLocator().settingsService.settings;
    _global = s.useDefaultInitialText;
    _markdown = s.useDefaultInitialTextMarkdown;
    _textCards = s.useDefaultInitialTextTextCards;
    _svg = s.useDefaultInitialTextSvg;
    _html = s.useDefaultInitialTextHtml;
    _transformer = s.useDefaultInitialTextTransformer;
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    final theme = Theme.of(context);
    return Scaffold(
      appBar: AppBar(
        title: Text(l10n.contentDefaultsTitle),
        backgroundColor: theme.colorScheme.surface,
        foregroundColor: theme.colorScheme.onSurface,
        elevation: 0,
      ),
      body: ListView(
        children: [
          _buildSectionHeader(l10n.general),
          SwitchListTile.adaptive(
            title: Text(l10n.useDefaultInitialText),
            subtitle: Text(l10n.useDefaultInitialTextDescription),
            value: _global,
            onChanged: (v) async {
              await ServiceLocator().settingsService
                  .updateUseDefaultInitialText(v);
              setState(() => _global = v);
            },
          ),
          const Divider(height: 1),

          _buildSectionHeader(l10n.contentSettings),
          SwitchListTile.adaptive(
            title: Text(l10n.markdownDefaultSampleTitle),
            subtitle: Text(l10n.markdownDefaultSampleDesc),
            value: _markdown,
            onChanged: (v) async {
              await ServiceLocator().settingsService
                  .updateUseDefaultInitialTextMarkdown(v);
              setState(() => _markdown = v);
            },
          ),
          SwitchListTile.adaptive(
            title: Text(l10n.textCardsDefaultSampleTitle),
            subtitle: Text(l10n.textCardsDefaultSampleDesc),
            value: _textCards,
            onChanged: (v) async {
              await ServiceLocator().settingsService
                  .updateUseDefaultInitialTextTextCards(v);
              setState(() => _textCards = v);
            },
          ),
          SwitchListTile.adaptive(
            title: Text(l10n.svgBuiltInPresetTitle),
            subtitle: Text(l10n.svgBuiltInPresetDesc),
            value: _svg,
            onChanged: (v) async {
              await ServiceLocator().settingsService
                  .updateUseDefaultInitialTextSvg(v);
              setState(() => _svg = v);
            },
          ),
          SwitchListTile.adaptive(
            title: Text(l10n.htmlDefaultSampleTitle),
            subtitle: Text(l10n.htmlDefaultSampleDesc),
            value: _html,
            onChanged: (v) async {
              await ServiceLocator().settingsService
                  .updateUseDefaultInitialTextHtml(v);
              setState(() => _html = v);
            },
          ),
          SwitchListTile.adaptive(
            title: Text(l10n.transformerExampleInputTitle),
            subtitle: Text(l10n.transformerExampleInputDesc),
            value: _transformer,
            onChanged: (v) async {
              await ServiceLocator().settingsService
                  .updateUseDefaultInitialTextTransformer(v);
              setState(() => _transformer = v);
            },
          ),
          const SizedBox(height: 16),
        ],
      ),
    );
  }

  Widget _buildSectionHeader(String title) {
    final theme = Theme.of(context);
    return Padding(
      padding: const EdgeInsets.fromLTRB(16, 20, 16, 8),
      child: Text(
        title,
        style: TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.w600,
          color: theme.colorScheme.primary,
        ),
      ),
    );
  }
}
