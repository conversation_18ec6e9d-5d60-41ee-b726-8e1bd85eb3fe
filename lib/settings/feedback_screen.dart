import 'dart:io';
import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:device_info_plus/device_info_plus.dart';
import 'package:package_info_plus/package_info_plus.dart';

import '../config/app_theme.dart';
import '../generated/l10n/app_localizations.dart';

class FeedbackScreen extends StatefulWidget {
  const FeedbackScreen({super.key});

  @override
  State<FeedbackScreen> createState() => _FeedbackScreenState();
}

class _FeedbackScreenState extends State<FeedbackScreen> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _contentController = TextEditingController();
  final _emailController = TextEditingController();
  
  FeedbackType _selectedType = FeedbackType.suggestion;
  bool _includeSystemInfo = true;
  bool _isSubmitting = false;
  late AppLocalizations _l10n;

  @override
  void dispose() {
    _titleController.dispose();
    _contentController.dispose();
    _emailController.dispose();
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _l10n = AppLocalizations.of(context);
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.bgLightColor,
      appBar: AppBar(
        title: const Text(
          '意见反馈',
          style: TextStyle(
            color: AppTheme.textDarkColor,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: AppTheme.bgWhiteColor,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: AppTheme.textDarkColor),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: Form(
        key: _formKey,
        child: Column(
          children: [
            // 顶部说明
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                gradient: AppTheme.purpleGradient,
                borderRadius: const BorderRadius.only(
                  bottomLeft: Radius.circular(20),
                  bottomRight: Radius.circular(20),
                ),
              ),
              child: Column(
                children: [
                  const Icon(
                    Icons.feedback,
                    size: 48,
                    color: Colors.white,
                  ),
                  const SizedBox(height: 12),
                  const Text(
                    '您的意见很重要',
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    '告诉我们您的想法，帮助我们改进应用',
                    style: TextStyle(
                      fontSize: 16,
                      color: Colors.white70,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),

            // 表单内容
            Expanded(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // 反馈类型选择
                    _buildSectionTitle('反馈类型'),
                    _buildFeedbackTypeSelector(),
                    const SizedBox(height: 20),

                    // 标题输入
                    _buildSectionTitle('标题'),
                    _buildTitleField(),
                    const SizedBox(height: 20),

                    // 详细描述
                    _buildSectionTitle('详细描述'),
                    _buildContentField(),
                    const SizedBox(height: 20),

                    // 联系邮箱
                    _buildSectionTitle('联系邮箱（可选）'),
                    _buildEmailField(),
                    const SizedBox(height: 20),

                    // 系统信息选项
                    _buildSystemInfoOption(),
                    const SizedBox(height: 30),

                    // 提交按钮
                    _buildSubmitButton(),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Text(
        title,
        style: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.bold,
          color: AppTheme.textDarkColor,
        ),
      ),
    );
  }

  Widget _buildFeedbackTypeSelector() {
    return Container(
      decoration: BoxDecoration(
        color: AppTheme.bgWhiteColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: FeedbackType.values.map((type) {
          return RadioListTile<FeedbackType>(
            title: Text(_getFeedbackTypeTitle(type)),
            subtitle: Text(_getFeedbackTypeDescription(type)),
            value: type,
            groupValue: _selectedType,
            onChanged: (value) {
              setState(() {
                _selectedType = value!;
              });
            },
          );
        }).toList(),
      ),
    );
  }

  Widget _buildTitleField() {
    return Container(
      decoration: BoxDecoration(
        color: AppTheme.bgWhiteColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TextFormField(
        controller: _titleController,
        decoration: const InputDecoration(
          hintText: '请简要描述您的反馈',
          border: OutlineInputBorder(borderSide: BorderSide.none),
          contentPadding: EdgeInsets.all(16),
        ),
        validator: (value) {
          if (value == null || value.trim().isEmpty) {
            return '请输入反馈标题';
          }
          return null;
        },
      ),
    );
  }

  Widget _buildContentField() {
    return Container(
      decoration: BoxDecoration(
        color: AppTheme.bgWhiteColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TextFormField(
        controller: _contentController,
        maxLines: 6,
        decoration: const InputDecoration(
          hintText: '请详细描述您的问题、建议或想法...',
          border: OutlineInputBorder(borderSide: BorderSide.none),
          contentPadding: EdgeInsets.all(16),
        ),
        validator: (value) {
          if (value == null || value.trim().isEmpty) {
            return '请输入详细描述';
          }
          if (value.trim().length < 10) {
            return '描述内容至少需要10个字符';
          }
          return null;
        },
      ),
    );
  }

  Widget _buildEmailField() {
    return Container(
      decoration: BoxDecoration(
        color: AppTheme.bgWhiteColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: TextFormField(
        controller: _emailController,
        keyboardType: TextInputType.emailAddress,
        decoration: const InputDecoration(
          hintText: '<EMAIL>',
          border: OutlineInputBorder(borderSide: BorderSide.none),
          contentPadding: EdgeInsets.all(16),
        ),
        validator: (value) {
          if (value != null && value.isNotEmpty) {
            if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(value)) {
              return '请输入有效的邮箱地址';
            }
          }
          return null;
        },
      ),
    );
  }

  Widget _buildSystemInfoOption() {
    return Container(
      decoration: BoxDecoration(
        color: AppTheme.bgWhiteColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: SwitchListTile(
        title: const Text('包含系统信息'),
        subtitle: const Text('帮助我们更好地诊断问题'),
        value: _includeSystemInfo,
        onChanged: (value) {
          setState(() {
            _includeSystemInfo = value;
          });
        },
      ),
    );
  }

  Widget _buildSubmitButton() {
    return SizedBox(
      width: double.infinity,
      height: 50,
      child: ElevatedButton(
        onPressed: _isSubmitting ? null : _submitFeedback,
        style: ElevatedButton.styleFrom(
          backgroundColor: AppTheme.primaryColor,
          foregroundColor: Colors.white,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(12),
          ),
        ),
        child: _isSubmitting
            ? const SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(
                  strokeWidth: 2,
                  valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                ),
              )
            : const Text(
                '提交反馈',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
      ),
    );
  }

  String _getFeedbackTypeTitle(FeedbackType type) {
    switch (type) {
      case FeedbackType.bug:
        return '错误报告';
      case FeedbackType.suggestion:
        return '功能建议';
      case FeedbackType.complaint:
        return '问题投诉';
      case FeedbackType.praise:
        return '表扬赞美';
    }
  }

  String _getFeedbackTypeDescription(FeedbackType type) {
    switch (type) {
      case FeedbackType.bug:
        return '报告应用中的错误或异常';
      case FeedbackType.suggestion:
        return '建议新功能或改进现有功能';
      case FeedbackType.complaint:
        return '对应用的不满或问题';
      case FeedbackType.praise:
        return '对应用的赞美或好评';
    }
  }

  Future<void> _submitFeedback() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() {
      _isSubmitting = true;
    });

    try {
      String systemInfo = '';
      if (_includeSystemInfo) {
        systemInfo = await _getSystemInfo();
      }

      final feedbackContent = _buildFeedbackEmail(systemInfo);
      await _sendFeedbackEmail(feedbackContent);

      if (mounted) {
        _showSuccessDialog();
      }
    } catch (e) {
      if (mounted) {
        _showErrorSnackBar('提交反馈时出现错误：$e');
      }
    } finally {
      if (mounted) {
        setState(() {
          _isSubmitting = false;
        });
      }
    }
  }

  Future<String> _getSystemInfo() async {
    try {
      final deviceInfo = DeviceInfoPlugin();
      final packageInfo = await PackageInfo.fromPlatform();
      
      String systemInfo = '';
      systemInfo += '应用版本: ${packageInfo.version}\n';
      systemInfo += '构建号: ${packageInfo.buildNumber}\n';
      
      if (Platform.isIOS) {
        final iosInfo = await deviceInfo.iosInfo;
        systemInfo += '设备: ${iosInfo.name}\n';
        systemInfo += '系统版本: iOS ${iosInfo.systemVersion}\n';
        systemInfo += '设备型号: ${iosInfo.model}\n';
      } else if (Platform.isAndroid) {
        final androidInfo = await deviceInfo.androidInfo;
        systemInfo += '设备: ${androidInfo.model}\n';
        systemInfo += '系统版本: Android ${androidInfo.version.release}\n';
        systemInfo += '制造商: ${androidInfo.manufacturer}\n';
      }
      
      return systemInfo;
    } catch (e) {
      return '系统信息获取失败';
    }
  }

  String _buildFeedbackEmail(String systemInfo) {
    final buffer = StringBuffer();
    buffer.writeln('反馈类型: ${_getFeedbackTypeTitle(_selectedType)}');
    buffer.writeln('标题: ${_titleController.text}');
    buffer.writeln('');
    buffer.writeln('详细描述:');
    buffer.writeln(_contentController.text);
    buffer.writeln('');
    
    if (_emailController.text.isNotEmpty) {
      buffer.writeln('联系邮箱: ${_emailController.text}');
      buffer.writeln('');
    }
    
    if (_includeSystemInfo && systemInfo.isNotEmpty) {
      buffer.writeln('系统信息:');
      buffer.writeln(systemInfo);
    }
    
    return buffer.toString();
  }

  Future<void> _sendFeedbackEmail(String content) async {
    const email = '<EMAIL>';
    final subject = '${_l10n.appNameChinese} - ${_getFeedbackTypeTitle(_selectedType)}';
    
    final Uri emailUri = Uri(
      scheme: 'mailto',
      path: email,
      query: 'subject=${Uri.encodeComponent(subject)}&body=${Uri.encodeComponent(content)}',
    );

    if (await canLaunchUrl(emailUri)) {
      await launchUrl(emailUri);
    } else {
      throw Exception('无法打开邮件应用');
    }
  }

  void _showSuccessDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('反馈提交成功'),
        content: const Text('感谢您的反馈！我们会认真考虑您的建议。'),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              Navigator.of(context).pop();
            },
            child: const Text('确定'),
          ),
        ],
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppTheme.redDark,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }
}

enum FeedbackType {
  bug,
  suggestion,
  complaint,
  praise,
}
