import 'dart:io';
import 'package:flutter/material.dart';
import 'package:path_provider/path_provider.dart';

import '../config/app_theme.dart';
import '../generated/l10n/app_localizations.dart';

class StorageManagementScreen extends StatefulWidget {
  const StorageManagementScreen({super.key});

  @override
  State<StorageManagementScreen> createState() => _StorageManagementScreenState();
}

class _StorageManagementScreenState extends State<StorageManagementScreen> {
  bool _isLoading = true;
  StorageInfo? _storageInfo;
  late AppLocalizations _l10n;

  @override
  void initState() {
    super.initState();
    _loadStorageInfo();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    _l10n = AppLocalizations.of(context);
  }

  Future<void> _loadStorageInfo() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final storageInfo = await _calculateStorageInfo();
      setState(() {
        _storageInfo = storageInfo;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      _showErrorSnackBar(_l10n.storageLoadStorageInfoFailed(e.toString()));
    }
  }

  Future<StorageInfo> _calculateStorageInfo() async {
    final appDir = await getApplicationDocumentsDirectory();
    final cacheDir = await getTemporaryDirectory();
    
    final appSize = await _getDirectorySize(appDir);
    final cacheSize = await _getDirectorySize(cacheDir);
    
    // 计算各类数据大小
    final contentSize = await _getContentDataSize();
    final settingsSize = await _getSettingsDataSize();
    final voiceSize = await _getVoiceDataSize();
    final imageSize = await _getImageDataSize();
    
    return StorageInfo(
      totalAppSize: appSize,
      cacheSize: cacheSize,
      contentDataSize: contentSize,
      settingsDataSize: settingsSize,
      voiceDataSize: voiceSize,
      imageDataSize: imageSize,
    );
  }

  Future<int> _getDirectorySize(Directory directory) async {
    int size = 0;
    try {
      if (await directory.exists()) {
        await for (final entity in directory.list(recursive: true)) {
          if (entity is File) {
            size += await entity.length();
          }
        }
      }
    } catch (e) {
      debugPrint('计算目录大小失败: $e');
    }
    return size;
  }

  Future<int> _getContentDataSize() async {
    // 计算内容数据大小（Hive数据库等）
    try {
      final appDir = await getApplicationDocumentsDirectory();
      final hiveDir = Directory('${appDir.path}/hive');
      return await _getDirectorySize(hiveDir);
    } catch (e) {
      return 0;
    }
  }

  Future<int> _getSettingsDataSize() async {
    // 计算设置数据大小
    try {
      // 这里可以根据实际的存储实现来计算
      return 1024; // 假设设置数据很小
    } catch (e) {
      return 0;
    }
  }

  Future<int> _getVoiceDataSize() async {
    // 计算语音数据大小
    try {
      final appDir = await getApplicationDocumentsDirectory();
      final voiceDir = Directory('${appDir.path}/voice');
      return await _getDirectorySize(voiceDir);
    } catch (e) {
      return 0;
    }
  }

  Future<int> _getImageDataSize() async {
    // 计算图片数据大小
    try {
      final appDir = await getApplicationDocumentsDirectory();
      final imageDir = Directory('${appDir.path}/images');
      return await _getDirectorySize(imageDir);
    } catch (e) {
      return 0;
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.bgLightColor,
      appBar: AppBar(
        title: Text(
          _l10n.settingsStorageManagement,
          style: TextStyle(
            color: AppTheme.textDarkColor,
            fontWeight: FontWeight.bold,
          ),
        ),
        backgroundColor: AppTheme.bgWhiteColor,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: AppTheme.textDarkColor),
          onPressed: () => Navigator.of(context).pop(),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh, color: AppTheme.textDarkColor),
            onPressed: _loadStorageInfo,
          ),
        ],
      ),
      body: _isLoading
          ? const Center(child: CircularProgressIndicator())
          : _storageInfo == null
              ? Center(child: Text(_l10n.storageLoadStorageInfoFailed('unknown error')))
              : _buildStorageContent(),
    );
  }

  Widget _buildStorageContent() {
    final info = _storageInfo!;
    final totalSize = info.totalAppSize + info.cacheSize;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 总体存储概览
          _buildStorageOverview(totalSize),
          const SizedBox(height: 24),

          // 存储详情
          _buildSectionTitle(_l10n.storageDetails),
          _buildStorageItem(
            _l10n.storageAppData,
            info.totalAppSize,
            Icons.apps,
            AppTheme.blueGradient,
          ),
          _buildStorageItem(
            _l10n.storageCacheFiles,
            info.cacheSize,
            Icons.cached,
            AppTheme.orangeGradient,
            onTap: () => _clearCache(),
          ),
          _buildStorageItem(
            _l10n.storageContentData,
            info.contentDataSize,
            Icons.text_snippet,
            AppTheme.greenGradient,
          ),
          _buildStorageItem(
            _l10n.storageVoiceFiles,
            info.voiceDataSize,
            Icons.mic,
            AppTheme.purpleGradient,
            onTap: () => _manageVoiceFiles(),
          ),
          _buildStorageItem(
            _l10n.storageImageFiles,
            info.imageDataSize,
            Icons.image,
            AppTheme.chineseGradient,
            onTap: () => _manageImageFiles(),
          ),
          _buildStorageItem(
            _l10n.storageSettingsData,
            info.settingsDataSize,
            Icons.settings,
            AppTheme.primaryGradient,
          ),

          const SizedBox(height: 24),

          // 清理选项
          _buildSectionTitle(_l10n.storageCleanupOptions),
          _buildCleanupOptions(),

          const SizedBox(height: 24),

          // 数据管理
          _buildSectionTitle(_l10n.storageDataManagement),
          _buildDataManagementOptions(),
        ],
      ),
    );
  }

  Widget _buildStorageOverview(int totalSize) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: AppTheme.primaryGradient,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        children: [
          const Icon(
            Icons.storage,
            size: 48,
            color: Colors.white,
          ),
          const SizedBox(height: 12),
          Text(
            _l10n.storageTotalUsage,
            style: TextStyle(
              fontSize: 18,
              color: Colors.white70,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            _formatBytes(totalSize),
            style: const TextStyle(
              fontSize: 32,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Text(
        title,
        style: const TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.bold,
          color: AppTheme.textDarkColor,
        ),
      ),
    );
  }

  Widget _buildStorageItem(
    String title,
    int size,
    IconData icon,
    LinearGradient gradient, {
    VoidCallback? onTap,
  }) {
    return Card(
      margin: const EdgeInsets.only(bottom: 8),
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: ListTile(
        leading: Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            gradient: gradient,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(icon, color: Colors.white, size: 24),
        ),
        title: Text(
          title,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Text(_formatBytes(size)),
        trailing: onTap != null
            ? const Icon(Icons.arrow_forward_ios, size: 16)
            : null,
        onTap: onTap,
      ),
    );
  }

  Widget _buildCleanupOptions() {
    return Column(
      children: [
        _buildActionCard(
          _l10n.storageClearCache,
          _l10n.storageClearCacheDesc,
          Icons.cleaning_services,
          AppTheme.orangeGradient,
          () => _clearCache(),
        ),
        const SizedBox(height: 8),
        _buildActionCard(
          _l10n.storageClearTempFiles,
          _l10n.storageClearTempFilesDesc,
          Icons.delete_sweep,
          AppTheme.redGradient,
          () => _clearTempFiles(),
        ),
      ],
    );
  }

  Widget _buildDataManagementOptions() {
    return Column(
      children: [
        _buildActionCard(
          _l10n.storageExportData,
          _l10n.storageExportDataDesc,
          Icons.file_download,
          AppTheme.greenGradient,
          () => _exportData(),
        ),
        const SizedBox(height: 8),
        _buildActionCard(
          _l10n.storageImportData,
          _l10n.storageImportDataDesc,
          Icons.file_upload,
          AppTheme.blueGradient,
          () => _importData(),
        ),
        const SizedBox(height: 8),
        _buildActionCard(
          _l10n.storageResetAppData,
          _l10n.storageResetAppDataDesc,
          Icons.restore,
          AppTheme.redGradient,
          () => _resetAppData(),
        ),
      ],
    );
  }

  Widget _buildActionCard(
    String title,
    String subtitle,
    IconData icon,
    LinearGradient gradient,
    VoidCallback onTap,
  ) {
    return Card(
      elevation: 2,
      shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(12)),
      child: ListTile(
        leading: Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            gradient: gradient,
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(icon, color: Colors.white, size: 24),
        ),
        title: Text(
          title,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
        subtitle: Text(subtitle),
        trailing: const Icon(Icons.arrow_forward_ios, size: 16),
        onTap: onTap,
      ),
    );
  }

  String _formatBytes(int bytes) {
    if (bytes < 1024) return '$bytes B';
    if (bytes < 1024 * 1024) return '${(bytes / 1024).toStringAsFixed(1)} KB';
    if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    }
    return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
  }

  Future<void> _clearCache() async {
    try {
      final cacheDir = await getTemporaryDirectory();
      if (await cacheDir.exists()) {
        await cacheDir.delete(recursive: true);
        await cacheDir.create();
      }
      _showSuccessSnackBar(_l10n.storageCacheCleared);
      _loadStorageInfo();
    } catch (e) {
      _showErrorSnackBar(_l10n.storageClearCacheFailed(e.toString()));
    }
  }

  Future<void> _clearTempFiles() async {
    try {
      // 清理临时文件的逻辑
      _showSuccessSnackBar(_l10n.storageTempFilesCleared);
      _loadStorageInfo();
    } catch (e) {
      _showErrorSnackBar(_l10n.storageClearTempFilesFailed(e.toString()));
    }
  }

  void _manageVoiceFiles() {
    // 导航到语音文件管理页面
    _showInfoSnackBar(_l10n.storageVoiceManagementInDevelopment);
  }

  void _manageImageFiles() {
    // 导航到图片文件管理页面
    _showInfoSnackBar(_l10n.storageImageManagementInDevelopment);
  }

  void _exportData() {
    // 导出数据功能
    _showInfoSnackBar(_l10n.storageDataExportInDevelopment);
  }

  void _importData() {
    // 导入数据功能
    _showInfoSnackBar(_l10n.storageDataImportInDevelopment);
  }

  void _resetAppData() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(_l10n.storageResetDataTitle),
        content: Text(_l10n.storageResetDataMessage),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text(_l10n.storageCancel),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _performReset();
            },
            child: Text(_l10n.storageConfirm, style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  Future<void> _performReset() async {
    try {
      // 重置应用数据的逻辑
      _showSuccessSnackBar(_l10n.storageDataResetComplete);
      _loadStorageInfo();
    } catch (e) {
      _showErrorSnackBar(_l10n.storageDataResetFailed(e.toString()));
    }
  }

  void _showSuccessSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppTheme.greenDark,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppTheme.redDark,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }

  void _showInfoSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppTheme.primaryColor,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }
}

class StorageInfo {
  final int totalAppSize;
  final int cacheSize;
  final int contentDataSize;
  final int settingsDataSize;
  final int voiceDataSize;
  final int imageDataSize;

  StorageInfo({
    required this.totalAppSize,
    required this.cacheSize,
    required this.contentDataSize,
    required this.settingsDataSize,
    required this.voiceDataSize,
    required this.imageDataSize,
  });
}
