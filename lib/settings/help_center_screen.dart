import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';

import '../config/app_theme.dart';
import '../config/constants.dart';
import '../generated/l10n/app_localizations.dart';

class HelpCenterScreen extends StatefulWidget {
  const HelpCenterScreen({super.key});

  @override
  State<HelpCenterScreen> createState() => _HelpCenterScreenState();
}

class _HelpCenterScreenState extends State<HelpCenterScreen> {
  late AppLocalizations _l10n;
  
  @override
  void initState() {
    super.initState();
    _l10n = AppLocalizations.of(context);
  }
  
  List<HelpItem> get _helpItems => [
    HelpItem(
      title: '如何开始使用',
      content:
          '欢迎使用${_l10n.appNameChinese}！您可以从主页选择需要的功能模块，如文本卡片、Markdown编辑、PDF处理等。',
      icon: Icons.play_circle_outline,
    ),
    HelpItem(
      title: '文本卡片功能',
      content: '文本卡片功能可以帮您将文本内容转换为精美的卡片图片，支持多种模板和样式自定义。',
      icon: Icons.text_snippet,
    ),
    HelpItem(
      title: 'Markdown编辑',
      content: 'Markdown编辑器支持实时预览、多种主题、导出为HTML/PDF等功能，让您的文档编写更高效。',
      icon: Icons.text_fields,
    ),
    HelpItem(
      title: '引流图片生成',
      content: '引流图片生成功能可以创建吸引眼球的营销图片，支持添加干扰、水印等防盗用功能。',
      icon: Icons.trending_up,
    ),
    HelpItem(
      title: '语音功能',
      content: '语音功能包括录音、转录、文字转语音等，支持多种语言和高质量的语音处理。',
      icon: Icons.mic,
    ),
    HelpItem(
      title: 'PDF处理',
      content: 'PDF处理功能支持查看、注释、安全设置等，让您更好地管理PDF文档。',
      icon: Icons.picture_as_pdf,
    ),
    HelpItem(
      title: '数据同步与备份',
      content: '您的数据会自动保存在本地，建议定期使用导出功能备份重要内容。',
      icon: Icons.backup,
    ),
    HelpItem(
      title: '隐私与安全',
      content: '我们重视您的隐私，所有数据处理都在本地进行，不会上传到服务器。',
      icon: Icons.security,
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return CupertinoPageScaffold(
      backgroundColor: CupertinoColors.systemGroupedBackground,
      navigationBar: CupertinoNavigationBar(
        middle: const Text('帮助中心'),
        backgroundColor: CupertinoColors.systemBackground,
        trailing: CupertinoButton(
          padding: EdgeInsets.zero,
          onPressed: _showSearchDialog,
          child: const Icon(CupertinoIcons.search),
        ),
      ),
      child: SafeArea(
        child: Column(
          children: [
            // 顶部横幅
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(20),
              decoration: BoxDecoration(
                gradient: AppTheme.primaryGradient,
                borderRadius: const BorderRadius.only(
                  bottomLeft: Radius.circular(20),
                  bottomRight: Radius.circular(20),
                ),
              ),
              child: Column(
                children: [
                  const Icon(
                    CupertinoIcons.question_circle,
                    size: 48,
                    color: Colors.white,
                  ),
                  const SizedBox(height: 12),
                  const Text(
                    '需要帮助？',
                    style: TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                    ),
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    '查找常见问题的解答，或联系我们获取支持',
                    style: TextStyle(fontSize: 16, color: Colors.white70),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),

            // 快速操作按钮
            Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  Expanded(
                    child: _buildQuickActionButton(
                      '联系支持',
                      CupertinoIcons.chat_bubble_2,
                      AppTheme.blueGradient,
                      _contactSupport,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _buildQuickActionButton(
                      '用户手册',
                      CupertinoIcons.book,
                      AppTheme.greenGradient,
                      _openUserManual,
                    ),
                  ),
                ],
              ),
            ),

            // 帮助项目列表
            Expanded(
              child: ListView.builder(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                itemCount: _helpItems.length,
                itemBuilder: (context, index) {
                  final item = _helpItems[index];
                  return _buildHelpItem(item);
                },
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActionButton(
    String title,
    IconData icon,
    LinearGradient gradient,
    VoidCallback onTap,
  ) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 12),
        decoration: BoxDecoration(
          gradient: gradient,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Column(
          children: [
            Icon(icon, color: Colors.white, size: 28),
            const SizedBox(height: 8),
            Text(
              title,
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
                fontSize: 14,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildHelpItem(HelpItem item) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        color: CupertinoColors.systemBackground,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: CupertinoListTile(
        leading: Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            color: AppTheme.primaryColor.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(item.icon, color: AppTheme.primaryColor, size: 24),
        ),
        title: Text(
          item.title,
          style: const TextStyle(fontWeight: FontWeight.bold, fontSize: 16),
        ),
        subtitle: Text(
          item.content,
          style: const TextStyle(
            fontSize: 14,
            color: AppTheme.textMediumColor,
            height: 1.5,
          ),
        ),
        trailing: const Icon(CupertinoIcons.chevron_down),
      ),
    );
  }

  void _showSearchDialog() {
    showCupertinoDialog(
      context: context,
      builder:
          (context) => CupertinoAlertDialog(
            title: const Text('搜索帮助'),
            content: const Padding(
              padding: EdgeInsets.only(top: 16),
              child: CupertinoTextField(
                placeholder: '输入关键词搜索...',
                prefix: Padding(
                  padding: EdgeInsets.only(left: 8),
                  child: Icon(CupertinoIcons.search, size: 20),
                ),
              ),
            ),
            actions: [
              CupertinoDialogAction(
                onPressed: () => Navigator.pop(context),
                child: const Text('取消'),
              ),
              CupertinoDialogAction(
                onPressed: () => Navigator.pop(context),
                child: const Text('搜索'),
              ),
            ],
          ),
    );
  }

  void _contactSupport() async {
    const email = '<EMAIL>';
    final subject = '${_l10n.appNameChinese} 支持请求';
    const body = '请描述您遇到的问题...';

    final Uri emailUri = Uri(
      scheme: 'mailto',
      path: email,
      query: 'subject=$subject&body=$body',
    );

    try {
      if (await canLaunchUrl(emailUri)) {
        await launchUrl(emailUri);
      } else {
        _showErrorSnackBar('无法打开邮件应用');
      }
    } catch (e) {
      _showErrorSnackBar('发送邮件时出现错误');
    }
  }

  void _openUserManual() async {
    const url = '${AppConstants.projectUrl}/help';
    try {
      final Uri uri = Uri.parse(url);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri, mode: LaunchMode.externalApplication);
      } else {
        _showErrorSnackBar('无法打开用户手册');
      }
    } catch (e) {
      _showErrorSnackBar('打开用户手册时出现错误');
    }
  }

  void _showErrorSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppTheme.redDark,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }
}

class HelpItem {
  final String title;
  final String content;
  final IconData icon;

  HelpItem({required this.title, required this.content, required this.icon});
}
