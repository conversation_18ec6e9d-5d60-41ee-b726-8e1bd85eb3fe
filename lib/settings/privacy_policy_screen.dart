import 'package:flutter/material.dart';
import 'package:flutter/services.dart' show rootBundle;
import 'package:flutter_markdown/flutter_markdown.dart';
import '../generated/l10n/app_localizations.dart';

class PrivacyPolicyScreen extends StatefulWidget {
  const PrivacyPolicyScreen({super.key});

  @override
  State<PrivacyPolicyScreen> createState() => _PrivacyPolicyScreenState();
}

class _PrivacyPolicyScreenState extends State<PrivacyPolicyScreen> {
  String? _markdown;
  bool _failed = false;
  bool _initialized = false;

  @override
  void initState() {
    super.initState();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    if (!_initialized) {
      final locale = Localizations.localeOf(context);
      final languageCode = locale.languageCode.toLowerCase();
      _loadMarkdownForLang(languageCode);
      _initialized = true;
    }
  }

  Future<void> _loadMarkdownForLang(String languageCode) async {
    final candidates = <String>[
      if (languageCode.startsWith('zh')) 'assets/privacy/privacy_zh-Hans.md',
      if (languageCode == 'ja') 'assets/privacy/privacy_ja.md',
      if (languageCode == 'ko') 'assets/privacy/privacy_ko.md',
      'assets/privacy/privacy_en.md',
    ];
    for (final asset in candidates) {
      try {
        final text = await rootBundle.loadString(asset);
        if (!mounted) return;
        setState(() {
          _markdown = text;
        });
        return;
      } catch (_) {
        // try next
      }
    }
    if (!mounted) return;
    setState(() {
      _failed = true;
    });
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    return Scaffold(
      appBar: AppBar(title: Text(l10n.privacyPolicyTitle)),
      body:
          _failed
              ? Center(child: Text(l10n.error))
              : _markdown == null
              ? const Center(child: CircularProgressIndicator())
              : Markdown(
                data: _markdown!,
                selectable: true,
                padding: const EdgeInsets.all(16),
              ),
    );
  }
}
