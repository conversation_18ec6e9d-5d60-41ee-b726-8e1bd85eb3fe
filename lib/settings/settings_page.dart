import 'dart:async';
import 'dart:math' as math;
import 'package:flutter/material.dart';
import '../config/app_theme.dart';
import '../config/constants.dart';
import '../subscription/subscription_screen.dart';
import '../generated/l10n/app_localizations.dart';
import '../home/<USER>/background_painter.dart';
import '../services/localization_service.dart';
import '../services/service_locator.dart';
import '../subscription/subscription_service.dart';
import '../subscription/subscription_model.dart';

import 'language_settings_page.dart';
import 'privacy_policy_screen.dart';
import 'content_defaults_settings_page.dart';
import 'package:package_info_plus/package_info_plus.dart';

/// 现代化设置页面
class SettingsPage extends StatefulWidget {
  final LocalizationService localizationService;

  const SettingsPage({super.key, required this.localizationService});

  @override
  State<SettingsPage> createState() => _SettingsPageState();
}

class _SettingsPageState extends State<SettingsPage>
    with SingleTickerProviderStateMixin, WidgetsBindingObserver {
  late AnimationController _animationController;
  final ScrollController _scrollController = ScrollController();
  bool _isScrolled = false;
  ThemeMode _themeMode = ThemeMode.system;
  late SubscriptionService _subscriptionService;
  String _appVersion = '';
  StreamSubscription<UserSubscription>? _subscriptionSub;

  @override
  void initState() {
    super.initState();
    _subscriptionService = ServiceLocator().subscriptionService;
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(seconds: 20),
    )..repeat();

    _scrollController.addListener(_onScroll);
    _loadThemeMode();
    _loadAppVersion();

    // 监听订阅状态变化
    _subscriptionSub = _subscriptionService.subscriptionStream.listen((
      subscription,
    ) {
      if (mounted) {
        setState(() {
          // 订阅状态发生变化时重新构建UI
        });
      }
    });

    // 添加应用生命周期监听器
    WidgetsBinding.instance.addObserver(this);
  }

  void _onScroll() {
    if (_scrollController.offset > 0 && !_isScrolled) {
      setState(() => _isScrolled = true);
    } else if (_scrollController.offset <= 0 && _isScrolled) {
      setState(() => _isScrolled = false);
    }
  }

  Future<void> _loadAppVersion() async {
    try {
      final info = await PackageInfo.fromPlatform();
      final parsed =
          info.buildNumber.isNotEmpty
              ? '${info.version}+${info.buildNumber}'
              : info.version;
      if (mounted) {
        setState(() => _appVersion = parsed);
      }
    } catch (_) {
      // ignore and keep default
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    _scrollController.dispose();
    _subscriptionSub?.cancel();
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    // 当应用重新获得焦点时，刷新订阅状态
    if (state == AppLifecycleState.resumed) {
      if (mounted) {
        setState(() {
          // 触发重新构建以更新订阅状态
        });
      }
    }
  }

  /// 加载设置
  void _loadThemeMode() {
    if (ServiceLocator().isInitialized) {
      final settings = ServiceLocator().settingsService.settings;
      setState(() {
        _themeMode = settings.themeMode;
      });
    }
  }

  /// 保存主题模式
  Future<void> _saveThemeMode(ThemeMode themeMode) async {
    await ServiceLocator().settingsService.updateThemeMode(themeMode);
    setState(() {
      _themeMode = themeMode;
    });
  }

  /// 获取主题模式显示名称
  String _getThemeModeDisplayName(AppLocalizations l10n) {
    switch (_themeMode) {
      case ThemeMode.light:
        return l10n.lightMode;
      case ThemeMode.dark:
        return l10n.darkMode;
      case ThemeMode.system:
        return l10n.systemMode;
    }
  }

  @override
  Widget build(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    final theme = Theme.of(context);
    final colorScheme = theme.colorScheme;
    final isDark = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor:
          isDark ? theme.scaffoldBackgroundColor : const Color(0xFFF9FAFB),
      extendBodyBehindAppBar: true,
      body: Stack(
        children: [
          // 动态背景
          Positioned.fill(
            child: AnimatedBuilder(
              animation: _animationController,
              builder: (context, child) {
                return CustomPaint(
                  painter: BackgroundPainter(_animationController.value),
                );
              },
            ),
          ),

          // 背景装饰
          _buildBackgroundDecorations(),

          // 主内容
          CustomScrollView(
            controller: _scrollController,
            physics: const BouncingScrollPhysics(),
            slivers: [
              // 现代化应用栏
              SliverAppBar(
                expandedHeight: 130,
                floating: false,
                pinned: true,
                stretch: true,
                elevation: _isScrolled ? 4 : 0,
                backgroundColor:
                    _isScrolled
                        ? (isDark
                            ? colorScheme.surface.withValues(alpha: 0.95)
                            : Colors.white.withValues(alpha: 0.95))
                        : Colors.transparent,
                title:
                    _isScrolled
                        ? Text(
                          l10n.settings,
                          style: TextStyle(
                            color:
                                isDark
                                    ? colorScheme.onSurface
                                    : const Color(0xFF1F2937),
                            fontWeight: FontWeight.bold,
                            fontSize: 20,
                          ),
                        )
                        : null,
                flexibleSpace: FlexibleSpaceBar(
                  title: null,
                  centerTitle: false,
                  collapseMode: CollapseMode.pin,
                  background: Stack(
                    fit: StackFit.expand,
                    children: [
                      // 渐变背景
                      Container(
                        decoration: BoxDecoration(
                          gradient: LinearGradient(
                            begin: Alignment.topCenter,
                            end: Alignment.bottomCenter,
                            colors:
                                isDark
                                    ? [
                                      colorScheme.primary.withValues(
                                        alpha: 0.7,
                                      ),
                                      colorScheme.primary.withValues(
                                        alpha: 0.5,
                                      ),
                                      colorScheme.primary.withValues(
                                        alpha: 0.2,
                                      ),
                                      Colors.transparent,
                                    ]
                                    : [
                                      const Color(
                                        0xFF6366F1,
                                      ).withValues(alpha: 0.7),
                                      const Color(
                                        0xFF6366F1,
                                      ).withValues(alpha: 0.5),
                                      const Color(
                                        0xFF6366F1,
                                      ).withValues(alpha: 0.2),
                                      Colors.transparent,
                                    ],
                          ),
                        ),
                      ),
                      // 装饰图案
                      Positioned(
                        top: -30,
                        right: -30,
                        child: Transform.rotate(
                          angle: math.pi / 6,
                          child: Container(
                            width: 180,
                            height: 180,
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                colors:
                                    isDark
                                        ? [
                                          colorScheme.primary,
                                          colorScheme.secondary,
                                        ]
                                        : [
                                          const Color(0xFF6366F1),
                                          const Color(0xFF8B5CF6),
                                        ],
                              ),
                              borderRadius: BorderRadius.circular(40),
                            ),
                          ),
                        ),
                      ),
                      // 标题区
                      Positioned(
                        left: 20,
                        bottom: 35,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text(
                              l10n.settings,
                              style: const TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                                fontSize: 28,
                              ),
                            ),
                            const SizedBox(height: 4),
                            Text(
                              l10n.personalizeYourAppExperience,
                              style: TextStyle(
                                color: Colors.white.withValues(alpha: 0.9),
                                fontSize: 14,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                ),
                leading: IconButton(
                  icon: Icon(
                    Icons.arrow_back_ios,
                    color:
                        _isScrolled
                            ? (isDark
                                ? colorScheme.onSurface
                                : const Color(0xFF1F2937))
                            : Colors.white,
                  ),
                  onPressed: () => Navigator.pop(context),
                ),
              ),

              // 设置内容
              SliverPadding(
                padding: const EdgeInsets.fromLTRB(16, 20, 16, 32),
                sliver: SliverList(
                  delegate: SliverChildListDelegate([
                    // 外观设置组
                    _buildModernSettingsGroup(
                      title: l10n.appearance,
                      gradient: LinearGradient(
                        colors:
                            isDark
                                ? [
                                  colorScheme.primary,
                                  colorScheme.primary.withValues(alpha: 0.7),
                                ]
                                : [
                                  const Color(0xFF3B82F6),
                                  const Color(0xFF60A5FA),
                                ],
                      ),
                      children: [
                        _buildModernSettingsItem(
                          context: context,
                          icon: Icons.language,
                          title: l10n.language,
                          subtitle:
                              widget.localizationService.currentLocaleName,
                          gradient: LinearGradient(
                            colors:
                                isDark
                                    ? [
                                      colorScheme.primary,
                                      colorScheme.primary.withValues(
                                        alpha: 0.7,
                                      ),
                                    ]
                                    : [
                                      const Color(0xFF3B82F6),
                                      const Color(0xFF60A5FA),
                                    ],
                          ),
                          onTap: () => _openLanguageSettings(context),
                        ),
                        _buildModernSettingsItem(
                          context: context,
                          icon: Icons.palette,
                          title: l10n.theme,
                          subtitle: _getThemeModeDisplayName(l10n),
                          gradient: LinearGradient(
                            colors:
                                isDark
                                    ? [
                                      colorScheme.primary,
                                      colorScheme.primary.withValues(
                                        alpha: 0.7,
                                      ),
                                    ]
                                    : [
                                      const Color(0xFF3B82F6),
                                      const Color(0xFF60A5FA),
                                    ],
                          ),
                          onTap: () => _showThemeDialog(context),
                        ),
                      ],
                    ),

                    const SizedBox(height: 24),

                    // 内容设置组
                    _buildModernSettingsGroup(
                      title: l10n.contentSettings,
                      gradient: LinearGradient(
                        colors:
                            isDark
                                ? [
                                  colorScheme.primaryContainer,
                                  colorScheme.primaryContainer.withValues(
                                    alpha: 0.7,
                                  ),
                                ]
                                : [
                                  const Color(0xFF8B5CF6),
                                  const Color(0xFFA78BFA),
                                ],
                      ),
                      children: [
                        _buildModernSettingsItem(
                          context: context,
                          icon: Icons.tune,
                          title: l10n.useDefaultInitialText,
                          subtitle: l10n.useDefaultInitialTextDescription,
                          gradient: LinearGradient(
                            colors:
                                isDark
                                    ? [
                                      colorScheme.primaryContainer,
                                      colorScheme.primaryContainer.withValues(
                                        alpha: 0.7,
                                      ),
                                    ]
                                    : [
                                      const Color(0xFF8B5CF6),
                                      const Color(0xFFA78BFA),
                                    ],
                          ),
                          onTap: () {
                            Navigator.push(
                              context,
                              MaterialPageRoute(
                                builder:
                                    (_) => const ContentDefaultsSettingsPage(),
                              ),
                            ).then((_) => _loadThemeMode());
                          },
                        ),
                      ],
                    ),

                    const SizedBox(height: 24),

                    // 订阅与法律
                    _buildSubscriptionSection(
                      context,
                      l10n,
                      isDark,
                      colorScheme,
                    ),

                    const SizedBox(height: 24),

                    // 关于设置组
                    _buildModernSettingsGroup(
                      title: l10n.about,
                      gradient: LinearGradient(
                        colors:
                            isDark
                                ? [
                                  colorScheme.secondaryContainer,
                                  colorScheme.secondaryContainer.withValues(
                                    alpha: 0.7,
                                  ),
                                ]
                                : [
                                  const Color(0xFF10B981),
                                  const Color(0xFF34D399),
                                ],
                      ),
                      children: [
                        _buildModernSettingsItem(
                          context: context,
                          icon: Icons.info_outline,
                          title: l10n.versionInfo,
                          subtitle:
                              _appVersion.isNotEmpty
                                  ? _appVersion
                                  : AppConstants.appVersion,
                          gradient: LinearGradient(
                            colors:
                                isDark
                                    ? [
                                      colorScheme.secondaryContainer,
                                      colorScheme.secondaryContainer.withValues(
                                        alpha: 0.7,
                                      ),
                                    ]
                                    : [
                                      const Color(0xFF10B981),
                                      const Color(0xFF34D399),
                                    ],
                          ),
                          onTap: () => _showAboutDialog(context),
                        ),
                        _buildModernSettingsItem(
                          context: context,
                          icon: Icons.privacy_tip_outlined,
                          title: l10n.privacyPolicyTitle,
                          subtitle: l10n.privacyPolicySubtitle,
                          gradient: LinearGradient(
                            colors:
                                isDark
                                    ? [
                                      colorScheme.secondaryContainer,
                                      colorScheme.secondaryContainer.withValues(
                                        alpha: 0.7,
                                      ),
                                    ]
                                    : [
                                      const Color(0xFF10B981),
                                      const Color(0xFF34D399),
                                    ],
                          ),
                          onTap: () => _openPrivacyPolicy(context),
                        ),
                        _buildModernSettingsItem(
                          context: context,
                          icon: Icons.balance_outlined,
                          title: l10n.openSourceLicensesTitle,
                          subtitle: l10n.openSourceLicensesSubtitle,
                          gradient: LinearGradient(
                            colors:
                                isDark
                                    ? [
                                      colorScheme.secondaryContainer,
                                      colorScheme.secondaryContainer.withValues(
                                        alpha: 0.7,
                                      ),
                                    ]
                                    : [
                                      const Color(0xFF10B981),
                                      const Color(0xFF34D399),
                                    ],
                          ),
                          onTap: () => _showLicenses(context),
                        ),
                        _buildModernSettingsItem(
                          context: context,
                          icon: Icons.help_outline,
                          title: l10n.helpAndFeedback,
                          subtitle: l10n.getHelpOrProvideFeedback,
                          gradient: LinearGradient(
                            colors:
                                isDark
                                    ? [
                                      colorScheme.secondaryContainer,
                                      colorScheme.secondaryContainer.withValues(
                                        alpha: 0.7,
                                      ),
                                    ]
                                    : [
                                      const Color(0xFF10B981),
                                      const Color(0xFF34D399),
                                    ],
                          ),
                          onTap: () => _showHelpDialog(context),
                        ),
                      ],
                    ),
                  ]),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// 构建背景装饰
  Widget _buildBackgroundDecorations() {
    return Stack(
      children: [
        // 右上角光晕
        Positioned(
          top: -100,
          right: -100,
          child: Container(
            width: 300,
            height: 300,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              gradient: RadialGradient(
                colors: [
                  const Color(0xFF6366F1).withValues(alpha: 0.3),
                  const Color(0xFF6366F1).withValues(alpha: 0.1),
                  Colors.transparent,
                ],
              ),
            ),
          ),
        ),
        // 左下角光晕
        Positioned(
          bottom: -50,
          left: -50,
          child: Container(
            width: 200,
            height: 200,
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              gradient: RadialGradient(
                colors: [
                  const Color(0xFF8B5CF6).withValues(alpha: 0.2),
                  Colors.transparent,
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// 构建现代化设置组
  Widget _buildModernSettingsGroup({
    required String title,
    required LinearGradient gradient,
    required List<Widget> children,
  }) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    return TweenAnimationBuilder<double>(
      duration: const Duration(milliseconds: 600),
      tween: Tween(begin: 0.0, end: 1.0),
      builder: (context, value, child) {
        return Transform.translate(
          offset: Offset(0, 30 * (1 - value)),
          child: Opacity(
            opacity: value,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Padding(
                  padding: const EdgeInsets.only(left: 8, bottom: 12),
                  child: Text(
                    title,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      color: isDark ? Colors.white : gradient.colors.first,
                    ),
                  ),
                ),
                Container(
                  decoration: BoxDecoration(
                    color:
                        isDark
                            ? theme.colorScheme.surface.withValues(alpha: 0.8)
                            : Colors.white,
                    borderRadius: BorderRadius.circular(20),
                    boxShadow: [
                      BoxShadow(
                        color:
                            isDark
                                ? Colors.black.withValues(alpha: 0.3)
                                : gradient.colors.first.withValues(alpha: 0.15),
                        blurRadius: isDark ? 16 : 20,
                        offset: const Offset(0, 8),
                        spreadRadius: isDark ? 0 : -4,
                      ),
                    ],
                    border:
                        isDark
                            ? Border.all(
                              color: theme.colorScheme.outline.withValues(
                                alpha: 0.2,
                              ),
                              width: 0.5,
                            )
                            : null,
                  ),
                  child: Column(children: children),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// 构建现代化设置项
  Widget _buildModernSettingsItem({
    required BuildContext context,
    required IconData icon,
    required String title,
    required String subtitle,
    required LinearGradient gradient,
    required VoidCallback onTap,
  }) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(16),
      child: Container(
        padding: const EdgeInsets.all(20),
        child: Row(
          children: [
            // 渐变图标容器
            Container(
              width: 48,
              height: 48,
              decoration: BoxDecoration(
                gradient: gradient,
                borderRadius: BorderRadius.circular(14),
                boxShadow: [
                  BoxShadow(
                    color: gradient.colors.first.withValues(alpha: 0.3),
                    blurRadius: 8,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Icon(icon, color: Colors.white, size: 24),
            ),
            const SizedBox(width: 16),
            // 文本内容
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w600,
                      color:
                          isDark
                              ? theme.colorScheme.onSurface
                              : const Color(0xFF1F2937),
                    ),
                    overflow: TextOverflow.ellipsis,
                    maxLines: 1,
                  ),
                  const SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: TextStyle(
                      fontSize: 14,
                      color:
                          isDark
                              ? theme.colorScheme.onSurfaceVariant
                              : const Color(0xFF6B7280),
                    ),
                    overflow: TextOverflow.ellipsis,
                    maxLines: 2,
                  ),
                ],
              ),
            ),
            // 箭头图标
            Container(
              width: 32,
              height: 32,
              decoration: BoxDecoration(
                color:
                    isDark
                        ? theme.colorScheme.surface.withValues(alpha: 0.6)
                        : const Color(0xFFF3F4F6),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                Icons.arrow_forward_ios,
                size: 16,
                color:
                    isDark
                        ? theme.colorScheme.onSurfaceVariant
                        : const Color(0xFF9CA3AF),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 已移除未使用的 _buildModernSwitchItem（改为独立配置页）

  /// 打开语言设置
  void _openLanguageSettings(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder:
            (context) => LanguageSettingsPage(
              localizationService: widget.localizationService,
            ),
      ),
    );
  }

  /// 显示现代化主题选择对话框
  void _showThemeDialog(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    showDialog(
      context: context,
      builder:
          (context) => Dialog(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(20),
            ),
            child: Container(
              padding: const EdgeInsets.all(24),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(20),
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors:
                      isDark
                          ? [
                            theme.colorScheme.surface,
                            theme.colorScheme.surface.withValues(alpha: 0.8),
                          ]
                          : [Colors.white, const Color(0xFFF8FAFC)],
                ),
              ),
              child: Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Text(
                    l10n.selectTheme,
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                      color:
                          isDark
                              ? theme.colorScheme.onSurface
                              : const Color(0xFF1F2937),
                    ),
                  ),
                  const SizedBox(height: 20),
                  _buildThemeOption(
                    context: context,
                    icon: Icons.brightness_auto,
                    title: l10n.systemMode,
                    isSelected: _themeMode == ThemeMode.system,
                    gradient: const LinearGradient(
                      colors: [Color(0xFF6366F1), Color(0xFF8B5CF6)],
                    ),
                    onTap: () {
                      _saveThemeMode(ThemeMode.system);
                      Navigator.pop(context);
                    },
                  ),
                  const SizedBox(height: 12),
                  _buildThemeOption(
                    context: context,
                    icon: Icons.brightness_high,
                    title: l10n.lightMode,
                    isSelected: _themeMode == ThemeMode.light,
                    gradient: const LinearGradient(
                      colors: [Color(0xFFF59E0B), Color(0xFFFBBF24)],
                    ),
                    onTap: () {
                      _saveThemeMode(ThemeMode.light);
                      Navigator.pop(context);
                    },
                  ),
                  const SizedBox(height: 12),
                  _buildThemeOption(
                    context: context,
                    icon: Icons.brightness_low,
                    title: l10n.darkMode,
                    isSelected: _themeMode == ThemeMode.dark,
                    gradient: const LinearGradient(
                      colors: [Color(0xFF374151), Color(0xFF6B7280)],
                    ),
                    onTap: () {
                      _saveThemeMode(ThemeMode.dark);
                      Navigator.pop(context);
                    },
                  ),
                ],
              ),
            ),
          ),
    );
  }

  /// 构建主题选项
  Widget _buildThemeOption({
    required BuildContext context,
    required IconData icon,
    required String title,
    required bool isSelected,
    required LinearGradient gradient,
    required VoidCallback onTap,
  }) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(16),
      child: Container(
        padding: const EdgeInsets.all(16),
        decoration: BoxDecoration(
          color:
              isSelected
                  ? gradient.colors.first.withValues(alpha: isDark ? 0.2 : 0.1)
                  : isDark
                  ? theme.colorScheme.surface.withValues(alpha: 0.4)
                  : Colors.transparent,
          borderRadius: BorderRadius.circular(16),
          border: Border.all(
            color:
                isSelected
                    ? gradient.colors.first
                    : isDark
                    ? theme.colorScheme.outline.withValues(alpha: 0.3)
                    : const Color(0xFFE5E7EB),
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Row(
          children: [
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                gradient: gradient,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(icon, color: Colors.white, size: 20),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Text(
                title,
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w500,
                  color:
                      isSelected
                          ? gradient.colors.first
                          : isDark
                          ? theme.colorScheme.onSurfaceVariant
                          : const Color(0xFF374151),
                ),
              ),
            ),
            if (isSelected)
              Container(
                width: 24,
                height: 24,
                decoration: BoxDecoration(
                  gradient: gradient,
                  shape: BoxShape.circle,
                ),
                child: const Icon(Icons.check, color: Colors.white, size: 16),
              ),
          ],
        ),
      ),
    );
  }

  /// 显示关于对话框
  void _showAboutDialog(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    showAboutDialog(
      context: context,
      applicationName: l10n.appName,
      applicationVersion:
          _appVersion.isNotEmpty ? _appVersion : AppConstants.appVersion,
      applicationIcon: Container(
        width: 64,
        height: 64,
        decoration: BoxDecoration(
          gradient: AppTheme.primaryGradient,
          borderRadius: BorderRadius.circular(16),
        ),
        child: const Icon(Icons.content_paste, color: Colors.white, size: 32),
      ),
      children: [Text(l10n.appDescription)],
    );
  }

  /// 显示帮助对话框
  void _showHelpDialog(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    showDialog(
      context: context,
      builder:
          (context) => AlertDialog(
            title: Text(l10n.helpAndFeedback),
            content: Text(l10n.helpAndFeedbackContent),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                child: Text(l10n.ok),
              ),
            ],
          ),
    );
  }

  // 工具函数与动作

  /// 构建订阅状态部分
  Widget _buildSubscriptionSection(
    BuildContext context,
    AppLocalizations l10n,
    bool isDark,
    ColorScheme colorScheme,
  ) {
    final subscription = _subscriptionService.subscription;
    final isActive = subscription.isActive && subscription.isPaid;

    return _buildModernSettingsGroup(
      title: l10n.subscriptionManagement,
      gradient: LinearGradient(
        colors:
            isDark
                ? [
                  colorScheme.tertiary,
                  colorScheme.tertiary.withValues(alpha: 0.7),
                ]
                : [const Color(0xFF0EA5E9), const Color(0xFF38BDF8)],
      ),
      children: [
        isActive
            ? _buildPremiumSubscriptionCard(context, l10n, isDark, colorScheme)
            : _buildUpgradeSubscriptionCard(context, l10n, isDark, colorScheme),
      ],
    );
  }

  /// 构建付费订阅卡片（炫酷效果）
  Widget _buildPremiumSubscriptionCard(
    BuildContext context,
    AppLocalizations l10n,
    bool isDark,
    ColorScheme colorScheme,
  ) {
    final subscription = _subscriptionService.subscription;

    return AnimatedBuilder(
      animation: _animationController,
      builder: (context, child) {
        return Container(
          margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(20),
            gradient: _buildPremiumGradient(isDark),
            boxShadow: _buildPremiumShadow(isDark),
          ),
          child: Container(
            margin: const EdgeInsets.all(2),
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(18),
              color: isDark ? const Color(0xFF1A1A1A) : Colors.white,
            ),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                borderRadius: BorderRadius.circular(18),
                onTap: () => _openSubscriptionCenter(context),
                child: Padding(
                  padding: const EdgeInsets.all(20),
                  child: Row(
                    children: [
                      // 炫酷图标
                      Container(
                        width: 60,
                        height: 60,
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          gradient: _buildIconGradient(isDark),
                          boxShadow: _buildIconShadow(isDark),
                        ),
                        child: Icon(
                          Icons.workspace_premium,
                          color:
                              isDark ? Colors.white : const Color(0xFF8B4513),
                          size: 30,
                        ),
                      ),

                      const SizedBox(width: 16),

                      // 订阅信息
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Expanded(
                                  child: Text(
                                    _getSubscriptionDisplayName(
                                      subscription,
                                      l10n,
                                    ),
                                    style: TextStyle(
                                      fontSize: 18,
                                      fontWeight: FontWeight.bold,
                                      color:
                                          isDark
                                              ? Colors.white
                                              : Colors.black87,
                                    ),
                                    overflow: TextOverflow.ellipsis,
                                    maxLines: 1,
                                  ),
                                ),
                                const SizedBox(width: 8),
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                    horizontal: 8,
                                    vertical: 4,
                                  ),
                                  decoration: BoxDecoration(
                                    gradient: _buildStatusLabelGradient(isDark),
                                    borderRadius: BorderRadius.circular(12),
                                  ),
                                  child: Text(
                                    l10n.active,
                                    style: TextStyle(
                                      color: _getStatusLabelTextColor(isDark),
                                      fontSize: 12,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 4),
                            Text(
                              _getSubscriptionDescription(subscription, l10n),
                              style: TextStyle(
                                fontSize: 14,
                                color:
                                    isDark
                                        ? Colors.grey[300]
                                        : Colors.grey[600],
                              ),
                              overflow: TextOverflow.ellipsis,
                              maxLines: 2,
                            ),
                          ],
                        ),
                      ),

                      // 箭头图标
                      Icon(
                        Icons.arrow_forward_ios,
                        color: isDark ? Colors.grey[400] : Colors.grey[600],
                        size: 16,
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  /// 构建升级订阅卡片
  Widget _buildUpgradeSubscriptionCard(
    BuildContext context,
    AppLocalizations l10n,
    bool isDark,
    ColorScheme colorScheme,
  ) {
    return _buildModernSettingsItem(
      context: context,
      icon: Icons.workspace_premium,
      title: l10n.upgradeSubscription,
      subtitle: l10n.upgradeSubscriptionDesc,
      gradient: LinearGradient(
        colors:
            isDark
                ? [
                  colorScheme.tertiary,
                  colorScheme.tertiary.withValues(alpha: 0.7),
                ]
                : [const Color(0xFF0EA5E9), const Color(0xFF38BDF8)],
      ),
      onTap: () => _openSubscriptionCenter(context),
    );
  }

  void _openSubscriptionCenter(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const SubscriptionScreen()),
    );
  }

  void _openPrivacyPolicy(BuildContext context) {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (_) => const PrivacyPolicyScreen()),
    );
  }

  // 已简化订阅入口，移除设置页中的“订阅管理/恢复购买”操作

  void _showLicenses(BuildContext context) {
    final l10n = AppLocalizations.of(context);
    showLicensePage(
      context: context,
      applicationName: l10n.appName,
      applicationVersion:
          _appVersion.isNotEmpty ? _appVersion : AppConstants.appVersion,
    );
  }

  /// 获取订阅显示名称
  String _getSubscriptionDisplayName(
    UserSubscription subscription,
    AppLocalizations l10n,
  ) {
    switch (subscription.type) {
      case SubscriptionType.monthly:
        return l10n.subscriptionPlanMonthlyName;
      case SubscriptionType.yearly:
        return l10n.subscriptionPlanYearlyName;
      case SubscriptionType.lifetime:
        return l10n.subscriptionPlanLifetimeName;
      case SubscriptionType.free:
        return l10n.subscriptionPlanFreeName;
    }
  }

  /// 获取订阅描述
  String _getSubscriptionDescription(
    UserSubscription subscription,
    AppLocalizations l10n,
  ) {
    if (subscription.type == SubscriptionType.lifetime) {
      return l10n.subscriptionPeriodLifetime;
    } else if (subscription.type == SubscriptionType.free) {
      return l10n.subscriptionPeriodFree;
    } else if (subscription.endDate != null) {
      final endDate = subscription.endDate!;
      final now = DateTime.now();
      final daysLeft = endDate.difference(now).inDays;

      if (daysLeft > 0) {
        return l10n.daysUntilExpiry(daysLeft);
      } else {
        return l10n.subscriptionExpired;
      }
    } else {
      return l10n.subscriptionPeriodFree;
    }
  }

  /// 构建高级订阅渐变效果
  LinearGradient _buildPremiumGradient(bool isDark) {
    // 计算动画进度，使用正弦波让动画更自然
    final progress = _animationController.value;
    final wave1 = (math.sin(progress * 2 * math.pi) + 1) / 2;
    final wave2 = (math.sin(progress * 2 * math.pi + math.pi / 2) + 1) / 2;
    final wave3 = (math.sin(progress * 2 * math.pi + math.pi) + 1) / 2;

    if (isDark) {
      // 深色模式：使用更鲜艳的颜色
      return LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [
          Color.lerp(
            const Color(0xFFFFD700),
            const Color(0xFFFFA500),
            wave1,
          )!, // 金色到橙色
          Color.lerp(
            const Color(0xFFFFA500),
            const Color(0xFFFF6B6B),
            wave2,
          )!, // 橙色到红色
          Color.lerp(
            const Color(0xFFFF6B6B),
            const Color(0xFF4ECDC4),
            wave3,
          )!, // 红色到青色
          Color.lerp(
            const Color(0xFF4ECDC4),
            const Color(0xFFFFD700),
            wave1,
          )!, // 青色到金色
          Color.lerp(
            const Color(0xFFFFD700),
            const Color(0xFF9D4EDD),
            wave2,
          )!, // 金色到紫色
        ],
        stops: [
          0.0,
          0.25 + wave1 * 0.1,
          0.5 + wave2 * 0.1,
          0.75 + wave3 * 0.1,
          1.0,
        ],
      );
    } else {
      // 浅色模式：使用更柔和、饱和度较低的颜色
      return LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [
          Color.lerp(
            const Color(0xFFFFF8DC),
            const Color(0xFFFFE4B5),
            wave1,
          )!, // 柔和金色
          Color.lerp(
            const Color(0xFFFFE4B5),
            const Color(0xFFFFB6C1),
            wave2,
          )!, // 柔和橙色到粉色
          Color.lerp(
            const Color(0xFFFFB6C1),
            const Color(0xFFB0E0E6),
            wave3,
          )!, // 粉色到浅青色
          Color.lerp(
            const Color(0xFFB0E0E6),
            const Color(0xFFE6E6FA),
            wave1,
          )!, // 浅青色到淡紫色
          Color.lerp(
            const Color(0xFFE6E6FA),
            const Color(0xFFFFF8DC),
            wave2,
          )!, // 淡紫色到柔和金色
        ],
        stops: [
          0.0,
          0.25 + wave1 * 0.05,
          0.5 + wave2 * 0.05,
          0.75 + wave3 * 0.05,
          1.0,
        ],
      );
    }
  }

  /// 构建高级订阅阴影效果
  List<BoxShadow> _buildPremiumShadow(bool isDark) {
    final progress = _animationController.value;
    final intensity = (math.sin(progress * 4 * math.pi) + 1) / 2 * 0.2 + 0.1;

    if (isDark) {
      return [
        BoxShadow(
          color: const Color(0xFFFFD700).withValues(alpha: 0.3 + intensity),
          blurRadius: 20 + intensity * 10,
          spreadRadius: 2,
          offset: const Offset(0, 8),
        ),
        BoxShadow(
          color: const Color(
            0xFF4ECDC4,
          ).withValues(alpha: 0.2 + intensity * 0.5),
          blurRadius: 15 + intensity * 5,
          spreadRadius: 1,
          offset: const Offset(-4, -4),
        ),
      ];
    } else {
      return [
        BoxShadow(
          color: const Color(
            0xFFFFD700,
          ).withValues(alpha: 0.15 + intensity * 0.1),
          blurRadius: 15 + intensity * 5,
          spreadRadius: 1,
          offset: const Offset(0, 6),
        ),
        BoxShadow(
          color: const Color(
            0xFFB0E0E6,
          ).withValues(alpha: 0.1 + intensity * 0.05),
          blurRadius: 10 + intensity * 3,
          spreadRadius: 0,
          offset: const Offset(-2, -2),
        ),
      ];
    }
  }

  /// 构建图标渐变效果
  LinearGradient _buildIconGradient(bool isDark) {
    if (isDark) {
      return const LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [
          Color(0xFFFFD700), // 金色
          Color(0xFFFFA500), // 橙色
        ],
      );
    } else {
      return const LinearGradient(
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
        colors: [
          Color(0xFFFFF8DC), // 柔和金色
          Color(0xFFFFE4B5), // 柔和橙色
        ],
      );
    }
  }

  /// 构建图标阴影效果
  List<BoxShadow> _buildIconShadow(bool isDark) {
    if (isDark) {
      return [
        BoxShadow(
          color: const Color(0xFFFFD700).withValues(alpha: 0.4),
          blurRadius: 15,
          spreadRadius: 1,
        ),
      ];
    } else {
      return [
        BoxShadow(
          color: const Color(0xFFFFD700).withValues(alpha: 0.2),
          blurRadius: 10,
          spreadRadius: 0,
        ),
      ];
    }
  }

  /// 构建状态标签渐变效果
  LinearGradient _buildStatusLabelGradient(bool isDark) {
    if (isDark) {
      return const LinearGradient(
        colors: [
          Color(0xFFFFD700), // 金色
          Color(0xFFFFA500), // 橙色
        ],
      );
    } else {
      return const LinearGradient(
        colors: [
          Color(0xFFDEB887), // 柔和金色
          Color(0xFFCD853F), // 柔和橙色
        ],
      );
    }
  }

  /// 获取状态标签文本颜色
  Color _getStatusLabelTextColor(bool isDark) {
    if (isDark) {
      return Colors.white;
    } else {
      return Colors.white; // 浅色模式下也使用白色，因为背景色足够深
    }
  }
}
