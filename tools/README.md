# ContentPal 开发工具

本目录包含 ContentPal 项目的开发工具和脚本。

## 目录结构

```
tools/
├── README.md                   # 本文件
└── l10n/                       # 国际化工具
    ├── add_chinese_translations.py    # 添加中文翻译脚本
    ├── analyze_arb.py                 # ARB文件分析脚本
    ├── header.txt                     # 翻译文件头部模板
    ├── ja_missing.txt                 # 日语缺失翻译列表
    └── zh_missing.txt                 # 中文缺失翻译列表
```

## 工具说明

### l10n/ - 国际化工具

#### add_chinese_translations.py
用于批量添加中文翻译到ARB文件的Python脚本。

#### analyze_arb.py  
分析ARB文件，检查翻译完整性和一致性的工具。

#### *.txt 文件
记录各语言缺失翻译的状态文件，用于跟踪翻译进度。

## 使用方法

在项目根目录下运行工具：

```bash
# 分析ARB文件
python tools/l10n/analyze_arb.py

# 添加中文翻译
python tools/l10n/add_chinese_translations.py
```

## 注意事项

- 工具脚本应放在 `tools/` 目录下，不要放在 `lib/` 目录中
- `lib/` 目录应该只包含 Dart 源代码和资源文件(.arb)
- 运行脚本前请确保已安装相应的Python依赖
