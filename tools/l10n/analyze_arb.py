#!/usr/bin/env python3
"""
ARB文件分析工具
用于分析英文、中文和日文ARB文件，找出缺失的翻译
"""

import json
import re
from pathlib import Path

def load_arb_file(file_path):
    """加载ARB文件"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"Error loading {file_path}: {e}")
        return {}

def extract_keys(arb_data):
    """提取ARB文件中的所有键（排除以@开头的元数据键）"""
    keys = []
    for key in arb_data.keys():
        if not key.startswith('@'):
            keys.append(key)
    return sorted(keys)

def find_missing_keys(source_keys, target_keys):
    """找出目标文件中缺失的键"""
    return [key for key in source_keys if key not in target_keys]

def analyze_arb_files():
    """分析ARB文件"""
    base_path = Path("/Users/<USER>/MyProjects/Startup/contentpal/flutter/contentpal/lib/l10n")
    
    # 加载文件
    en_data = load_arb_file(base_path / "app_en.arb")
    zh_data = load_arb_file(base_path / "app_zh.arb") 
    ja_data = load_arb_file(base_path / "app_ja.arb")
    
    # 提取键
    en_keys = extract_keys(en_data)
    zh_keys = extract_keys(zh_data)
    ja_keys = extract_keys(ja_data)
    
    print(f"英文键总数: {len(en_keys)}")
    print(f"中文键总数: {len(zh_keys)}")
    print(f"日文键总数: {len(ja_keys)}")
    
    # 找出缺失的键
    zh_missing = find_missing_keys(en_keys, zh_keys)
    ja_missing = find_missing_keys(en_keys, ja_keys)
    
    print(f"\n中文缺失的键: {len(zh_missing)}")
    print(f"日文缺失的键: {len(ja_missing)}")
    
    # 保存缺失键列表
    with open(base_path / "zh_missing.txt", 'w', encoding='utf-8') as f:
        for key in zh_missing:
            f.write(f"{key}\n")
    
    with open(base_path / "ja_missing.txt", 'w', encoding='utf-8') as f:
        for key in ja_missing:
            f.write(f"{key}\n")
    
    print("\n缺失键列表已保存到 zh_missing.txt 和 ja_missing.txt")
    
    return en_data, zh_data, ja_data, zh_missing, ja_missing

def show_sample_missing_keys(en_data, zh_missing, ja_missing, limit=10):
    """显示一些缺失键的示例"""
    print("\n=== 中文缺失键示例 ===")
    for i, key in enumerate(zh_missing[:limit]):
        en_value = en_data.get(key, "N/A")
        print(f"{i+1}. {key}: {en_value}")
    
    print("\n=== 日文缺失键示例 ===")
    for i, key in enumerate(ja_missing[:limit]):
        en_value = en_data.get(key, "N/A")
        print(f"{i+1}. {key}: {en_value}")

if __name__ == "__main__":
    en_data, zh_data, ja_data, zh_missing, ja_missing = analyze_arb_files()
    show_sample_missing_keys(en_data, zh_missing, ja_missing)