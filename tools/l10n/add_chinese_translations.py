#!/usr/bin/env python3
"""
中文翻译生成工具
为中文ARB文件添加缺失的翻译
"""

import json
import re
from pathlib import Path

def load_arb_file(file_path):
    """加载ARB文件"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"Error loading {file_path}: {e}")
        return {}

def save_arb_file(file_path, data):
    """保存ARB文件"""
    try:
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        print(f"Saved to {file_path}")
    except Exception as e:
        print(f"Error saving {file_path}: {e}")

def get_chinese_translation(key, en_value):
    """获取中文翻译"""
    # 中文翻译字典
    translations = {
        # Common words
        "commonCarryOn": "继续",
        "commonEndure": "忍受",
        "commonGoOn": "继续",
        "commonKeepOn": "继续",
        "commonLast": "持续",
        "commonPersevere": "坚持",
        "commonPersist": "坚持",
        "complete": "完成",
        
        # HTML related
        "htmlCopy": "复制",
        "htmlNewDocumentTitle": "新建HTML文档",
        "htmlRenderError": "渲染错误",
        "htmlUntitled": "未命名",
        
        # PDF related
        "pdfAllowCopying": "允许复制",
        "pdfCreatedTime": "创建时间",
        "pdfDocumentInformation": "文档信息",
        "pdfEncryptedMessage": "加密信息",
        "pdfEncryptionAlgorithm": "加密算法",
        "pdfEncryptionAlgorithmDesc": "PDF文档使用的加密算法类型",
        "pdfImportToStart": "导入开始",
        "pdfModifiedTime": "修改时间",
        "pdfMultiDeviceSync": "多设备同步",
        "pdfMultipleFormats": "多种格式",
        "pdfPasswordProtectionDesc": "使用密码保护您的PDF文档",
        "pdfPermissionControlDesc": "控制文档的打印、复制等权限",
        "pdfPermissionsFailed": "权限设置失败",
        "pdfPermissionsSuccess": "权限设置成功",
        "pdfProtectYourDocuments": "保护您的文档",
        "pdfSecurityEncrypted": "已加密",
        "pdfSecurityOpen": "开放",
        "pdfSecurityReadOnly": "只读",
        "pdfSecurityRestricted": "受限",
        "pdfSettingsFailed": "设置失败",
        "pdfUsageTips": "使用提示",
        
        # Voice related
        "voiceAddToPlaylist": "添加到播放列表",
        "voiceAddedToPlaylist": "已添加到播放列表",
        "voiceAudioAdjustment": "音频调整",
        "voiceAudioAdjustmentDesc": "调整音频的播放效果和质量",
        "voiceAudioDurationAbnormal": "音频时长异常",
        "voiceAudioFileNotExist": "音频文件不存在",
        "voiceAudioFileNotExistOrCorrupted": "音频文件不存在或已损坏",
        "voiceAutoAddPunctuation": "自动添加标点",
        "voiceBatchProcessingProgress": "批量处理进度",
        "voiceBatchTranscription": "批量转录",
        "voiceBatchTranscriptionFeature": "批量转录功能",
        "voiceBatchTranscriptionInDev": "批量转录功能开发中",
        "voiceClear": "清除",
        "voiceClickToStartRealtime": "点击开始实时转录",
        "voiceClickToStartTranscription": "点击开始转录",
        "voiceCloudSync": "云端同步",
        "voiceCloudSyncDesc": "将录音和转录内容同步到云端",
        "voiceConfidenceThreshold": "置信度阈值",
        "voiceConvertTextToVoice": "将文字转换为语音",
        "voiceCumulativeDuration": "累计时长",
        "voiceDetectDifferentSpeakers": "检测不同说话者",
        "voiceDirectIosPermissionTest": "直接iOS权限测试",
        "voiceEnablePunctuation": "启用标点符号",
        "voiceExport": "导出",
        "voiceExportFeatureInDev": "导出功能开发中",
        "voiceFeatureInDev": "功能开发中",
        "voiceFileButCorrupted": "文件存在但已损坏",
        "voiceFileExists": "文件存在",
        "voiceFileNotExists": "文件不存在",
        "voiceFileSelected": "文件已选择",
        "voiceFileStatusRechecked": "文件状态已重新检查",
        "voiceFilesSelected": "已选择文件",
        "voiceIosDirectPermissionTest": "iOS直接权限测试",
        "voiceIosFailed": "iOS失败",
        "voiceIosInitialMicrophonePermission": "iOS初始麦克风权限",
        "voiceIosInitialSpeechPermission": "iOS初始语音权限",
        "voiceIosMicrophonePermissionStatus": "iOS麦克风权限状态",
        "voiceIosNonIosPlatform": "非iOS平台",
        "voiceIosOperationLogs": "iOS操作日志",
        "voiceIosPageLoaded": "iOS页面已加载",
        "voiceIosPermissionTest": "iOS权限测试",
        "voiceIosRequestMicrophonePermission": "iOS请求麦克风权限",
        "voiceIosRequestSpeechPermission": "iOS请求语音权限",
        "voiceIosSpeechPermissionStatus": "iOS语音权限状态",
        "voiceIosSpeechRecognitionInitialization": "iOS语音识别初始化",
        "voiceIosSpeechRecognitionStatus": "iOS语音识别状态",
        "voiceIosSpeechRecognitionTestError": "iOS语音识别测试错误",
        "voiceIosStartListening": "iOS开始监听",
        "voiceIosStopListening": "iOS停止监听",
        "voiceIosSuccess": "iOS成功",
        "voiceIosTestRecordingFunction": "iOS测试录音功能",
        "voiceIosRecorderClosed": "iOS录音器已关闭",
        "voiceIosRecorderError": "iOS录音器错误",
        "voiceIosRecorderInitialized": "iOS录音器已初始化",
        "voiceIosRecorderInstanceCreated": "iOS录音器实例已创建",
        "voiceIosRecorderTestCompleted": "iOS录音器测试完成",
        "voiceIosRecognitionResult": "iOS识别结果",
        "voiceIosTestSpeechRecognition": "iOS测试语音识别",
        "voiceLanguageChineseSimplified": "中文简体",
        "voiceLanguageChineseTraditional": "中文繁体",
        "voiceLanguageEnglish": "英语",
        "voiceLanguageJapanese": "日语",
        "voiceLanguageKorean": "韩语",
        "voiceLanguageSelector": "语言选择器",
        "voiceLoadAudioFailed": "加载音频失败",
        "voiceMicrophonePermissionRequired": "需要麦克风权限",
        "voiceNoTranscriptionContent": "无转录内容",
        "voiceNoTranscriptionContentToSave": "无转录内容可保存",
        "voiceNoTranscriptionContentToShare": "无转录内容可分享",
        "voiceOK": "确定",
        "voiceOpenAppSettings": "打开应用设置",
        "voiceOperationLog": "操作日志",
        "voicePageLoaded": "页面已加载",
        "voicePause": "暂停",
        "voicePaused": "已暂停",
        "voicePermissionRequiredMessage": "需要权限",
        "voicePlayFailed": "播放失败",
        "voicePlaying": "正在播放",
        "voicePlaylist": "播放列表",
        "voicePlaylistDesc": "管理和播放录音列表",
        "voicePlaylistEmpty": "播放列表为空",
        "voicePlaylistTitle": "播放列表",
        "voicePowerfulFeatures": "强大功能",
        "voiceRecordDetailTitle": "录音详情",
        "voiceRecordNewVoice": "录制新音频",
        "voiceRecordingComplete": "录音完成",
        "voiceRecordingCount": "录音数量",
        "voiceRecordingFailedRetry": "录音失败，请重试",
        "voiceRecordingFileMayBeCorrupted": "录音文件可能已损坏",
        "voiceRecordingsUnit": "个录音",
        "voiceRecheck": "重新检查",
        "voiceRealtimeTranscription": "实时转录",
        "voiceRecentRecordings": "最近录音",
        "voiceReselectFile": "重新选择文件",
        "voiceSaveAllChanges": "保存所有更改",
        "voiceSaveFailed": "保存失败",
        "voiceSaveTranscriptionResult": "保存转录结果",
        "voiceSaveTranscriptionText": "保存转录文本",
        "voiceSelectAudioFile": "选择音频文件",
        "voiceSelectBatchFilesFailed": "选择批量文件失败",
        "voiceSelectFile": "选择文件",
        "voiceSelectFileFailed": "选择文件失败",
        "voiceSelectMultipleFiles": "选择多个文件",
        "voiceSelectMultipleFilesBtn": "选择多个文件",
        "voiceServiceInitializationFailed": "服务初始化失败",
        "voiceShare": "分享",
        "voiceShareFeatureInDev": "分享功能开发中",
        "voiceSmartTranscription": "智能转录",
        "voiceSmartTranscriptionDesc": "使用AI技术提供准确的语音转录",
        "voiceSmartTranscriptionPageTitle": "智能转录",
        "voiceSpeakerDetection": "说话者检测",
        "voiceSpeechRate": "语速",
        "voiceStartFirstRecording": "开始第一次录音",
        "voiceStartTranscriptionFailed": "开始转录失败",
        "voiceStop": "停止",
        "voiceStopTranscription": "停止转录",
        "voiceTextToSpeech": "文本转语音",
        "voiceTtsPlayerTitle": "文本转语音播放器",
        "voiceTtsSettings": "文本转语音设置",
        "voiceTranscriptionContentPreview": "转录内容预览",
        "voiceTranscriptionFailedRetry": "转录失败，请重试",
        "voiceTranscriptionIdle": "转录空闲",
        "voiceTranscriptionInProgress": "转录进行中",
        "voiceTranscriptionResult": "转录结果",
        "voiceTranscriptionResultSaved": "转录结果已保存",
        "voiceTranscriptionResultTitle": "转录结果",
        "voiceTranscriptionSettings": "转录设置",
        "voiceTranscriptionTextSaved": "转录文本已保存",
        "voiceTranscriptionTitle": "转录",
        "voiceUsageStats": "使用统计",
        "voiceViewAll": "查看全部",
        "voiceVoiceTranscription": "语音转录",
        "voiceVolume": "音量"
    }
    
    # 如果字典中有对应的翻译，直接返回
    if key in translations:
        return translations[key]
    
    # 对于包含占位符的文本，需要特殊处理
    if "{" in en_value and "}" in en_value:
        # 这里可以添加更复杂的翻译逻辑
        return en_value  # 暂时返回英文，实际项目中需要人工翻译
    
    # 对于没有预定义翻译的键，返回基本的翻译
    return en_value  # 暂时返回英文，实际项目中需要人工翻译

def add_chinese_translations():
    """为中文ARB文件添加缺失的翻译"""
    base_path = Path("/Users/<USER>/MyProjects/Startup/contentpal/flutter/contentpal/lib/l10n")
    
    # 加载文件
    en_data = load_arb_file(base_path / "app_en.arb")
    zh_data = load_arb_file(base_path / "app_zh.arb")
    
    # 读取缺失键列表
    with open(base_path / "zh_missing.txt", 'r', encoding='utf-8') as f:
        missing_keys = [line.strip() for line in f.readlines()]
    
    print(f"需要添加的中文翻译数量: {len(missing_keys)}")
    
    # 添加缺失的翻译
    added_count = 0
    for key in missing_keys:
        if key in en_data and key not in zh_data:
            en_value = en_data[key]
            zh_value = get_chinese_translation(key, en_value)
            zh_data[key] = zh_value
            added_count += 1
            print(f"Added: {key} -> {zh_value}")
    
    # 保存文件
    save_arb_file(base_path / "app_zh.arb", zh_data)
    print(f"成功添加 {added_count} 个中文翻译")

if __name__ == "__main__":
    add_chinese_translations()