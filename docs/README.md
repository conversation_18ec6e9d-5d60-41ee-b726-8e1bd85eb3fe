# ContentPal 文档

本目录包含 ContentPal 项目的相关文档。

## 项目目录结构

```
ContentPal/
├── docs/                       # 📚 项目文档
│   ├── README.md               # 文档总览
│   ├── traffic_guide/          # 流量引导功能文档
│   └── text_cards/             # 文本卡片功能文档
├── tools/                      # 🛠️ 开发工具
│   └── l10n/                   # 国际化工具
│       ├── add_chinese_translations.py
│       ├── analyze_arb.py
│       └── *.txt               # 翻译状态文件
└── lib/                        # 💻 Dart源代码
    ├── l10n/                   # 国际化资源文件(.arb)
    └── **/*.dart               # 所有Dart源文件
```

## 文档说明

### traffic_guide/
包含流量引导功能的开发文档，包括：
- 功能设计和重构记录
- 移动端优化方案
- 导出和自适应修复
- 测试说明
- 最终修复记录

### text_cards/
包含文本卡片功能的相关文档。

## 注意事项

- 所有项目文档应放在 `docs/` 目录下，不要放在 `lib/` 目录中
- `lib/` 目录应该只包含 Dart 源代码文件
- 文档按功能模块分类组织
