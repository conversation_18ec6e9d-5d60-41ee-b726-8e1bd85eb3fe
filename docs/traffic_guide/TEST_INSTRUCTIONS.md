# 实时预览功能测试指南

## 测试目标
验证实时预览功能是否正确响应所有用户配置更改。

## 测试步骤

### 1. 基础文字显示测试
1. 打开高级图片生成器
2. 在"文字"标签页输入测试文字："这是一个测试文字，用来验证预览功能是否正常工作"
3. **预期结果**: 预览区域应该完整显示所有文字，不会被截断

### 2. 字体大小测试
1. 在"文字"标签页调整字体大小滑块
2. 从最小值拖动到最大值
3. **预期结果**: 预览中的文字大小应该实时跟随滑块变化

### 3. 颜色配置测试
1. 点击"文字颜色"按钮，选择不同颜色
2. 点击"背景颜色"按钮，选择不同颜色
3. **预期结果**: 
   - 文字颜色应该立即改变
   - 背景颜色应该立即改变
   - 颜色选择器关闭后预览保持新颜色

### 4. 干扰效果测试
1. 切换到"效果"标签页
2. 调整"干扰程度"滑块从0%到100%
3. **预期结果**: 
   - 0%时没有干扰效果
   - 随着百分比增加，应该看到越来越多的白色点和线条
   - 100%时干扰效果最明显

### 5. 扭曲效果测试
1. 在"效果"标签页调整"扭曲程度"滑块
2. 从0%拖动到100%
3. **预期结果**:
   - 0%时没有扭曲效果
   - 随着百分比增加，应该看到波浪线条和几何形状
   - 100%时扭曲效果最明显

### 6. 水印功能测试
1. 在"效果"标签页勾选"添加水印"
2. 输入水印文本："测试水印"
3. **预期结果**:
   - 勾选后预览右下角应该出现水印
   - 输入文本后水印内容应该立即更新
   - 取消勾选后水印应该消失

### 7. 模板切换测试
1. 切换到"模板"标签页
2. 选择不同的模板（极简、现代、创意等）
3. **预期结果**:
   - 背景样式应该根据模板改变
   - 但用户已配置的文字、颜色等应该保持不变
   - 只有模板相关的装饰效果会改变

### 8. 综合测试
1. 输入长文本："这是一个综合测试，包含多行文字。我们要验证所有功能都能正常工作，包括字体大小、颜色、干扰效果、扭曲效果和水印功能。"
2. 设置字体大小为60
3. 设置文字颜色为红色
4. 设置背景颜色为蓝色
5. 设置干扰程度为50%
6. 设置扭曲程度为30%
7. 添加水印"综合测试"
8. **预期结果**: 所有设置都应该在预览中正确显示

## 问题排查

### 如果文字显示不完整
- 检查是否正确使用了 `widget.config.text`
- 确认 `maxLines` 设置合理
- 验证容器大小是否足够

### 如果配置更改无效
- 检查是否正确使用了用户配置而不是模板配置
- 确认 `setState` 被正确调用
- 验证配置传递链路是否完整

### 如果效果不显示
- 检查自定义绘制器是否正确实现
- 确认条件渲染逻辑是否正确
- 验证 `shouldRepaint` 方法是否正确

## 性能测试

### 响应速度测试
1. 快速连续调整滑块
2. **预期结果**: 预览应该流畅跟随，无明显延迟

### 内存使用测试
1. 长时间使用各种功能
2. **预期结果**: 内存使用应该稳定，无内存泄漏

## 成功标准
- ✅ 所有文字完整显示
- ✅ 字体大小实时响应
- ✅ 颜色更改立即生效
- ✅ 干扰效果正确显示
- ✅ 扭曲效果正确显示
- ✅ 水印功能正常工作
- ✅ 模板切换保持用户配置
- ✅ 响应速度流畅
- ✅ 无崩溃或错误

## 注意事项
1. 测试时请使用真实设备或模拟器
2. 确保网络连接正常（如果需要加载资源）
3. 测试不同屏幕尺寸的适配性
4. 验证在不同主题模式下的显示效果

通过以上测试，可以确保实时预览功能达到预期的专业水准。
