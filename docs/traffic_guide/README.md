# 内容引流模块

## 功能概述

内容引流模块是一个专门用于生成引流内容和规避平台审查的工具集合。该模块提供了多种功能来帮助用户创建不易被平台识别和过滤的内容。

## 主要功能

### 1. 引流图片生成
- **功能描述**: 生成带有干扰信息的不规则图片，避免平台识别
- **主要特性**:
  - 自定义文本内容和样式
  - 可调节的干扰程度和扭曲效果
  - 多种字体和颜色选择
  - 支持添加水印
  - 实时预览和保存功能

### 2. 文本转换工具
- **功能描述**: 将英文、数字转换为特殊Unicode字符，规避平台审查
- **主要特性**:
  - Emoji转换：数字和字母转换为特殊Unicode字符
  - Unicode变体：添加变音字符和特殊Unicode
  - 不可见字符：在文本中插入不可见字符
  - 敏感词干扰：对敏感词进行字符干扰
  - 自定义字符映射

### 3. 水印处理
- **功能描述**: 添加和移除隐形水印
- **主要特性**:
  - 隐形水印：使用不可见字符添加水印
  - 可见水印：支持透明度、字体大小、旋转角度设置
  - 双向处理：支持添加和移除水印

### 4. 项目管理
- **功能描述**: 保存和管理引流项目配置
- **主要特性**:
  - 项目创建和编辑
  - 配置保存和加载
  - 项目列表管理

## 技术实现

### 核心服务 (TrafficGuideService)

#### 不可见字符映射
```dart
static const Map<String, String> _invisibleChars = {
  '\u200B': '零宽空格',
  '\u200C': '零宽非连接符',
  '\u200D': '零宽连接符',
  '\u2060': '词连接符',
  '\uFEFF': '零宽不换行空格',
  // ... 更多不可见字符
};
```

#### 数字到Emoji映射
```dart
static const Map<String, String> _numberToEmoji = {
  '0': '🄀',
  '1': '🄁',
  '2': '🄂',
  // ... 更多映射
};
```

#### 字母到特殊Unicode映射
```dart
static const Map<String, String> _letterToUnicode = {
  'a': 'ᴀ',
  'b': 'ʙ',
  'c': 'ᴄ',
  // ... 更多映射
};
```

### 图片生成算法

1. **背景绘制**: 使用Canvas绘制背景色
2. **干扰元素**: 随机添加圆形噪点
3. **文本渲染**: 使用TextPainter绘制主文本
4. **扭曲效果**: 应用矩阵变换实现扭曲
5. **水印添加**: 在指定位置添加水印文本

### 文本转换算法

1. **Emoji转换**: 将数字和字母替换为对应的特殊字符
2. **Unicode变体**: 随机在字符间插入变音字符
3. **不可见字符**: 在字符间随机插入不可见字符
4. **敏感词干扰**: 在敏感词中间插入不可见字符

### 水印算法

1. **隐形水印**: 将水印文本编码为不可见字符序列
2. **可见水印**: 在文本末尾添加可见水印
3. **解码算法**: 从文本中提取隐形水印并解码

## 使用说明

### 1. 引流图片生成
1. 打开"引流图片生成"功能
2. 输入要显示的文本内容
3. 调整字体大小、颜色等样式设置
4. 设置干扰程度和扭曲程度
5. 选择是否添加水印
6. 点击"生成图片"按钮
7. 预览并保存图片

### 2. 文本转换
1. 打开"文本转换"功能
2. 在输入框中输入原始文本
3. 选择需要的转换选项：
   - Emoji转换
   - Unicode变体
   - 不可见字符
   - 敏感词干扰
4. 在高级设置中配置敏感词列表和自定义映射
5. 查看转换结果并复制

### 3. 水印处理
1. 打开"水印处理"功能
2. 选择处理模式（添加/移除）
3. 输入水印内容
4. 选择水印类型（隐形/可见）
5. 配置水印参数（透明度、字体大小等）
6. 输入要处理的文本
7. 点击处理按钮查看结果

### 4. 项目管理
1. 点击"新建项目"创建新项目
2. 配置项目的基本信息
3. 设置图片生成参数
4. 配置文本转换选项
5. 设置水印参数
6. 保存项目配置

## 注意事项

1. **合规使用**: 请确保使用本工具生成的内容符合相关法律法规
2. **平台政策**: 不同平台有不同的内容政策，请谨慎使用
3. **效果测试**: 建议在使用前测试转换效果
4. **备份原始内容**: 建议保留原始内容的备份

## 技术特点

- **模块化设计**: 各功能模块独立，便于维护和扩展
- **配置化**: 支持详细的参数配置
- **实时预览**: 提供实时预览功能
- **数据持久化**: 支持项目配置的保存和加载
- **用户友好**: 简洁直观的用户界面

## 依赖项

- `flutter`: Flutter框架
- `path_provider`: 文件路径管理
- `uuid`: UUID生成
- `image_gallery_saver`: 图片保存功能

## 文件结构

```
lib/traffic_guide/
├── models/
│   └── traffic_guide_models.dart      # 数据模型
├── services/
│   └── traffic_guide_service.dart     # 核心服务
├── screens/
│   ├── image_generator_screen.dart    # 图片生成界面
│   ├── text_transformer_screen.dart   # 文本转换界面
│   └── project_editor_screen.dart     # 项目编辑界面
├── traffic_guide_home_page.dart       # 主页面
└── README.md                          # 说明文档
``` 