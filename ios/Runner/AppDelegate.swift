import Flutter
import UIKit
import os.log

@main
@objc class AppDelegate: FlutterAppDelegate {
  private let CHANNEL = "com.example.contentpal/file_intent"
  private var initialIntent: [String: Any]?
  
  override func application(
    _ application: UIApplication,
    didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
  ) -> Bool {
    let controller : FlutterViewController = window?.rootViewController as! FlutterViewController
    let channel = FlutterMethodChannel(name: CHANNEL, binaryMessenger: controller.binaryMessenger)
    
    channel.setMethodCallHandler { [weak self] (call: FlutterMethodCall, result: @escaping FlutterResult) in
      switch call.method {
      case "getInitialIntent":
        result(self?.initialIntent)
      default:
        result(FlutterMethodNotImplemented)
      }
    }
    
    GeneratedPluginRegistrant.register(with: self)
    return super.application(application, didFinishLaunchingWithOptions: launchOptions)
  }
  
  // 处理从其他应用打开文件
  override func application(_ app: UIApplication, open url: URL, options: [UIApplication.OpenURLOptionsKey : Any] = [:]) -> <PERSON><PERSON> {
    handleFileURL(url)
    return true
  }
  
  // 处理文档打开（iOS 9+）
  override func application(_ application: UIApplication, open url: URL, sourceApplication: String?, annotation: Any) -> Bool {
    handleFileURL(url)
    return true
  }
  
  // 处理继续用户活动（Handoff等）
  override func application(_ application: UIApplication, continue userActivity: NSUserActivity, restorationHandler: @escaping ([UIUserActivityRestoring]?) -> Void) -> Bool {
    
    if userActivity.activityType == NSUserActivityTypeBrowsingWeb {
      if let url = userActivity.webpageURL {
        handleFileURL(url)
      }
    }
    
    return true
  }
  
  private func handleFileURL(_ url: URL) {
    os_log("处理文件URL: %{public}@", log: .default, type: .info, url.absoluteString)
    
    // iOS文件处理需要更复杂的逻辑
    var filePath: String?
    
    // 检查是否是文件选择器返回的文件
    if url.scheme == "file" {
      filePath = url.path
      os_log("文件路径: %{public}@", log: .default, type: .info, filePath ?? "nil")
      
      // 检查是否是File Provider Storage路径
      if filePath?.contains("File Provider Storage") == true {
        os_log("检测到File Provider Storage路径，需要安全作用域访问", log: .default, type: .info)
        // 对于这种路径，我们需要使用安全作用域访问
        _handleFileProviderURL(url)
        return
      }
    } else if url.scheme == "content" || url.scheme == "assets-library" {
      // 处理content URI
      os_log("Content URI需要特殊处理", log: .default, type: .info)
      return
    }
    
    // 确保文件存在并可访问
    guard let path = filePath, FileManager.default.fileExists(atPath: path) else {
      os_log("文件不存在或无法访问", log: .default, type: .error)
      return
    }
    
    // 检查文件是否可读
    guard FileManager.default.isReadableFile(atPath: path) else {
      os_log("文件不可读", log: .default, type: .error)
      return
    }
    
    // 获取文件属性
    do {
      let attributes = try FileManager.default.attributesOfItem(atPath: path)
      if let fileSize = attributes[.size] as? UInt64 {
        os_log("文件大小: %llu 字节", log: .default, type: .info, fileSize)
      }
    } catch {
      os_log("获取文件属性失败: %{public}@", log: .default, type: .error, error.localizedDescription)
    }
    
    // 使用统一的方法发送文件路径
    _sendFilePathToFlutter(path)
  }
  
  private func handleTextIntent(_ text: String) {
    os_log("处理文本意图，长度: %d", log: .default, type: .info, text.count)
    
    let intentData: [String: Any] = [
      "type": "text",
      "text": text
    ]
    
    // 如果Flutter引擎已经准备好，直接发送
    if let controller = window?.rootViewController as? FlutterViewController {
      let channel = FlutterMethodChannel(name: CHANNEL, binaryMessenger: controller.binaryMessenger)
      channel.invokeMethod("handleTextIntent", arguments: ["text": text])
    } else {
      // 否则保存以供后续使用
      initialIntent = intentData
    }
  }
  
  // 处理File Provider Storage的文件
  private func _handleFileProviderURL(_ url: URL) {
    os_log("处理File Provider URL: %{public}@", log: .default, type: .info, url.absoluteString)
    
    // 尝试使用安全作用域访问
    if url.startAccessingSecurityScopedResource() {
      defer { url.stopAccessingSecurityScopedResource() }
      
      // 将文件复制到临时目录
      do {
        let tempDir = FileManager.default.temporaryDirectory
        let tempURL = tempDir.appendingPathComponent("contentpal_\(Date().timeIntervalSince1970).\(url.pathExtension)")
        try FileManager.default.copyItem(at: url, to: tempURL)
        
        os_log("文件已复制到临时目录: %{public}@", log: .default, type: .info, tempURL.path)
        
        // 使用临时文件路径
        _sendFilePathToFlutter(tempURL.path)
      } catch {
        os_log("复制文件失败: %{public}@", log: .default, type: .error, error.localizedDescription)
      }
    } else {
      os_log("无法获取安全作用域访问权限", log: .default, type: .error)
    }
  }
  
  // 发送文件路径到Flutter
  private func _sendFilePathToFlutter(_ path: String) {
    let intentData: [String: Any] = [
      "type": "file",
      "filePath": path
    ]
    
    if let controller = window?.rootViewController as? FlutterViewController {
      let channel = FlutterMethodChannel(name: CHANNEL, binaryMessenger: controller.binaryMessenger)
      channel.invokeMethod("handleFileIntent", arguments: ["filePath": path])
    } else {
      initialIntent = intentData
    }
  }
  
  // 处理UIDocumentPickerViewController返回的文件
  func handleDocumentPickerURL(_ url: URL) {
    os_log("处理文档选择器URL: %{public}@", log: .default, type: .info, url.absoluteString)
    
    // iOS文档选择器返回的文件可能需要安全访问
    var filePath: String?
    
    // 检查是否有安全作用域
    if url.startAccessingSecurityScopedResource() {
      defer { url.stopAccessingSecurityScopedResource() }
      
      // 如果是iCloud文件，需要复制到本地
      if url.isFileURL {
        filePath = url.path
      } else {
        // 复制到临时目录
        do {
          let tempDir = FileManager.default.temporaryDirectory
          let tempURL = tempDir.appendingPathComponent(url.lastPathComponent)
          try FileManager.default.copyItem(at: url, to: tempURL)
          filePath = tempURL.path
          os_log("文件已复制到临时目录: %{public}@", log: .default, type: .info, filePath ?? "nil")
        } catch {
          os_log("复制文件失败: %{public}@", log: .default, type: .error, error.localizedDescription)
          return
        }
      }
    } else {
      // 直接访问
      filePath = url.path
    }
    
    guard let path = filePath else {
      os_log("无法获取文件路径", log: .default, type: .error)
      return
    }
    
    // 验证文件
    guard FileManager.default.fileExists(atPath: path) else {
      os_log("文件不存在", log: .default, type: .error)
      return
    }
    
    os_log("最终文件路径: %{public}@", log: .default, type: .info, path)
    
    // 使用统一的方法发送文件路径
    _sendFilePathToFlutter(path)
  }
}
