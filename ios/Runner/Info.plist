<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDisplayName</key>
	<string>ContentPal</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>contentpal</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(FLUTTER_BUILD_NAME)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleVersion</key>
	<string>$(FLUTTER_BUILD_NUMBER)</string>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>
	<key>ITSAppUsesNonExemptEncryption</key>
	<false/>
	
	<!-- 权限描述 -->
	
	<!-- Photo Library Permissions -->
	<key>NSPhotoLibraryUsageDescription</key>
	<string>ContentPal needs photo library access to save generated content including text cards, HTML pages, and SVG exports</string>
	<key>NSPhotoLibraryAddUsageDescription</key>
	<string>ContentPal needs photo library access to save generated images from text cards, markdown documents, and other content</string>
	
	<!-- Speech Recognition Permissions -->
	<key>NSSpeechRecognitionUsageDescription</key>
	<string>ContentPal needs speech recognition access to convert your voice to text in the voice recording feature, allowing you to create content through dictation and transcribe audio recordings</string>

	
	<!-- iOS 14+ 限制相册访问权限 -->
	<key>PHPhotoLibraryPreventAutomaticLimitedAccessAlert</key>
	<true/>
	
		
	<!-- 文档处理配置 -->
	<key>LSSupportsOpeningDocumentsInPlace</key>
	<true/>
	<key>UIFileSharingEnabled</key>
	<true/>
	
	<!-- 支持从其他应用打开 -->
	<key>UISupportsDocumentBrowser</key>
	<true/>
	
	<!-- 支持的文档类型 -->
	<key>CFBundleDocumentTypes</key>
	<array>
		<!-- Markdown文件 -->
		<dict>
			<key>CFBundleTypeName</key>
			<string>Markdown Document</string>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>LSHandlerRank</key>
			<string>Owner</string>
			<key>LSItemContentTypes</key>
			<array>
				<string>net.daringfireball.markdown</string>
				<string>public.plain-text</string>
			</array>
			<key>CFBundleTypeExtensions</key>
			<array>
				<string>md</string>
				<string>markdown</string>
			</array>
		</dict>
		
		<!-- 文本文件 -->
		<dict>
			<key>CFBundleTypeName</key>
			<string>Text Document</string>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>LSHandlerRank</key>
			<string>Alternate</string>
			<key>LSItemContentTypes</key>
			<array>
				<string>public.plain-text</string>
				<string>public.text</string>
			</array>
			<key>CFBundleTypeExtensions</key>
			<array>
				<string>txt</string>
				<string>text</string>
			</array>
		</dict>
		
		<!-- SVG文件 -->
		<dict>
			<key>CFBundleTypeName</key>
			<string>SVG Document</string>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>LSHandlerRank</key>
			<string>Owner</string>
			<key>LSItemContentTypes</key>
			<array>
				<string>public.svg-image</string>
			</array>
			<key>CFBundleTypeExtensions</key>
			<array>
				<string>svg</string>
			</array>
		</dict>
		
		<!-- HTML文件 -->
		<dict>
			<key>CFBundleTypeName</key>
			<string>HTML Document</string>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>LSHandlerRank</key>
			<string>Alternate</string>
			<key>LSItemContentTypes</key>
			<array>
				<string>public.html</string>
			</array>
			<key>CFBundleTypeExtensions</key>
			<array>
				<string>html</string>
				<string>htm</string>
			</array>
		</dict>
	</array>
	
	<!-- UTI导出定义 -->
	<key>UTExportedTypeDeclarations</key>
	<array>
		<dict>
			<key>UTTypeIdentifier</key>
			<string>net.daringfireball.markdown</string>
			<key>UTTypeDescription</key>
			<string>Markdown Document</string>
			<key>UTTypeConformsTo</key>
			<array>
				<string>public.plain-text</string>
			</array>
			<key>UTTypeTagSpecification</key>
			<dict>
				<key>public.filename-extension</key>
				<array>
					<string>md</string>
					<string>markdown</string>
				</array>
				<key>public.mime-type</key>
				<array>
					<string>text/markdown</string>
					<string>text/x-markdown</string>
				</array>
			</dict>
		</dict>
	</array>
</dict>
</plist>
