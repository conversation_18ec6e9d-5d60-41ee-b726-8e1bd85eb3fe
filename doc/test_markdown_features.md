# Markdown渲染器功能测试

## 1. 标题中的样式文本测试

### **粗体标题** 测试
### *斜体标题* 测试  
### __下划线粗体标题__ 测试
### _下划线斜体标题_ 测试
### `代码标题` 测试
### **粗体** 和 *斜体* 混合标题

## 2. 表格测试

| 姓名 | 年龄 | 职业 |
|------|------|------|
| **张三** | 25 | *程序员* |
| __李四__ | 30 | _设计师_ |
| `王五` | 28 | 产品经理 |

### 复杂表格

| 功能 | 状态 | 描述 | 优先级 |
|------|------|------|--------|
| **表格支持** | ✅ | 支持基本表格渲染 | *高* |
| __标题样式__ | ✅ | 标题中支持加粗、斜体等 | __高__ |
| `分割线` | ✅ | 支持水平分割线 | _中_ |
| 代码块 | ⚠️ | 部分支持 | 低 |

## 3. 分割线测试

这是第一段内容。

---

这是分割线后的内容。

***

另一种分割线样式。

___

第三种分割线样式。

## 4. 内联样式测试

这是一段包含 **粗体文本**、*斜体文本*、__下划线粗体__、_下划线斜体_ 和 `行内代码` 的段落。

### 混合样式

这里有 **粗体和 *嵌套斜体* 文本**，还有 `代码` 和 __下划线__ 样式。

## 5. 列表测试

### 无序列表
- 第一项包含 **粗体**
- 第二项包含 *斜体*
- 第三项包含 `代码`
- 第四项包含 __下划线粗体__

### 有序列表
1. **重要**：支持表格渲染
2. *注意*：标题中的样式文本
3. `提示`：分割线在非分块模式下显示
4. __警告__：测试所有功能

## 6. 引用测试

> 这是一个包含 **粗体** 和 *斜体* 的引用。
> 
> 引用中也可以包含 `代码` 和 __下划线__ 样式。

## 7. 代码块测试

```dart
// 这是代码块
class MarkdownRenderer {
  void render() {
    print("渲染Markdown");
  }
}
```

## 8. 分块模式测试说明

在分块模式下：
- 分割线（---）不应该被渲染为水平线
- 应该作为分块的分隔符使用

在非分块模式下：
- 分割线（---）应该被渲染为水平线
- 提供视觉分隔效果

---

## 测试结果验证

请验证以下功能：

1. ✅ 表格正确渲染，包含边框和表头样式
2. ✅ 标题中的加粗、斜体、下划线等样式正确显示
3. ✅ 分割线在非分块模式下显示为水平线
4. ✅ 分割线在分块模式下不显示（作为分隔符）
5. ✅ 内联样式（粗体、斜体、下划线、代码）正确渲染
6. ✅ 列表项中的样式文本正确显示
7. ✅ 引用中的样式文本正确显示

---

测试完成！
