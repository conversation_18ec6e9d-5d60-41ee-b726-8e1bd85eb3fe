<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>手机端Markdown编辑器 - 优化版UI设计</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            color: #333;
            overflow-x: hidden;
        }

        .app-container {
            max-width: 414px;
            margin: 0 auto;
            background: #fff;
            min-height: 100vh;
            position: relative;
        }

        /* 顶部导航栏 */
        .top-nav {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 12px 16px;
            position: sticky;
            top: 0;
            z-index: 100;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .nav-left {
            display: flex;
            align-items: center;
            gap: 12px;
        }

        .back-btn {
            background: none;
            border: none;
            color: white;
            font-size: 20px;
            cursor: pointer;
            padding: 8px;
            border-radius: 50%;
            transition: background 0.3s;
        }

        .back-btn:hover {
            background: rgba(255, 255, 255, 0.2);
        }

        .nav-title {
            font-size: 18px;
            font-weight: 600;
        }

        .nav-actions {
            display: flex;
            gap: 8px;
        }

        .nav-btn {
            background: rgba(255, 255, 255, 0.2);
            border: none;
            color: white;
            padding: 8px 12px;
            border-radius: 20px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s;
        }

        .nav-btn:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        /* 底部标签栏 */
        .bottom-tabs {
            position: fixed;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 100%;
            max-width: 414px;
            background: white;
            border-top: 1px solid #e0e0e0;
            display: flex;
            padding: 8px 0;
            box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
            z-index: 100;
        }

        .tab-item {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 8px 4px;
            border: none;
            background: none;
            cursor: pointer;
            transition: all 0.3s;
            color: #666;
        }

        .tab-item.active {
            color: #667eea;
        }

        .tab-icon {
            font-size: 20px;
            margin-bottom: 4px;
        }

        .tab-label {
            font-size: 12px;
            font-weight: 500;
        }

        /* 主内容区域 */
        .main-content {
            padding: 16px;
            padding-bottom: 80px; /* 为底部标签栏留出空间 */
        }

        /* 编辑模式 */
        .edit-mode {
            display: block;
        }

        .preview-mode {
            display: none;
        }

        .edit-mode.active {
            display: block;
        }

        .preview-mode.active {
            display: block;
        }

        /* 编辑器 */
        .editor-container {
            background: #fafafa;
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 16px;
        }

        .editor-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 12px;
        }

        .editor-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
        }

        .word-count {
            font-size: 12px;
            color: #666;
        }

        .markdown-input {
            width: 100%;
            min-height: 200px;
            border: none;
            outline: none;
            background: transparent;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 14px;
            line-height: 1.6;
            color: #333;
            resize: none;
        }

        /* 预览模式 */
        .preview-container {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 16px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .preview-title {
            font-size: 24px;
            font-weight: bold;
            color: #333;
            margin-bottom: 16px;
            text-align: center;
        }

        .preview-content {
            font-size: 16px;
            line-height: 1.6;
            color: #555;
        }

        .preview-content h2 {
            color: #667eea;
            margin: 16px 0 8px 0;
            font-size: 20px;
        }

        .preview-content p {
            margin: 8px 0;
        }

        .preview-content ul, .preview-content ol {
            margin: 8px 0;
            padding-left: 20px;
        }

        .preview-content li {
            margin: 4px 0;
        }

        .preview-content blockquote {
            border-left: 4px solid #667eea;
            padding-left: 16px;
            margin: 12px 0;
            font-style: italic;
            color: #666;
        }

        .preview-content code {
            background: #f1f3f4;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 13px;
        }

        /* 设置面板 */
        .settings-panel {
            background: white;
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 16px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .settings-section {
            margin-bottom: 20px;
        }

        .section-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 12px;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .setting-item {
            margin-bottom: 16px;
        }

        .setting-label {
            font-size: 14px;
            color: #666;
            margin-bottom: 8px;
            display: block;
        }

        .setting-input {
            width: 100%;
            padding: 12px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            font-size: 14px;
            transition: all 0.3s;
        }

        .setting-input:focus {
            border-color: #667eea;
            outline: none;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .color-picker {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }

        .color-option {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            border: 2px solid transparent;
            cursor: pointer;
            transition: all 0.3s;
        }

        .color-option.selected {
            border-color: #333;
            box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.3);
        }

        /* 浮动操作按钮 */
        .fab {
            position: fixed;
            bottom: 80px;
            right: 20px;
            width: 56px;
            height: 56px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border: none;
            color: white;
            font-size: 24px;
            cursor: pointer;
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
            transition: all 0.3s;
            z-index: 1000;
        }

        .fab:hover {
            transform: scale(1.1);
            box-shadow: 0 6px 16px rgba(102, 126, 234, 0.5);
        }

        /* 快捷工具栏 */
        .quick-toolbar {
            background: white;
            border-radius: 12px;
            padding: 12px;
            margin-bottom: 16px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .toolbar-title {
            font-size: 14px;
            font-weight: 600;
            color: #333;
            margin-bottom: 12px;
        }

        .toolbar-buttons {
            display: flex;
            gap: 8px;
            flex-wrap: wrap;
        }

        .toolbar-btn {
            padding: 8px 12px;
            border: 1px solid #e0e0e0;
            border-radius: 20px;
            background: white;
            color: #666;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s;
        }

        .toolbar-btn:hover {
            border-color: #667eea;
            color: #667eea;
        }

        /* 状态指示器 */
        .status-bar {
            position: fixed;
            top: 60px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 12px;
            z-index: 1000;
            display: none;
        }

        .status-bar.show {
            display: block;
        }

        /* 响应式设计 */
        @media (max-width: 375px) {
            .main-content {
                padding: 12px;
            }
            
            .editor-container,
            .preview-container,
            .settings-panel {
                padding: 12px;
            }
        }

        /* 动画效果 */
        @keyframes slideIn {
            from { transform: translateY(20px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }

        .slide-in {
            animation: slideIn 0.3s ease-out;
        }

        /* 深色模式支持 */
        @media (prefers-color-scheme: dark) {
            body {
                background: #1a1a1a;
                color: #fff;
            }
            
            .app-container {
                background: #2a2a2a;
            }
            
            .editor-container {
                background: #333;
            }
            
            .preview-container,
            .settings-panel,
            .quick-toolbar {
                background: #333;
                color: #fff;
            }
            
            .markdown-input {
                color: #fff;
            }
            
            .setting-input {
                background: #444;
                border-color: #555;
                color: #fff;
            }
        }
    </style>
</head>
<body>
    <div class="app-container">
        <!-- 顶部导航栏 -->
        <div class="top-nav">
            <div class="nav-left">
                <button class="back-btn">←</button>
                <span class="nav-title">Markdown编辑器</span>
            </div>
            <div class="nav-actions">
                <button class="nav-btn">💾</button>
                <button class="nav-btn">📤</button>
            </div>
        </div>

        <!-- 主内容区域 -->
        <div class="main-content">
            <!-- 编辑模式 -->
            <div class="edit-mode active">
                <!-- 快捷工具栏 -->
                <div class="quick-toolbar">
                    <div class="toolbar-title">快捷操作</div>
                    <div class="toolbar-buttons">
                        <button class="toolbar-btn">**粗体**</button>
                        <button class="toolbar-btn">*斜体*</button>
                        <button class="toolbar-btn">[链接]</button>
                        <button class="toolbar-btn">![图片]</button>
                        <button class="toolbar-btn">- 列表</button>
                        <button class="toolbar-btn">1. 列表</button>
                        <button class="toolbar-btn">> 引用</button>
                        <button class="toolbar-btn">```代码```</button>
                    </div>
                </div>

                <!-- 编辑器 -->
                <div class="editor-container">
                    <div class="editor-header">
                        <div class="editor-title">编辑内容</div>
                        <div class="word-count">0 字</div>
                    </div>
                    <textarea class="markdown-input" placeholder="开始编写您的Markdown内容..."># 欢迎使用手机端Markdown编辑器

这是一个专为手机端设计的Markdown编辑器，提供了简洁直观的用户界面。

## 主要特性

- 📱 **手机优化** - 专为触屏操作设计
- ⚡ **快速编辑** - 快捷工具栏一键插入
- 👀 **实时预览** - 所见即所得的编辑体验
- 🎨 **丰富模板** - 多种预设模板可选
- 💧 **水印功能** - 支持自定义水印
- 🧩 **分块管理** - 智能内容分块

## 使用方法

1. 在编辑区域输入Markdown内容
2. 使用快捷工具栏快速插入格式
3. 切换到预览模式查看效果
4. 在设置中调整样式和模板

> 💡 **提示**: 使用快捷工具栏可以快速插入常用格式

## 快捷键

| 功能 | 操作 |
|------|------|
| 粗体 | 选中文本后点击粗体按钮 |
| 斜体 | 选中文本后点击斜体按钮 |
| 链接 | 点击链接按钮插入链接格式 |
| 图片 | 点击图片按钮插入图片格式 |

## 代码示例

```javascript
function helloMobile() {
    console.log("Hello, Mobile Markdown!");
}
```</textarea>
                </div>
            </div>

            <!-- 预览模式 -->
            <div class="preview-mode">
                <div class="preview-container">
                    <h1 class="preview-title">欢迎使用手机端Markdown编辑器</h1>
                    <div class="preview-content">
                        <p>这是一个专为手机端设计的Markdown编辑器，提供了简洁直观的用户界面。</p>
                        
                        <h2>主要特性</h2>
                        <ul>
                            <li><strong>手机优化</strong> - 专为触屏操作设计</li>
                            <li><strong>快速编辑</strong> - 快捷工具栏一键插入</li>
                            <li><strong>实时预览</strong> - 所见即所得的编辑体验</li>
                            <li><strong>丰富模板</strong> - 多种预设模板可选</li>
                            <li><strong>水印功能</strong> - 支持自定义水印</li>
                            <li><strong>分块管理</strong> - 智能内容分块</li>
                        </ul>
                        
                        <h2>使用方法</h2>
                        <ol>
                            <li>在编辑区域输入Markdown内容</li>
                            <li>使用快捷工具栏快速插入格式</li>
                            <li>切换到预览模式查看效果</li>
                            <li>在设置中调整样式和模板</li>
                        </ol>
                        
                        <blockquote>
                            <p><strong>提示</strong>: 使用快捷工具栏可以快速插入常用格式</p>
                        </blockquote>

                        <h2>快捷键</h2>
                        <table>
                            <tr><th>功能</th><th>操作</th></tr>
                            <tr><td>粗体</td><td>选中文本后点击粗体按钮</td></tr>
                            <tr><td>斜体</td><td>选中文本后点击斜体按钮</td></tr>
                            <tr><td>链接</td><td>点击链接按钮插入链接格式</td></tr>
                            <tr><td>图片</td><td>点击图片按钮插入图片格式</td></tr>
                        </table>

                        <h2>代码示例</h2>
                        <pre><code>function helloMobile() {
    console.log("Hello, Mobile Markdown!");
}</code></pre>
                    </div>
                </div>
            </div>

            <!-- 设置面板 -->
            <div class="settings-panel" style="display: none;">
                <!-- 模板设置 -->
                <div class="settings-section">
                    <div class="section-title">
                        <span>🎨</span>
                        模板选择
                    </div>
                    <div class="setting-item">
                        <label class="setting-label">当前模板</label>
                        <select class="setting-input">
                            <option>现代简约</option>
                            <option>商务专业</option>
                            <option>创意艺术</option>
                            <option>学术论文</option>
                        </select>
                    </div>
                </div>

                <!-- 样式设置 -->
                <div class="settings-section">
                    <div class="section-title">
                        <span>🎯</span>
                        样式设置
                    </div>
                    <div class="setting-item">
                        <label class="setting-label">字体大小</label>
                        <input type="range" class="setting-input" min="12" max="20" value="14">
                    </div>
                    <div class="setting-item">
                        <label class="setting-label">主题色彩</label>
                        <div class="color-picker">
                            <div class="color-option selected" style="background: #667eea;"></div>
                            <div class="color-option" style="background: #ff6b6b;"></div>
                            <div class="color-option" style="background: #51cf66;"></div>
                            <div class="color-option" style="background: #ffd43b;"></div>
                            <div class="color-option" style="background: #ae3ec9;"></div>
                            <div class="color-option" style="background: #fd7e14;"></div>
                        </div>
                    </div>
                </div>

                <!-- 水印设置 -->
                <div class="settings-section">
                    <div class="section-title">
                        <span>💧</span>
                        水印设置
                    </div>
                    <div class="setting-item">
                        <label class="setting-label">水印文本</label>
                        <input type="text" class="setting-input" placeholder="输入水印文本">
                    </div>
                    <div class="setting-item">
                        <label class="setting-label">水印透明度</label>
                        <input type="range" class="setting-input" min="0" max="100" value="30">
                    </div>
                </div>

                <!-- 分块设置 -->
                <div class="settings-section">
                    <div class="section-title">
                        <span>🧩</span>
                        分块设置
                    </div>
                    <div class="setting-item">
                        <label class="setting-label">启用分块模式</label>
                        <input type="checkbox" class="setting-input" style="width: auto;">
                    </div>
                    <div class="setting-item">
                        <label class="setting-label">分块大小</label>
                        <select class="setting-input">
                            <option>小 (200字)</option>
                            <option>中 (300字)</option>
                            <option>大 (500字)</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>

        <!-- 底部标签栏 -->
        <div class="bottom-tabs">
            <button class="tab-item active" data-tab="edit">
                <div class="tab-icon">✏️</div>
                <div class="tab-label">编辑</div>
            </button>
            <button class="tab-item" data-tab="preview">
                <div class="tab-icon">👀</div>
                <div class="tab-label">预览</div>
            </button>
            <button class="tab-item" data-tab="settings">
                <div class="tab-icon">⚙️</div>
                <div class="tab-label">设置</div>
            </button>
            <button class="tab-item" data-tab="templates">
                <div class="tab-icon">🎨</div>
                <div class="tab-label">模板</div>
            </button>
        </div>

        <!-- 浮动操作按钮 -->
        <button class="fab" title="更多操作">+</button>

        <!-- 状态指示器 -->
        <div class="status-bar">
            <span>已保存</span>
        </div>
    </div>

    <script>
        // 标签页切换
        document.querySelectorAll('.tab-item').forEach(tab => {
            tab.addEventListener('click', function() {
                const targetTab = this.getAttribute('data-tab');
                
                // 更新标签页状态
                document.querySelectorAll('.tab-item').forEach(t => t.classList.remove('active'));
                this.classList.add('active');
                
                // 切换内容显示
                document.querySelectorAll('.edit-mode, .preview-mode, .settings-panel').forEach(content => {
                    content.style.display = 'none';
                });
                
                if (targetTab === 'edit') {
                    document.querySelector('.edit-mode').style.display = 'block';
                } else if (targetTab === 'preview') {
                    document.querySelector('.preview-mode').style.display = 'block';
                } else if (targetTab === 'settings') {
                    document.querySelector('.settings-panel').style.display = 'block';
                } else if (targetTab === 'templates') {
                    document.querySelector('.settings-panel').style.display = 'block';
                }
            });
        });

        // 快捷工具栏按钮
        document.querySelectorAll('.toolbar-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                const textarea = document.querySelector('.markdown-input');
                const text = this.textContent;
                
                // 插入文本到光标位置
                const start = textarea.selectionStart;
                const end = textarea.selectionEnd;
                const selectedText = textarea.value.substring(start, end);
                
                let insertText = text;
                if (selectedText) {
                    if (text.includes('**')) {
                        insertText = `**${selectedText}**`;
                    } else if (text.includes('*')) {
                        insertText = `*${selectedText}*`;
                    } else if (text.includes('[')) {
                        insertText = `[${selectedText}](链接地址)`;
                    } else if (text.includes('![')) {
                        insertText = `![${selectedText}](图片地址)`;
                    } else {
                        insertText = text;
                    }
                }
                
                textarea.value = textarea.value.substring(0, start) + insertText + textarea.value.substring(end);
                textarea.focus();
                
                // 更新字数统计
                updateWordCount();
            });
        });

        // 字数统计
        function updateWordCount() {
            const textarea = document.querySelector('.markdown-input');
            const wordCount = textarea.value.length;
            document.querySelector('.word-count').textContent = `${wordCount} 字`;
        }

        // 监听输入事件
        document.querySelector('.markdown-input').addEventListener('input', updateWordCount);

        // 颜色选择器
        document.querySelectorAll('.color-option').forEach(option => {
            option.addEventListener('click', function() {
                document.querySelectorAll('.color-option').forEach(o => o.classList.remove('selected'));
                this.classList.add('selected');
            });
        });

        // 浮动按钮
        document.querySelector('.fab').addEventListener('click', function() {
            // 显示更多操作菜单
            showStatus('更多操作');
        });

        // 显示状态消息
        function showStatus(message) {
            const statusBar = document.querySelector('.status-bar');
            statusBar.textContent = message;
            statusBar.classList.add('show');
            
            setTimeout(() => {
                statusBar.classList.remove('show');
            }, 2000);
        }

        // 模拟自动保存
        setInterval(() => {
            showStatus('已自动保存');
        }, 30000); // 每30秒自动保存

        // 初始化字数统计
        updateWordCount();
    </script>
</body>
</html>
