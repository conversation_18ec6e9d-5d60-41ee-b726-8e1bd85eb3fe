# 导出选项对话框改进说明

## 问题分析

原来的导出选项对话框存在以下问题：

1. **用户体验差**：用户无法预览不同尺寸的效果
2. **配置复杂**：图片质量调整选项不必要，默认就是最佳质量
3. **信息不清晰**：预览信息显示的是代码而不是用户友好的信息
4. **功能缺失**：缺少文件大小估算和适用场景建议

## 改进方案

### 1. 添加预览功能
- 实时预览不同尺寸的效果
- 显示宽高比的可视化表示
- 直观展示最终导出效果

### 2. 简化配置选项
- 移除图片质量调整（固定为最佳质量100%）
- 重点配置宽高比和像素值
- 保留水印开关，但简化描述

### 3. 改进信息显示
- 显示详细的导出参数（尺寸、比例、文件大小）
- 添加文件大小估算功能
- 提供适用场景建议
- 使用用户友好的语言描述

### 4. 现代化UI设计
- 采用Material Design 3设计语言
- 改进布局和视觉层次
- 优化交互体验

## 技术实现

### 模型更新
- 扩展`ExportConfig`模型，添加文件大小估算
- 添加适用场景判断逻辑
- 支持自定义尺寸配置

### 国际化支持
- 添加新的国际化键值
- 支持中文、英文、日文三种语言
- 统一术语和描述

### 组件重构
- 重新设计对话框布局
- 分离预览、信息、配置三个区域
- 改进状态管理和交互逻辑

## 新增功能

### 1. 预览效果区域
```dart
/// 构建预览效果区域
Widget _buildPreviewSection() {
  final dimensions = _config.getDimensions();
  final aspectRatio = dimensions.aspectRatio;
  
  return Column(
    crossAxisAlignment: CrossAxisAlignment.start,
    children: [
      Text(_l10n.textCardPreviewEffect),
      // 预览容器
      Container(
        child: Center(
          child: Container(
            width: 80,
            height: 80 / aspectRatio,
            // 显示尺寸比例
          ),
        ),
      ),
    ],
  );
}
```

### 2. 导出信息区域
```dart
/// 构建导出信息区域
Widget _buildExportInfoSection() {
  final dimensions = _config.getDimensions();
  final fileSize = _config.getEstimatedFileSize();
  final usageScenario = _config.getUsageScenario();
  
  return Column(
    children: [
      _buildInfoRow(_l10n.textCardImageDimensions, dimensions.displayText),
      _buildInfoRow(_l10n.textCardAspectRatio, '${dimensions.aspectRatio.toStringAsFixed(2)}:1'),
      _buildInfoRow(_l10n.textCardFileSize, '${fileSize.toStringAsFixed(1)} KB'),
      _buildInfoRow(_l10n.textCardUsageScenario, usageScenario),
    ],
  );
}
```

### 3. 文件大小估算
```dart
/// 估算文件大小（KB）
double getEstimatedFileSize() {
  final dimensions = getDimensions();
  final pixels = dimensions.width * dimensions.height;
  final qualityFactor = quality / 100.0;
  
  // 基于经验公式估算文件大小
  final bytesPerPixel = 0.2 * qualityFactor;
  final estimatedBytes = pixels * bytesPerPixel;
  
  return estimatedBytes / 1024.0; // 转换为KB
}
```

### 4. 适用场景建议
```dart
/// 获取适用场景描述
String getUsageScenario() {
  final fileSize = getEstimatedFileSize();
  
  if (fileSize < 100) {
    return '适合快速分享，文件小';
  } else if (fileSize < 500) {
    return '适合社交媒体，清晰度好';
  } else if (fileSize < 1000) {
    return '适合高清分享，细节丰富';
  } else {
    return '适合专业用途，打印质量';
  }
}
```

## 国际化键值

新增的国际化键值：

```json
{
  "textCardPreviewEffect": "预览效果",
  "textCardExportInfo": "导出信息", 
  "textCardImageDimensions": "图片尺寸",
  "textCardAspectRatio": "宽高比",
  "textCardFileSize": "文件大小",
  "textCardUsageScenario": "适用场景",
  "textCardBestQuality": "最佳质量 (100%)",
  "textCardWatermarkDescription": "在图片底部添加应用标识"
}
```

## 测试覆盖

创建了完整的测试用例：

1. **预览功能测试**：验证预览效果区域正确显示
2. **配置更新测试**：验证尺寸和比例选择功能
3. **信息显示测试**：验证文件大小和适用场景信息
4. **交互测试**：验证用户交互和状态更新

## 使用示例

```dart
// 显示导出选项对话框
final result = await showDialog<ExportConfig>(
  context: context,
  builder: (context) => ExportOptionsDialog(initialConfig: config),
);

if (result != null) {
  // 使用更新后的配置
  print('选择的尺寸: ${result.size.label}');
  print('选择的比例: ${result.ratio.label}');
  print('预估文件大小: ${result.getEstimatedFileSize().toStringAsFixed(1)} KB');
}
```

## 总结

通过这次改进，导出选项对话框变得更加：

1. **用户友好**：提供直观的预览和清晰的信息
2. **功能完善**：添加文件大小估算和场景建议
3. **设计现代**：采用最新的Material Design 3设计
4. **易于维护**：代码结构清晰，测试覆盖完整

这些改进显著提升了用户体验，使导出功能更加专业和易用。
