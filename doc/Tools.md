# 工具/流程 使用说明

## 发布前检查

### 1. 代码质量检查
```shell
flutter analyze
flutter test
```

### 2. 依赖检查
```shell
flutter pub deps --no-dev
flutter pub outdated
```

## 构建正式包

### Android APK
```shell
# 标准APK
flutter build apk --release --obfuscate --split-debug-info=./symbols

# 分架构APK（减小体积）
flutter build apk --release --split-per-abi --obfuscate --split-debug-info=./symbols

# AAB格式（Google Play推荐）
flutter build appbundle --release --obfuscate --split-debug-info=./symbols
```

### iOS正式包
```shell
flutter build ios --release --obfuscate --split-debug-info=./symbols
```

## Android签名配置

### 1. 生成签名密钥
```shell
keytool -genkey -v -keystore ~/upload-keystore.jks -keyalg RSA -keysize 2048 -validity 10000 -alias upload
```

### 2. 创建key.properties文件
在android目录下创建key.properties文件：
```
storePassword=你的密钥库密码
keyPassword=你的密钥密码
keyAlias=upload
storeFile=/Users/<USER>/upload-keystore.jks
```

## 版本管理

### 更新版本号
在pubspec.yaml中更新：
```yaml
version: 1.0.1+2  # 版本名+构建号
```

### Git标签
```shell
git tag v1.0.1
git push origin v1.0.1
```

## 测试命令

### 性能测试
```shell
flutter run --profile
flutter run --release
```

### 设备测试
```shell
flutter devices
flutter run -d <device-id>
```

