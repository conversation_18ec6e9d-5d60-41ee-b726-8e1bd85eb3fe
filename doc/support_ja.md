# ContentPal 技術サポート（日本語）

バージョン: 1.0 | 施行日: 2025-08-13

## 基本情報

- アプリ名: ContentPal
- 開発者サイト: <https://www.jiangkang.tech>
- サポートメール: <<EMAIL>>
- 対応プラットフォーム: iOS / iPadOS / macOS / Android / Web / デスクトップ

## 機能概要

- テキストカード: テンプレート、バッチ生成、HD 書き出し
- Markdown: ライブプレビュー、テーマ、スクリーンショット書き出し、写真/ファイル保存
- 集客画像: テンプレート、カラー/テクスチャ/ノイズ効果、HD 書き出し
- PDF: 閲覧および基本的なセキュリティ（今後変更の可能性あり）
- サブスクリプション: 月間/年間/永久ライセンス、購入の復元対応

注: 処理は主に端末内で実行されます。写真への保存には「写真」権限が必要です。

## はじめに

1. App Store からインストール。
2. 初回の書き出し時、表示に従い写真アクセスを許可。
3. テキストカード・Markdown・集客画像から作成を開始。

## サブスクリプションと購入

- 特典: HD/透かし無しの書き出し、バッチ処理、高度なテンプレート/ツール。
- 復元: アプリ → サブスクリプション画面 → 「購入を復元」（購入時と同じ Apple ID を利用）。
- 請求と払い戻し: Apple により処理。Apple のポリシーに従います。払い戻しは Apple サポートへお問い合わせください。

## FAQ

- 写真に保存できない: iOS 設定 → プライバシーとセキュリティ → 写真 で本アプリのアクセスを許可してください。
- 購入の復元に失敗: ネットワーク・Apple ID・購入記録をご確認ください。解決しない場合はご連絡ください。
- 書き出し品質: 高解像度を選択する、または文章を適度に短くしてください。

## トラブルシューティング

- 最新版へアップデート。
- サブスクリプション画面の診断（利用可能な場合）。
- 書き出し失敗時: アプリ再起動、権限確認、ストレージ確保、解像度を下げて再試行。

## 権限とデータ（概要）

- 写真: 生成画像をフォトライブラリへ保存。
- ローカル保存（Hive）: 設定、サブスクリプション状態、ローカル利用回数。
- 個人を特定できる情報は収集しません。クロスアプリ追跡は行いません。

詳細は「プライバシーポリシー」をご確認ください。

## 連絡先

- メール: <<EMAIL>>
- ウェブサイト: <https://www.jiangkang.tech>
