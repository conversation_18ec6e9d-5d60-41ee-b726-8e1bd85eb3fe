# Markdown 分块功能重新设计说明

## 概述

本次重新设计针对 Markdown 渲染屏幕中的分块 tab 进行了全面的用户界面和逻辑优化，旨在提供更直观、更易用的分块功能体验。

## 主要问题分析

### 原有问题
1. **用户体验不够直观**：分块功能隐藏在子标签页中，用户难以快速理解和使用
2. **功能分散**：配置、管理、预览分散在不同标签页，操作流程不连贯
3. **缺乏实时反馈**：用户修改配置后无法立即看到效果
4. **管理功能复杂**：分块管理界面过于复杂，用户难以快速操作
5. **缺乏智能提示**：没有引导用户如何使用分块功能

## 重新设计方案

### 1. 单页面布局设计
- **智能配置区域**：提供预设配置和快速切换
- **实时预览区域**：显示分块效果
- **分块管理区域**：简化的分块操作
- **导出选项区域**：快速导出分块内容

### 2. 智能配置系统

#### 预设配置模式
- **文章模式**：按标题自动分块，适合长文章
  - 支持 # 和 ## 标题分块
  - 显示分块标题
  - 无边框设计
  
- **卡片模式**：按分隔符分块，适合卡片式内容
  - 使用 `---` 分隔符
  - 显示边框
  - 紧凑布局
  
- **混合模式**：同时支持标题和分隔符，灵活分块
  - 支持多种分隔方式
  - 显示标题和边框
  - 平衡的间距

#### 高级设置
- 分隔符设置：按一级/二级标题分隔
- 外观设置：分块间距、标题显示、边框显示
- 实时配置更新

### 3. 实时预览系统
- **即时反馈**：配置修改后立即显示效果
- **分块统计**：显示当前分块数量
- **可视化标识**：不同类型分块使用不同颜色和图标

### 4. 简化的分块管理
- **快速操作**：显示全部、隐藏全部、复制内容
- **分块摘要**：显示总分块数、可见数、隐藏数
- **详细管理**：通过对话框提供完整的管理功能

### 5. 导出功能集成
- **多种格式**：Markdown、图片、PDF、分享
- **一键操作**：快速导出当前分块内容
- **批量处理**：支持选中分块导出

## 技术实现

### 1. 组件结构优化
```dart
// 主要组件
- MarkdownRenderTabs (主容器)
  - _buildSmartConfigSection() (智能配置)
  - _buildPreviewSection() (实时预览)
  - _buildBlockManagementSection() (分块管理)
  - _buildExportSection() (导出选项)
```

### 2. 配置系统
```dart
class BlockRenderConfig {
  final bool enabled;
  final BlockMode mode;
  final bool splitByH1;
  final bool splitByH2;
  final double blockSpacing;
  final bool showBlockTitles;
  final bool showBlockBorders;
  final String customSeparatorPattern;
}
```

### 3. 渲染优化
- 支持分块标题显示
- 支持分块边框
- 不同类型分块使用不同颜色标识
- 响应式布局适配

## 用户体验改进

### 1. 引导式设计
- **使用指南**：未启用时显示详细的使用说明
- **示例代码**：提供各种模式的 Markdown 示例
- **智能提示**：操作过程中的友好提示

### 2. 视觉优化
- **卡片式布局**：清晰的功能分区
- **颜色编码**：不同类型分块使用不同颜色
- **图标标识**：直观的功能识别

### 3. 交互优化
- **一键切换**：快速启用/禁用分块功能
- **实时预览**：配置修改即时生效
- **批量操作**：支持多选和批量处理

## 功能特性

### 1. 智能分块
- 自动识别标题结构
- 支持自定义分隔符
- 灵活的分块模式

### 2. 可视化管理
- 分块列表视图
- 搜索和筛选功能
- 排序和统计

### 3. 导出选项
- Markdown 格式导出
- 图片格式导出
- PDF 格式导出（开发中）
- 内容分享功能

## 使用场景

### 1. 长文章处理
- 适合：技术文档、学术论文、长篇小说
- 模式：文章模式
- 优势：按章节自动分块，便于阅读和管理

### 2. 卡片式内容
- 适合：知识卡片、学习笔记、要点总结
- 模式：卡片模式
- 优势：清晰的分隔，便于复习和分享

### 3. 混合内容
- 适合：教程、指南、混合文档
- 模式：混合模式
- 优势：灵活的分块策略，适应复杂内容

## 未来规划

### 1. 功能扩展
- PDF 导出功能完善
- 更多分隔符支持
- 自定义分块规则

### 2. 性能优化
- 大文档分块性能优化
- 实时预览性能提升
- 内存使用优化

### 3. 用户体验
- 拖拽排序功能
- 分块合并/拆分
- 模板系统

## 总结

通过本次重新设计，分块功能从原来的复杂、分散的界面转变为直观、易用的单页面体验。用户可以通过预设配置快速开始使用，通过实时预览了解效果，通过简化的管理功能控制分块，最终通过集成的导出功能完成内容输出。整个流程更加流畅，用户体验得到显著提升。
