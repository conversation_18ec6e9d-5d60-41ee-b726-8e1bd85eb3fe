<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Markdown编辑器 - 优化版UI设计</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: #333;
        }

        .app-container {
            display: flex;
            height: 100vh;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
        }

        /* 左侧主编辑区域 */
        .main-editor {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: #fff;
            border-radius: 20px;
            margin: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
        }

        /* 顶部工具栏 */
        .toolbar {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 20px 30px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            position: relative;
        }

        .toolbar-left {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .logo {
            font-size: 24px;
            font-weight: bold;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .view-mode-toggle {
            display: flex;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 25px;
            padding: 5px;
        }

        .view-mode-btn {
            padding: 8px 16px;
            border: none;
            background: transparent;
            color: white;
            border-radius: 20px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .view-mode-btn.active {
            background: rgba(255, 255, 255, 0.3);
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .toolbar-right {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .action-btn {
            padding: 10px 15px;
            border: none;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border-radius: 25px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
        }

        .action-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            transform: translateY(-2px);
        }

        .primary-btn {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            box-shadow: 0 4px 15px rgba(255, 107, 107, 0.4);
        }

        /* 标签页导航 */
        .tab-navigation {
            display: flex;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
            position: relative;
        }

        .tab-item {
            padding: 15px 25px;
            border: none;
            background: transparent;
            cursor: pointer;
            position: relative;
            font-weight: 500;
            color: #6c757d;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .tab-item.active {
            color: #667eea;
        }

        .tab-item.active::after {
            content: '';
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            border-radius: 3px 3px 0 0;
        }

        /* 主内容区域 */
        .content-area {
            flex: 1;
            display: flex;
            overflow: hidden;
        }

        /* 编辑区域 */
        .editor-panel {
            flex: 1;
            display: flex;
            flex-direction: column;
            background: #fff;
        }

        .editor-header {
            padding: 20px 30px;
            border-bottom: 1px solid #e9ecef;
            background: #fafbfc;
        }

        .editor-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
        }

        .editor-subtitle {
            font-size: 14px;
            color: #6c757d;
        }

        .editor-content {
            flex: 1;
            padding: 30px;
            overflow-y: auto;
        }

        .markdown-input {
            width: 100%;
            min-height: 400px;
            border: none;
            outline: none;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 14px;
            line-height: 1.6;
            color: #333;
            background: transparent;
            resize: none;
        }

        /* 预览区域 */
        .preview-panel {
            flex: 1;
            background: #fafbfc;
            border-left: 1px solid #e9ecef;
            overflow-y: auto;
            padding: 30px;
        }

        .preview-content {
            background: white;
            border-radius: 15px;
            padding: 40px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }

        .preview-title {
            font-size: 28px;
            font-weight: bold;
            color: #333;
            margin-bottom: 20px;
            text-align: center;
        }

        .preview-text {
            font-size: 16px;
            line-height: 1.8;
            color: #555;
        }

        .preview-text h2 {
            color: #667eea;
            margin: 20px 0 10px 0;
        }

        .preview-text ul, .preview-text ol {
            margin: 10px 0;
            padding-left: 20px;
        }

        .preview-text li {
            margin: 5px 0;
        }

        .preview-text blockquote {
            border-left: 4px solid #667eea;
            padding-left: 20px;
            margin: 20px 0;
            font-style: italic;
            color: #666;
        }

        .preview-text code {
            background: #f1f3f4;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 13px;
        }

        /* 右侧设置面板 */
        .settings-panel {
            width: 350px;
            background: #fff;
            border-radius: 20px;
            margin: 20px 20px 20px 0;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            overflow: hidden;
            display: flex;
            flex-direction: column;
        }

        .settings-header {
            padding: 25px 30px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            font-size: 18px;
            font-weight: 600;
        }

        .settings-content {
            flex: 1;
            overflow-y: auto;
            padding: 20px;
        }

        .setting-section {
            margin-bottom: 30px;
        }

        .section-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .setting-item {
            margin-bottom: 20px;
        }

        .setting-label {
            font-size: 14px;
            color: #666;
            margin-bottom: 8px;
            display: block;
        }

        .setting-input {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .setting-input:focus {
            border-color: #667eea;
            outline: none;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .color-picker {
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .color-option {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            border: 3px solid transparent;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .color-option:hover {
            transform: scale(1.1);
        }

        .color-option.selected {
            border-color: #333;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.3);
        }

        /* 浮动工具栏 */
        .floating-toolbar {
            position: fixed;
            bottom: 30px;
            right: 30px;
            background: white;
            border-radius: 25px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.2);
            padding: 15px;
            display: flex;
            gap: 10px;
            z-index: 1000;
        }

        .floating-btn {
            width: 50px;
            height: 50px;
            border: none;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
        }

        .floating-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        }

        /* 响应式设计 */
        @media (max-width: 1200px) {
            .settings-panel {
                width: 300px;
            }
        }

        @media (max-width: 768px) {
            .app-container {
                flex-direction: column;
            }
            
            .settings-panel {
                width: 100%;
                margin: 0 20px 20px 20px;
            }
            
            .content-area {
                flex-direction: column;
            }
            
            .preview-panel {
                border-left: none;
                border-top: 1px solid #e9ecef;
            }
        }

        /* 动画效果 */
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .fade-in {
            animation: fadeIn 0.5s ease-out;
        }

        /* 滚动条样式 */
        ::-webkit-scrollbar {
            width: 8px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #a8a8a8;
        }

        /* 状态指示器 */
        .status-indicator {
            position: fixed;
            top: 20px;
            right: 20px;
            background: rgba(255, 255, 255, 0.9);
            padding: 10px 15px;
            border-radius: 20px;
            font-size: 12px;
            color: #666;
            backdrop-filter: blur(10px);
            z-index: 1000;
        }

        .status-dot {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #51cf66;
            margin-right: 8px;
            animation: pulse 2s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.5; }
            100% { opacity: 1; }
        }
    </style>
</head>
<body>
    <div class="app-container fade-in">
        <!-- 主编辑区域 -->
        <div class="main-editor">
            <!-- 顶部工具栏 -->
            <div class="toolbar">
                <div class="toolbar-left">
                    <div class="logo">
                        <span>📝</span>
                        <span>Markdown编辑器</span>
                    </div>
                    <div class="view-mode-toggle">
                        <button class="view-mode-btn active">编辑</button>
                        <button class="view-mode-btn">预览</button>
                        <button class="view-mode-btn">分屏</button>
                    </div>
                </div>
                <div class="toolbar-right">
                    <button class="action-btn">
                        <span>💾</span>
                        保存
                    </button>
                    <button class="action-btn">
                        <span>📤</span>
                        导出
                    </button>
                    <button class="action-btn primary-btn">
                        <span>🤖</span>
                        AI助手
                    </button>
                </div>
            </div>

            <!-- 标签页导航 -->
            <div class="tab-navigation">
                <button class="tab-item active">
                    <span>✏️</span>
                    编辑
                </button>
                <button class="tab-item">
                    <span>🎨</span>
                    模板
                </button>
                <button class="tab-item">
                    <span>🎯</span>
                    样式
                </button>
                <button class="tab-item">
                    <span>💧</span>
                    水印
                </button>
                <button class="tab-item">
                    <span>🧩</span>
                    分块
                </button>
            </div>

            <!-- 主内容区域 -->
            <div class="content-area">
                <!-- 编辑面板 -->
                <div class="editor-panel">
                    <div class="editor-header">
                        <div class="editor-title">我的文档</div>
                        <div class="editor-subtitle">最后编辑于 2分钟前</div>
                    </div>
                    <div class="editor-content">
                        <textarea class="markdown-input" placeholder="开始编写您的Markdown内容..."># 欢迎使用Markdown编辑器

这是一个现代化的Markdown编辑器，提供了丰富的功能和优雅的用户界面。

## 主要特性

- ✨ **现代化设计** - 简洁美观的界面
- 🚀 **实时预览** - 所见即所得的编辑体验
- 🎨 **丰富模板** - 多种预设模板可选
- 💧 **水印功能** - 支持自定义水印
- 🧩 **分块管理** - 智能内容分块
- 🤖 **AI助手** - 智能写作辅助

## 使用方法

1. 在左侧编辑区域输入Markdown内容
2. 在右侧设置面板调整样式和模板
3. 实时预览渲染效果
4. 导出或分享您的内容

> 💡 **提示**: 使用快捷键 `Ctrl+S` 快速保存文档

## 快捷键

| 功能 | 快捷键 |
|------|--------|
| 保存 | Ctrl+S |
| 撤销 | Ctrl+Z |
| 重做 | Ctrl+Y |
| 加粗 | Ctrl+B |
| 斜体 | Ctrl+I |

## 代码示例

```javascript
function helloWorld() {
    console.log("Hello, Markdown!");
}
```</textarea>
                    </div>
                </div>

                <!-- 预览面板 -->
                <div class="preview-panel">
                    <div class="preview-content">
                        <h1 class="preview-title">欢迎使用Markdown编辑器</h1>
                        <div class="preview-text">
                            <p>这是一个现代化的Markdown编辑器，提供了丰富的功能和优雅的用户界面。</p>
                            
                            <h2>主要特性</h2>
                            <ul>
                                <li><strong>现代化设计</strong> - 简洁美观的界面</li>
                                <li><strong>实时预览</strong> - 所见即所得的编辑体验</li>
                                <li><strong>丰富模板</strong> - 多种预设模板可选</li>
                                <li><strong>水印功能</strong> - 支持自定义水印</li>
                                <li><strong>分块管理</strong> - 智能内容分块</li>
                                <li><strong>AI助手</strong> - 智能写作辅助</li>
                            </ul>
                            
                            <h2>使用方法</h2>
                            <ol>
                                <li>在左侧编辑区域输入Markdown内容</li>
                                <li>在右侧设置面板调整样式和模板</li>
                                <li>实时预览渲染效果</li>
                                <li>导出或分享您的内容</li>
                            </ol>
                            
                            <blockquote>
                                <p><strong>提示</strong>: 使用快捷键 <code>Ctrl+S</code> 快速保存文档</p>
                            </blockquote>

                            <h2>快捷键</h2>
                            <table>
                                <tr><th>功能</th><th>快捷键</th></tr>
                                <tr><td>保存</td><td>Ctrl+S</td></tr>
                                <tr><td>撤销</td><td>Ctrl+Z</td></tr>
                                <tr><td>重做</td><td>Ctrl+Y</td></tr>
                                <tr><td>加粗</td><td>Ctrl+B</td></tr>
                                <tr><td>斜体</td><td>Ctrl+I</td></tr>
                            </table>

                            <h2>代码示例</h2>
                            <pre><code>function helloWorld() {
    console.log("Hello, Markdown!");
}</code></pre>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 右侧设置面板 -->
        <div class="settings-panel">
            <div class="settings-header">
                <span>⚙️</span>
                设置面板
            </div>
            <div class="settings-content">
                <!-- 模板设置 -->
                <div class="setting-section">
                    <div class="section-title">
                        <span>🎨</span>
                        模板选择
                    </div>
                    <div class="setting-item">
                        <label class="setting-label">当前模板</label>
                        <select class="setting-input">
                            <option>现代简约</option>
                            <option>商务专业</option>
                            <option>创意艺术</option>
                            <option>学术论文</option>
                        </select>
                    </div>
                </div>

                <!-- 样式设置 -->
                <div class="setting-section">
                    <div class="section-title">
                        <span>🎯</span>
                        样式设置
                    </div>
                    <div class="setting-item">
                        <label class="setting-label">字体大小</label>
                        <input type="range" class="setting-input" min="12" max="24" value="16">
                    </div>
                    <div class="setting-item">
                        <label class="setting-label">主题色彩</label>
                        <div class="color-picker">
                            <div class="color-option selected" style="background: #667eea;"></div>
                            <div class="color-option" style="background: #ff6b6b;"></div>
                            <div class="color-option" style="background: #51cf66;"></div>
                            <div class="color-option" style="background: #ffd43b;"></div>
                            <div class="color-option" style="background: #ae3ec9;"></div>
                            <div class="color-option" style="background: #fd7e14;"></div>
                        </div>
                    </div>
                </div>

                <!-- 水印设置 -->
                <div class="setting-section">
                    <div class="section-title">
                        <span>💧</span>
                        水印设置
                    </div>
                    <div class="setting-item">
                        <label class="setting-label">水印文本</label>
                        <input type="text" class="setting-input" placeholder="输入水印文本">
                    </div>
                    <div class="setting-item">
                        <label class="setting-label">水印透明度</label>
                        <input type="range" class="setting-input" min="0" max="100" value="30">
                    </div>
                </div>

                <!-- 分块设置 -->
                <div class="setting-section">
                    <div class="section-title">
                        <span>🧩</span>
                        分块设置
                    </div>
                    <div class="setting-item">
                        <label class="setting-label">启用分块模式</label>
                        <input type="checkbox" class="setting-input" style="width: auto;">
                    </div>
                    <div class="setting-item">
                        <label class="setting-label">分块大小</label>
                        <select class="setting-input">
                            <option>小 (300字)</option>
                            <option>中 (500字)</option>
                            <option>大 (800字)</option>
                        </select>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 浮动工具栏 -->
    <div class="floating-toolbar">
        <button class="floating-btn" title="保存">💾</button>
        <button class="floating-btn" title="分享">📤</button>
        <button class="floating-btn" title="AI助手">🤖</button>
        <button class="floating-btn" title="设置">⚙️</button>
    </div>

    <!-- 状态指示器 -->
    <div class="status-indicator">
        <span class="status-dot"></span>
        已保存
    </div>

    <script>
        // 标签页切换
        document.querySelectorAll('.tab-item').forEach(tab => {
            tab.addEventListener('click', function() {
                document.querySelectorAll('.tab-item').forEach(t => t.classList.remove('active'));
                this.classList.add('active');
            });
        });

        // 视图模式切换
        document.querySelectorAll('.view-mode-btn').forEach(btn => {
            btn.addEventListener('click', function() {
                document.querySelectorAll('.view-mode-btn').forEach(b => b.classList.remove('active'));
                this.classList.add('active');
            });
        });

        // 颜色选择器
        document.querySelectorAll('.color-option').forEach(option => {
            option.addEventListener('click', function() {
                document.querySelectorAll('.color-option').forEach(o => o.classList.remove('selected'));
                this.classList.add('selected');
            });
        });

        // 浮动按钮动画
        document.querySelectorAll('.floating-btn').forEach(btn => {
            btn.addEventListener('mouseenter', function() {
                this.style.transform = 'translateY(-3px) scale(1.1)';
            });
            
            btn.addEventListener('mouseleave', function() {
                this.style.transform = 'translateY(0) scale(1)';
            });
        });

        // 模拟实时保存状态
        let saveStatus = document.querySelector('.status-indicator');
        let saveDot = document.querySelector('.status-dot');
        
        setInterval(() => {
            saveStatus.textContent = '已保存';
            saveDot.style.background = '#51cf66';
            
            setTimeout(() => {
                saveStatus.textContent = '保存中...';
                saveDot.style.background = '#ffd43b';
            }, 3000);
        }, 5000);
    </script>
</body>
</html>
