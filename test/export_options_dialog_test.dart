import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:contentpal/text_cards/widgets/export_options_dialog.dart';
import 'package:contentpal/text_cards/models/export_config.dart';

void main() {
  group('ExportOptionsDialog Tests', () {
    testWidgets('should display preview effect section', (
      WidgetTester tester,
    ) async {
      final config = ExportConfig();

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Builder(
              builder:
                  (context) => ElevatedButton(
                    onPressed:
                        () => showDialog(
                          context: context,
                          builder:
                              (context) =>
                                  ExportOptionsDialog(initialConfig: config),
                        ),
                    child: const Text('Show Dialog'),
                  ),
            ),
          ),
        ),
      );

      await tester.tap(find.text('Show Dialog'));
      await tester.pumpAndSettle();

      // 验证预览效果区域存在
      expect(find.text('预览效果'), findsOneWidget);

      // 验证导出信息区域存在
      expect(find.text('导出信息'), findsOneWidget);

      // 验证配置选项区域存在
      expect(find.text('配置选项'), findsOneWidget);
    });

    testWidgets('should update config when size is changed', (
      WidgetTester tester,
    ) async {
      final config = ExportConfig();

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Builder(
              builder:
                  (context) => ElevatedButton(
                    onPressed:
                        () => showDialog(
                          context: context,
                          builder:
                              (context) =>
                                  ExportOptionsDialog(initialConfig: config),
                        ),
                    child: const Text('Show Dialog'),
                  ),
            ),
          ),
        ),
      );

      await tester.tap(find.text('Show Dialog'));
      await tester.pumpAndSettle();

      // 点击小尺寸选项
      await tester.tap(find.text('小'));
      await tester.pumpAndSettle();

      // 验证尺寸已更新
      final dialog = tester.widget<ExportOptionsDialog>(
        find.byType(ExportOptionsDialog),
      );
      expect(dialog.initialConfig.size, ExportSize.small);
    });

    testWidgets('should update config when ratio is changed', (
      WidgetTester tester,
    ) async {
      final config = ExportConfig();

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Builder(
              builder:
                  (context) => ElevatedButton(
                    onPressed:
                        () => showDialog(
                          context: context,
                          builder:
                              (context) =>
                                  ExportOptionsDialog(initialConfig: config),
                        ),
                    child: const Text('Show Dialog'),
                  ),
            ),
          ),
        ),
      );

      await tester.tap(find.text('Show Dialog'));
      await tester.pumpAndSettle();

      // 点击1:1比例选项
      await tester.tap(find.text('1:1'));
      await tester.pumpAndSettle();

      // 验证比例已更新
      final dialog = tester.widget<ExportOptionsDialog>(
        find.byType(ExportOptionsDialog),
      );
      expect(dialog.initialConfig.ratio, ExportRatio.ratio1x1);
    });

    testWidgets('should display file size estimation', (
      WidgetTester tester,
    ) async {
      final config = ExportConfig();

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Builder(
              builder:
                  (context) => ElevatedButton(
                    onPressed:
                        () => showDialog(
                          context: context,
                          builder:
                              (context) =>
                                  ExportOptionsDialog(initialConfig: config),
                        ),
                    child: const Text('Show Dialog'),
                  ),
            ),
          ),
        ),
      );

      await tester.tap(find.text('Show Dialog'));
      await tester.pumpAndSettle();

      // 验证文件大小信息显示
      expect(find.textContaining('KB'), findsOneWidget);
    });

    testWidgets('should display usage scenario', (WidgetTester tester) async {
      final config = ExportConfig();

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: Builder(
              builder:
                  (context) => ElevatedButton(
                    onPressed:
                        () => showDialog(
                          context: context,
                          builder:
                              (context) =>
                                  ExportOptionsDialog(initialConfig: config),
                        ),
                    child: const Text('Show Dialog'),
                  ),
            ),
          ),
        ),
      );

      await tester.tap(find.text('Show Dialog'));
      await tester.pumpAndSettle();

      // 验证适用场景信息显示
      expect(find.textContaining('适合'), findsOneWidget);
    });
  });
}
