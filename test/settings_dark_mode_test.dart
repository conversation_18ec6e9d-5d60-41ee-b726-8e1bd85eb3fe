import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:contentpal/generated/l10n/app_localizations.dart';
import 'package:contentpal/services/localization_service.dart';
import 'package:contentpal/settings/settings_page.dart';
import 'package:contentpal/config/app_theme.dart';

void main() {
  group('Settings Page Dark Mode Tests', () {
    late LocalizationService localizationService;

    setUp(() {
      localizationService = LocalizationService();
    });

    testWidgets('SettingsPage should adapt to light theme', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.getTheme(AppThemeType.materialYou, false), // Light theme
          locale: const Locale('en'),
          localizationsDelegates: const [
            AppLocalizations.delegate,
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          supportedLocales: LocalizationService.supportedLocales,
          home: SettingsPage(localizationService: localizationService),
        ),
      );

      // Verify the page renders without errors in light mode
      expect(find.text('Settings'), findsOneWidget);
      expect(find.text('Personalize your app experience'), findsOneWidget);
      expect(find.text('Appearance'), findsOneWidget);
      expect(find.text('Language'), findsOneWidget);
      expect(find.text('Theme'), findsOneWidget);
    });

    testWidgets('SettingsPage should adapt to dark theme', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.getTheme(AppThemeType.materialYou, true), // Dark theme
          locale: const Locale('en'),
          localizationsDelegates: const [
            AppLocalizations.delegate,
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          supportedLocales: LocalizationService.supportedLocales,
          home: SettingsPage(localizationService: localizationService),
        ),
      );

      // Verify the page renders without errors in dark mode
      expect(find.text('Settings'), findsOneWidget);
      expect(find.text('Personalize your app experience'), findsOneWidget);
      expect(find.text('Appearance'), findsOneWidget);
      expect(find.text('Language'), findsOneWidget);
      expect(find.text('Theme'), findsOneWidget);
    });

    testWidgets('SettingsPage should display localized content in dark mode', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.getTheme(AppThemeType.materialYou, true), // Dark theme
          locale: const Locale('zh'),
          localizationsDelegates: const [
            AppLocalizations.delegate,
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          supportedLocales: LocalizationService.supportedLocales,
          home: SettingsPage(localizationService: localizationService),
        ),
      );

      // Verify Chinese content is displayed correctly in dark mode
      expect(find.text('设置'), findsOneWidget);
      expect(find.text('个性化您的应用体验'), findsOneWidget);
      expect(find.text('外观'), findsOneWidget);
      expect(find.text('语言'), findsOneWidget);
      expect(find.text('主题'), findsOneWidget);
    });

    testWidgets('Theme dialog should work in dark mode', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.getTheme(AppThemeType.materialYou, true), // Dark theme
          locale: const Locale('en'),
          localizationsDelegates: const [
            AppLocalizations.delegate,
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          supportedLocales: LocalizationService.supportedLocales,
          home: SettingsPage(localizationService: localizationService),
        ),
      );

      // Tap on theme setting to open dialog
      await tester.tap(find.text('Theme'));
      await tester.pumpAndSettle();

      // Verify theme dialog shows correctly in dark mode
      expect(find.text('Select Theme'), findsOneWidget);
      expect(find.text('Light Mode'), findsOneWidget);
      expect(find.text('Dark Mode'), findsOneWidget);
      expect(find.text('Follow System'), findsOneWidget);
      
      // Should show check mark for current selection
      expect(find.byIcon(Icons.check), findsOneWidget);
    });

    testWidgets('All settings sections should be visible in dark mode', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          theme: AppTheme.getTheme(AppThemeType.materialYou, true), // Dark theme
          locale: const Locale('en'),
          localizationsDelegates: const [
            AppLocalizations.delegate,
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          supportedLocales: LocalizationService.supportedLocales,
          home: SettingsPage(localizationService: localizationService),
        ),
      );

      // Verify all sections are visible
      expect(find.text('Appearance'), findsOneWidget);
      expect(find.text('Developer'), findsOneWidget);
      expect(find.text('About'), findsOneWidget);
      
      // Verify all settings items are visible
      expect(find.text('Language'), findsOneWidget);
      expect(find.text('Theme'), findsOneWidget);
      expect(find.text('Content Library Demo'), findsOneWidget);
      expect(find.text('Internationalization Demo'), findsOneWidget);
      expect(find.text('Version Info'), findsOneWidget);
      expect(find.text('Help & Feedback'), findsOneWidget);
    });
  });
}
