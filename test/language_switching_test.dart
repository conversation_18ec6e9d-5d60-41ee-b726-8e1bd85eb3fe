import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:contentpal/generated/l10n/app_localizations.dart';
import 'package:contentpal/services/localization_service.dart';
import 'package:contentpal/home.dart';
import 'package:contentpal/settings/settings_page.dart';

void main() {
  group('Language Switching Integration Tests', () {
    late LocalizationService localizationService;

    setUp(() {
      localizationService = LocalizationService();
    });

    testWidgets('HomePage should display correct language after switching', (WidgetTester tester) async {
      // Set initial language to English
      await localizationService.setLocale(const Locale('en'));

      await tester.pumpWidget(
        MaterialApp(
          locale: const Locale('en'),
          localizationsDelegates: const [
            AppLocalizations.delegate,
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          supportedLocales: LocalizationService.supportedLocales,
          home: HomePage(localizationService: localizationService),
        ),
      );

      // Verify English content is displayed
      expect(find.text('My Content Library'), findsOneWidget);
      expect(find.text('Manage and browse all your content'), findsOneWidget);
      expect(find.text('Recommended Tools'), findsOneWidget);

      // Switch to Chinese
      await localizationService.setLocale(const Locale('zh'));
      await tester.pumpWidget(
        MaterialApp(
          locale: const Locale('zh'),
          localizationsDelegates: const [
            AppLocalizations.delegate,
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          supportedLocales: LocalizationService.supportedLocales,
          home: HomePage(localizationService: localizationService),
        ),
      );

      // Verify Chinese content is displayed
      expect(find.text('我的内容库'), findsOneWidget);
      expect(find.text('管理和浏览您的所有内容'), findsOneWidget);
      expect(find.text('推荐工具'), findsOneWidget);
    });

    testWidgets('SettingsPage should display correct language after switching', (WidgetTester tester) async {
      // Set initial language to English
      await localizationService.setLocale(const Locale('en'));

      await tester.pumpWidget(
        MaterialApp(
          locale: const Locale('en'),
          localizationsDelegates: const [
            AppLocalizations.delegate,
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          supportedLocales: LocalizationService.supportedLocales,
          home: SettingsPage(localizationService: localizationService),
        ),
      );

      // Verify English content is displayed
      expect(find.text('Appearance'), findsOneWidget);
      expect(find.text('Developer'), findsOneWidget);
      expect(find.text('About'), findsOneWidget);

      // Switch to Japanese
      await localizationService.setLocale(const Locale('ja'));
      await tester.pumpWidget(
        MaterialApp(
          locale: const Locale('ja'),
          localizationsDelegates: const [
            AppLocalizations.delegate,
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          supportedLocales: LocalizationService.supportedLocales,
          home: SettingsPage(localizationService: localizationService),
        ),
      );

      // Verify Japanese content is displayed
      expect(find.text('外観'), findsOneWidget);
      expect(find.text('開発者'), findsOneWidget);
      expect(find.text('について'), findsOneWidget);
    });

    test('LocalizationService should correctly handle locale changes', () async {
      // Test setting English
      await localizationService.setLocale(const Locale('en'));
      expect(localizationService.currentLocale?.languageCode, 'en');
      expect(localizationService.currentLocaleName, 'English');

      // Test setting Chinese
      await localizationService.setLocale(const Locale('zh'));
      expect(localizationService.currentLocale?.languageCode, 'zh');
      expect(localizationService.currentLocaleName, '中文');

      // Test setting Japanese
      await localizationService.setLocale(const Locale('ja'));
      expect(localizationService.currentLocale?.languageCode, 'ja');
      expect(localizationService.currentLocaleName, '日本語');

      // Test setting to follow system
      await localizationService.setLocale(null);
      expect(localizationService.currentLocale, null);
      expect(localizationService.currentLocaleName, 'Follow System');
    });

    test('LocalizationService should persist locale settings', () async {
      // Set a locale
      await localizationService.setLocale(const Locale('zh'));
      expect(localizationService.currentLocale?.languageCode, 'zh');

      // Create a new instance to simulate app restart
      final newService = LocalizationService();
      await newService.initialize();
      
      // The locale should be persisted
      expect(newService.currentLocale?.languageCode, 'zh');
    });
  });
}
