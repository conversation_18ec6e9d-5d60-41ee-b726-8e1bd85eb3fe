import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:contentpal/text_cards/widgets/advanced_text_editor.dart';
import 'package:contentpal/text_cards/models/enhanced_card_template.dart';

void main() {
  group('AdvancedTextEditor Color Tests', () {
    testWidgets('should display text editor with dark background template', (
      WidgetTester tester,
    ) async {
      // 使用深色背景的模板
      final darkTemplate = EnhancedCardTemplate(
        id: 'test_dark',
        name: 'Test Dark',
        category: 'test',
        description: 'Test dark template',
        backgroundGradient: const LinearGradient(
          colors: [Color(0xFF2C3E50), Color(0xFF34495E)],
        ),
        textColor: Colors.white,
        titleColor: Colors.white,
        accentColor: Colors.orange,
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: AdvancedTextEditor(
              initialText: 'Test content',
              template: darkTemplate,
              onTextChanged: (text) {},
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // 验证文本输入框存在
      expect(find.byType(TextField), findsOneWidget);

      // 验证文本输入框有内容
      expect(find.text('Test content'), findsOneWidget);
    });

    testWidgets('should display text editor with light background template', (
      WidgetTester tester,
    ) async {
      // 使用浅色背景的模板
      final lightTemplate = EnhancedCardTemplate(
        id: 'test_light',
        name: 'Test Light',
        category: 'test',
        description: 'Test light template',
        backgroundGradient: const LinearGradient(
          colors: [Color(0xFFFFFFFF), Color(0xFFF8F9FA)],
        ),
        textColor: const Color(0xFF333333),
        titleColor: const Color(0xFF1A1A1A),
        accentColor: const Color(0xFF4F46E5),
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: AdvancedTextEditor(
              initialText: 'Test content',
              template: lightTemplate,
              onTextChanged: (text) {},
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // 验证文本输入框存在
      expect(find.byType(TextField), findsOneWidget);

      // 验证文本输入框有内容
      expect(find.text('Test content'), findsOneWidget);
    });

    testWidgets('should handle text input correctly', (
      WidgetTester tester,
    ) async {
      String currentText = 'Initial text';

      final template = EnhancedCardTemplate(
        id: 'test',
        name: 'Test',
        category: 'test',
        description: 'Test template',
        backgroundGradient: const LinearGradient(
          colors: [Color(0xFF667EEA), Color(0xFF764BA2)],
        ),
        textColor: Colors.white,
        titleColor: Colors.white,
        accentColor: const Color(0xFFE2E8F0),
      );

      await tester.pumpWidget(
        MaterialApp(
          home: Scaffold(
            body: AdvancedTextEditor(
              initialText: currentText,
              template: template,
              onTextChanged: (text) {
                currentText = text;
              },
            ),
          ),
        ),
      );

      await tester.pumpAndSettle();

      // 验证初始文本存在
      expect(find.text('Initial text'), findsOneWidget);

      // 输入新文本
      await tester.enterText(find.byType(TextField), 'New text content');
      await tester.pump();

      // 验证文本已更新
      expect(currentText, equals('New text content'));
    });
  });
}
