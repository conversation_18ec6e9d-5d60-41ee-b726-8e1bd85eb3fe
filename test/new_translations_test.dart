import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:contentpal/generated/l10n/app_localizations.dart';
import 'package:contentpal/services/localization_service.dart';

void main() {
  group('New Translation Keys Tests', () {
    testWidgets('New translation keys should work in English', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          locale: const Locale('en'),
          localizationsDelegates: const [
            AppLocalizations.delegate,
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          supportedLocales: LocalizationService.supportedLocales,
          home: Builder(
            builder: (context) {
              final l10n = AppLocalizations.of(context);
              return Scaffold(
                body: Column(
                  children: [
                    Text(l10n.appearance),
                    Text(l10n.followSystem),
                    Text(l10n.languageChangeEffect),
                    Text(l10n.manageAllCards),
                    Text(l10n.browseBeautifulTemplates),
                    Text(l10n.inputTextToSplit),
                    Text(l10n.pasteClipboard),
                    Text(l10n.clearContent),
                    Text(l10n.cardNumber(1)),
                    Text(l10n.loadingDemoData),
                    Text(l10n.createNewContent),
                    Text(l10n.selectContentType),
                    Text(l10n.bold),
                    Text(l10n.italic),
                    Text(l10n.heading1),
                  ],
                ),
              );
            },
          ),
        ),
      );

      // Verify new English translations
      expect(find.text('Appearance'), findsOneWidget);
      expect(find.text('Follow System'), findsOneWidget);
      expect(find.text('Language changes will take effect immediately'), findsOneWidget);
      expect(find.text('Manage All Cards'), findsOneWidget);
      expect(find.text('Browse Beautiful Templates'), findsOneWidget);
      expect(find.text('Input text to split'), findsOneWidget);
      expect(find.text('Paste Clipboard'), findsOneWidget);
      expect(find.text('Clear Content'), findsOneWidget);
      expect(find.text('Card 1'), findsOneWidget);
      expect(find.text('Loading demo data...'), findsOneWidget);
      expect(find.text('Create New Content'), findsOneWidget);
      expect(find.text('Select the type of content you want to create'), findsOneWidget);
      expect(find.text('**Bold**'), findsOneWidget);
      expect(find.text('*Italic*'), findsOneWidget);
      expect(find.text('# Heading 1'), findsOneWidget);
    });

    testWidgets('New translation keys should work in Chinese', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          locale: const Locale('zh'),
          localizationsDelegates: const [
            AppLocalizations.delegate,
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          supportedLocales: LocalizationService.supportedLocales,
          home: Builder(
            builder: (context) {
              final l10n = AppLocalizations.of(context);
              return Scaffold(
                body: Column(
                  children: [
                    Text(l10n.appearance),
                    Text(l10n.followSystem),
                    Text(l10n.languageChangeEffect),
                    Text(l10n.manageAllCards),
                    Text(l10n.browseBeautifulTemplates),
                    Text(l10n.inputTextToSplit),
                    Text(l10n.pasteClipboard),
                    Text(l10n.clearContent),
                    Text(l10n.cardNumber(1)),
                    Text(l10n.loadingDemoData),
                    Text(l10n.createNewContent),
                    Text(l10n.selectContentType),
                    Text(l10n.bold),
                    Text(l10n.italic),
                    Text(l10n.heading1),
                  ],
                ),
              );
            },
          ),
        ),
      );

      // Verify new Chinese translations
      expect(find.text('外观'), findsOneWidget);
      expect(find.text('跟随系统'), findsOneWidget);
      expect(find.text('更改语言后，应用将立即生效'), findsOneWidget);
      expect(find.text('管理所有卡片'), findsOneWidget);
      expect(find.text('浏览精美模板'), findsOneWidget);
      expect(find.text('输入要拆分的文本'), findsOneWidget);
      expect(find.text('粘贴剪贴板'), findsOneWidget);
      expect(find.text('清空内容'), findsOneWidget);
      expect(find.text('卡片 1'), findsOneWidget);
      expect(find.text('正在加载演示数据...'), findsOneWidget);
      expect(find.text('创建新内容'), findsOneWidget);
      expect(find.text('选择您要创建的内容类型'), findsOneWidget);
      expect(find.text('**粗体**'), findsOneWidget);
      expect(find.text('*斜体*'), findsOneWidget);
      expect(find.text('# 标题1'), findsOneWidget);
    });

    testWidgets('New translation keys should work in Japanese', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          locale: const Locale('ja'),
          localizationsDelegates: const [
            AppLocalizations.delegate,
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          supportedLocales: LocalizationService.supportedLocales,
          home: Builder(
            builder: (context) {
              final l10n = AppLocalizations.of(context);
              return Scaffold(
                body: Column(
                  children: [
                    Text(l10n.appearance),
                    Text(l10n.followSystem),
                    Text(l10n.languageChangeEffect),
                    Text(l10n.manageAllCards),
                    Text(l10n.browseBeautifulTemplates),
                    Text(l10n.inputTextToSplit),
                    Text(l10n.pasteClipboard),
                    Text(l10n.clearContent),
                    Text(l10n.cardNumber(1)),
                    Text(l10n.loadingDemoData),
                    Text(l10n.createNewContent),
                    Text(l10n.selectContentType),
                    Text(l10n.bold),
                    Text(l10n.italic),
                    Text(l10n.heading1),
                  ],
                ),
              );
            },
          ),
        ),
      );

      // Verify new Japanese translations
      expect(find.text('外観'), findsOneWidget);
      expect(find.text('システムに従う'), findsOneWidget);
      expect(find.text('言語の変更は即座に有効になります'), findsOneWidget);
      expect(find.text('すべてのカードを管理'), findsOneWidget);
      expect(find.text('美しいテンプレートを閲覧'), findsOneWidget);
      expect(find.text('分割するテキストを入力'), findsOneWidget);
      expect(find.text('クリップボードから貼り付け'), findsOneWidget);
      expect(find.text('コンテンツをクリア'), findsOneWidget);
      expect(find.text('カード 1'), findsOneWidget);
      expect(find.text('デモデータを読み込み中...'), findsOneWidget);
      expect(find.text('新しいコンテンツを作成'), findsOneWidget);
      expect(find.text('作成するコンテンツの種類を選択してください'), findsOneWidget);
      expect(find.text('**太字**'), findsOneWidget);
      expect(find.text('*斜体*'), findsOneWidget);
      expect(find.text('# 見出し1'), findsOneWidget);
    });
  });
}
