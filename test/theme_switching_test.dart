import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:contentpal/generated/l10n/app_localizations.dart';
import 'package:contentpal/services/localization_service.dart';
import 'package:contentpal/settings/settings_page.dart';

void main() {
  group('Theme Switching Tests', () {
    late LocalizationService localizationService;

    setUp(() {
      localizationService = LocalizationService();
    });

    testWidgets('SettingsPage should display theme options in correct language', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          locale: const Locale('en'),
          localizationsDelegates: const [
            AppLocalizations.delegate,
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          supportedLocales: LocalizationService.supportedLocales,
          home: SettingsPage(localizationService: localizationService),
        ),
      );

      // Verify English theme-related text is displayed
      expect(find.text('Theme'), findsOneWidget);
      expect(find.text('Appearance'), findsOneWidget);

      // Tap on theme setting to open dialog
      await tester.tap(find.text('Theme'));
      await tester.pumpAndSettle();

      // Verify theme dialog shows English options
      expect(find.text('Select Theme'), findsOneWidget);
      expect(find.text('Light Mode'), findsOneWidget);
      expect(find.text('Dark Mode'), findsOneWidget);
      expect(find.text('Follow System'), findsOneWidget);
    });

    testWidgets('SettingsPage should display theme options in Chinese', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          locale: const Locale('zh'),
          localizationsDelegates: const [
            AppLocalizations.delegate,
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          supportedLocales: LocalizationService.supportedLocales,
          home: SettingsPage(localizationService: localizationService),
        ),
      );

      // Verify Chinese theme-related text is displayed
      expect(find.text('主题'), findsOneWidget);
      expect(find.text('外观'), findsOneWidget);

      // Tap on theme setting to open dialog
      await tester.tap(find.text('主题'));
      await tester.pumpAndSettle();

      // Verify theme dialog shows Chinese options
      expect(find.text('选择主题'), findsOneWidget);
      expect(find.text('浅色模式'), findsOneWidget);
      expect(find.text('深色模式'), findsOneWidget);
      expect(find.text('跟随系统'), findsOneWidget);
    });

    testWidgets('SettingsPage should display theme options in Japanese', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          locale: const Locale('ja'),
          localizationsDelegates: const [
            AppLocalizations.delegate,
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          supportedLocales: LocalizationService.supportedLocales,
          home: SettingsPage(localizationService: localizationService),
        ),
      );

      // Verify Japanese theme-related text is displayed
      expect(find.text('テーマ'), findsOneWidget);
      expect(find.text('外観'), findsOneWidget);

      // Tap on theme setting to open dialog
      await tester.tap(find.text('テーマ'));
      await tester.pumpAndSettle();

      // Verify theme dialog shows Japanese options
      expect(find.text('テーマを選択'), findsOneWidget);
      expect(find.text('ライトモード'), findsOneWidget);
      expect(find.text('ダークモード'), findsOneWidget);
      expect(find.text('システムに従う'), findsOneWidget);
    });

    testWidgets('Theme dialog should show current selection with check mark', (WidgetTester tester) async {
      await tester.pumpWidget(
        MaterialApp(
          locale: const Locale('en'),
          localizationsDelegates: const [
            AppLocalizations.delegate,
            GlobalMaterialLocalizations.delegate,
            GlobalWidgetsLocalizations.delegate,
            GlobalCupertinoLocalizations.delegate,
          ],
          supportedLocales: LocalizationService.supportedLocales,
          home: SettingsPage(localizationService: localizationService),
        ),
      );

      // Tap on theme setting to open dialog
      await tester.tap(find.text('Theme'));
      await tester.pumpAndSettle();

      // Should show check mark for current selection (default is system)
      expect(find.byIcon(Icons.check), findsOneWidget);
    });
  });
}
