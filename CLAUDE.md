# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

ContentPal (内容君) is a comprehensive Flutter-based content processing application that provides multiple content editing and processing features with cross-platform deployment capabilities. The app follows a modular architecture with support for iOS, Android, Web, and Desktop platforms.

## Key Development Commands

### Build and Analysis
- `flutter analyze` - Run static analysis (required after code changes)
- `flutter test` - Run unit tests
- `flutter build apk` - Build Android APK
- `flutter build ios` - Build iOS app
- `flutter build web` - Build web version
- `flutter pub get` - Get dependencies
- `flutter clean` - Clean build cache

### Development
- `flutter run` - Run the app (only when explicitly requested)
- Avoid running unless explicitly requested by the user

## Architecture Overview

### Core Structure
The app uses a modular architecture with clear separation of concerns and a centralized service locator pattern:

```
lib/
├── main.dart                 # App entry point with initialization
├── home.dart                 # Main dashboard with navigation
├── config/                   # Global configuration
│   ├── app_settings.dart     # App-wide settings
│   ├── app_theme.dart        # Theme and color configuration
│   ├── constants.dart        # App constants
│   └── chinese_traditional_colors.dart  # Color themes
├── services/                 # Service layer with dependency injection
│   ├── service_locator.dart  # Central service manager
│   ├── storage_service.dart  # Data persistence (Hive)
│   ├── settings_service.dart  # User preferences
│   ├── content_service.dart  # Content management
│   └── ...                  # Other services
├── models/                   # Data models and entities
├── common/                   # Shared utilities and widgets
├── [feature_modules]/        # Individual feature modules
└── generated/                # Auto-generated localization files
```

### Service Layer Architecture
- **ServiceLocator**: Central dependency injection container managing all services
- **Services**: Business logic separated from UI components
- **Storage**: Hive for local database, SharedPreferences for settings
- **State Management**: flutter_bloc for complex state, StatefulWidget for simple state

### Theme System
The app includes a comprehensive theming system with 6 theme types:
- Material You dynamic theme
- Morandi color scheme
- Monochrome theme
- Nature color scheme
- Tech theme
- Chinese traditional colors theme

### Feature Modules
Each feature module is self-contained with its own models, services, and UI:
- **content/**: General content management and library
- **markdown/**: Markdown editing, preview, and export with block-based system
- **html/**: HTML editing and processing
- **svg/**: SVG icon editing and optimization
- **pdf/**: PDF viewing, annotation, and security features
- **voice/**: Audio recording, speech-to-text, and transcription
- **text_cards/**: Text card creation with templates and export
- **traffic_guide/**: Traffic guide content generation and image creation
- **settings/**: App configuration and preferences
- **subscription/**: In-app purchase management

## Development Guidelines

### Code Quality
- **File limit**: Maximum 600 lines per source file
- **Analysis**: Must run `flutter analyze` after changes and fix all issues
- **Linting**: Follow rules in `analysis_options.yaml` (includes `use_build_context_synchronously: false`)
- **SOLID Principles**: Adhere to all SOLID design principles

### Dependencies
- **Allowed**: flutter_bloc, hive, shared_preferences, standard Flutter packages
- **Prohibited**: GetX, code generation libraries (build_runner, json_annotation)
- **Third-party**: Only use well-maintained packages from pub.dev
- **Key dependencies**: flutter_bloc, hive, shared_preferences, flutter_svg, syncfusion_flutter_pdf

### State Management
- Use `flutter_bloc` for complex business logic
- Use `StatefulWidget` for simple UI state
- Avoid global state when possible
- Separate business logic from UI components

### File Organization
- Group related functionality together
- Use feature-based directory structure
- Implement barrel exports (`index.dart`) for clean imports
- Follow naming conventions: camelCase for variables, PascalCase for classes

### Performance
- Use `const` constructors where possible
- Implement proper `shouldRepaint` and `shouldRebuild` logic
- Use `ListView.builder` for long lists
- Properly dispose controllers and streams

## Key Features

### Markdown Processing
- Real-time preview with customizable styles
- Block-based editing system
- Template system for different output formats
- Watermark functionality
- Export to multiple formats
- Section management with preview

### Text Cards
- Visual card creation with templates
- Smart text splitting
- Export to images and PDF
- Rich text editing capabilities
- Template gallery with multiple designs

### Voice Features
- Audio recording with flutter_sound
- Speech-to-text conversion
- Text-to-speech playback
- Smart transcription

### Traffic Guide
- Image generation for traffic guides
- Text transformation tools
- Watermark system
- Real-time preview
- Template-based design

### Multi-platform Support
- iOS: Full support with native integrations, ICP备案 support
- Android: Complete feature set
- Web: Basic functionality available
- Desktop: Linux, macOS, Windows support

## Internationalization
- Full i18n support with ARB files
- Localized strings in `lib/l10n/`
- Support for Chinese (Simplified), English, Japanese
- Runtime language switching

## Testing
- Unit tests in `test/` directory
- Widget tests for UI components
- Integration tests for critical flows
- Maintain 70%+ test coverage

## Build Configuration
- **SDK Constraint**: ^3.7.2
- **Flutter**: Uses material design with custom themes
- **Fonts**: Multiple Chinese font families (SourceHanSansCN, AlibabaPuHuiTi, HarmonyOS_Sans_SC, MiSans)
- **Assets**: Images and fonts in `assets/` directory

## Platform-Specific Notes
- **iOS**: Additional permissions and configurations for voice features, file intent handling
- **Android**: Standard Flutter setup with custom permissions
- **Web**: Limited features due to browser API restrictions
- **Desktop**: Full feature set with platform-specific optimizations

## Important Development Notes
- Always run `flutter analyze` after making changes
- Follow the 600-line file limit strictly
- Use the ServiceLocator pattern for dependency injection
- Maintain the modular architecture with clear feature separation
- All services must be registered in the ServiceLocator
- Use the existing theme system for UI consistency